BWXP_ENEMY_LEVEL EQU $D213
BWXP_ENEMY_SPECIES EQU $D206
BWXP_MAX_LEVEL EQU 100
BWXP_BATTLE_TYPE EQU $D22D
BWXP_MULTIPLICAND EQU $FFB3
BWXP_MULTIPLIER EQU $FFB7
BWXP_DIVIDEND EQU BWXP_MULTIPLICAND
BWXP_DIVISOR EQU BWXP_MULTIPLIER
BWXP_INBUILT_MULTIPLY EQU $3119 
BWXP_INBUILT_DIVIDE EQU $3124 
BWXP_INBUILT_PARTYPARAMLOC EQU $3917 ; $3917 = GetPartyParamLocation
BWXP_PARTYPARAM_LEVEL EQU $1F
BWXP_PARTYPARAM_TID EQU $06
BWXP_PARTYPARAM_HELDITEM EQU $01
BWXP_PARTYPARAM_HP EQU $02
BWXP_NUM_PARTICIPANTS EQU $D265
BWXP_SCRATCH5B_1 EQU $D208
BWXP_SCRATCH5B_2 EQU $D20D
BWXP_SCRATCH1B EQU $D212
BWXP_MULTIPLIER_STOR EQU $FFB9
BWXP_BIG_MULTIPLICAND EQU $FFB2
BWXP_BIG_DIVIDEND EQU BWXP_BIG_MULTIPLICAND
BWXP_PLAYER_TID EQU $D47B
BWXP_BOOSTED_EXP_FLAG EQU $D088
BWXP_DIVIDEEXP_RETURN_POINT EQU $70EA
BWXP_PARTYCOUNT EQU $DCD7
BWXP_PARTYMON1 EQU $DCDF
BWXP_PARTYMON2 EQU $DD0F
BWXP_MAIN_RETURN_POINT EQU $6F02
BWXP_EXPADDER_RETURN_POINT EQU $6F3D
BWXP_UNKNOWNFUNC1 EQU $7136
BWXP_UNKNOWNFUNC2 EQU $309D