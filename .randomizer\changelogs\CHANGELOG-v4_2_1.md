# Changes - v4.2.1

---
## General

- CHANGED: Launcher Scripts
    - The launcher scripts no longer close automatically when the randomizer is closed.

- FIX: Crash When GitHub is Inaccessible
    - Fixed an issue where the randomizer would crash if it couldn't connect to GitHub.

---
## Pokemon Traits

### Pokemon Evolutions

- CHANGED: <PERSON><PERSON><PERSON>'s Beauty Evolution
    - Omega Ruby/Alpha Sapphire: When evolutions are randomized, <PERSON><PERSON><PERSON> can now evolve when it reaches 170 Beauty.
        - It will evolve into the same Pokemon it will evolve into when using a Prism Scale.

---
## Starters, Statics & Trades

### Starter Pokemon

- FIX: Starter Text
    - Fire Red/Leaf Green: Fixed <PERSON>'s text being incorrect if <PERSON><PERSON><PERSON> or <PERSON><PERSON><PERSON><PERSON> appear as starters.

### Static Pokemon

- ADDED SUPPORT: Randomized Static Pokemon
    - The following Static Pokemon are now also randomized:
        - Heart Gold/Soul Silver: The Pokemon available in the Goldenrod and Celadon Game Corners are now randomized. (All versions except Korean).
        - Black/White: The roaming Tornadus/Thundurus are now randomized.

---
## <PERSON><PERSON>

- Setting: Random
    - Gen 4: No longer allows <PERSON><PERSON><PERSON><PERSON><PERSON> to appear as a trainer <PERSON><PERSON><PERSON> due to it reverting back to Altered Forme immediately without the Griseous Orb.

- FIX: Rival Carries Starter
    - Heart Gold/Soul Silver: Fixed an issue where the Rival's starter did not carry forward the same ability when abilities were randomized.

---
## Wild Pokemon

- Settings: Random & Area 1-to-1 Mapping & Global 1-to-1 Mapping
    - Ruby/Sapphire/Emerald: Unown is now allowed to appear as a wild Pokemon.

---
## TM/HMs & Tutors

### TMs & HMs

- Setting: Random
    - Gen 3: Now changes text related to TMs in all English games to reflect what the TMs are after randomization.
    - Gen 4: Now changes text related to TMs in all English games to reflect what the TMs are after randomization.

### Move Tutors

- Setting: Random
    - Gen 3: Now changes text related to Move Tutors in all English games to reflect what the Move Tutor moves are after randomization.

- FIX: HG/SS Headbutt Tutor
    - Fixes an issue where the Move Tutor in Ilex Forest could still teach Headbutt even if Move Tutors were randomized.