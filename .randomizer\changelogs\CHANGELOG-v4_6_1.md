# Changes

---
## General

- ADDED SUPPORT: <PERSON>/<PERSON>'s <PERSON> Po<PERSON>mon
    - <PERSON>/<PERSON><PERSON>'s <PERSON><PERSON><PERSON> in HG/SS can now be randomized into Pokemon with big sprites (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, etc.)
---
## Pokemon Traits

### Pokemon Abilities

- FIX: Combine Duplicate Abilities
    - Fixed an issue where this setting would rarely result in duplicate abilities.

---
## Starters, Statics & Trades

### Starter Pokemon

- FIX: Randomize Starter Held Items
    - Fixed an issue where this setting would not be set properly when loading settings for Gen 4+ games.

### Static Pokemon

- FIX: Fix Music
    - B2/W2: Fixed an issue where this setting would crash the game when entering a wild Pokemon encounter if evolutions were randomized.

---
## Moves & Movesets

### Move Data

- CHANGED: Update Moves to Generation
    - When updating moves to Gen 9, this setting will now include move changes introduced in later patches.
        - The moves affected by this change are Luster Purge and Mist Ball.
