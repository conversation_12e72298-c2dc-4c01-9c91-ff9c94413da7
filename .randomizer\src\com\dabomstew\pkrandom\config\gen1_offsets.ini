[Red (U)]
Game=POKEMON RED
Version=0
NonJapanese=1
Type=RB
ExtraTableFile=rby_english
BWXPTweak=bwexp/rb_en_bwxp
XAccNerfTweak=rb_en_xaccnerf
CritRateTweak=rb_en_critrate
InternalPokemonCount=190
PokedexOrder=0x41024
PokemonNamesOffset=0x1C21E
PokemonNamesLength=10
PokemonStatsOffset=0x383DE
MewStatsOffset=0x425B
WildPokemonTableOffset=0xCEEB
OldRodOffset=0xE252
GoodRodOffset=0xE27F
SuperRodTableOffset=0xE919
MapNameTableOffset=0x71313
MoveCount=165
MoveDataOffset=0x38000
MoveNamesOffset=0xB0000
ItemNamesOffset=0x472B
TypeEffectivenessOffset=0x3E474
PokemonMovesetsTableOffset=0x3B05C
PokemonMovesetsDataSize=0x814
PokemonMovesetsExtraSpaceOffset=0x3BBE6
StarterOffsets1=[0x1D126, 0x1CC84, 0x1D10E, 0x39CF8, 0x50FB3, 0x510DD]
StarterOffsets2=[0x1D104, 0x19591, 0x1CC88, 0x1CDC8, 0x1D11F, 0x50FAF, 0x510D9, 0x51CAF, 0x6060E, 0x61450, 0x75F9E]
StarterOffsets3=[0x1D115, 0x19599, 0x1CDD0, 0x1D130, 0x39CF2, 0x50FB1, 0x510DB, 0x51CB7, 0x60616, 0x61458, 0x75FA6]
PatchPokedex=1
CanChangeStarterText=1
CanChangeTrainerText=1
StarterTextOffsets=[0x94E07, 0x94E30, 0x94E58]
StarterPokedexOnOffset=0x5C0DC
StarterPokedexOffOffset=0x5C0E6
StarterPokedexBranchOffset=0x5E000
PokedexRamOffset=0xD2F7
TrainerDataTableOffset=0x39D3B
TrainerDataClassCounts=[0, 13, 14, 18, 8, 9, 24, 7, 12, 14, 15, 9, 3, 0, 11, 15, 9, 7, 15, 4, 2, 8, 6, 17, 9, 9, 3, 0, 13, 3, 41, 10, 8, 1, 1, 1, 1, 1, 1, 1, 1, 5, 12, 3, 1, 24, 1, 1]
ExtraTrainerMovesTableOffset=0x39D32
GymLeaderMovesTableOffset=0x39D23
TMMovesOffset=0x13773
TrainerClassNamesOffsets=[0x27EC2, 0x399FF]
IntroPokemonOffset=0x616D
IntroCryOffset=0x1C73
MapBanks=0xC23D
MapAddresses=0x01AE
SpecialMapList=0x46A40
SpecialMapPointerTable=0x46A96
HiddenItemRoutine=0x76688
TradeTableOffset=0x71B7B
TradeTableSize=10
TradeNameLength=11
TradesUnused=[2]
TextDelayFunctionOffset=0x38D3
PCPotionOffset=0x6134
CatchingTutorialMonOffset=0x19085
MonPaletteIndicesOffset=0x725C8
SGBPalettesOffset=0x72660
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x49320], Level=[0x4931F]} // Magikarp
StaticPokemon{}={Species=[0x1DD49], Level=[0x1DD48]} // Eevee
StaticPokemon{}={Species=[0x5CF17], Level=[0x5CF2F]} // Hitmonlee
StaticPokemon{}={Species=[0x5CF5F], Level=[0x5CF77]} // Hitmonchan
StaticPokemon{}={Species=[0x51DAD], Level=[0x51DAC]} // Lapras
StaticPokemon{}={Species=[0x61068], Level=[0x75DB5]} // Omanyte
StaticPokemon{}={Species=[0x6106C], Level=[0x75DB5]} // Kabuto
StaticPokemon{}={Species=[0x61064], Level=[0x75DB5]} // Aerodactyl
StaticPokemon{}={Species=[0x59630, 0x61BEB], Level=[0x59635]} // Snorlax 1
StaticPokemon{}={Species=[0x59970], Level=[0x59975]} // Snorlax 2
StaticPokemon{}={Species=[0x1E3D5], Level=[0x1E3D6]} // Voltorb 1
StaticPokemon{}={Species=[0x1E3DD], Level=[0x1E3DE]} // Voltorb 2
StaticPokemon{}={Species=[0x1E3E5], Level=[0x1E3E6]} // Voltorb 3
StaticPokemon{}={Species=[0x1E3F5], Level=[0x1E3F6]} // Voltorb 4
StaticPokemon{}={Species=[0x1E3FD], Level=[0x1E3FE]} // Voltorb 5
StaticPokemon{}={Species=[0x1E40D], Level=[0x1E40E]} // Voltorb 6
StaticPokemon{}={Species=[0x1E3ED], Level=[0x1E3EE]} // Electrode 1
StaticPokemon{}={Species=[0x1E405], Level=[0x1E406]} // Electrode 2
StaticPokemon{}={Species=[0x468A8, 0x468E8, 0x5DB9E], Level=[0x468E9]} // Articuno
StaticPokemon{}={Species=[0x1E3B5, 0x1E415], Level=[0x1E416]} // Zapdos
StaticPokemon{}={Species=[0x518C0, 0x51963], Level=[0x51964]} // Moltres
StaticPokemon{}={Species=[0x45F2C, 0x45F44], Level=[0x45F45]} // Mewtwo
StaticPokemon{}={Species=[0x52859, 0x5298A], Level=[0x5298B]} // Abra
StaticPokemon{}={Species=[0x5285A, 0x5298C], Level=[0x5298D]} // Clefairy
StaticPokemon{}={Species=[0x5285B, 0x5298E], Level=[0x5298F]} // Nidorina
StaticPokemon{}={Species=[0x52864, 0x52990], Level=[0x52991]} // Dratini
StaticPokemon{}={Species=[0x52865, 0x52992], Level=[0x52993]} // Scyther
StaticPokemon{}={Species=[0x52866, 0x52994], Level=[0x52995]} // Porygon
StaticPokemonGhostMarowak{}={Species=[0xD6F4, 0x3EF9A, 0x58DE4, 0x60B33, 0x60C0A, 0x708E1], Level=[0x60B38]}
TMText[]=[6,0x0A0100,\pTM06 contains\n%m!\e]
TMText[]=[11,0x98A7C,TM11 teaches\n%m!\e]
TMText[]=[13,0x9CC22,contains\n%m!\e]
TMText[]=[18,0x9C86F,TM18 is\n%m!\e]
TMText[]=[21,0x9D521,\pTM21 contains\n%m.\e]
TMText[]=[24,0x9C0F6,\pTM24 contains\n%m!\e]
TMText[]=[27,0x96096,\pTM27 is\n%m!\e]
TMText[]=[28,0x9875D,Those miserable\nROCKETs!\pLook what they\ndid here!\pThey stole a TM\nfor teaching\l[POKé]MON how to\l%m!\e]
TMText[]=[28,0x987E3,I figure what's\nlost is lost!\pI decided to get\n%m\lwithout a TM!\e]
TMText[]=[29,0xA253F,TM29 is\n%m!\e]
TMText[]=[31,0xA168A,\pTM31 contains my\nfavorite,\l%m!\e]
TMText[]=[34,0x980C1,\pA TM contains a\ntechnique that\lcan be taught to\l[POKé]MON!\pA TM is good only\nonce! So when you\luse one to teach\la new technique,\lpick the [POKé]MON\lcarefully!\pTM34 contains\n%m!\e]
TMText[]=[36,0x824CA,TM36 is\n%m!\e]
TMText[]=[38,0xA09BD,\pTM38 contains\n%m!\e]
TMText[]=[39,0x8C8DA,TM39 is the move\n%m.\e]
TMText[]=[41,0xA5B6F,TM41 teaches\n%m!\pMany [POKé]MON\ncan use it!\e]
TMText[]=[42,0xA46AE,TM42 contains\n%m...\e]
TMText[]=[46,0xA1DE1,\pTM46 is\n%m!\e]
TMText[]=[48,0x9CCAD,contains\n%m!\e]
TMText[]=[49,0x9CD31,\pTM49 is\n%m!\e]
CRC32=9F7FDD53

[Blue (U)]
Game=POKEMON BLUE
Version=0
NonJapanese=1
Type=RB
CopyTMText=1
CopyFrom=Red (U)
BWXPTweak=bwexp/rb_en_bwxp
XAccNerfTweak=rb_en_xaccnerf
CritRateTweak=rb_en_critrate
HiddenItemRoutine=0x76689
StarterOffsets2=[0x1D104, 0x19591, 0x1CC88, 0x1CDC8, 0x1D11F, 0x50FAF, 0x510D9, 0x51CAF, 0x6060E, 0x61450, 0x75F9F]
StarterOffsets3=[0x1D115, 0x19599, 0x1CDD0, 0x1D130, 0x39CF2, 0x50FB1, 0x510DB, 0x51CB7, 0x60616, 0x61458, 0x75FA7]
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x49320], Level=[0x4931F]} // Magikarp
StaticPokemon{}={Species=[0x1DD49], Level=[0x1DD48]} // Eevee
StaticPokemon{}={Species=[0x5CF17], Level=[0x5CF2F]} // Hitmonlee
StaticPokemon{}={Species=[0x5CF5F], Level=[0x5CF77]} // Hitmonchan
StaticPokemon{}={Species=[0x51DAD], Level=[0x51DAC]} // Lapras
StaticPokemon{}={Species=[0x61068], Level=[0x75DB6]} // Omanyte
StaticPokemon{}={Species=[0x6106C], Level=[0x75DB6]} // Kabuto
StaticPokemon{}={Species=[0x61064], Level=[0x75DB6]} // Aerodactyl
StaticPokemon{}={Species=[0x59630, 0x61BEB], Level=[0x59635]} // Snorlax 1
StaticPokemon{}={Species=[0x59970], Level=[0x59975]} // Snorlax 2
StaticPokemon{}={Species=[0x1E3D5], Level=[0x1E3D6]} // Voltorb 1
StaticPokemon{}={Species=[0x1E3DD], Level=[0x1E3DE]} // Voltorb 2
StaticPokemon{}={Species=[0x1E3E5], Level=[0x1E3E6]} // Voltorb 3
StaticPokemon{}={Species=[0x1E3F5], Level=[0x1E3F6]} // Voltorb 4
StaticPokemon{}={Species=[0x1E3FD], Level=[0x1E3FE]} // Voltorb 5
StaticPokemon{}={Species=[0x1E40D], Level=[0x1E40E]} // Voltorb 6
StaticPokemon{}={Species=[0x1E3ED], Level=[0x1E3EE]} // Electrode 1
StaticPokemon{}={Species=[0x1E405], Level=[0x1E406]} // Electrode 2
StaticPokemon{}={Species=[0x468A8, 0x468E8, 0x5DB9E], Level=[0x468E9]} // Articuno
StaticPokemon{}={Species=[0x1E3B5, 0x1E415], Level=[0x1E416]} // Zapdos
StaticPokemon{}={Species=[0x518C0, 0x51963], Level=[0x51964]} // Moltres
StaticPokemon{}={Species=[0x45F2C, 0x45F44], Level=[0x45F45]} // Mewtwo
StaticPokemon{}={Species=[0x52859, 0x5298A], Level=[0x5298B]} // Abra
StaticPokemon{}={Species=[0x5285A, 0x5298C], Level=[0x5298D]} // Clefairy
StaticPokemon{}={Species=[0x5285B, 0x5298E], Level=[0x5298F]} // Nidorino
StaticPokemon{}={Species=[0x52864, 0x52990], Level=[0x52991]} // Pinsir
StaticPokemon{}={Species=[0x52865, 0x52992], Level=[0x52993]} // Dratini
StaticPokemon{}={Species=[0x52866, 0x52994], Level=[0x52995]} // Porygon
StaticPokemonGhostMarowak{}={Species=[0xD6F4, 0x3EF9A, 0x58DE4, 0x60B33, 0x60C0A, 0x708E1], Level=[0x60B38]}
CRC32=D6DA8A1A

[Yellow (U)]
Game=POKEMON YELLOW
Version=0
NonJapanese=1
Type=Yellow
CopyFrom=Red (U)
BWXPTweak=bwexp/yellow_en_bwxp
XAccNerfTweak=yellow_en_xaccnerf
CritRateTweak=yellow_en_critrate
PokedexOrder=0x410B1
PokemonNamesOffset=0xE8000
MewStatsOffset=0
MoveNamesOffset=0xBC000
ItemNamesOffset=0x45B7
WildPokemonTableOffset=0xCB95
OldRodOffset=0xE0FF
GoodRodOffset=0xE12C
SuperRodTableOffset=0xF5EDA
MapNameTableOffset=0x7139C
TypeEffectivenessOffset=0x3E5FA
PokemonMovesetsTableOffset=0x3B1E5
PokemonMovesetsDataSize=0xC9F
PokemonMovesetsExtraSpaceOffset=0
StarterOffsets1=[0x18F19, 0x1CB41, 0x1CB66]
StarterOffsets2=[0x3A28A]
TrainerDataTableOffset=0x39DD1
TrainerDataClassCounts=[0, 14, 15, 19, 8, 10, 25, 7, 12, 14, 15, 9, 3, 0, 11, 15, 9, 7, 15, 4, 2, 8, 6, 17, 9, 3, 3, 0, 13, 3, 49, 10, 8, 1, 1, 1, 1, 1, 1, 1, 1, 5, 10, 3, 1, 24, 1, 1]
ExtraTrainerMovesTableOffset=0x39C6B
GymLeaderMovesTableOffset=0
TMMovesOffset=0x1232D
TrainerClassNamesOffsets=[0x27E77, 0x3997E]
IntroPokemonOffset=0x5EDB
IntroCryOffset=0x1A4C
MapBanks=0xFC3E4
MapAddresses=0xFC1F2
SpecialMapPointerTable=0xF268D
HiddenItemRoutine=0x75F74
TradeTableOffset=0x71C1D
TradeTableSize=10
TradeNameLength=11
TradesUnused=[2,4,6]
TextDelayFunctionOffset=0x38C8
PCPotionOffset=0x5EA2
PikachuEvoJumpOffset=0xD809
CatchingTutorialMonOffset=0x190EA
MonPaletteIndicesOffset=0x72921
SGBPalettesOffset=0x729B9
StaticPokemonSupport=1
StaticPokemon{}={Species=[0xF21C0], Level=[0xF21BF]} // Magikarp
StaticPokemon{}={Species=[0x1CF7B, 0x1CF8C, 0x1CFEB], Level=[0x1CF8B]} // Bulbasaur
StaticPokemon{}={Species=[0x5159E, 0x515AF], Level=[0x515AE]} // Charmander
StaticPokemon{}={Species=[0xF1A34, 0xF1A45], Level=[0xF1A44]} // Squirtle
StaticPokemon{}={Species=[0x1D652], Level=[0x1D651]} // Eevee
StaticPokemon{}={Species=[0x5CE0D], Level=[0x5CE25]} // Hitmonlee
StaticPokemon{}={Species=[0x5CE55], Level=[0x5CE6D]} // Hitmonchan
StaticPokemon{}={Species=[0x51DD6], Level=[0x51DD5]} // Lapras
StaticPokemon{}={Species=[0x61054], Level=[0x75630]} // Omanyte
StaticPokemon{}={Species=[0x61058], Level=[0x75630]} // Kabuto
StaticPokemon{}={Species=[0x61050], Level=[0x75630]} // Aerodactyl
StaticPokemon{}={Species=[0x594CC, 0x61C0E], Level=[0x594D1]} // Snorlax 1
StaticPokemon{}={Species=[0x5980C], Level=[0x59811]} // Snorlax 2
StaticPokemon{}={Species=[0x1DCDF], Level=[0x1DCE0]} // Voltorb 1
StaticPokemon{}={Species=[0x1DCE7], Level=[0x1DCE8]} // Voltorb 2
StaticPokemon{}={Species=[0x1DCEF], Level=[0x1DCF0]} // Voltorb 3
StaticPokemon{}={Species=[0x1DCFF], Level=[0x1DD00]} // Voltorb 4
StaticPokemon{}={Species=[0x1DD07], Level=[0x1DD08]} // Voltorb 5
StaticPokemon{}={Species=[0x1DD17], Level=[0x1DD18]} // Voltorb 6
StaticPokemon{}={Species=[0x1DCF7], Level=[0x1DCF8]} // Electrode 1
StaticPokemon{}={Species=[0x1DD0F], Level=[0x1DD10]} // Electrode 2
StaticPokemon{}={Species=[0x46B1A, 0x46B5A, 0x5DBD3], Level=[0x46B5B]} // Articuno
StaticPokemon{}={Species=[0x1DCBF, 0x1DD1F], Level=[0x1DD20]} // Zapdos
StaticPokemon{}={Species=[0x51902, 0x519A5], Level=[0x519A6]} // Moltres
StaticPokemon{}={Species=[0x4618D, 0x461A5], Level=[0x461A6]} // Mewtwo
StaticPokemon{}={Species=[0x527BA, 0x528EA], Level=[0x528EB]} // Abra
StaticPokemon{}={Species=[0x527BB, 0x528EC], Level=[0x528ED]} // Vulpix
StaticPokemon{}={Species=[0x527BC, 0x528EE], Level=[0x528EF]} // Wigglytuff
StaticPokemon{}={Species=[0x527C5, 0x528F0], Level=[0x528F1]} // Scyther
StaticPokemon{}={Species=[0x527C6, 0x528F2], Level=[0x528F3]} // Pinsir
StaticPokemon{}={Species=[0x527C7, 0x528F4], Level=[0x528F5]} // Porygon
StaticPokemonGhostMarowak{}={Species=[0xD43D, 0x60B22, 0x60BF9, 0x70945, 0xF4070, 0xF6095], Level=[0x60B27]}
PikachuHappinessCheckOffset=0x1CF64
TMText[]=[6,0x0B0EE4,\pTM06 contains\n%m!\e]
TMText[]=[11,0xAB2D6,TM11 teaches\n%m!\e]
TMText[]=[13,0xAE5CA,contains\n%m!\e]
TMText[]=[18,0xAE3F3,TM18 is\n%m!\e]
TMText[]=[21,0xAF141,\pTM21 contains\n%m.\e]
TMText[]=[24,0xAD9FE,\pTM24 contains\n%m!\e]
TMText[]=[27,0xA9BEC,\pTM27 is\n%m!\e]
TMText[]=[28,0xAAEC4,Those miserable\nROCKETs!\pLook what they\ndid here!\pThey stole a TM\nfor teaching\l[POKé]MON how to\l%m!\e]
TMText[]=[28,0xAAF4A,I figure what's\nlost is lost!\pI decided to get\n%m\lwithout a TM!\e]
TMText[]=[29,0xB343D,TM29 is\n%m!\e]
TMText[]=[31,0xB2593,\pTM31 contains my\nfavorite,\l%m!\e]
TMText[]=[34,0xAA729,\pA TM contains a\ntechnique that\lcan be taught to\l[POKé]MON!\pA TM is good only\nonce! So when you\luse one to teach\la new technique,\lpick the [POKé]MON\lcarefully!\pTM34 contains\n%m!\e]
TMText[]=[36,0x9A5B0,TM36 is\n%m!\e]
TMText[]=[38,0xB17A1,\pTM38 contains\n%m!\e]
TMText[]=[39,0xA1BDE,TM39 is the move\n%m.\e]
TMText[]=[41,0xB5E1F,TM41 teaches\n%m!\pMany [POKé]MON\ncan use it!\e]
TMText[]=[42,0xB48A0,TM42 contains\n%m...\e]
TMText[]=[46,0xB2CEA,\pTM46 is\n%m!\e]
TMText[]=[48,0xAE655,contains\n%m!\e]
TMText[]=[49,0xAE6B7,\pTM49 is\n%m!\e]
CRC32=7D527D62

[Red (J)]
Game=POKEMON RED
Version=0
NonJapanese=0
Type=RB
InternalPokemonCount=190
PokedexOrder=0x4279A
PokemonNamesOffset=0x39068
PokemonNamesLength=5
PokemonStatsOffset=0x38000
MewStatsOffset=0x4200
WildPokemonTableOffset=0xCF61
OldRodOffset=0xE3A1
GoodRodOffset=0xE3CE
SuperRodTableOffset=0xEC24
MapNameTableOffset=0x718AF
MoveCount=165
MoveNamesOffset=0x10000
MoveDataOffset=0x39658
ItemNamesOffset=0x433F
TypeEffectivenessOffset=0x3E756
PokemonMovesetsTableOffset=0x3B427
PokemonMovesetsDataSize=0x814
PokemonMovesetsExtraSpaceOffset=0
StarterOffsets1=[0x1CBBF, 0x1C6C2, 0x1CBA7, 0x3A069, 0x514A1, 0x515CB]
StarterOffsets2=[0x1CB9D, 0x19C66, 0x1C6C6, 0x1C806, 0x1CBB8, 0x5149D, 0x515C7, 0x52A1D, 0x606AD, 0x61F2D, 0x77003]
StarterOffsets3=[0x1CBAE, 0x19C6E, 0x1C80E, 0x1CBC9, 0x3A063, 0x5149F, 0x515C9, 0x52A25, 0x606B5, 0x61F35, 0x7700B]
PatchPokedex=0
CanChangeStarterText=0
CanChangeTrainerText=0
TrainerDataTableOffset=0x3A0AC
TrainerDataClassCounts=[0, 13, 14, 18, 8, 9, 24, 7, 12, 14, 15, 9, 3, 0, 11, 15, 9, 7, 15, 4, 2, 8, 6, 17, 9, 9, 3, 0, 13, 3, 41, 10, 8, 1, 1, 1, 1, 1, 1, 1, 1, 5, 12, 3, 1, 24, 1, 1]
ExtraTrainerMovesTableOffset=0x3A0A3
GymLeaderMovesTableOffset=0x3A094
TMMovesOffset=0x12276
TrainerClassNamesOffsets=[0x27F2A, 0x39D1C]
IntroPokemonOffset=0x5FB1
IntroCryOffset=0x716
MapBanks=0xC883
MapAddresses=0x1BCB
SpecialMapList=0x47965
SpecialMapPointerTable=0x479BB
HiddenItemRoutine=0x77D78
TradeTableOffset=0x72043
TradeTableSize=10
TradeNameLength=5
TradesUnused=[2]
TextDelayFunctionOffset=0x391D
PCPotionOffset=0x5F78
CatchingTutorialMonOffset=0x19181
MonPaletteIndicesOffset=0x72A1E
SGBPalettesOffset=0x72AB6
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x4A557], Level=[0x4A556]} // Magikarp
StaticPokemon{}={Species=[0x1E771], Level=[0x1E770]} // Eevee
StaticPokemon{}={Species=[0x5E330], Level=[0x5E348]} // Hitmonlee
StaticPokemon{}={Species=[0x5E390], Level=[0x5E3A8]} // Hitmonchan
StaticPokemon{}={Species=[0x52B1B], Level=[0x52B1A]} // Lapras
StaticPokemon{}={Species=[0x617DB], Level=[0x76C43]} // Omanyte
StaticPokemon{}={Species=[0x617DF], Level=[0x76C43]} // Kabuto
StaticPokemon{}={Species=[0x617D7], Level=[0x76C43]} // Aerodactyl
StaticPokemon{}={Species=[0x59E34, 0x62EE0], Level=[0x59E39]} // Snorlax 1
StaticPokemon{}={Species=[0x5A684], Level=[0x5A689]} // Snorlax 2
StaticPokemon{}={Species=[0x1F0A7], Level=[0x1F0A8]} // Voltorb 1
StaticPokemon{}={Species=[0x1F0AF], Level=[0x1F0B0]} // Voltorb 2
StaticPokemon{}={Species=[0x1F0B7], Level=[0x1F0B8]} // Voltorb 3
StaticPokemon{}={Species=[0x1F0C7], Level=[0x1F0C8]} // Voltorb 4
StaticPokemon{}={Species=[0x1F0CF], Level=[0x1F0D0]} // Voltorb 5
StaticPokemon{}={Species=[0x1F0DF], Level=[0x1F0E0]} // Voltorb 6
StaticPokemon{}={Species=[0x1F0BF], Level=[0x1F0C0]} // Electrode 1
StaticPokemon{}={Species=[0x1F0D7], Level=[0x1F0D8]} // Electrode 2
StaticPokemon{}={Species=[0x477AE, 0x4780D, 0x5F91B], Level=[0x4780E]} // Articuno
StaticPokemon{}={Species=[0x1F087, 0x1F0E7], Level=[0x1F0E8]} // Zapdos
StaticPokemon{}={Species=[0x5267C, 0x526D4], Level=[0x526D5]} // Moltres
StaticPokemon{}={Species=[0x46C3F, 0x46C57], Level=[0x46C58]} // Mewtwo
StaticPokemon{}={Species=[0x53C10, 0x53D6A], Level=[0x53D6B]} // Abra
StaticPokemon{}={Species=[0x53C11, 0x53D6C], Level=[0x53D6D]} // Clefairy
StaticPokemon{}={Species=[0x53C12, 0x53D6E], Level=[0x53D6F]} // Nidorina/Nidorino
StaticPokemon{}={Species=[0x53C1B, 0x53D70], Level=[0x53D71]} // Dratini/Pinsir
StaticPokemon{}={Species=[0x53C1C, 0x53D72], Level=[0x53D73]} // Scyther/Dratini
StaticPokemon{}={Species=[0x53C1D, 0x53D74], Level=[0x53D75]} // Porygon
StaticPokemonGhostMarowak{}={Species=[0xD764, 0x3F28D, 0x5BDD2, 0x60F66, 0x61129, 0x70E4D], Level=[0x60F6B]}
CRC32=13652705

[Green (J)]
Game=POKEMON GREEN
Version=0
NonJapanese=0
Type=RB
CopyStaticPokemon=1
CopyFrom=Red (J)
IntroPokemonOffset=0x5FB2
PCPotionOffset=0x5F79
CRC32=BAEACD2B

[Green (J)(T-Eng)]
Game=POKEMON GREEN
Version=0
NonJapanese=0
CRCInHeader=0xF57E
Type=RB
CopyStaticPokemon=1
CopyFrom=Green (J)
ExtraTableFile=green_translation
CanChangeTrainerText=1
TrainerClassNamesOffsets=[0xB21BF]
ItemNamesOffset=0xB1DB1
MoveNamesOffset=0xB1657

[Blue (J)]
Game=POKEMON BLUE
Version=0
NonJapanese=0
Type=RB
CopyFrom=Red (J)
PokedexOrder=0x42784
PokemonNamesOffset=0x39446
PokemonStatsOffset=0x383DE
MewStatsOffset=0x425B
OldRodOffset=0xE3C6
GoodRodOffset=0xE3F3
SuperRodTableOffset=0xEC49
MapNameTableOffset=0x7189E
MoveDataOffset=0x38000
TypeEffectivenessOffset=0x3E75B
ItemNamesOffset=0x4733
StarterOffsets1=[0x1CBBF, 0x1C6C2, 0x1CBA7, 0x3A069, 0x514A1, 0x515CB]
StarterOffsets2=[0x1CB9D, 0x19C5E, 0x1C6C6, 0x1C806, 0x1CBB8, 0x5149D, 0x515C7, 0x52A1F, 0x606AD, 0x61F2D, 0x77006]
StarterOffsets3=[0x1CBAE, 0x19C66, 0x1C80E, 0x1CBC9, 0x3A063, 0x5149F, 0x515C9, 0x52A27, 0x606B5, 0x61F35, 0x7700E]
TMMovesOffset=0x13C93
TrainerClassNamesOffsets=[0x27EA1, 0x39DB5]
IntroPokemonOffset=0x60C4
IntroCryOffset=0x1C77
MapBanks=0xC275
MapAddresses=0x0167
SpecialMapList=0x47965
SpecialMapPointerTable=0x479BB
HiddenItemRoutine=0x77D7B
TradeTableOffset=0x72033
TradeTableSize=10
TradeNameLength=4
TradesUnused=[2]
TextDelayFunctionOffset=0x3931
PCPotionOffset=0x608B
CatchingTutorialMonOffset=0x1917D
MonPaletteIndicesOffset=0x72A0D
SGBPalettesOffset=0x72AA5
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x4A553], Level=[0x4A552]} // Magikarp
StaticPokemon{}={Species=[0x1E771], Level=[0x1E770]} // Eevee
StaticPokemon{}={Species=[0x5E32F], Level=[0x5E347]} // Hitmonlee
StaticPokemon{}={Species=[0x5E38F], Level=[0x5E3A7]} // Hitmonchan
StaticPokemon{}={Species=[0x52B1D], Level=[0x52B1C]} // Lapras
StaticPokemon{}={Species=[0x617DB], Level=[0x76C46]} // Omanyte
StaticPokemon{}={Species=[0x617DF], Level=[0x76C46]} // Kabuto
StaticPokemon{}={Species=[0x617D7], Level=[0x76C46]} // Aerodactyl
StaticPokemon{}={Species=[0x5A079, 0x62DBF], Level=[0x5A07E]} // Snorlax 1
StaticPokemon{}={Species=[0x5A8C9], Level=[0x5A8CE]} // Snorlax 2
StaticPokemon{}={Species=[0x1F0A7], Level=[0x1F0A8]} // Voltorb 1
StaticPokemon{}={Species=[0x1F0AF], Level=[0x1F0B0]} // Voltorb 2
StaticPokemon{}={Species=[0x1F0B7], Level=[0x1F0B8]} // Voltorb 3
StaticPokemon{}={Species=[0x1F0C7], Level=[0x1F0C8]} // Voltorb 4
StaticPokemon{}={Species=[0x1F0CF], Level=[0x1F0D0]} // Voltorb 5
StaticPokemon{}={Species=[0x1F0DF], Level=[0x1F0E0]} // Voltorb 6
StaticPokemon{}={Species=[0x1F0BF], Level=[0x1F0C0]} // Electrode 1
StaticPokemon{}={Species=[0x1F0D7], Level=[0x1F0D8]} // Electrode 2
StaticPokemon{}={Species=[0x477AE, 0x4780D, 0x5F91B], Level=[0x4780E]} // Articuno
StaticPokemon{}={Species=[0x1F087, 0x1F0E7], Level=[0x1F0E8]} // Zapdos
StaticPokemon{}={Species=[0x5252E, 0x526D6], Level=[0x526D7]} // Moltres
StaticPokemon{}={Species=[0x46C3F, 0x46C57], Level=[0x46C58]} // Mewtwo
StaticPokemon{}={Species=[0x53C12, 0x53D6C], Level=[0x53D6D]} // Abra
StaticPokemon{}={Species=[0x53C13, 0x53D6E], Level=[0x53D6F]} // Pikachu
StaticPokemon{}={Species=[0x53C14, 0x53D70], Level=[0x53D71]} // Horsea
StaticPokemon{}={Species=[0x53C1D, 0x53D72], Level=[0x53D73]} // Clefable
StaticPokemon{}={Species=[0x53C1E, 0x53D74], Level=[0x53D75]} // Dragonair
StaticPokemon{}={Species=[0x53C1F, 0x53D76], Level=[0x53D77]} // Porygon
StaticPokemonGhostMarowak{}={Species=[0xD77A, 0x3F28E, 0x58DE4, 0x60F66, 0x6103D, 0x70E3E], Level=[0x60F6B]}
CRC32=E4468D14

[Yellow (J)]
Game=POKEMON YELLOW
Version=0
NonJapanese=0
Type=Yellow
CopyFrom=Red (J)
PokedexOrder=0x4282D
PokemonNamesOffset=0x39462
PokemonNamesLength=5
PokemonStatsOffset=0x383DE
MewStatsOffset=0
WildPokemonTableOffset=0xCB91
OldRodOffset=0xE214
GoodRodOffset=0xE241
SuperRodTableOffset=0xA4C7D
MapNameTableOffset=0x713CA
MoveDataOffset=0x38000
TypeEffectivenessOffset=0x3E8EA
PokemonMovesetsTableOffset=0x3B59C
PokemonMovesetsDataSize=0x84A
PokemonMovesetsExtraSpaceOffset=0
StarterOffsets1=[0x18F19, 0x1D0A7, 0x1D0CC]
StarterOffsets2=[0x3A5FB]
ItemNamesOffset=0x45C4
CanChangeTrainerText=0
TrainerDataTableOffset=0x3A142
TrainerDataClassCounts=[0, 14, 15, 19, 8, 10, 25, 7, 12, 14, 15, 9, 3, 0, 11, 15, 9, 7, 15, 4, 2, 8, 6, 17, 9, 3, 3, 0, 13, 3, 49, 10, 8, 1, 1, 1, 1, 1, 1, 1, 1, 5, 10, 3, 1, 24, 1, 1]
ExtraTrainerMovesTableOffset=0x39FDC
GymLeaderMovesTableOffset=0
TMMovesOffset=0x1286C
TrainerClassNamesOffsets=[0x27E56, 0x39D34]
IntroPokemonOffset=0x5E3A
IntroCryOffset=0x1ABF
MapBanks=0xFC3E4
MapAddresses=0xFC1F2
SpecialMapPointerTable=0xF2B7F
HiddenItemRoutine=0x77C4D
TradeTableOffset=0x71B77
TradeTableSize=10
TradeNameLength=5
TradesUnused=[2,4,6]
TextDelayFunctionOffset=0x38E9
PCPotionOffset=0x5E01
PikachuEvoJumpOffset=0xD8BF
CatchingTutorialMonOffset=0x1920B
MonPaletteIndicesOffset=0x7264F
SGBPalettesOffset=0x726E7
StaticPokemonSupport=1
StaticPokemon{}={Species=[0xF20D5], Level=[0xF20D4]} // Magikarp
StaticPokemon{}={Species=[0x1DB2F, 0x1DB40, 0x1DC33], Level=[0x1DB3F]} // Bulbasaur
StaticPokemon{}={Species=[0x51DED, 0x51DFE], Level=[0x51DFD]} // Charmander
StaticPokemon{}={Species=[0xF0C0E, 0xF0C1F], Level=[0xF0C1E]} // Squirtle
StaticPokemon{}={Species=[0x1E81D], Level=[0x1E81C]} // Eevee
StaticPokemon{}={Species=[0x5E0E6], Level=[0x5E0FE]} // Hitmonlee
StaticPokemon{}={Species=[0x5E146], Level=[0x5E15E]} // Hitmonchan
StaticPokemon{}={Species=[0x529E7], Level=[0x529E6]} // Lapras
StaticPokemon{}={Species=[0x616A9], Level=[0x76AA7]} // Omanyte
StaticPokemon{}={Species=[0x616AD], Level=[0x76AA7]} // Kabuto
StaticPokemon{}={Species=[0x616A5], Level=[0x76AA7]} // Aerodactyl
StaticPokemon{}={Species=[0x59E7A, 0x62C44], Level=[0x59E7F]} // Snorlax 1
StaticPokemon{}={Species=[0x5A6CA], Level=[0x5A6CF]} // Snorlax 2
StaticPokemon{}={Species=[0x1F155], Level=[0x1F156]} // Voltorb 1
StaticPokemon{}={Species=[0x1F15D], Level=[0x1F15E]} // Voltorb 2
StaticPokemon{}={Species=[0x1F165], Level=[0x1F166]} // Voltorb 3
StaticPokemon{}={Species=[0x1F175], Level=[0x1F176]} // Voltorb 4
StaticPokemon{}={Species=[0x1F17D], Level=[0x1F17E]} // Voltorb 5
StaticPokemon{}={Species=[0x1F18D], Level=[0x1F18E]} // Voltorb 6
StaticPokemon{}={Species=[0x1F16D], Level=[0x1F16E]} // Electrode 1
StaticPokemon{}={Species=[0x1F185], Level=[0x1F186]} // Electrode 2
StaticPokemon{}={Species=[0x47A8C, 0x47AEB, 0x5F701], Level=[0x47AEC]} // Articuno
StaticPokemon{}={Species=[0x1F135, 0x1F195], Level=[0x1F196]} // Zapdos
StaticPokemon{}={Species=[0x52411, 0x525B9], Level=[0x525BA]} // Moltres
StaticPokemon{}={Species=[0x46F0C, 0x46F24], Level=[0x46F25]} // Mewtwo
StaticPokemon{}={Species=[0x53A1B, 0x53B74], Level=[0x53B75]} // Abra
StaticPokemon{}={Species=[0x53A1C, 0x53B76], Level=[0x53B77]} // Vulpix
StaticPokemon{}={Species=[0x53A1D, 0x53B78], Level=[0x53B79]} // Wigglytuff
StaticPokemon{}={Species=[0x53A26, 0x53B7A], Level=[0x53B7B]} // Scyther
StaticPokemon{}={Species=[0x53A27, 0x53B7C], Level=[0x53B7D]} // Pinsir
StaticPokemon{}={Species=[0x53A28, 0x53B7E], Level=[0x53B7F]} // Porygon
StaticPokemonGhostMarowak{}={Species=[0xD439, 0x60F55, 0x6102C, 0x70957, 0xF4070, 0xF539C], Level=[0x60F5A]}
PikachuHappinessCheckOffset=0x1DB18
CRC32=4EC85504

[Red (F)]
Game=POKEMON RED
Version=0
NonJapanese=1
Type=RB
CRCInHeader=0x7AFC
CopyFrom=Red (U)
ExtraTableFile=rby_freger
OldRodOffset=0xE242
GoodRodOffset=0xE26F
SuperRodTableOffset=0xE909
MapNameTableOffset=0x71317
PokedexOrder=0x40FAA
TypeEffectivenessOffset=0x3E489
PokemonMovesetsTableOffset=0x3B05F
PokemonMovesetsDataSize=0x814
PokemonMovesetsExtraSpaceOffset=0x3BBE9
ItemNamesOffset=0x472D
StarterOffsets1=[0x1D126, 0x1CC84, 0x1D10E, 0x39CFB, 0x50FB3, 0x510DD]
StarterOffsets2=[0x1D104, 0x19596, 0x1CC88, 0x1CDC8, 0x1D11F, 0x50FAF, 0x510D9, 0x51CB2, 0x6060E, 0x61450, 0x75FE0]
StarterOffsets3=[0x1D115, 0x1959E, 0x1CDD0, 0x1D130, 0x39CF5, 0x50FB1, 0x510DB, 0x51CBA, 0x60616, 0x61458, 0x75FE8]
CanChangeStarterText=0
PokedexRamOffset=0xD2FC
TrainerDataTableOffset=0x39D3E
ExtraTrainerMovesTableOffset=0x39D35
GymLeaderMovesTableOffset=0x39D26
TMMovesOffset=0x13782
TrainerClassNamesOffsets=[0x27EBF, 0x399FF]
IntroPokemonOffset=0x6208
IntroCryOffset=0x1C6F
HiddenItemRoutine=0x766CA
TradeTableOffset=0x71B4C
TextDelayFunctionOffset=0x38F0
PCPotionOffset=0x61CF
MonPaletteIndicesOffset=0x72599
SGBPalettesOffset=0x72631
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x4931F], Level=[0x4931E]} // Magikarp
StaticPokemon{}={Species=[0x1DD4C], Level=[0x1DD4B]} // Eevee
StaticPokemon{}={Species=[0x5CF0F], Level=[0x5CF27]} // Hitmonlee
StaticPokemon{}={Species=[0x5CF57], Level=[0x5CF6F]} // Hitmonchan
StaticPokemon{}={Species=[0x51DB0], Level=[0x51DAF]} // Lapras
StaticPokemon{}={Species=[0x61068], Level=[0x75DF7]} // Omanyte
StaticPokemon{}={Species=[0x6106C], Level=[0x75DF7]} // Kabuto
StaticPokemon{}={Species=[0x61064], Level=[0x75DF7]} // Aerodactyl
StaticPokemon{}={Species=[0x59630, 0x61BEB], Level=[0x59635]} // Snorlax 1
StaticPokemon{}={Species=[0x59970], Level=[0x59975]} // Snorlax 2
StaticPokemon{}={Species=[0x1E3D8], Level=[0x1E3D9]} // Voltorb 1
StaticPokemon{}={Species=[0x1E3E0], Level=[0x1E3E1]} // Voltorb 2
StaticPokemon{}={Species=[0x1E3E8], Level=[0x1E3E9]} // Voltorb 3
StaticPokemon{}={Species=[0x1E3F8], Level=[0x1E3F9]} // Voltorb 4
StaticPokemon{}={Species=[0x1E400], Level=[0x1E401]} // Voltorb 5
StaticPokemon{}={Species=[0x1E410], Level=[0x1E411]} // Voltorb 6
StaticPokemon{}={Species=[0x1E3F0], Level=[0x1E3F1]} // Electrode 1
StaticPokemon{}={Species=[0x1E408], Level=[0x1E409]} // Electrode 2
StaticPokemon{}={Species=[0x468A8, 0x468E8, 0x5DB92], Level=[0x468E9]} // Articuno
StaticPokemon{}={Species=[0x1E3B8, 0x1E418], Level=[0x1E419]} // Zapdos
StaticPokemon{}={Species=[0x518C3, 0x51966], Level=[0x51967]} // Moltres
StaticPokemon{}={Species=[0x45F2C, 0x45F44], Level=[0x45F45]} // Mewtwo
StaticPokemon{}={Species=[0x5285C, 0x5298F], Level=[0x52990]} // Abra
StaticPokemon{}={Species=[0x5285D, 0x52991], Level=[0x52992]} // Clefairy
StaticPokemon{}={Species=[0x5285E, 0x52993], Level=[0x52994]} // Nidorina/Nidorino
StaticPokemon{}={Species=[0x52867, 0x52995], Level=[0x52996]} // Dratini/Pinsir
StaticPokemon{}={Species=[0x52868, 0x52997], Level=[0x52998]} // Scyther/Dratini
StaticPokemon{}={Species=[0x52869, 0x52999], Level=[0x5299A]} // Porygon
StaticPokemonGhostMarowak{}={Species=[0xD6F4, 0x3EFAF, 0x58DE4, 0x60B33, 0x60C0A, 0x708E2], Level=[0x60B38]}
CRC32=337FCE11

[Blue (F)]
Game=POKEMON BLUE
Version=0
NonJapanese=1
Type=RB
CopyStaticPokemon=1
CRCInHeader=0x56A4
CopyFrom=Red (F)
CRC32=50E2FC1D

[Red (S)]
Game=POKEMON RED
Version=0
NonJapanese=1
Type=RB
CRCInHeader=0x384A
CopyFrom=Red (U)
ExtraTableFile=rby_espita
OldRodOffset=0xE254
GoodRodOffset=0xE281
SuperRodTableOffset=0xE91B
MapNameTableOffset=0x71316
PokedexOrder=0x40FB4
TypeEffectivenessOffset=0x3E483
PokemonMovesetsTableOffset=0x3B069
PokemonMovesetsDataSize=0x814
PokemonMovesetsExtraSpaceOffset=0x3BBF3
ItemNamesOffset=0x472B
StarterOffsets1=[0x1D126, 0x1CC84, 0x1D10E, 0x39D05, 0x50FB3, 0x510DD]
StarterOffsets2=[0x1D104, 0x19596, 0x1CC88, 0x1CDC8, 0x1D11F, 0x50FAF, 0x510D9, 0x51CAB, 0x6060E, 0x61450, 0x76026]
StarterOffsets3=[0x1D115, 0x1959E, 0x1CDD0, 0x1D130, 0x39CFF, 0x50FB1, 0x510DB, 0x51CB3, 0x60616, 0x61458, 0x7602E]
CanChangeStarterText=0
PokedexRamOffset=0xD2FC
TrainerDataTableOffset=0x39D48
ExtraTrainerMovesTableOffset=0x39D3F
GymLeaderMovesTableOffset=0x39D30
TMMovesOffset=0x13798
TrainerClassNamesOffsets=[0x27ECB, 0x399FF]
IntroPokemonOffset=0x61CC
IntroCryOffset=0x1C72
HiddenItemRoutine=0x76710
TradeTableOffset=0x71B6B
TextDelayFunctionOffset=0x38F2
PCPotionOffset=0x6193
MonPaletteIndicesOffset=0x725B8
SGBPalettesOffset=0x72650
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x49323], Level=[0x49322]} // Magikarp
StaticPokemon{}={Species=[0x1DD4A], Level=[0x1DD49]} // Eevee
StaticPokemon{}={Species=[0x5CF1B], Level=[0x5CF33]} // Hitmonlee
StaticPokemon{}={Species=[0x5CF63], Level=[0x5CF7B]} // Hitmonchan
StaticPokemon{}={Species=[0x51DA9], Level=[0x51DA8]} // Lapras
StaticPokemon{}={Species=[0x61068], Level=[0x75E3D]} // Omanyte
StaticPokemon{}={Species=[0x6106C], Level=[0x75E3D]} // Kabuto
StaticPokemon{}={Species=[0x61064], Level=[0x75E3D]} // Aerodactyl
StaticPokemon{}={Species=[0x59630, 0x61BEB], Level=[0x59635]} // Snorlax 1
StaticPokemon{}={Species=[0x59970], Level=[0x59975]} // Snorlax 2
StaticPokemon{}={Species=[0x1E3D6], Level=[0x1E3D7]} // Voltorb 1
StaticPokemon{}={Species=[0x1E3DE], Level=[0x1E3DF]} // Voltorb 2
StaticPokemon{}={Species=[0x1E3E6], Level=[0x1E3E7]} // Voltorb 3
StaticPokemon{}={Species=[0x1E3F6], Level=[0x1E3F7]} // Voltorb 4
StaticPokemon{}={Species=[0x1E3FE], Level=[0x1E3FF]} // Voltorb 5
StaticPokemon{}={Species=[0x1E40E], Level=[0x1E40F]} // Voltorb 6
StaticPokemon{}={Species=[0x1E3EE], Level=[0x1E3EF]} // Electrode 1
StaticPokemon{}={Species=[0x1E406], Level=[0x1E407]} // Electrode 2
StaticPokemon{}={Species=[0x468A8, 0x468E8, 0x5DBA4], Level=[0x468E9]} // Articuno
StaticPokemon{}={Species=[0x1E3B6, 0x1E416], Level=[0x1E417]} // Zapdos
StaticPokemon{}={Species=[0x518BC, 0x5195F], Level=[0x51960]} // Moltres
StaticPokemon{}={Species=[0x45F2C, 0x45F44], Level=[0x45F45]} // Mewtwo
StaticPokemon{}={Species=[0x52856, 0x52989], Level=[0x5298A]} // Abra
StaticPokemon{}={Species=[0x52857, 0x5298B], Level=[0x5298C]} // Clefairy
StaticPokemon{}={Species=[0x52858, 0x5298D], Level=[0x5298E]} // Nidorina/Nidorino
StaticPokemon{}={Species=[0x52861, 0x5298F], Level=[0x52990]} // Dratini/Pinsir
StaticPokemon{}={Species=[0x52862, 0x52991], Level=[0x52992]} // Scyther/Dratini
StaticPokemon{}={Species=[0x52863, 0x52993], Level=[0x52994]} // Porygon
StaticPokemonGhostMarowak{}={Species=[0xD6F4, 0x3EFA9, 0x58DE4, 0x60B33, 0x60C0A, 0x708E2], Level=[0x60B38]}
CRC32=D8507D8A

[Blue (S)]
Game=POKEMON BLUE
Version=0
NonJapanese=1
Type=RB
CopyStaticPokemon=1
CRCInHeader=0x14D7
CopyFrom=Red (S)
CRC32=D95416F9

[Red (G)]
Game=POKEMON RED
Version=0
NonJapanese=1
Type=RB
CRCInHeader=0x5CDC
CopyFrom=Red (U)
ExtraTableFile=rby_freger
OldRodOffset=0xE24B
GoodRodOffset=0xE278
SuperRodTableOffset=0xE912
MapNameTableOffset=0x71310
PokedexOrder=0x40F96
TypeEffectivenessOffset=0x3E482
PokemonMovesetsTableOffset=0x3B064
PokemonMovesetsDataSize=0x814
PokemonMovesetsExtraSpaceOffset=0x3BBEE
ItemNamesOffset=0x472D
StarterOffsets1=[0x1D126, 0x1CC84, 0x1D10E, 0x39D00, 0x50FB3, 0x510DD]
StarterOffsets2=[0x1D104, 0x19596, 0x1CC88, 0x1CDC8, 0x1D11F, 0x50FAF, 0x510D9, 0x51CA8, 0x6060E, 0x61450, 0x75FF1]
StarterOffsets3=[0x1D115, 0x1959E, 0x1CDD0, 0x1D130, 0x39CFA, 0x50FB1, 0x510DB, 0x51CB0, 0x60616, 0x61458, 0x75FF9]
CanChangeStarterText=0
PokedexRamOffset=0xD2FC
TrainerDataTableOffset=0x39D43
TrainerDataClassCounts=[0, 13, 14, 18, 8, 9, 24, 7, 12, 14, 15, 9, 3, 0, 11, 15, 9, 7, 15, 4, 2, 8, 6, 17, 9, 9, 3, 0, 13, 3, 41, 10, 8, 1, 1, 1, 1, 1, 1, 1, 1, 5, 12, 3, 1, 24, 1, 1]
ExtraTrainerMovesTableOffset=0x39D3A
GymLeaderMovesTableOffset=0x39D2B
TMMovesOffset=0x13774
TrainerClassNamesOffsets=[0x27EC3, 0x399FF]
IntroPokemonOffset=0x6194
IntroCryOffset=0x1C73
HiddenItemRoutine=0x766D8
TradeTableOffset=0x71B55
TextDelayFunctionOffset=0x38ED
PCPotionOffset=0x615B
MonPaletteIndicesOffset=0x725A2
SGBPalettesOffset=0x7263A
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x49323], Level=[0x49322]} // Magikarp
StaticPokemon{}={Species=[0x1DD49], Level=[0x1DD48]} // Eevee
StaticPokemon{}={Species=[0x5CF15], Level=[0x5CF2D]} // Hitmonlee
StaticPokemon{}={Species=[0x5CF5D], Level=[0x5CF75]} // Hitmonchan
StaticPokemon{}={Species=[0x51DA6], Level=[0x51DA5]} // Lapras
StaticPokemon{}={Species=[0x61068], Level=[0x75E08]} // Omanyte
StaticPokemon{}={Species=[0x6106C], Level=[0x75E08]} // Kabuto
StaticPokemon{}={Species=[0x61064], Level=[0x75E08]} // Aerodactyl
StaticPokemon{}={Species=[0x59630, 0x61BEB], Level=[0x59635]} // Snorlax 1
StaticPokemon{}={Species=[0x59970], Level=[0x59975]} // Snorlax 2
StaticPokemon{}={Species=[0x1E3D5], Level=[0x1E3D6]} // Voltorb 1
StaticPokemon{}={Species=[0x1E3DD], Level=[0x1E3DE]} // Voltorb 2
StaticPokemon{}={Species=[0x1E3E5], Level=[0x1E3E6]} // Voltorb 3
StaticPokemon{}={Species=[0x1E3F5], Level=[0x1E3F6]} // Voltorb 4
StaticPokemon{}={Species=[0x1E3FD], Level=[0x1E3FE]} // Voltorb 5
StaticPokemon{}={Species=[0x1E40D], Level=[0x1E40E]} // Voltorb 6
StaticPokemon{}={Species=[0x1E3ED], Level=[0x1E3EE]} // Electrode 1
StaticPokemon{}={Species=[0x1E405], Level=[0x1E406]} // Electrode 2
StaticPokemon{}={Species=[0x468A8, 0x468E8, 0x5DB9E], Level=[0x468E9]} // Articuno
StaticPokemon{}={Species=[0x1E3B5, 0x1E415], Level=[0x1E416]} // Zapdos
StaticPokemon{}={Species=[0x518B9, 0x5195C], Level=[0x5195D]} // Moltres
StaticPokemon{}={Species=[0x45F2C, 0x45F44], Level=[0x45F45]} // Mewtwo
StaticPokemon{}={Species=[0x52851, 0x52984], Level=[0x52985]} // Abra
StaticPokemon{}={Species=[0x52852, 0x52986], Level=[0x52987]} // Clefairy
StaticPokemon{}={Species=[0x52853, 0x52988], Level=[0x52989]} // Nidorina
StaticPokemon{}={Species=[0x5285C, 0x5298A], Level=[0x5298B]} // Dratini
StaticPokemon{}={Species=[0x5285D, 0x5298C], Level=[0x5298D]} // Scyther
StaticPokemon{}={Species=[0x5285E, 0x5298E], Level=[0x5298F]} // Porygon
StaticPokemonGhostMarowak{}={Species=[0xD6F4, 0x3EFA8, 0x58DE4, 0x60B33, 0x60C0A, 0x708DD], Level=[0x60B38]}
CRC32=89197825

[Blue (G)]
Game=POKEMON BLUE
Version=0
NonJapanese=1
Type=RB
CRCInHeader=0x2EBC
CopyFrom=Red (G)
HiddenItemRoutine=0x766D9
StarterOffsets2=[0x1D104, 0x19596, 0x1CC88, 0x1CDC8, 0x1D11F, 0x50FAF, 0x510D9, 0x51CA8, 0x6060E, 0x61450, 0x75FF2]
StarterOffsets3=[0x1D115, 0x1959E, 0x1CDD0, 0x1D130, 0x39CFA, 0x50FB1, 0x510DB, 0x51CB0, 0x60616, 0x61458, 0x75FFA]
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x49323], Level=[0x49322]} // Magikarp
StaticPokemon{}={Species=[0x1DD49], Level=[0x1DD48]} // Eevee
StaticPokemon{}={Species=[0x5CF15], Level=[0x5CF2D]} // Hitmonlee
StaticPokemon{}={Species=[0x5CF5D], Level=[0x5CF75]} // Hitmonchan
StaticPokemon{}={Species=[0x51DA6], Level=[0x51DA5]} // Lapras
StaticPokemon{}={Species=[0x61068], Level=[0x75E09]} // Omanyte
StaticPokemon{}={Species=[0x6106C], Level=[0x75E09]} // Kabuto
StaticPokemon{}={Species=[0x61064], Level=[0x75E09]} // Aerodactyl
StaticPokemon{}={Species=[0x59630, 0x61BEB], Level=[0x59635]} // Snorlax 1
StaticPokemon{}={Species=[0x59970], Level=[0x59975]} // Snorlax 2
StaticPokemon{}={Species=[0x1E3D5], Level=[0x1E3D6]} // Voltorb 1
StaticPokemon{}={Species=[0x1E3DD], Level=[0x1E3DE]} // Voltorb 2
StaticPokemon{}={Species=[0x1E3E5], Level=[0x1E3E6]} // Voltorb 3
StaticPokemon{}={Species=[0x1E3F5], Level=[0x1E3F6]} // Voltorb 4
StaticPokemon{}={Species=[0x1E3FD], Level=[0x1E3FE]} // Voltorb 5
StaticPokemon{}={Species=[0x1E40D], Level=[0x1E40E]} // Voltorb 6
StaticPokemon{}={Species=[0x1E3ED], Level=[0x1E3EE]} // Electrode 1
StaticPokemon{}={Species=[0x1E405], Level=[0x1E406]} // Electrode 2
StaticPokemon{}={Species=[0x468A8, 0x468E8, 0x5DB9E], Level=[0x468E9]} // Articuno
StaticPokemon{}={Species=[0x1E3B5, 0x1E415], Level=[0x1E416]} // Zapdos
StaticPokemon{}={Species=[0x518B9, 0x5195C], Level=[0x5195D]} // Moltres
StaticPokemon{}={Species=[0x45F2C, 0x45F44], Level=[0x45F45]} // Mewtwo
StaticPokemon{}={Species=[0x52851, 0x52984], Level=[0x52985]} // Abra
StaticPokemon{}={Species=[0x52852, 0x52986], Level=[0x52987]} // Clefairy
StaticPokemon{}={Species=[0x52853, 0x52988], Level=[0x52989]} // Nidorino
StaticPokemon{}={Species=[0x5285C, 0x5298A], Level=[0x5298B]} // Pinsir
StaticPokemon{}={Species=[0x5285D, 0x5298C], Level=[0x5298D]} // Dratini
StaticPokemon{}={Species=[0x5285E, 0x5298E], Level=[0x5298F]} // Porygon
StaticPokemonGhostMarowak{}={Species=[0xD6F4, 0x3EFA8, 0x58DE4, 0x60B33, 0x60C0A, 0x708DD], Level=[0x60B38]}
CRC32=9C336307

[Red (I)]
Game=POKEMON RED
Version=0
NonJapanese=1
Type=RB
CRCInHeader=0x89D2
CopyFrom=Red (U)
ExtraTableFile=rby_espita
OldRodOffset=0xE24C
GoodRodOffset=0xE279
SuperRodTableOffset=0xE913
MapNameTableOffset=0x71313
PokedexOrder=0x40FB6
TypeEffectivenessOffset=0x3E477
PokemonMovesetsTableOffset=0x3B096
PokemonMovesetsDataSize=0x814
PokemonMovesetsExtraSpaceOffset=0x3BC20
ItemNamesOffset=0x472D
StarterOffsets1=[0x1D126, 0x1CC84, 0x1D10E, 0x39D20, 0x50FB3, 0x510DD]
StarterOffsets2=[0x1D104, 0x19596, 0x1CC88, 0x1CDC8, 0x1D11F, 0x50FAF, 0x510D9, 0x51CAE, 0x6060E, 0x61450, 0x76015]
StarterOffsets3=[0x1D115, 0x1959E, 0x1CDD0, 0x1D130, 0x39D1A, 0x50FB1, 0x510DB, 0x51CB6, 0x60616, 0x61458, 0x7601D]
CanChangeStarterText=0
PokedexRamOffset=0xD2FC
TrainerDataTableOffset=0x39D63
ExtraTrainerMovesTableOffset=0x39D5A
GymLeaderMovesTableOffset=0x39D4B
TMMovesOffset=0x13798
TrainerClassNamesOffsets=[0x27ECD, 0x399FF]
IntroPokemonOffset=0x61CC
IntroCryOffset=0x1C73
HiddenItemRoutine=0x766FF
TradeTableOffset=0x71BBB
TextDelayFunctionOffset=0x38EB
PCPotionOffset=0x6193
MonPaletteIndicesOffset=0x72608
SGBPalettesOffset=0x726A0
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x49322], Level=[0x49321]} // Magikarp
StaticPokemon{}={Species=[0x1DD4A], Level=[0x1DD49]} // Eevee
StaticPokemon{}={Species=[0x5CF12], Level=[0x5CF2A]} // Hitmonlee
StaticPokemon{}={Species=[0x5CF5A], Level=[0x5CF72]} // Hitmonchan
StaticPokemon{}={Species=[0x51DAC], Level=[0x51DAB]} // Lapras
StaticPokemon{}={Species=[0x61068], Level=[0x75E2C]} // Omanyte
StaticPokemon{}={Species=[0x6106C], Level=[0x75E2C]} // Kabuto
StaticPokemon{}={Species=[0x61064], Level=[0x75E2C]} // Aerodactyl
StaticPokemon{}={Species=[0x59630, 0x61BEB], Level=[0x59635]} // Snorlax 1
StaticPokemon{}={Species=[0x59970], Level=[0x59975]} // Snorlax 2
StaticPokemon{}={Species=[0x1E3D6], Level=[0x1E3D7]} // Voltorb 1
StaticPokemon{}={Species=[0x1E3DE], Level=[0x1E3DF]} // Voltorb 2
StaticPokemon{}={Species=[0x1E3E6], Level=[0x1E3E7]} // Voltorb 3
StaticPokemon{}={Species=[0x1E3F6], Level=[0x1E3F7]} // Voltorb 4
StaticPokemon{}={Species=[0x1E3FE], Level=[0x1E3FF]} // Voltorb 5
StaticPokemon{}={Species=[0x1E40E], Level=[0x1E40F]} // Voltorb 6
StaticPokemon{}={Species=[0x1E3EE], Level=[0x1E3EF]} // Electrode 1
StaticPokemon{}={Species=[0x1E406], Level=[0x1E407]} // Electrode 2
StaticPokemon{}={Species=[0x468A8, 0x468E8, 0x5DB9A], Level=[0x468E9]} // Articuno
StaticPokemon{}={Species=[0x1E3B6, 0x1E416], Level=[0x1E417]} // Zapdos
StaticPokemon{}={Species=[0x518BF, 0x51962], Level=[0x51963]} // Moltres
StaticPokemon{}={Species=[0x45F2C, 0x45F44], Level=[0x45F45]} // Mewtwo
StaticPokemon{}={Species=[0x52858, 0x5298C], Level=[0x5298D]} // Abra
StaticPokemon{}={Species=[0x52859, 0x5298E], Level=[0x5298F]} // Clefairy
StaticPokemon{}={Species=[0x5285A, 0x52990], Level=[0x52991]} // Nidorina
StaticPokemon{}={Species=[0x52863, 0x52992], Level=[0x52993]} // Dratini
StaticPokemon{}={Species=[0x52864, 0x52994], Level=[0x52995]} // Scyther
StaticPokemon{}={Species=[0x52865, 0x52996], Level=[0x52997]} // Porygon
StaticPokemonGhostMarowak{}={Species=[0xD6F4, 0x3EF9D, 0x58DE4, 0x60B33, 0x60C0A, 0x708DF], Level=[0x60B38]}
CRC32=2945ACEB

[Blue (I)]
Game=POKEMON BLUE
Version=0
NonJapanese=1
Type=RB
CRCInHeader=0x5E9C
CopyFrom=Red (I)
HiddenItemRoutine=0x766FD
PCPotionOffset=0x6192
StarterOffsets2=[0x1D104, 0x19596, 0x1CC88, 0x1CDC8, 0x1D11F, 0x50FAF, 0x510D9, 0x51CAE, 0x6060E, 0x61450, 0x76013]
StarterOffsets3=[0x1D115, 0x1959E, 0x1CDD0, 0x1D130, 0x39D1A, 0x50FB1, 0x510DB, 0x51CB6, 0x60616, 0x61458, 0x7601B]
IntroPokemonOffset=0x61CB
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x49322], Level=[0x49321]} // Magikarp
StaticPokemon{}={Species=[0x1DD4A], Level=[0x1DD49]} // Eevee
StaticPokemon{}={Species=[0x5CF12], Level=[0x5CF2A]} // Hitmonlee
StaticPokemon{}={Species=[0x5CF5A], Level=[0x5CF72]} // Hitmonchan
StaticPokemon{}={Species=[0x51DAC], Level=[0x51DAB]} // Lapras
StaticPokemon{}={Species=[0x61068], Level=[0x75E2A]} // Omanyte
StaticPokemon{}={Species=[0x6106C], Level=[0x75E2A]} // Kabuto
StaticPokemon{}={Species=[0x61064], Level=[0x75E2A]} // Aerodactyl
StaticPokemon{}={Species=[0x59630, 0x61BEB], Level=[0x59635]} // Snorlax 1
StaticPokemon{}={Species=[0x59970], Level=[0x59975]} // Snorlax 2
StaticPokemon{}={Species=[0x1E3D6], Level=[0x1E3D7]} // Voltorb 1
StaticPokemon{}={Species=[0x1E3DE], Level=[0x1E3DF]} // Voltorb 2
StaticPokemon{}={Species=[0x1E3E6], Level=[0x1E3E7]} // Voltorb 3
StaticPokemon{}={Species=[0x1E3F6], Level=[0x1E3F7]} // Voltorb 4
StaticPokemon{}={Species=[0x1E3FE], Level=[0x1E3FF]} // Voltorb 5
StaticPokemon{}={Species=[0x1E40E], Level=[0x1E40F]} // Voltorb 6
StaticPokemon{}={Species=[0x1E3EE], Level=[0x1E3EF]} // Electrode 1
StaticPokemon{}={Species=[0x1E406], Level=[0x1E407]} // Electrode 2
StaticPokemon{}={Species=[0x468A8, 0x468E8, 0x5DB9A], Level=[0x468E9]} // Articuno
StaticPokemon{}={Species=[0x1E3B6, 0x1E416], Level=[0x1E417]} // Zapdos
StaticPokemon{}={Species=[0x518BF, 0x51962], Level=[0x51963]} // Moltres
StaticPokemon{}={Species=[0x45F2C, 0x45F44], Level=[0x45F45]} // Mewtwo
StaticPokemon{}={Species=[0x52858, 0x5298C], Level=[0x5298D]} // Abra
StaticPokemon{}={Species=[0x52859, 0x5298E], Level=[0x5298F]} // Clefairy
StaticPokemon{}={Species=[0x5285A, 0x52990], Level=[0x52991]} // Nidorino
StaticPokemon{}={Species=[0x52863, 0x52992], Level=[0x52993]} // Pinsir
StaticPokemon{}={Species=[0x52864, 0x52994], Level=[0x52995]} // Dratini
StaticPokemon{}={Species=[0x52865, 0x52996], Level=[0x52997]} // Porygon
StaticPokemonGhostMarowak{}={Species=[0xD6F4, 0x3EF9D, 0x58DE4, 0x60B33, 0x60C0A, 0x708DF], Level=[0x60B38]}
CRC32=4D0984A9

[Yellow (F)]
Game=POKEMON YELAPSF
Version=0
NonJapanese=1
Type=Yellow
CopyFrom=Yellow (U)
ExtraTableFile=rby_freger
OldRodOffset=0xE0EF
GoodRodOffset=0xE11C
SuperRodTableOffset=0xF5ED4
MapNameTableOffset=0x713A0
PokedexOrder=0x41036
TypeEffectivenessOffset=0x3E610
PokemonMovesetsTableOffset=0x3B1E8
PokemonMovesetsDataSize=0xC9C
PokemonMovesetsExtraSpaceOffset=0
ItemNamesOffset=0x45B8
StarterOffsets2=[0x3A28D]
TrainerDataTableOffset=0x39DD4
ExtraTrainerMovesTableOffset=0x39C6E
TMMovesOffset=0x1233C
TrainerClassNamesOffsets=[0x27E74, 0x3997E]
IntroPokemonOffset=0x5F64
IntroCryOffset=0x1A48
SpecialMapPointerTable=0xF25CF
HiddenItemRoutine=0x75F69
TradeTableOffset=0x71BEE
TextDelayFunctionOffset=0x38CB
PCPotionOffset=0x5F2B
MonPaletteIndicesOffset=0x728F2
SGBPalettesOffset=0x7298A
StaticPokemonSupport=1
StaticPokemon{}={Species=[0xF2102], Level=[0xF2101]} // Magikarp
StaticPokemon{}={Species=[0x1CF7B, 0x1CF8C, 0x1CFEB], Level=[0x1CF8B]} // Bulbasaur
StaticPokemon{}={Species=[0x515A1, 0x515B2], Level=[0x515B1]} // Charmander
StaticPokemon{}={Species=[0xF1976, 0xF1987], Level=[0xF1986]} // Squirtle
StaticPokemon{}={Species=[0x1D655], Level=[0x1D654]} // Eevee
StaticPokemon{}={Species=[0x5CE05], Level=[0x5CE1D]} // Hitmonlee
StaticPokemon{}={Species=[0x5CE4D], Level=[0x5CE65]} // Hitmonchan
StaticPokemon{}={Species=[0x51DD9], Level=[0x51DD8]} // Lapras
StaticPokemon{}={Species=[0x61054], Level=[0x75625]} // Omanyte
StaticPokemon{}={Species=[0x61058], Level=[0x75625]} // Kabuto
StaticPokemon{}={Species=[0x61050], Level=[0x75625]} // Aerodactyl
StaticPokemon{}={Species=[0x594CC, 0x61C0E], Level=[0x594D1]} // Snorlax 1
StaticPokemon{}={Species=[0x5980C], Level=[0x59811]} // Snorlax 2
StaticPokemon{}={Species=[0x1DCE2], Level=[0x1DCE3]} // Voltorb 1
StaticPokemon{}={Species=[0x1DCEA], Level=[0x1DCEB]} // Voltorb 2
StaticPokemon{}={Species=[0x1DCF2], Level=[0x1DCF3]} // Voltorb 3
StaticPokemon{}={Species=[0x1DD02], Level=[0x1DD03]} // Voltorb 4
StaticPokemon{}={Species=[0x1DD0A], Level=[0x1DD0B]} // Voltorb 5
StaticPokemon{}={Species=[0x1DD1A], Level=[0x1DD1B]} // Voltorb 6
StaticPokemon{}={Species=[0x1DCFA], Level=[0x1DCFB]} // Electrode 1
StaticPokemon{}={Species=[0x1DD12], Level=[0x1DD13]} // Electrode 2
StaticPokemon{}={Species=[0x46B1A, 0x46B5A, 0x5DBC7], Level=[0x46B5B]} // Articuno
StaticPokemon{}={Species=[0x1DCC2, 0x1DD22], Level=[0x1DD23]} // Zapdos
StaticPokemon{}={Species=[0x51905, 0x519A8], Level=[0x519A9]} // Moltres
StaticPokemon{}={Species=[0x4618D, 0x461A5], Level=[0x461A6]} // Mewtwo
StaticPokemon{}={Species=[0x527BD, 0x528EF], Level=[0x528F0]} // Abra
StaticPokemon{}={Species=[0x527BE, 0x528F1], Level=[0x528F2]} // Vulpix
StaticPokemon{}={Species=[0x527BF, 0x528F3], Level=[0x528F4]} // Wigglytuff
StaticPokemon{}={Species=[0x527C8, 0x528F5], Level=[0x528F6]} // Scyther
StaticPokemon{}={Species=[0x527C9, 0x528F7], Level=[0x528F8]} // Pinsir
StaticPokemon{}={Species=[0x527CA, 0x528F9], Level=[0x528FA]} // Porygon
StaticPokemonGhostMarowak{}={Species=[0xD43D, 0x60B22, 0x60BF9, 0x70946, 0xF4070, 0xF608F], Level=[0x60B27]}
PikachuHappinessCheckOffset=0x1CF64
CRC32=D03426E9

[Yellow (G)]
Game=POKEMON YELAPSD
Version=0
NonJapanese=1
Type=Yellow
CopyFrom=Yellow (U)
ExtraTableFile=rby_freger
OldRodOffset=0xE0F4
GoodRodOffset=0xE121
SuperRodTableOffset=0xF5ED3
MapNameTableOffset=0x71399
PokedexOrder=0x41023
TypeEffectivenessOffset=0x3E609
PokemonMovesetsTableOffset=0x3B1F2
PokemonMovesetsDataSize=0xC92
PokemonMovesetsExtraSpaceOffset=0
ItemNamesOffset=0x45B8
StarterOffsets2=[0x3A297]
TrainerDataTableOffset=0x39DDE
ExtraTrainerMovesTableOffset=0x39C78
TMMovesOffset=0x1232E
TrainerClassNamesOffsets=[0x27E78, 0x3997E]
IntroPokemonOffset=0x5EF0
IntroCryOffset=0x1A51
SpecialMapPointerTable=0xF25EE
HiddenItemRoutine=0x75F6E
TradeTableOffset=0x71BFC
TextDelayFunctionOffset=0x38CD
PCPotionOffset=0x5EB7
MonPaletteIndicesOffset=0x72900
SGBPalettesOffset=0x72998
StaticPokemonSupport=1
StaticPokemon{}={Species=[0xF2121], Level=[0xF2120]} // Magikarp
StaticPokemon{}={Species=[0x1CF7B, 0x1CF8C, 0x1CFEB], Level=[0x1CF8B]} // Bulbasaur
StaticPokemon{}={Species=[0x51597, 0x515A8], Level=[0x515A7]} // Charmander
StaticPokemon{}={Species=[0xF1995, 0xF19A6], Level=[0xF19A5]} // Squirtle
StaticPokemon{}={Species=[0x1D652], Level=[0x1D651]} // Eevee
StaticPokemon{}={Species=[0x5CE0B], Level=[0x5CE23]} // Hitmonlee
StaticPokemon{}={Species=[0x5CE53], Level=[0x5CE6B]} // Hitmonchan
StaticPokemon{}={Species=[0x51DCF], Level=[0x51DCE]} // Lapras
StaticPokemon{}={Species=[0x61054], Level=[0x7562D]} // Omanyte
StaticPokemon{}={Species=[0x61058], Level=[0x7562D]} // Kabuto
StaticPokemon{}={Species=[0x61050], Level=[0x7562D]} // Aerodactyl
StaticPokemon{}={Species=[0x594CC, 0x61C0E], Level=[0x594D1]} // Snorlax 1
StaticPokemon{}={Species=[0x5980C], Level=[0x59811]} // Snorlax 2
StaticPokemon{}={Species=[0x1DCDF], Level=[0x1DCE0]} // Voltorb 1
StaticPokemon{}={Species=[0x1DCE7], Level=[0x1DCE8]} // Voltorb 2
StaticPokemon{}={Species=[0x1DCEF], Level=[0x1DCF0]} // Voltorb 3
StaticPokemon{}={Species=[0x1DCFF], Level=[0x1DD00]} // Voltorb 4
StaticPokemon{}={Species=[0x1DD07], Level=[0x1DD08]} // Voltorb 5
StaticPokemon{}={Species=[0x1DD17], Level=[0x1DD18]} // Voltorb 6
StaticPokemon{}={Species=[0x1DCF7], Level=[0x1DCF8]} // Electrode 1
StaticPokemon{}={Species=[0x1DD0F], Level=[0x1DD10]} // Electrode 2
StaticPokemon{}={Species=[0x46B1A, 0x46B5A, 0x5DBD3], Level=[0x46B5B]} // Articuno
StaticPokemon{}={Species=[0x1DCBF, 0x1DD1F], Level=[0x1DD20]} // Zapdos
StaticPokemon{}={Species=[0x518FB, 0x5199E], Level=[0x5199F]} // Moltres
StaticPokemon{}={Species=[0x4618D, 0x461A5], Level=[0x461A6]} // Mewtwo
StaticPokemon{}={Species=[0x527B2, 0x528E4], Level=[0x528E5]} // Abra
StaticPokemon{}={Species=[0x527B3, 0x528E6], Level=[0x528E7]} // Vulpix
StaticPokemon{}={Species=[0x527B4, 0x528E8], Level=[0x528E9]} // Wigglytuff
StaticPokemon{}={Species=[0x527BD, 0x528EA], Level=[0x528EB]} // Scyther
StaticPokemon{}={Species=[0x527BE, 0x528EC], Level=[0x528ED]} // Pinsir
StaticPokemon{}={Species=[0x527BF, 0x528EE], Level=[0x528EF]} // Porygon
StaticPokemonGhostMarowak{}={Species=[0xD43D, 0x60B22, 0x60BF9, 0x70941, 0xF4070, 0xF608E], Level=[0x60B27]}
PikachuHappinessCheckOffset=0x1CF64
CRC32=7A01E45A

[Yellow (I)]
Game=POKEMON YELAPSI
Version=0
NonJapanese=1
Type=Yellow
CopyFrom=Yellow (U)
ExtraTableFile=rby_espita
OldRodOffset=0xE103
GoodRodOffset=0xE130
SuperRodTableOffset=0xF5ECE
MapNameTableOffset=0x7139C
PokedexOrder=0x41043
TypeEffectivenessOffset=0x3E60C
PokemonMovesetsTableOffset=0x3B20D
PokemonMovesetsDataSize=0xC77
PokemonMovesetsExtraSpaceOffset=0
ItemNamesOffset=0x45B8
StarterOffsets2=[0x3A2B2]
TrainerDataTableOffset=0x39DF9
ExtraTrainerMovesTableOffset=0x39C93
TMMovesOffset=0x12352
TrainerClassNamesOffsets=[0x27E82, 0x3997E]
IntroPokemonOffset=0x5F2A
IntroCryOffset=0x1A4C
SpecialMapPointerTable=0xF2609
HiddenItemRoutine=0x75F70
TradeTableOffset=0x71C5D
TextDelayFunctionOffset=0x38C6
PCPotionOffset=0x5EF1
MonPaletteIndicesOffset=0x72961
SGBPalettesOffset=0x729F9
StaticPokemonSupport=1
StaticPokemon{}={Species=[0xF213C], Level=[0xF213B]} // Magikarp
StaticPokemon{}={Species=[0x1CF7B, 0x1CF8C, 0x1CFEB], Level=[0x1CF8B]} // Bulbasaur
StaticPokemon{}={Species=[0x5159D, 0x515AE], Level=[0x515AD]} // Charmander
StaticPokemon{}={Species=[0xF19B0, 0xF19C1], Level=[0xF19C0]} // Squirtle
StaticPokemon{}={Species=[0x1D653], Level=[0x1D652]} // Eevee
StaticPokemon{}={Species=[0x5CE08], Level=[0x5CE20]} // Hitmonlee
StaticPokemon{}={Species=[0x5CE50], Level=[0x5CE68]} // Hitmonchan
StaticPokemon{}={Species=[0x51DD5], Level=[0x51DD4]} // Lapras
StaticPokemon{}={Species=[0x61054], Level=[0x7562C]} // Omanyte
StaticPokemon{}={Species=[0x61058], Level=[0x7562C]} // Kabuto
StaticPokemon{}={Species=[0x61050], Level=[0x7562C]} // Aerodactyl
StaticPokemon{}={Species=[0x594CC, 0x61C0E], Level=[0x594D1]} // Snorlax 1
StaticPokemon{}={Species=[0x5980C], Level=[0x59811]} // Snorlax 2
StaticPokemon{}={Species=[0x1DCE0], Level=[0x1DCE1]} // Voltorb 1
StaticPokemon{}={Species=[0x1DCE8], Level=[0x1DCE9]} // Voltorb 2
StaticPokemon{}={Species=[0x1DCF0], Level=[0x1DCF1]} // Voltorb 3
StaticPokemon{}={Species=[0x1DD00], Level=[0x1DD01]} // Voltorb 4
StaticPokemon{}={Species=[0x1DD08], Level=[0x1DD09]} // Voltorb 5
StaticPokemon{}={Species=[0x1DD18], Level=[0x1DD19]} // Voltorb 6
StaticPokemon{}={Species=[0x1DCF8], Level=[0x1DCF9]} // Electrode 1
StaticPokemon{}={Species=[0x1DD10], Level=[0x1DD11]} // Electrode 2
StaticPokemon{}={Species=[0x46B1A, 0x46B5A, 0x5DBCF], Level=[0x46B5B]} // Articuno
StaticPokemon{}={Species=[0x1DCC0, 0x1DD20], Level=[0x1DD21]} // Zapdos
StaticPokemon{}={Species=[0x51901, 0x519A4], Level=[0x519A5]} // Moltres
StaticPokemon{}={Species=[0x4618D, 0x461A5], Level=[0x461A6]} // Mewtwo
StaticPokemon{}={Species=[0x527B9, 0x528EC], Level=[0x528ED]} // Abra
StaticPokemon{}={Species=[0x527BA, 0x528EE], Level=[0x528EF]} // Vulpix
StaticPokemon{}={Species=[0x527BB, 0x528F0], Level=[0x528F1]} // Wigglytuff
StaticPokemon{}={Species=[0x527C4, 0x528F2], Level=[0x528F3]} // Scyther
StaticPokemon{}={Species=[0x527C5, 0x528F4], Level=[0x528F5]} // Pinsir
StaticPokemon{}={Species=[0x527C6, 0x528F6], Level=[0x528F7]} // Porygon
StaticPokemonGhostMarowak{}={Species=[0xD43D, 0x60B22, 0x60BF9, 0x70943, 0xF4070, 0xF6089], Level=[0x60B27]}
PikachuHappinessCheckOffset=0x1CF64
CRC32=8B56FE33

[Yellow (S)]
Game=POKEMON YELAPSS
Version=0
NonJapanese=1
Type=Yellow
CopyFrom=Yellow (U)
ExtraTableFile=rby_espita
OldRodOffset=0xE102
GoodRodOffset=0xE12F
SuperRodTableOffset=0xF5ED2
MapNameTableOffset=0x7139F
PokedexOrder=0x41041
TypeEffectivenessOffset=0x3E60A
PokemonMovesetsTableOffset=0x3B1F2
PokemonMovesetsDataSize=0xC92
PokemonMovesetsExtraSpaceOffset=0
ItemNamesOffset=0x45B8
StarterOffsets2=[0x3A297]
TrainerDataTableOffset=0x39DDE
ExtraTrainerMovesTableOffset=0x39C78
TMMovesOffset=0x12352
TrainerClassNamesOffsets=[0x27E80, 0x3997E]
IntroPokemonOffset=0x5F22
IntroCryOffset=0x1A4B
SpecialMapPointerTable=0xF2609
HiddenItemRoutine=0x75F6F
TradeTableOffset=0x71C0D
TextDelayFunctionOffset=0x38CD
PCPotionOffset=0x5EE9
MonPaletteIndicesOffset=0x72911
SGBPalettesOffset=0x729A9
StaticPokemonSupport=1
StaticPokemon{}={Species=[0xF213C], Level=[0xF213B]} // Magikarp
StaticPokemon{}={Species=[0x1CF7B, 0x1CF8C, 0x1CFEB], Level=[0x1CF8B]} // Bulbasaur
StaticPokemon{}={Species=[0x5159A, 0x515AB], Level=[0x515AA]} // Charmander
StaticPokemon{}={Species=[0xF19B0, 0xF19C1], Level=[0xF19C0]} // Squirtle
StaticPokemon{}={Species=[0x1D653], Level=[0x1D652]} // Eevee
StaticPokemon{}={Species=[0x5CE11], Level=[0x5CE29]} // Hitmonlee
StaticPokemon{}={Species=[0x5CE59], Level=[0x5CE71]} // Hitmonchan
StaticPokemon{}={Species=[0x51DD2], Level=[0x51DD1]} // Lapras
StaticPokemon{}={Species=[0x61054], Level=[0x7562B]} // Omanyte
StaticPokemon{}={Species=[0x61058], Level=[0x7562B]} // Kabuto
StaticPokemon{}={Species=[0x61050], Level=[0x7562B]} // Aerodactyl
StaticPokemon{}={Species=[0x594CC, 0x61C0E], Level=[0x594D1]} // Snorlax 1
StaticPokemon{}={Species=[0x5980C], Level=[0x59811]} // Snorlax 2
StaticPokemon{}={Species=[0x1DCE0], Level=[0x1DCE1]} // Voltorb 1
StaticPokemon{}={Species=[0x1DCE8], Level=[0x1DCE9]} // Voltorb 2
StaticPokemon{}={Species=[0x1DCF0], Level=[0x1DCF1]} // Voltorb 3
StaticPokemon{}={Species=[0x1DD00], Level=[0x1DD01]} // Voltorb 4
StaticPokemon{}={Species=[0x1DD08], Level=[0x1DD09]} // Voltorb 5
StaticPokemon{}={Species=[0x1DD18], Level=[0x1DD19]} // Voltorb 6
StaticPokemon{}={Species=[0x1DCF8], Level=[0x1DCF9]} // Electrode 1
StaticPokemon{}={Species=[0x1DD10], Level=[0x1DD11]} // Electrode 2
StaticPokemon{}={Species=[0x46B1A, 0x46B5A, 0x5DBD9], Level=[0x46B5B]} // Articuno
StaticPokemon{}={Species=[0x1DCC0, 0x1DD20], Level=[0x1DD21]} // Zapdos
StaticPokemon{}={Species=[0x518FE, 0x519A1], Level=[0x519A2]} // Moltres
StaticPokemon{}={Species=[0x4618D, 0x461A5], Level=[0x461A6]} // Mewtwo
StaticPokemon{}={Species=[0x527B7, 0x528E9], Level=[0x528EA]} // Abra
StaticPokemon{}={Species=[0x527B8, 0x528EB], Level=[0x528EC]} // Vulpix
StaticPokemon{}={Species=[0x527B9, 0x528ED], Level=[0x528EE]} // Wigglytuff
StaticPokemon{}={Species=[0x527C2, 0x528EF], Level=[0x528F0]} // Scyther
StaticPokemon{}={Species=[0x527C3, 0x528F1], Level=[0x528F2]} // Pinsir
StaticPokemon{}={Species=[0x527C4, 0x528F3], Level=[0x528F4]} // Porygon
StaticPokemonGhostMarowak{}={Species=[0xD43D, 0x60B22, 0x60BF9, 0x70946, 0xF4070, 0xF608D], Level=[0x60B27]}
PikachuHappinessCheckOffset=0x1CF64
CRC32=964B7A10
