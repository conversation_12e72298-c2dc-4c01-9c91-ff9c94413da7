[Gold (U)]
Game=AAUE
Version=0
NonJapanese=1
Type=GS
ExtraTableFile=gsc_english
BWXPTweak=bwexp/gs_en_bwxp
PokemonNamesOffset=0x1B0B74
PokemonNamesLength=10
PokemonStatsOffset=0x51B0B
WildPokemonOffset=0x2AB35
FishingWildsOffset=0x92A52
HeadbuttWildsOffset=0xBA47C
HeadbuttTableSize=7
BCCWildsOffset=0x97BB8
FleeingDataOffset=0x3C551
MoveDataOffset=0x41AFE
MoveNamesOffset=0x1B1574
ItemNamesOffset=0x1B0000
PokemonMovesetsTableOffset=0x427BD
EggMovesTableOffset=0x239FE
SupportsFourStartingMoves=0
StarterOffsets1=[0x1800D2, 0x1800D4, 0x1800EB, 0x1800F6]
StarterOffsets2=[0x180114, 0x180116, 0x18012D, 0x180138]
StarterOffsets3=[0x180150, 0x180152, 0x180169, 0x180174]
StarterHeldItems=[0x1800F8, 0x18013A, 0x180176]
CanChangeStarterText=1
CanChangeTrainerText=1
StarterTextOffsets=[0x1805F4, 0x180620, 0x18064D]
TrainerClassAmount=0x42
TrainerDataTableOffset=0x3993E
TrainerDataClassCounts=[1, 1, 1, 1, 1, 1, 1, 1, 15, 0, 1, 3, 1, 1, 1, 1, 1, 1, 1, 5, 1, 12, 18, 19, 15, 1, 19, 20, 16, 13, 31, 5, 2, 3, 1, 14, 22, 21, 19, 12, 12, 6, 2, 20, 9, 1, 3, 8, 5, 9, 4, 12, 21, 19, 2, 9, 7, 3, 12, 6, 8, 5, 1, 1, 2, 5]
TMMovesOffset=0x11A66
TrainerClassNamesOffset=0x1B0955
MaxSumOfTrainerNameLengths=4895
DoublesTrainerClasses=[60] // only twins
IntroSpriteOffset=0x5FDE
IntroCryOffset=0x6061
MapHeaders=0x940ED
LandmarkTableOffset=0x92382
LandmarkCount=95
TradeTableOffset=0xFCC24
TradeTableSize=6
TradeNameLength=11
TradeOTLength=11
TradesUnused=[]
TextDelayFunctionOffset=0x31E2
CatchingTutorialOffsets=[0x128DBB, 0x128DF1, 0x128E39]
PicPointers=0x48000
PokemonPalettes=0xAD3D
TypeEffectivenessOffset=0x34D01
GuaranteedCatchPrefix=D147FA19D1FE03
StaticPokemonSupport=1
GameCornerPokemonNameLength=11
StaticPokemon{}={Species=[0x111772, 0x111775], Level=[0x111776]} // Lapras
StaticPokemon{}={Species=[0x114DBA, 0x114DBD], Level=[0x114DBE]} // Electrode1
StaticPokemon{}={Species=[0x114DE5, 0x114DE8], Level=[0x114DE9]} // Electrode2
StaticPokemon{}={Species=[0x114E10, 0x114E13], Level=[0x114E14]} // Electrode3
StaticPokemon{}={Species=[0x11C1A6, 0x11C1B6], Level=[0x11C1B7]} // Lugia
StaticPokemon{}={Species=[0x124F76, 0x124F7A], Level=[0x124F7B]} // RedGyarados
StaticPokemon{}={Species=[0x12E1D6], Level=[0x12E1D7]} // Sudowoodo
StaticPokemon{}={Species=[0x13D2A4, 0x13D2AB], Level=[0x13D2AC]} // Snorlax
StaticPokemon{}={Species=[0x16E919, 0x16E929], Level=[0x16E92A]} // Ho-Oh
StaticPokemon{}={Species=[0x1146F3, 0x1146FE], Level=[0x1146FF]} // Voltorb
StaticPokemon{}={Species=[0x114706, 0x114711], Level=[0x114712]} // Geodude
StaticPokemon{}={Species=[0x114719, 0x114724], Level=[0x114725]} // Koffing
StaticPokemon{}={Species=[0x73E6], Level=[0x73EB]} // Shuckle
StaticPokemon{}={Species=[0x119F20], Level=[0x119F21]} // Tyrogue
StaticPokemon{}={Species=[0x15924F]} // Togepi (egg)
StaticPokemon{}={Species=[0x1599FC], Level=[0x1599FD]} // Kenya
StaticPokemon{}={Species=[0x15CC10], Level=[0x15CC11]} // Eevee
StaticPokemon{}={Species=[0x2A7D8, 0x3C568, 0x1093D5], Level=[0x2A7E7]} // Raikou
StaticPokemon{}={Species=[0x2A7DD, 0x3C569, 0x1093E3], Level=[0x2A7E7]} // Entei
StaticPokemon{}={Species=[0x2A7E2, 0x3C56A, 0x1093F1], Level=[0x2A7E7]} // Suicune
StaticPokemonGameCorner{}={Species=[0x15E8B7, 0x15E8C8, 0x15E8CD, 0x15E93D], Level=[0x15E8CE]} // Abra
StaticPokemonGameCorner{}={Species=[0x15E8E5, 0x15E8F6, 0x15E8FB, 0x15E94D], Level=[0x15E8FC]} // Ekans
StaticPokemonGameCorner{}={Species=[0x15E913, 0x15E924, 0x15E929, 0x15E95D], Level=[0x15E92A]} // Dratini
StaticPokemonGameCorner{}={Species=[0x179B9C, 0x179BAD, 0x179BB2, 0x179C22], Level=[0x179BB3]} // Mr.Mime
StaticPokemonGameCorner{}={Species=[0x179BCA, 0x179BDB, 0x179BE0, 0x179C32], Level=[0x179BE1]} // Eevee
StaticPokemonGameCorner{}={Species=[0x179BF8, 0x179C09, 0x179C0E, 0x179C42], Level=[0x179C0F]} // Porygon
StaticEggPokemonOffsets=[14]
TMText[]=[1,0x1755D0,That is\n%m.\e]
TMText[]=[3,0x17933A,TM03 is\n%m.\pIt's a terrifying\nmove!\e]
TMText[]=[5,0x12CC1A,WROOOAR!\nIT'S %m!\e]
TMText[]=[6,0x17031D,JANINE: You're so\ntough! I have a \lspecial gift!\pIt's %m!\e]
TMText[]=[7,0x151346,MANAGER: TM07 is\nmy %m.\pIt's a powerful\ntechnique!\e]
TMText[]=[8,0x12E465,That happens to be\n%m.\pIf any rocks are\nin your way, find\lROCK SMASH!\e]
TMText[]=[10,0x14D5B5,Do you see it? It\n is %m!\e]
TMText[]=[11,0x10DEC0,It's %m.\nUse it wisely.\e]
TMText[]=[12,0x15F058,It's %m.\pUse it on\nenemy [POKé]MON.\e]
TMText[]=[13,0x14519F,That there's\n%m.\pIt's a rare move.\e]
TMText[]=[16,0x1456C0,That TM contains\n%m.\pIt demonstrates\nthe harshness of\lwinter.\e]
TMText[]=[19,0x17A052,ERIKA: That was a\ndelightful match.\pI felt inspired.\nPlease, I wish you\lto have this TM.\pIt's %m.\pIt is a wonderful\nmove!\pPlease use it if\nit pleases you…\e]
TMText[]=[23,0x144387,…That teaches\n%m.\e]
TMText[]=[24,0x11C6D7,That contains\n%m.\pIf you don't want\nit, you don't have\lto take it.\e]
TMText[]=[24,0x14C4A8,That contains\n%m.\pIf you don't want\nit, you don't have\lto take it.\e]
TMText[]=[29,0x184B57,TM29 is\n%m.\pIt may be\nuseful.\e]
TMText[]=[30,0x1493D7,It's %m.\pUse it if it\nappeals to you.\e]
TMText[]=[31,0x1583B6,By using a TM, a\n[POKé]MON will\pinstantly learn a\nnew move.\pThink before you\nact--a TM can be\lused only once.\pTM31 contains\n%m.\e]
TMText[]=[37,0x18244E,TM37 happens to be\n%m.\pIt's for advanced\ntrainers only.\pUse it if you\ndare. Good luck!\e]
TMText[]=[42,0x138344,TM42 contains\n%m…\p…Zzz…\e]
TMText[]=[45,0x15C308,It's %m!\pIsn't it just per-\nfect for a cutie\llike me?\e]
TMText[]=[49,0x155061,TM49 contains\n%m.\pIsn't it great?\nI discovered it!\e]
TMText[]=[50,0x129DBC,TM50 is\n%m.\pOoooh…\nIt's scary…\pI don't want to\nhave bad dreams.\e]
CRC32=6BDE3C3E

[Silver (U)]
Game=AAXE
Version=0
NonJapanese=1
Type=GS
CopyTMText=1
CopyFrom=Gold (U)
BWXPTweak=bwexp/gs_en_bwxp
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x111772, 0x111775], Level=[0x111776]} // Lapras
StaticPokemon{}={Species=[0x114DBA, 0x114DBD], Level=[0x114DBE]} // Electrode1
StaticPokemon{}={Species=[0x114DE5, 0x114DE8], Level=[0x114DE9]} // Electrode2
StaticPokemon{}={Species=[0x114E10, 0x114E13], Level=[0x114E14]} // Electrode3
StaticPokemon{}={Species=[0x11C1A6, 0x11C1C1], Level=[0x11C1C2]} // Lugia
StaticPokemon{}={Species=[0x124F76, 0x124F7A], Level=[0x124F7B]} // RedGyarados
StaticPokemon{}={Species=[0x12E1D6], Level=[0x12E1D7]} // Sudowoodo
StaticPokemon{}={Species=[0x13D2A4, 0x13D2AB], Level=[0x13D2AC]} // Snorlax
StaticPokemon{}={Species=[0x16E919, 0x16E934], Level=[0x16E935]} // Ho-Oh
StaticPokemon{}={Species=[0x1146F3, 0x1146FE], Level=[0x1146FF]} // Voltorb
StaticPokemon{}={Species=[0x114706, 0x114711], Level=[0x114712]} // Geodude
StaticPokemon{}={Species=[0x114719, 0x114724], Level=[0x114725]} // Koffing
StaticPokemon{}={Species=[0x73AC], Level=[0x73B1]} // Shuckle
StaticPokemon{}={Species=[0x119F20], Level=[0x119F21]} // Tyrogue
StaticPokemon{}={Species=[0x15924F]} // Togepi (egg)
StaticPokemon{}={Species=[0x1599FC], Level=[0x1599FD]} // Kenya
StaticPokemon{}={Species=[0x15CC10], Level=[0x15CC11]} // Eevee
StaticPokemon{}={Species=[0x2A7D8, 0x3C568, 0x1093D5], Level=[0x2A7E7]} // Raikou
StaticPokemon{}={Species=[0x2A7DD, 0x3C569, 0x1093E3], Level=[0x2A7E7]} // Entei
StaticPokemon{}={Species=[0x2A7E2, 0x3C56A, 0x1093F1], Level=[0x2A7E7]} // Suicune
StaticEggPokemonOffsets=[14]
StaticPokemonGameCorner{}={Species=[0x15E99C, 0x15E9AD, 0x15E9B2, 0x15EA22], Level=[0x15E9B3]} // Abra
StaticPokemonGameCorner{}={Species=[0x15E9CA, 0x15E9DB, 0x15E9E0, 0x15EA32], Level=[0x15E9E1]} // Sandshrew
StaticPokemonGameCorner{}={Species=[0x15E9F8, 0x15EA09, 0x15EA0E, 0x15EA42], Level=[0x15EA0F]} // Dratini
StaticPokemonGameCorner{}={Species=[0x179B9C, 0x179BAD, 0x179BB2, 0x179C22], Level=[0x179BB3]} // Mr.Mime
StaticPokemonGameCorner{}={Species=[0x179BCA, 0x179BDB, 0x179BE0, 0x179C32], Level=[0x179BE1]} // Eevee
StaticPokemonGameCorner{}={Species=[0x179BF8, 0x179C09, 0x179C0E, 0x179C42], Level=[0x179C0F]} // Porygon
CRC32=8AD48636

[Crystal (U)]
Game=BYTE
Version=0
NonJapanese=1
Type=Crystal
ExtraTableFile=gsc_english
BWXPTweak=bwexp/crystal_en_bwxp
PokemonNamesOffset=0x53384
PokemonNamesLength=10
PokemonStatsOffset=0x51424
WildPokemonOffset=0x2A5E9
FishingWildsOffset=0x924E3
HeadbuttWildsOffset=0xB82FA
HeadbuttTableSize=13
BCCWildsOffset=0x97D87
FleeingDataOffset=0x3C59A
MoveDataOffset=0x41AFB
MoveNamesOffset=0x1C9F29
ItemNamesOffset=0x1C8000
PokemonMovesetsTableOffset=0x425B1
EggMovesTableOffset=0x23B11
SupportsFourStartingMoves=1
StarterOffsets1=[0x78C7F, 0x78C81, 0x78C98, 0x78CA3]
StarterOffsets2=[0x78CC1, 0x78CC3, 0x78CDA, 0x78CE5]
StarterOffsets3=[0x78CFD, 0x78CFF, 0x78D16, 0x78D21]
StarterHeldItems=[0x78CA5, 0x78CE7, 0x78D23]
CanChangeStarterText=1
StarterTextOffsets=[0x793D9, 0x79405, 0x79432]
CanChangeTrainerText=1
TrainerClassAmount=0x43
TrainerDataTableOffset=0x39999
TrainerDataClassCounts=[1, 1, 1, 1, 1, 1, 1, 1, 15, 0, 1, 3, 1, 1, 1, 1, 1, 1, 1, 5, 1, 14, 24, 19, 17, 1, 20, 21, 17, 15, 31, 5, 2, 3, 1, 19, 25, 21, 19, 13, 14, 6, 2, 22, 9, 1, 3, 8, 6, 9, 4, 12, 26, 22, 2, 12, 7, 3, 14, 6, 10, 6, 1, 1, 2, 5, 1]
TMMovesOffset=0x1167A
TrainerClassNamesOffset=0x2C1EF
MaxSumOfTrainerNameLengths=4124
DoublesTrainerClasses=[60] // only twins
IntroSpriteOffset=0x5FD2
IntroCryOffset=0x6050
MapHeaders=0x94000
LandmarkTableOffset=0x1CA8C3
LandmarkCount=96
TradeTableOffset=0xFCE58
TradeTableSize=7
TradeNameLength=11
TradeOTLength=11
TradesUnused=[]
TextDelayFunctionOffset=0x313D
CatchingTutorialOffsets=[0x1A0F90, 0x1A0FC6, 0x1A100E]
PicPointers=0x120000
PokemonPalettes=0xA8CE
MoveTutorMoves=[0x492B4, 0x492B7, 0x492B1]
MoveTutorMenuOffset=0x19896C
MoveTutorMenuNewSpace=0x19BB00
TypeEffectivenessOffset=0x34BB1
GuaranteedCatchPrefix=47FA30D2FE03
StaticPokemonSupport=1
GameCornerPokemonNameLength=11
StaticPokemon{}={Species=[0x5A321, 0x5A324], Level=[0x5A325]} // Lapras
StaticPokemon{}={Species=[0x6D102, 0x6D105], Level=[0x6D106]} // Electrode1
StaticPokemon{}={Species=[0x6D12D, 0x6D130], Level=[0x6D131]} // Electrode2
StaticPokemon{}={Species=[0x6D158, 0x6D15B], Level=[0x6D15C]} // Electrode3
StaticPokemon{}={Species=[0x18C51E, 0x18C52A], Level=[0x18C52B]} // Lugia
StaticPokemon{}={Species=[0x7006A, 0x7006E], Level=[0x7006F]} // RedGyarados
StaticPokemon{}={Species=[0x194068], Level=[0x194069]} // Sudowoodo
StaticPokemon{}={Species=[0x1AA9B1, 0x1AA9B8], Level=[0x1AA9B9]} // Snorlax
StaticPokemon{}={Species=[0x7724A, 0x77256], Level=[0x77257]} // Ho-Oh
StaticPokemon{}={Species=[0x4A6FD, 0x71E35, 0x1850E5, 0x1850EA, 0x186198, 0x1861D2], Level=[0x1850EB]} // Suicune
StaticPokemon{}={Species=[0x6CA38, 0x6CA43], Level=[0x6CA44]} // Voltorb
StaticPokemon{}={Species=[0x6CA4B, 0x6CA56], Level=[0x6CA57]} // Geodude
StaticPokemon{}={Species=[0x6CA5E, 0x6CA69], Level=[0x6CA6A]} // Koffing
StaticPokemon{}={Species=[0x730A], Level=[0x730F]} // Shuckle
StaticPokemon{}={Species=[0x7E22A], Level=[0x7E22B]} // Tyrogue
StaticPokemon{}={Species=[0x694E2]} // Togepi (egg)
StaticPokemon{}={Species=[0x69D65], Level=[0x69D66]} // Kenya
StaticPokemon{}={Species=[0x54C06], Level=[0x54C07]} // Eevee
StaticPokemon{}={Species=[0x18D1D7], Level=[0x18D1D8]} // Dratini
StaticPokemon{}={Species=[0x2A2A1, 0x3C5B1, 0x4A6E9, 0x1850A5, 0x18617C], Level=[0x2A2AB]} // Raikou
StaticPokemon{}={Species=[0x2A2A6, 0x3C5B2, 0x4A6F3, 0x1850C6, 0x18618A], Level=[0x2A2AB]} // Entei
StaticPokemonOddEggOffset=0x1FB56E
StaticPokemonOddEggDataSize=0x3B
StaticPokemonGameCorner{}={Species=[0x56D34, 0x56D45, 0x56D4A, 0x56DBA], Level=[0x56D4B]} // Abra
StaticPokemonGameCorner{}={Species=[0x56D62, 0x56D73, 0x56D78, 0x56DCA], Level=[0x56D79]} // Cubone
StaticPokemonGameCorner{}={Species=[0x56D90, 0x56DA1, 0x56DA6, 0x56DDA], Level=[0x56DA7]} // Wobbuffet
StaticPokemonGameCorner{}={Species=[0x727FB, 0x7280C, 0x72811, 0x72881], Level=[0x72812]} // Pikachu
StaticPokemonGameCorner{}={Species=[0x72829, 0x7283A, 0x7283F, 0x72891], Level=[0x72840]} // Porygon
StaticPokemonGameCorner{}={Species=[0x72857, 0x72868, 0x7286D, 0x728A1], Level=[0x7286E]} // Larvitar
StaticEggPokemonOffsets=[15]
TMText[]=[1,0x9D8DB,That is\n%m.\e]
TMText[]=[3,0x71DB4,TM03 is\n%m.\pIt's a terrifying\nmove!\e]
TMText[]=[5,0x19118D,WROOOAR!\nIT'S %m!\e]
TMText[]=[6,0x196003,JANINE: You're so\ntough! I have a \lspecial gift!\pIt's %m!\e]
TMText[]=[7,0x1893F5,MANAGER: TM07 is\nmy %m.\pIt's a powerful\ntechnique!\e]
TMText[]=[8,0x19452D,That happens to be\n%m.\pIf any rocks are\nin your way, find\lROCK SMASH!\e]
TMText[]=[10,0x19A5DF,Do you see it? It\n is %m!\e]
TMText[]=[11,0x5E822,It's %m.\nUse it wisely.\e]
TMText[]=[12,0x62DF7,It's %m.\pUse it on\nenemy [POKé]MON.\e]
TMText[]=[13,0x9D1C8,That there's\n%m.\pIt's a rare move.\e]
TMText[]=[16,0x199DF0,That TM contains\n%m.\pIt demonstrates\nthe harshness of\lwinter.\e]
TMText[]=[19,0x72CB1,ERIKA: That was a\ndelightful match.\pI felt inspired.\nPlease, I wish you\lto have this TM.\pIt's %m.\pIt is a wonderful\nmove!\pPlease use it if\nit pleases you…\e]
TMText[]=[23,0x9C3A6,…That teaches\n%m.\e]
TMText[]=[24,0x18CA0E,That contains\n%m.\pIf you don't want\nit, you don't have\lto take it.\e]
TMText[]=[24,0x1951D2,That contains\n%m.\pIf you don't want\nit, you don't have\lto take it.\e]
TMText[]=[29,0x18A7BC,TM29 is\n%m.\pIt may be\nuseful.\e]
TMText[]=[30,0x9A0ED,It's %m.\pUse it if it\nappeals to you.\e]
TMText[]=[31,0x68649,By using a TM, a\n[POKé]MON will\pinstantly learn a\nnew move.\pThink before you\nact--a TM can be\lused only once.\pTM31 contains\n%m.\e]
TMText[]=[37,0x7B490,TM37 happens to be\n%m.\pIt's for advanced\ntrainers only.\pUse it if you\ndare. Good luck!\e]
TMText[]=[42,0x1A9D87,TM42 contains\n%m…\p…Zzz…\e]
TMText[]=[45,0x54303,It's %m!\pIsn't it just per-\nfect for a cutie\llike me?\e]
TMText[]=[49,0x18EEFB,TM49 contains\n%m.\pIsn't it great?\nI discovered it!\e]
TMText[]=[50,0x1A5897,TM50 is\n%m.\pOoooh…\nIt's scary…\pI don't want to\nhave bad dreams.\e]
CRC32=EE6F5188

[Crystal (U 1.1)]
Game=BYTE
Version=1
NonJapanese=1
Type=Crystal
CopyTMText=1
CopyStaticPokemon=1
CopyFrom=Crystal (U)
BWXPTweak=bwexp/crystal_en_bwxp
CRC32=3358E30A

[Crystal SpeedChoice v3]
Game=KAPB
Version=2
NonJapanese=1
Type=Crystal
ExtraTableFile=gsc_english
PokemonNamesOffset=0x53390
PokemonNamesLength=10
PokemonStatsOffset=0x51430
WildPokemonOffset=0x2A614
FishingWildsOffset=0x924E3
HeadbuttWildsOffset=0xB82FA
HeadbuttTableSize=13
BCCWildsOffset=0x97DAA
FleeingDataOffset=0x3C59A
MoveDataOffset=0x2F8E0
MoveNamesOffset=0x7442
ItemNamesOffset=0x1C8000
PokemonMovesetsTableOffset=0x41ED4
SupportsFourStartingMoves=1
StarterOffsets1=[0x78C7F, 0x78C81, 0x78C98, 0x78CA3]
StarterOffsets2=[0x78CC1, 0x78CC3, 0x78CDA, 0x78CE5]
StarterOffsets3=[0x78CFD, 0x78CFF, 0x78D16, 0x78D21]
StarterHeldItems=[0x78CA5, 0x78CE7, 0x78D23]
StarterTextOffsets=[0x793D9, 0x79405, 0x79432]
CanChangeStarterText=1
CanChangeTrainerText=1
TrainerClassAmount=0x43
TrainerDataTableOffset=0x39999
TrainerDataClassCounts=[1, 1, 1, 1, 1, 1, 1, 1, 15, 0, 1, 3, 1, 1, 1, 1, 1, 1, 1, 5, 1, 14, 24, 19, 17, 1, 20, 21, 17, 15, 31, 5, 2, 3, 1, 19, 25, 21, 19, 13, 14, 6, 2, 22, 9, 1, 3, 8, 6, 9, 4, 12, 26, 22, 2, 12, 7, 3, 14, 6, 10, 6, 1, 1, 2, 5, 1]
TMMovesOffset=0x1167A
TrainerClassNamesOffset=0x2C1EF
MaxSumOfTrainerNameLengths=4124
DoublesTrainerClasses=[60] // only twins
IntroSpriteOffset=0x5F93
IntroCryOffset=0x6011
MapHeaders=0x94000
LandmarkTableOffset=0x1CA8C3
LandmarkCount=96
TradeTableOffset=0xFCE58
TradeTableSize=7
TradeNameLength=11
TradeOTLength=11
TradesUnused=[]
TextDelayFunctionOffset=0
CatchingTutorialOffsets=[0x1A0F90, 0x1A0FC6, 0x1A100E]
PicPointers=0x120000
PokemonPalettes=0xA8CE
MoveTutorMoves=[0x492B4, 0x492B5, 0x492B6]
MoveTutorMenuOffset=0x19896C
MoveTutorMenuNewSpace=0x19899A
CheckValueOffset=0x3E95
StaticPokemonSupport=0
TMText[]=[1,0x9D8DB,That is\n%m.\e]
TMText[]=[3,0x71FD9,TM03 is\n%m.\pIt's a terrifying\nmove!\e]
TMText[]=[5,0x19118D,WROOOAR!\nIT'S %m!\e]
TMText[]=[6,0x196003,JANINE: You're so\ntough! I have a \lspecial gift!\pIt's %m!\e]
TMText[]=[7,0x1893F5,MANAGER: TM07 is\nmy %m.\pIt's a powerful\ntechnique!\e]
TMText[]=[8,0x19452D,That happens to be\n%m.\pIf any rocks are\nin your way, find\lROCK SMASH!\e]
TMText[]=[10,0x19A6DF,Do you see it? It\n is %m!\e]
TMText[]=[11,0x5E822,It's %m.\nUse it wisely.\e]
TMText[]=[12,0x62DF7,It's %m.\pUse it on\nenemy [POKé]MON.\e]
TMText[]=[13,0x9D1C8,That there's\n%m.\pIt's a rare move.\e]
TMText[]=[16,0x199EF0,That TM contains\n%m.\pIt demonstrates\nthe harshness of\lwinter.\e]
TMText[]=[19,0x72ED6,That was a\ndelightful match.\pI felt inspired.\nPlease, I wish you\lto have this TM.\pIt's %m.\pIt is a wonderful\nmove!\pPlease use it if\nit pleases you…\e]
TMText[]=[23,0x9C3A6,…That teaches\n%m.\e]
TMText[]=[24,0x18CA0E,That contains\n%m.\pIf you don't want\nit, you don't have\lto take it.\e]
TMText[]=[24,0x1951D2,That contains\n%m.\pIf you don't want\nit, you don't have\lto take it.\e]
TMText[]=[29,0x18A7BC,TM29 is\n%m.\pIt may be\nuseful.\e]
TMText[]=[30,0x9A0ED,It's %m.\pUse it if it\nappeals to you.\e]
TMText[]=[31,0x68654,By using a TM, a\n[POKé]MON will\pinstantly learn a\nnew move.\pThink before you\nact--a TM can be\lused only once.\pTM31 contains\n%m.\e]
TMText[]=[37,0x7B490,TM37 happens to be\n%m.\pIt's for advanced\ntrainers only.\pUse it if you\ndare. Good luck!\e]
TMText[]=[42,0x1A9D87,TM42 contains\n%m…\p…Zzz…\e]
TMText[]=[45,0x54303,It's %m!\pIsn't it just per-\nfect for a cutie\llike me?\e]
TMText[]=[49,0x18EF06,TM49 contains\n%m.\pIsn't it great?\nI discovered it!\e]
TMText[]=[50,0x1A5897,TM50 is\n%m.\pOoooh…\nIt's scary…\pI don't want to\nhave bad dreams.\e]

[Gold (J 1.0)]
Game=AAUJ
Version=0
NonJapanese=0
Type=GS
PokemonNamesOffset=0x53A09
PokemonNamesLength=5
PokemonStatsOffset=0x51AA9
WildPokemonOffset=0x2AC1B
FishingWildsOffset=0x929A1
HeadbuttWildsOffset=0xBA47C
HeadbuttTableSize=7
BCCWildsOffset=0x97BDC
FleeingDataOffset=0x3C551
MoveDataOffset=0x41C6C
MoveNamesOffset=0x4163E
ItemNamesOffset=0x7293
PokemonMovesetsTableOffset=0x4295F
EggMovesTableOffset=0x23B07
SupportsFourStartingMoves=0
StarterOffsets1=[0x4E598, 0x4E59A, 0x4E5B1, 0x4E5BC]
StarterOffsets2=[0x4E5DA, 0x4E5DC, 0x4E5F3, 0x4E5FE]
StarterOffsets3=[0x4E616, 0x4E618, 0x4E62F, 0x4E63A]
StarterHeldItems=[0x4E5BE, 0x4E600, 0x4E63C]
CanChangeStarterText=0
CanChangeTrainerText=0
TrainerClassAmount=0x42
TrainerDataTableOffset=0x3995C
TrainerDataClassCounts=[1, 1, 1, 1, 1, 1, 1, 1, 15, 0, 1, 3, 1, 1, 1, 1, 1, 1, 1, 5, 1, 12, 18, 19, 15, 1, 19, 20, 16, 13, 31, 5, 2, 3, 1, 14, 22, 21, 19, 12, 12, 6, 2, 20, 9, 1, 3, 8, 5, 9, 4, 12, 21, 19, 2, 9, 7, 3, 12, 6, 8, 5, 1, 1, 2, 5]
TMMovesOffset=0x11A00
TrainerClassNamesOffset=0x2D2D6
DoublesTrainerClasses=[60] // only twins
IntroSpriteOffset=0x5FEC
IntroCryOffset=0x60ED
MapHeaders=0x940ED
LandmarkTableOffset=0x924B6
LandmarkCount=95
TradeTableOffset=0xFCC23
TradeTableSize=6
TradeNameLength=4
TradeOTLength=3
TradesUnused=[]
TextDelayFunctionOffset=0x319E
CatchingTutorialOffsets=[0xD1295, 0xD12CB, 0xD1313]
PicPointers=0x48000
PokemonPalettes=0xACC3
TypeEffectivenessOffset=0x34CFD
GuaranteedCatchPrefix=47FA0BD1FE03
StaticPokemonSupport=0
CRC32=524478D4

[Gold (J 1.1)]
Game=AAUJ
Version=1
NonJapanese=0
Type=GS
CopyFrom=Gold (J 1.0)
CRC32=4EF7F2A5

[Silver (J 1.0)]
Game=AAXJ
Version=0
NonJapanese=0
Type=GS
CopyFrom=Gold (J 1.0)
CRC32=BE1B928A

[Silver (J 1.1)]
Game=AAXJ
Version=1
NonJapanese=0
Type=GS
CopyFrom=Silver (J 1.0)
CRC32=0AEA5383

[Crystal (J)]
Game=BXTJ
Version=0
NonJapanese=0
Type=Crystal
PokemonNamesOffset=0x5341A
PokemonNamesLength=5
PokemonStatsOffset=0x514BA
WildPokemonOffset=0x2A60C
FishingWildsOffset=0x92A56
HeadbuttWildsOffset=0xB82DA
HeadbuttTableSize=13
BCCWildsOffset=0x97D49
FleeingDataOffset=0x3C599
MoveDataOffset=0x41C69
MoveNamesOffset=0x4163B
ItemNamesOffset=0x70FA
PokemonMovesetsTableOffset=0x42753
EggMovesTableOffset=0x23B8C
SupportsFourStartingMoves=0
StarterOffsets1=[0x6E294, 0x6E296, 0x6E2AD, 0x6E2B8]
StarterOffsets2=[0x6E2D6, 0x6E2D8, 0x6E2EF, 0x6E2FA]
StarterOffsets3=[0x6E312, 0x6E314, 0x6E32B, 0x6E336]
StarterHeldItems=[0x6E2BA, 0x6E2FC, 0x6E338]
CanChangeStarterText=0
CanChangeTrainerText=0
TrainerClassAmount=0x43
TrainerDataTableOffset=0x399BA
TrainerDataClassCounts=[1, 1, 1, 1, 1, 1, 1, 1, 15, 0, 1, 3, 1, 1, 1, 1, 1, 1, 1, 5, 1, 14, 24, 19, 17, 1, 20, 21, 17, 15, 31, 5, 2, 3, 1, 19, 25, 21, 19, 13, 14, 6, 2, 22, 9, 1, 3, 8, 6, 9, 4, 12, 26, 22, 2, 12, 7, 3, 14, 6, 10, 6, 1, 1, 2, 5, 1]
TMMovesOffset=0x11614
TrainerClassNamesOffset=0x2D319
DoublesTrainerClasses=[60] // only twins
MapHeaders=0x94000
LandmarkTableOffset=0x92557
LandmarkCount=96
TradeTableOffset=0xFCE57
TradeTableSize=7
TradeNameLength=4
TradeOTLength=3
TradesUnused=[]
TextDelayFunctionOffset=0x3109
PicPointers=0x120000
PokemonPalettes=0xA88B
TypeEffectivenessOffset=0x34BB1
GuaranteedCatchPrefix=47FA61D2FE03
StaticPokemonSupport=0
IntroSpriteOffset=0x5FC2
IntroCryOffset=0x60BE
MoveTutorMoves=[0x49206, 0x49209, 0x49203]
CatchingTutorialOffsets=[0x993C1, 0x993F7, 0x9943F]
CRC32=270C4ECC

[Gold (F)]
Game=AAUF
Version=0
NonJapanese=1
Type=GS
CopyFrom=Gold (U)
ExtraTableFile=gsc_freger
PokemonNamesOffset=0x1B0BC5
PokemonStatsOffset=0x51B10
WildPokemonOffset=0x2ABB2
FishingWildsOffset=0x92454
BCCWildsOffset=0x97BD7
MoveDataOffset=0x41B09
MoveNamesOffset=0x1B15C5
PokemonMovesetsTableOffset=0x427C8
EggMovesTableOffset=0x23A00
TMMovesOffset=0x11A65
TrainerClassNamesOffset=0x1B0995
IntroSpriteOffset=0x6013
IntroCryOffset=0x6096
LandmarkTableOffset=0x9C02D
LandmarkCount=95
TextDelayFunctionOffset=0x31F4
CatchingTutorialOffsets=[0x128D9C, 0x128DD2, 0x128E1A]
StaticPokemonSupport=1
CanChangeStarterText=0
GameCornerPokemonNameLength=11
StaticPokemon{}={Species=[0x1117E7, 0x1117EA], Level=[0x1117EB]} // Lapras
StaticPokemon{}={Species=[0x114DA4, 0x114DA7], Level=[0x114DA8]} // Electrode1
StaticPokemon{}={Species=[0x114DCF, 0x114DD2], Level=[0x114DD3]} // Electrode2
StaticPokemon{}={Species=[0x114DFA, 0x114DFD], Level=[0x114DFE]} // Electrode3
StaticPokemon{}={Species=[0x11C1A6, 0x11C1B6], Level=[0x11C1B7]} // Lugia
StaticPokemon{}={Species=[0x125051, 0x125055], Level=[0x125056]} // RedGyarados
StaticPokemon{}={Species=[0x12E13B], Level=[0x12E13C]} // Sudowoodo
StaticPokemon{}={Species=[0x13D1A8, 0x13D1AF], Level=[0x13D1B0]} // Snorlax
StaticPokemon{}={Species=[0x16E919, 0x16E929], Level=[0x16E92A]} // Ho-Oh
StaticPokemon{}={Species=[0x1146FF, 0x11470A], Level=[0x11470B]} // Voltorb
StaticPokemon{}={Species=[0x114712, 0x11471D], Level=[0x11471E]} // Geodude
StaticPokemon{}={Species=[0x114725, 0x114730], Level=[0x114731]} // Koffing
StaticPokemon{}={Species=[0x741B], Level=[0x7420]} // Shuckle
StaticPokemon{}={Species=[0x119EC2], Level=[0x119EC3]} // Tyrogue
StaticPokemon{}={Species=[0x159572]} // Togepi (egg)
StaticPokemon{}={Species=[0x16035E], Level=[0x16035F]} // Kenya
StaticPokemon{}={Species=[0x15CEB9], Level=[0x15CEBA]} // Eevee
StaticPokemon{}={Species=[0x2A855, 0x3C568, 0x1094CB], Level=[0x2A864]} // Raikou
StaticPokemon{}={Species=[0x2A85A, 0x3C569, 0x1094D9], Level=[0x2A864]} // Entei
StaticPokemon{}={Species=[0x2A85F, 0x3C56A, 0x1094E7], Level=[0x2A864]} // Suicune
StaticEggPokemonOffsets=[14]
CRC32=37A70702

[Silver (F)]
Game=AAXF
Version=0
NonJapanese=1
Type=GS
CopyFrom=Gold (F)
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x1117E7, 0x1117EA], Level=[0x1117EB]} // Lapras
StaticPokemon{}={Species=[0x114DA4, 0x114DA7], Level=[0x114DA8]} // Electrode1
StaticPokemon{}={Species=[0x114DCF, 0x114DD2], Level=[0x114DD3]} // Electrode2
StaticPokemon{}={Species=[0x114DFA, 0x114DFD], Level=[0x114DFE]} // Electrode3
StaticPokemon{}={Species=[0x11C1A6, 0x11C1C1], Level=[0x11C1C2]} // Lugia
StaticPokemon{}={Species=[0x125051, 0x125055], Level=[0x125056]} // RedGyarados
StaticPokemon{}={Species=[0x12E13B], Level=[0x12E13C]} // Sudowoodo
StaticPokemon{}={Species=[0x13D1A8, 0x13D1AF], Level=[0x13D1B0]} // Snorlax
StaticPokemon{}={Species=[0x16E919, 0x16E934], Level=[0x16E935]} // Ho-Oh
StaticPokemon{}={Species=[0x1146FF, 0x11470A], Level=[0x11470B]} // Voltorb
StaticPokemon{}={Species=[0x114712, 0x11471D], Level=[0x11471E]} // Geodude
StaticPokemon{}={Species=[0x114725, 0x114730], Level=[0x114731]} // Koffing
StaticPokemon{}={Species=[0x73E1], Level=[0x73E6]} // Shuckle
StaticPokemon{}={Species=[0x119EC2], Level=[0x119EC3]} // Tyrogue
StaticPokemon{}={Species=[0x159572]} // Togepi (egg)
StaticPokemon{}={Species=[0x16035E], Level=[0x16035F]} // Kenya
StaticPokemon{}={Species=[0x15CEB9], Level=[0x15CEBA]} // Eevee
StaticPokemon{}={Species=[0x2A855, 0x3C568, 0x1094CB], Level=[0x2A864]} // Raikou
StaticPokemon{}={Species=[0x2A85A, 0x3C569, 0x1094D9], Level=[0x2A864]} // Entei
StaticPokemon{}={Species=[0x2A85F, 0x3C56A, 0x1094E7], Level=[0x2A864]} // Suicune
StaticEggPokemonOffsets=[14]
CRC32=E0C216EA

[Gold (G)]
Game=AAUD
Version=0
NonJapanese=1
Type=GS
CopyFrom=Gold (U)
ExtraTableFile=gsc_freger
PokemonNamesOffset=0x1B0B6B
PokemonStatsOffset=0x51B00
WildPokemonOffset=0x2ABB5
FishingWildsOffset=0x9245F
BCCWildsOffset=0x97BD7
MoveDataOffset=0x41AF1
MoveNamesOffset=0x1B156B
PokemonMovesetsTableOffset=0x427B0
EggMovesTableOffset=0x23A00
TMMovesOffset=0x11A5D
TrainerClassNamesOffset=0x1B0946
IntroSpriteOffset=0x6016
IntroCryOffset=0x6099
LandmarkTableOffset=0x9C02D
LandmarkCount=95
TextDelayFunctionOffset=0x320F
CatchingTutorialOffsets=[0x128EEE, 0x128F24, 0x128F6C]
StaticPokemonSupport=1
CanChangeStarterText=0
GameCornerPokemonNameLength=11
StaticPokemon{}={Species=[0x111A29, 0x111A2C], Level=[0x111A2D]} // Lapras
StaticPokemon{}={Species=[0x114E5F, 0x114E62], Level=[0x114E63]} // Electrode1
StaticPokemon{}={Species=[0x114E8A, 0x114E8D], Level=[0x114E8E]} // Electrode2
StaticPokemon{}={Species=[0x114EB5, 0x114EB8], Level=[0x114EB9]} // Electrode3
StaticPokemon{}={Species=[0x11C1A6, 0x11C1B6], Level=[0x11C1B7]} // Lugia
StaticPokemon{}={Species=[0x125100, 0x125104], Level=[0x125105]} // RedGyarados
StaticPokemon{}={Species=[0x12E5FE], Level=[0x12E5FF]} // Sudowoodo
StaticPokemon{}={Species=[0x13D4E7, 0x13D4EE], Level=[0x13D4EF]} // Snorlax
StaticPokemon{}={Species=[0x16EC22, 0x16EC32], Level=[0x16EC33]} // Ho-Oh
StaticPokemon{}={Species=[0x11470E, 0x114719], Level=[0x11471A]} // Voltorb
StaticPokemon{}={Species=[0x114721, 0x11472C], Level=[0x11472D]} // Geodude
StaticPokemon{}={Species=[0x114734, 0x11473F], Level=[0x114740]} // Koffing
StaticPokemon{}={Species=[0x741D], Level=[0x7422]} // Shuckle
StaticPokemon{}={Species=[0x11A249], Level=[0x11A24A]} // Tyrogue
StaticPokemon{}={Species=[0x159680]} // Togepi (egg)
StaticPokemon{}={Species=[0x1603CA], Level=[0x1603CB]} // Kenya
StaticPokemon{}={Species=[0x15CE51], Level=[0x15CE52]} // Eevee
StaticPokemon{}={Species=[0x2A858, 0x3C568, 0x1095C5], Level=[0x2A867]} // Raikou
StaticPokemon{}={Species=[0x2A85D, 0x3C569, 0x1095D3], Level=[0x2A867]} // Entei
StaticPokemon{}={Species=[0x2A862, 0x3C56A, 0x1095E1], Level=[0x2A867]} // Suicune
StaticEggPokemonOffsets=[14]
CRC32=4889DFAA

[Silver (G)]
Game=AAXD
Version=0
NonJapanese=1
Type=GS
CopyFrom=Gold (G)
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x111A29, 0x111A2C], Level=[0x111A2D]} // Lapras
StaticPokemon{}={Species=[0x114E5F, 0x114E62], Level=[0x114E63]} // Electrode1
StaticPokemon{}={Species=[0x114E8A, 0x114E8D], Level=[0x114E8E]} // Electrode2
StaticPokemon{}={Species=[0x114EB5, 0x114EB8], Level=[0x114EB9]} // Electrode3
StaticPokemon{}={Species=[0x11C1A6, 0x11C1C1], Level=[0x11C1C2]} // Lugia
StaticPokemon{}={Species=[0x125100, 0x125104], Level=[0x125105]} // RedGyarados
StaticPokemon{}={Species=[0x12E5FE], Level=[0x12E5FF]} // Sudowoodo
StaticPokemon{}={Species=[0x13D4E7, 0x13D4EE], Level=[0x13D4EF]} // Snorlax
StaticPokemon{}={Species=[0x16EC22, 0x16EC3D], Level=[0x16EC3E]} // Ho-Oh
StaticPokemon{}={Species=[0x11470E, 0x114719], Level=[0x11471A]} // Voltorb
StaticPokemon{}={Species=[0x114721, 0x11472C], Level=[0x11472D]} // Geodude
StaticPokemon{}={Species=[0x114734, 0x11473F], Level=[0x114740]} // Koffing
StaticPokemon{}={Species=[0x73E4], Level=[0x73E9]} // Shuckle
StaticPokemon{}={Species=[0x11A249], Level=[0x11A24A]} // Tyrogue
StaticPokemon{}={Species=[0x159680]} // Togepi (egg)
StaticPokemon{}={Species=[0x1603CA], Level=[0x1603CB]} // Kenya
StaticPokemon{}={Species=[0x15CE51], Level=[0x15CE52]} // Eevee
StaticPokemon{}={Species=[0x2A858, 0x3C568, 0x1095C5], Level=[0x2A867]} // Raikou
StaticPokemon{}={Species=[0x2A85D, 0x3C569, 0x1095D3], Level=[0x2A867]} // Entei
StaticPokemon{}={Species=[0x2A862, 0x3C56A, 0x1095E1], Level=[0x2A867]} // Suicune
StaticEggPokemonOffsets=[14]
CRC32=96C9DB95

[Gold (S)]
Game=AAUS
Version=0
NonJapanese=1
Type=GS
CopyFrom=Gold (U)
ExtraTableFile=gsc_espita
PokemonNamesOffset=0x1B0BB8
PokemonStatsOffset=0x51B19
WildPokemonOffset=0x2ABA0
FishingWildsOffset=0x92464
BCCWildsOffset=0x97BCF
MoveDataOffset=0x41B0F
MoveNamesOffset=0x1B15B8
PokemonMovesetsTableOffset=0x427CE
EggMovesTableOffset=0x239FF
TMMovesOffset=0x11A79
TrainerClassNamesOffset=0x1B098B
IntroSpriteOffset=0x6022
IntroCryOffset=0x60A5
LandmarkTableOffset=0x9C02D
LandmarkCount=95
TextDelayFunctionOffset=0x3206
CatchingTutorialOffsets=[0x128DC3, 0x128DF9, 0x128E41]
StaticPokemonSupport=1
CanChangeStarterText=0
GameCornerPokemonNameLength=11
StaticPokemon{}={Species=[0x111714, 0x111717], Level=[0x111718]} // Lapras
StaticPokemon{}={Species=[0x114DD6, 0x114DD9], Level=[0x114DDA]} // Electrode1
StaticPokemon{}={Species=[0x114E01, 0x114E04], Level=[0x114E05]} // Electrode2
StaticPokemon{}={Species=[0x114E2C, 0x114E2F], Level=[0x114E30]} // Electrode3
StaticPokemon{}={Species=[0x11C1A6, 0x11C1B6], Level=[0x11C1B7]} // Lugia
StaticPokemon{}={Species=[0x124F4F, 0x124F53], Level=[0x124F54]} // RedGyarados
StaticPokemon{}={Species=[0x12E1D3], Level=[0x12E1D4]} // Sudowoodo
StaticPokemon{}={Species=[0x13D2B7, 0x13D2BE], Level=[0x13D2BF]} // Snorlax
StaticPokemon{}={Species=[0x16E97D, 0x16E98D], Level=[0x16E98E]} // Ho-Oh
StaticPokemon{}={Species=[0x114702, 0x11470D], Level=[0x11470E]} // Voltorb
StaticPokemon{}={Species=[0x114715, 0x114720], Level=[0x114721]} // Geodude
StaticPokemon{}={Species=[0x114728, 0x114733], Level=[0x114734]} // Koffing
StaticPokemon{}={Species=[0x742E], Level=[0x7433]} // Shuckle
StaticPokemon{}={Species=[0x119F0B], Level=[0x119F0C]} // Tyrogue
StaticPokemon{}={Species=[0x15935C]} // Togepi (egg)
StaticPokemon{}={Species=[0x160329], Level=[0x16032A]} // Kenya
StaticPokemon{}={Species=[0x15CC68], Level=[0x15CC69]} // Eevee
StaticPokemon{}={Species=[0x2A843, 0x3C568, 0x109391], Level=[0x2A852]} // Raikou
StaticPokemon{}={Species=[0x2A848, 0x3C569, 0x10939F], Level=[0x2A852]} // Entei
StaticPokemon{}={Species=[0x2A84D, 0x3C56A, 0x1093AD], Level=[0x2A852]} // Suicune
StaticEggPokemonOffsets=[14]
CRC32=3434A92B

[Silver (S)]
Game=AAXS
Version=0
NonJapanese=1
Type=GS
CopyFrom=Gold (S)
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x111714, 0x111717], Level=[0x111718]} // Lapras
StaticPokemon{}={Species=[0x114DD6, 0x114DD9], Level=[0x114DDA]} // Electrode1
StaticPokemon{}={Species=[0x114E01, 0x114E04], Level=[0x114E05]} // Electrode2
StaticPokemon{}={Species=[0x114E2C, 0x114E2F], Level=[0x114E30]} // Electrode3
StaticPokemon{}={Species=[0x11C1A6, 0x11C1C1], Level=[0x11C1C2]} // Lugia
StaticPokemon{}={Species=[0x124F4F, 0x124F53], Level=[0x124F54]} // RedGyarados
StaticPokemon{}={Species=[0x12E1D3], Level=[0x12E1D4]} // Sudowoodo
StaticPokemon{}={Species=[0x13D2B7, 0x13D2BE], Level=[0x13D2BF]} // Snorlax
StaticPokemon{}={Species=[0x16E97D, 0x16E998], Level=[0x16E999]} // Ho-Oh
StaticPokemon{}={Species=[0x114702, 0x11470D], Level=[0x11470E]} // Voltorb
StaticPokemon{}={Species=[0x114715, 0x114720], Level=[0x114721]} // Geodude
StaticPokemon{}={Species=[0x114728, 0x114733], Level=[0x114734]} // Koffing
StaticPokemon{}={Species=[0x73F6], Level=[0x73FB]} // Shuckle
StaticPokemon{}={Species=[0x119F0B], Level=[0x119F0C]} // Tyrogue
StaticPokemon{}={Species=[0x15935C]} // Togepi (egg)
StaticPokemon{}={Species=[0x160329], Level=[0x16032A]} // Kenya
StaticPokemon{}={Species=[0x15CC68], Level=[0x15CC69]} // Eevee
StaticPokemon{}={Species=[0x2A843, 0x3C568, 0x109391], Level=[0x2A852]} // Raikou
StaticPokemon{}={Species=[0x2A848, 0x3C569, 0x10939F], Level=[0x2A852]} // Entei
StaticPokemon{}={Species=[0x2A84D, 0x3C56A, 0x1093AD], Level=[0x2A852]} // Suicune
StaticEggPokemonOffsets=[14]
CRC32=1D9FAAC5

[Gold (I)]
Game=AAUI
Version=0
NonJapanese=1
Type=GS
CopyFrom=Gold (U)
ExtraTableFile=gsc_espita
PokemonNamesOffset=0x1B0BD2
PokemonStatsOffset=0x51B19
WildPokemonOffset=0x2AB99
FishingWildsOffset=0x92447
BCCWildsOffset=0x97BD5
MoveDataOffset=0x41AFD
MoveNamesOffset=0x1B15D2
PokemonMovesetsTableOffset=0x427BC
EggMovesTableOffset=0x239FF
TMMovesOffset=0x11A65
TrainerClassNamesOffset=0x1B099A
IntroSpriteOffset=0x6011
IntroCryOffset=0x6094
LandmarkTableOffset=0x9C02D
LandmarkCount=95
TextDelayFunctionOffset=0x3207
CatchingTutorialOffsets=[0x128DFE, 0x128E34, 0x128E7C]
StaticPokemonSupport=1
CanChangeStarterText=0
GameCornerPokemonNameLength=11
StaticPokemon{}={Species=[0x1117CC, 0x1117CF], Level=[0x1117D0]} // Lapras
StaticPokemon{}={Species=[0x114DF5, 0x114DF8], Level=[0x114DF9]} // Electrode1
StaticPokemon{}={Species=[0x114E20, 0x114E23], Level=[0x114E24]} // Electrode2
StaticPokemon{}={Species=[0x114E4B, 0x114E4E], Level=[0x114E4F]} // Electrode3
StaticPokemon{}={Species=[0x11C1A6, 0x11C1B6], Level=[0x11C1B7]} // Lugia
StaticPokemon{}={Species=[0x124F0B, 0x124F0F], Level=[0x124F10]} // RedGyarados
StaticPokemon{}={Species=[0x12E26A], Level=[0x12E26B]} // Sudowoodo
StaticPokemon{}={Species=[0x13D2D2, 0x13D2D9], Level=[0x13D2DA]} // Snorlax
StaticPokemon{}={Species=[0x16E948, 0x16E958], Level=[0x16E959]} // Ho-Oh
StaticPokemon{}={Species=[0x1146CF, 0x1146DA], Level=[0x1146DB]} // Voltorb
StaticPokemon{}={Species=[0x1146E2, 0x1146ED], Level=[0x1146EE]} // Geodude
StaticPokemon{}={Species=[0x1146F5, 0x114700], Level=[0x114701]} // Koffing
StaticPokemon{}={Species=[0x7418], Level=[0x741D]} // Shuckle
StaticPokemon{}={Species=[0x119EF3], Level=[0x119EF4]} // Tyrogue
StaticPokemon{}={Species=[0x1593B0]} // Togepi (egg)
StaticPokemon{}={Species=[0x160332], Level=[0x160333]} // Kenya
StaticPokemon{}={Species=[0x15CC91], Level=[0x15CC92]} // Eevee
StaticPokemon{}={Species=[0x2A83C, 0x3C568, 0x109418], Level=[0x2A84B]} // Raikou
StaticPokemon{}={Species=[0x2A841, 0x3C569, 0x109426], Level=[0x2A84B]} // Entei
StaticPokemon{}={Species=[0x2A846, 0x3C56A, 0x109434], Level=[0x2A84B]} // Suicune
StaticEggPokemonOffsets=[14]
CRC32=4C184CE3

[Silver (I)]
Game=AAXI
Version=0
NonJapanese=1
Type=GS
CopyFrom=Gold (I)
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x1117CC, 0x1117CF], Level=[0x1117D0]} // Lapras
StaticPokemon{}={Species=[0x114DF5, 0x114DF8], Level=[0x114DF9]} // Electrode1
StaticPokemon{}={Species=[0x114E20, 0x114E23], Level=[0x114E24]} // Electrode2
StaticPokemon{}={Species=[0x114E4B, 0x114E4E], Level=[0x114E4F]} // Electrode3
StaticPokemon{}={Species=[0x11C1A6, 0x11C1C1], Level=[0x11C1C2]} // Lugia
StaticPokemon{}={Species=[0x124F0B, 0x124F0F], Level=[0x124F10]} // RedGyarados
StaticPokemon{}={Species=[0x12E26A], Level=[0x12E26B]} // Sudowoodo
StaticPokemon{}={Species=[0x13D2D2, 0x13D2D9], Level=[0x13D2DA]} // Snorlax
StaticPokemon{}={Species=[0x16E948, 0x16E963], Level=[0x16E964]} // Ho-Oh
StaticPokemon{}={Species=[0x1146CF, 0x1146DA], Level=[0x1146DB]} // Voltorb
StaticPokemon{}={Species=[0x1146E2, 0x1146ED], Level=[0x1146EE]} // Geodude
StaticPokemon{}={Species=[0x1146F5, 0x114700], Level=[0x114701]} // Koffing
StaticPokemon{}={Species=[0x73DE], Level=[0x73E3]} // Shuckle
StaticPokemon{}={Species=[0x119EF3], Level=[0x119EF4]} // Tyrogue
StaticPokemon{}={Species=[0x1593B0]} // Togepi (egg)
StaticPokemon{}={Species=[0x160332], Level=[0x160333]} // Kenya
StaticPokemon{}={Species=[0x15CC91], Level=[0x15CC92]} // Eevee
StaticPokemon{}={Species=[0x2A83C, 0x3C568, 0x109418], Level=[0x2A84B]} // Raikou
StaticPokemon{}={Species=[0x2A841, 0x3C569, 0x109426], Level=[0x2A84B]} // Entei
StaticPokemon{}={Species=[0x2A846, 0x3C56A, 0x109434], Level=[0x2A84B]} // Suicune
StaticEggPokemonOffsets=[14]
CRC32=CBA6D2D4

[Crystal (F)]
Game=BYTF
Version=0
NonJapanese=1
Type=Crystal
CopyFrom=Crystal (U)
ExtraTableFile=gsc_freger
PokemonNamesOffset=0x53377
PokemonStatsOffset=0x51417
WildPokemonOffset=0x2A5F2
FishingWildsOffset=0x924FA
HeadbuttWildsOffset=0xB830C
BCCWildsOffset=0x97D88
MoveDataOffset=0x41B06
MoveNamesOffset=0x1C9F96
PokemonMovesetsTableOffset=0x425BC
SupportsFourStartingMoves=1
StarterOffsets1=[0x78C67, 0x78C69, 0x78C80, 0x78C8B]
StarterOffsets2=[0x78CA9, 0x78CAB, 0x78CC2, 0x78CCD]
StarterOffsets3=[0x78CE5, 0x78CE7, 0x78CFE, 0x78D09]
StarterHeldItems=[0x78C8D, 0x78CCF, 0x78D0B]
TMMovesOffset=0x11679
TrainerClassNamesOffset=0x2C1EF
IntroSpriteOffset=0x5FEC
IntroCryOffset=0x606A
LandmarkTableOffset=0x1CA97F
LandmarkCount=96
TextDelayFunctionOffset=0x312A
CatchingTutorialOffsets=[0x1A0F54, 0x1A0F8A, 0x1A0FD2]
StaticPokemonSupport=1
CanChangeStarterText=0
GameCornerPokemonNameLength=11
StaticPokemon{}={Species=[0x5A400, 0x5A403], Level=[0x5A404]} // Lapras
StaticPokemon{}={Species=[0x6D10E, 0x6D111], Level=[0x6D112]} // Electrode1
StaticPokemon{}={Species=[0x6D139, 0x6D13C], Level=[0x6D13D]} // Electrode2
StaticPokemon{}={Species=[0x6D164, 0x6D167], Level=[0x6D168]} // Electrode3
StaticPokemon{}={Species=[0x18C515, 0x18C521], Level=[0x18C522]} // Lugia
StaticPokemon{}={Species=[0x7006A, 0x7006E], Level=[0x7006F]} // RedGyarados
StaticPokemon{}={Species=[0x194068], Level=[0x194069]} // Sudowoodo
StaticPokemon{}={Species=[0x1AA8E6, 0x1AA8ED], Level=[0x1AA8EE]} // Snorlax
StaticPokemon{}={Species=[0x77273, 0x7727F], Level=[0x77280]} // Ho-Oh
StaticPokemon{}={Species=[0x4A671, 0x71F4F, 0x1851CA, 0x1851CF, 0x186206, 0x186240], Level=[0x1851D0]} // Suicune
StaticPokemon{}={Species=[0x6CA74, 0x6CA7F], Level=[0x6CA80]} // Voltorb
StaticPokemon{}={Species=[0x6CA87, 0x6CA92], Level=[0x6CA93]} // Geodude
StaticPokemon{}={Species=[0x6CA9A, 0x6CAA5], Level=[0x6CAA6]} // Koffing
StaticPokemon{}={Species=[0x7324], Level=[0x7329]} // Shuckle
StaticPokemon{}={Species=[0x7E1C2], Level=[0x7E1C3]} // Tyrogue
StaticPokemon{}={Species=[0x697B9]} // Togepi (egg)
StaticPokemon{}={Species=[0x69FF3], Level=[0x69FF4]} // Kenya
StaticPokemon{}={Species=[0x54E6C], Level=[0x54E6D]} // Eevee
StaticPokemon{}={Species=[0x18D1A6], Level=[0x18D1A7]} // Dratini
StaticPokemon{}={Species=[0x2A2AA, 0x3C5B1, 0x4A65D, 0x18518A, 0x1861EA], Level=[0x2A2B4]} // Raikou
StaticPokemon{}={Species=[0x2A2AF, 0x3C5B2, 0x4A667, 0x1851AB, 0x1861F8], Level=[0x2A2B4]} // Entei
StaticPokemonOddEggOffset=0x1FB56E
StaticPokemonOddEggDataSize=0x3B
StaticEggPokemonOffsets=[15]
MoveTutorMoves=[0x49212, 0x49215, 0x4920F]
CRC32=878B2AA7

[Crystal (G)]
Game=BYTD
Version=0
NonJapanese=1
Type=Crystal
CopyFrom=Crystal (U)
ExtraTableFile=gsc_freger
PokemonNamesOffset=0x5336E
PokemonStatsOffset=0x5140E
WildPokemonOffset=0x2A5FE
FishingWildsOffset=0x92502
HeadbuttWildsOffset=0xB830C
BCCWildsOffset=0x97D88
MoveDataOffset=0x41AEB
MoveNamesOffset=0x1C9E9D
PokemonMovesetsTableOffset=0x425A1
SupportsFourStartingMoves=1
StarterOffsets1=[0x78DFA, 0x78DFC, 0x78E13, 0x78E1E]
StarterOffsets2=[0x78E3C, 0x78E3E, 0x78E55, 0x78E60]
StarterOffsets3=[0x78E78, 0x78E7A, 0x78E91, 0x78E9C]
StarterHeldItems=[0x78E20, 0x78E62, 0x78E9E]
TMMovesOffset=0x11671
TrainerClassNamesOffset=0x2C1EF
IntroSpriteOffset=0x5FF3
IntroCryOffset=0x6071
LandmarkTableOffset=0x1CA8E5
LandmarkCount=96
TextDelayFunctionOffset=0x3127
CatchingTutorialOffsets=[0x1A11AA, 0x1A11E0, 0x1A1228]
StaticPokemonSupport=1
CanChangeStarterText=0
GameCornerPokemonNameLength=11
StaticPokemon{}={Species=[0x5A6D8, 0x5A6DB], Level=[0x5A6DC]} // Lapras
StaticPokemon{}={Species=[0x6D1F9, 0x6D1FC], Level=[0x6D1FD]} // Electrode1
StaticPokemon{}={Species=[0x6D224, 0x6D227], Level=[0x6D228]} // Electrode2
StaticPokemon{}={Species=[0x6D24F, 0x6D252], Level=[0x6D253]} // Electrode3
StaticPokemon{}={Species=[0x18C5B9, 0x18C5C5], Level=[0x18C5C6]} // Lugia
StaticPokemon{}={Species=[0x7006A, 0x7006E], Level=[0x7006F]} // RedGyarados
StaticPokemon{}={Species=[0x194068], Level=[0x194069]} // Sudowoodo
StaticPokemon{}={Species=[0x1AAEBB, 0x1AAEC2], Level=[0x1AAEC3]} // Snorlax
StaticPokemon{}={Species=[0x7761A, 0x77626], Level=[0x77627]} // Ho-Oh
StaticPokemon{}={Species=[0x4A678, 0x72287, 0x18530B, 0x185310, 0x18653E, 0x186578], Level=[0x185311]} // Suicune
StaticPokemon{}={Species=[0x6CAA5, 0x6CAB0], Level=[0x6CAB1]} // Voltorb
StaticPokemon{}={Species=[0x6CAB8, 0x6CAC3], Level=[0x6CAC4]} // Geodude
StaticPokemon{}={Species=[0x6CACB, 0x6CAD6], Level=[0x6CAD7]} // Koffing
StaticPokemon{}={Species=[0x732B], Level=[0x7330]} // Shuckle
StaticPokemon{}={Species=[0x7E585], Level=[0x7E586]} // Tyrogue
StaticPokemon{}={Species=[0x69927]} // Togepi (egg)
StaticPokemon{}={Species=[0x6A2C8], Level=[0x6A2C9]} // Kenya
StaticPokemon{}={Species=[0x54E5A], Level=[0x54E5B]} // Eevee
StaticPokemon{}={Species=[0x18D34F], Level=[0x18D350]} // Dratini
StaticPokemon{}={Species=[0x2A2B6, 0x3C5B1, 0x4A664, 0x1852CB, 0x186522], Level=[0x2A2C0]} // Raikou
StaticPokemon{}={Species=[0x2A2BB, 0x3C5B2, 0x4A66E, 0x1852EC, 0x186530], Level=[0x2A2C0]} // Entei
StaticPokemonOddEggOffset=0x1FB56E
StaticPokemonOddEggDataSize=0x3B
StaticEggPokemonOffsets=[15]
MoveTutorMoves=[0x49213, 0x49216, 0x49210]
CRC32=616D85DE

[Crystal (S)]
Game=BYTS
Version=0
NonJapanese=1
Type=Crystal
CopyFrom=Crystal (U)
ExtraTableFile=gsc_espita
PokemonNamesOffset=0x5338D
PokemonStatsOffset=0x5142D
WildPokemonOffset=0x2A5F0
FishingWildsOffset=0x9250C
HeadbuttWildsOffset=0xB830C
BCCWildsOffset=0x97D80
MoveDataOffset=0x41B0E
MoveNamesOffset=0x1CA045
PokemonMovesetsTableOffset=0x425C4
SupportsFourStartingMoves=1
StarterOffsets1=[0x78C70, 0x78C72, 0x78C89, 0x78C94]
StarterOffsets2=[0x78CB2, 0x78CB4, 0x78CCB, 0x78CD6]
StarterOffsets3=[0x78CEE, 0x78CF0, 0x78D07, 0x78D12]
StarterHeldItems=[0x78C96, 0x78CD8, 0x78D14]
TMMovesOffset=0x1168D
TrainerClassNamesOffset=0x2C1EF
IntroSpriteOffset=0x5FF1
IntroCryOffset=0x606F
LandmarkTableOffset=0x1CAAB4
LandmarkCount=96
TextDelayFunctionOffset=0x3127
CatchingTutorialOffsets=[0x1A0FAC, 0x1A0FE2, 0x1A102A]
StaticPokemonSupport=1
CanChangeStarterText=0
GameCornerPokemonNameLength=11
StaticPokemon{}={Species=[0x5A3EE, 0x5A3F1], Level=[0x5A3F2]} // Lapras
StaticPokemon{}={Species=[0x6D155, 0x6D158], Level=[0x6D159]} // Electrode1
StaticPokemon{}={Species=[0x6D180, 0x6D183], Level=[0x6D184]} // Electrode2
StaticPokemon{}={Species=[0x6D1AB, 0x6D1AE], Level=[0x6D1AF]} // Electrode3
StaticPokemon{}={Species=[0x18C541, 0x18C54D], Level=[0x18C54E]} // Lugia
StaticPokemon{}={Species=[0x7006A, 0x7006E], Level=[0x7006F]} // RedGyarados
StaticPokemon{}={Species=[0x194068], Level=[0x194069]} // Sudowoodo
StaticPokemon{}={Species=[0x1AAA68, 0x1AAA6F], Level=[0x1AAA70]} // Snorlax
StaticPokemon{}={Species=[0x7724B, 0x77257], Level=[0x77258]} // Ho-Oh
StaticPokemon{}={Species=[0x4A66D, 0x71E41, 0x185134, 0x185139, 0x1862E5, 0x18631F], Level=[0x18513A]} // Suicune
StaticPokemon{}={Species=[0x6CA6E, 0x6CA79], Level=[0x6CA7A]} // Voltorb
StaticPokemon{}={Species=[0x6CA81, 0x6CA8C], Level=[0x6CA8D]} // Geodude
StaticPokemon{}={Species=[0x6CA94, 0x6CA9F], Level=[0x6CAA0]} // Koffing
StaticPokemon{}={Species=[0x7329], Level=[0x732E]} // Shuckle
StaticPokemon{}={Species=[0x7E1F7], Level=[0x7E1F8]} // Tyrogue
StaticPokemon{}={Species=[0x695F2]} // Togepi (egg)
StaticPokemon{}={Species=[0x69E73], Level=[0x69E74]} // Kenya
StaticPokemon{}={Species=[0x54C5B], Level=[0x54C5C]} // Eevee
StaticPokemon{}={Species=[0x18D224], Level=[0x18D225]} // Dratini
StaticPokemon{}={Species=[0x2A2A8, 0x3C5B1, 0x4A659, 0x1850F4, 0x1862C9], Level=[0x2A2B2]} // Raikou
StaticPokemon{}={Species=[0x2A2AD, 0x3C5B2, 0x4A663, 0x185115, 0x1862D7], Level=[0x2A2B2]} // Entei
StaticPokemonOddEggOffset=0x1FB56D
StaticPokemonOddEggDataSize=0x3B
StaticEggPokemonOffsets=[15]
MoveTutorMoves=[0x49211, 0x49214, 0x4920E]
CRC32=FF0A6F8A

[Crystal (I)]
Game=BYTI
Version=0
NonJapanese=1
Type=Crystal
CopyFrom=Crystal (U)
ExtraTableFile=gsc_espita
PokemonNamesOffset=0x53393
PokemonStatsOffset=0x51433
WildPokemonOffset=0x2A5E0
FishingWildsOffset=0x924F6
HeadbuttWildsOffset=0xB830C
BCCWildsOffset=0x97D86
MoveDataOffset=0x41AFA
MoveNamesOffset=0x1C9E86
PokemonMovesetsTableOffset=0x425B0
SupportsFourStartingMoves=1
StarterOffsets1=[0x78CD4, 0x78CD6, 0x78CED, 0x78CF8]
StarterOffsets2=[0x78D16, 0x78D18, 0x78D2F, 0x78D3A]
StarterOffsets3=[0x78D52, 0x78D54, 0x78D6B, 0x78D76]
StarterHeldItems=[0x78CFA, 0x78D3C, 0x78D78]
TMMovesOffset=0x11679
TrainerClassNamesOffset=0x2C1EF
IntroSpriteOffset=0x5FEB
IntroCryOffset=0x6069
LandmarkTableOffset=0x1CA8B4
LandmarkCount=96
TextDelayFunctionOffset=0x312B
CatchingTutorialOffsets=[0x1A0F97, 0x1A0FCD, 0x1A1015]
StaticPokemonSupport=1
CanChangeStarterText=0
GameCornerPokemonNameLength=11
StaticPokemon{}={Species=[0x5A35E, 0x5A361], Level=[0x5A362]} // Lapras
StaticPokemon{}={Species=[0x6D16C, 0x6D16F], Level=[0x6D170]} // Electrode1
StaticPokemon{}={Species=[0x6D197, 0x6D19A], Level=[0x6D19B]} // Electrode2
StaticPokemon{}={Species=[0x6D1C2, 0x6D1C5], Level=[0x6D1C6]} // Electrode3
StaticPokemon{}={Species=[0x18C528, 0x18C534], Level=[0x18C535]} // Lugia
StaticPokemon{}={Species=[0x7006A, 0x7006E], Level=[0x7006F]} // RedGyarados
StaticPokemon{}={Species=[0x194068], Level=[0x194069]} // Sudowoodo
StaticPokemon{}={Species=[0x1AAA70, 0x1AAA77], Level=[0x1AAA78]} // Snorlax
StaticPokemon{}={Species=[0x772AC, 0x772B8], Level=[0x772B9]} // Ho-Oh
StaticPokemon{}={Species=[0x4A678, 0x71EE1, 0x185182, 0x185187, 0x186245, 0x18627F], Level=[0x185188]} // Suicune
StaticPokemon{}={Species=[0x6CA3C, 0x6CA47], Level=[0x6CA48]} // Voltorb
StaticPokemon{}={Species=[0x6CA4F, 0x6CA5A], Level=[0x6CA5B]} // Geodude
StaticPokemon{}={Species=[0x6CA62, 0x6CA6D], Level=[0x6CA6E]} // Koffing
StaticPokemon{}={Species=[0x7323], Level=[0x7328]} // Shuckle
StaticPokemon{}={Species=[0x7E217], Level=[0x7E218]} // Tyrogue
StaticPokemon{}={Species=[0x69651]} // Togepi (egg)
StaticPokemon{}={Species=[0x69E5A], Level=[0x69E5B]} // Kenya
StaticPokemon{}={Species=[0x54CA2], Level=[0x54CA3]} // Eevee
StaticPokemon{}={Species=[0x18D1C3], Level=[0x18D1C4]} // Dratini
StaticPokemon{}={Species=[0x2A298, 0x3C5B1, 0x4A664, 0x185142, 0x186229], Level=[0x2A2A2]} // Raikou
StaticPokemon{}={Species=[0x2A29D, 0x3C5B2, 0x4A66E, 0x185163, 0x186237], Level=[0x2A2A2]} // Entei
StaticPokemonOddEggOffset=0x1FB56E
StaticPokemonOddEggDataSize=0x3B
StaticEggPokemonOffsets=[15]
MoveTutorMoves=[0x49215, 0x49218, 0x49212]
CRC32=D45AC039
