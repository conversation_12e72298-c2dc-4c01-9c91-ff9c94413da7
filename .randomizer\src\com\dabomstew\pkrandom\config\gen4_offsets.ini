[Diamond (U)]
Game=ADAE
Type=DP
Version=5
File<PokedexAreaData>=<application/zukanlist/zkn_data/zukan_enc_diamond.narc, 96EF04FA>
File<ExtraEncounters>=<arc/encdata_ex.narc, C64C7415>
File<BattleSkillSubSeq>=<battle/skill/sub_seq.narc, E03D048D>
File<WildPokemon>=<fielddata/encountdata/d_enc_data.narc, E58BE3CA>
File<Events>=<fielddata/eventdata/zone_event_release.narc, B15ED699>
File<MapTableFile>=<fielddata/maptable/mapname.bin, D80BFD77>
File<InGameTrades>=<fielddata/pokemon_trade/fld_trade.narc, 08464A7E>
File<Scripts>=<fielddata/script/scr_seq_release.narc, 5FDE722D>
File<ItemData>=<itemtool/itemdata/item_data.narc, 1E4BDEA1>
File<Text>=<msgdata/msg.narc, CC7250FE>
File<PokemonEvolutions>=<poketool/personal/evo.narc, 0F425176>
File<PokemonStats>=<poketool/personal/personal.narc, F963E181>
File<BabyPokemon>=<poketool/personal/pms.narc, 6531B9B2>
File<PokemonMovesets>=<poketool/personal/wotbl.narc, 2AC7EF62>
File<PokemonGraphics>=<poketool/pokegra/pokegra.narc, 953F8198>
File<TrainerData>=<poketool/trainer/trdata.narc, 4A3C0C89>
File<TrainerPokemon>=<poketool/trainer/trpoke.narc, C4AA9026>
File<MoveData>=<poketool/waza/waza_tbl.narc, 1D89A95D>
EncounterOvlNumber=6
BattleOvlNumber=11
IntroOvlNumber=59
StarterPokemonOvlNumber=64
FastestTextTweak=instant_text/dp_instant_text
NewIndexToMusicTweak=musicfix/diamond_musicfix
NationalDexAtStartTweak=national_dex/dp_national_dex
StarterPokemonOffset=0x1B88
StarterPokemonGraphicsPrefix=000222402104120C
StarterPokemonGraphicsPrefixInner=0290039002200002
StarterPokemonScriptOffset=342
StarterPokemonHeldItemOffset=0x2B4
HasExtraPokemonNames=Yes
PokemonNamesTextOffset=362
TrainerNamesTextOffset=559
TrainerClassesTextOffset=560
DoublesTrainerClasses=[8, 23, 31, 47, 70, 82]
EliteFourIndices=[261, 262, 263, 264, 267]
MoveDescriptionsTextOffset=587
MoveNamesTextOffset=588
AbilityNamesTextOffset=552
ItemNamesTextOffset=344
ItemDescriptionsTextOffset=343
StarterScreenTextOffset=320
PokedexSpeciesTextOffset=621
StarterLocationTextOffset=270
IngameTradesTextOffset=326
IngameTradePersonTextOffsets=[67,89,171,584]
HiddenItemTableOffset=0xF2DB4
HiddenItemCount=229
ItemBallsScriptOffset=370
ItemBallsSkip=[40, 196]
MapTableARM9Offset=0xEEDBC
MapTableNameIndexSize=2
MapNamesTextOffset=382
CatchingTutorialOpponentMonOffset=0x47960
NationalDexScriptOffset=990
StaticPokemonSupport=1
HoneyTreeOffsets=[2, 3, 4]
FossilTableOffset=0xF450C
FossilLevelScriptNumber=63
FossilLevelOffset=0x41A
PokedexAreaDataDungeonIndex=4
PokedexAreaDataDungeonSpecialPreNationalIndex=1489
PokedexAreaDataDungeonSpecialPostNationalIndex=1984
PokedexAreaDataOverworldIndex=2479
PokedexAreaDataOverworldSpecialPreNationalIndex=3964
PokedexAreaDataOverworldSpecialPostNationalIndex=4459
StaticPokemon{}={Species=[342:0x261, 342:0x2BE], Level=[342:0x2C0]} // Starly
StaticPokemon{}={Species=[230:0x4AE, 230:0xE9A, 230:0xECE, 230:0x1201, 230:0x1235], Level=[230:0xEE4, 230:0x124B]} // Dialga
StaticPokemon{}={Species=[230:0x4B4, 230:0xEA0, 230:0xED4, 230:0x1207, 230:0x123B], Level=[230:0xEE4, 230:0x124B]} // Palkia
StaticPokemon{}={Species=[352:0x39, 352:0x48], Level=[352:0x4A]} // Uxie
StaticPokemon{}={Species=[348:0x81, 348:0x90], Level=[348:0x92]} // Azelf
StaticPokemon{}={Species=[278:0x16F, 278:0x17E], Level=[278:0x180]} // Heatran
StaticPokemon{}={Species=[309:0x88, 309:0x94], Level=[309:0x96]} // Regigigas
StaticPokemon{}={Species=[283:0x50, 283:0x5F], Level=[283:0x61]} // Giratina
StaticPokemon{}={Species=[354:0x40], Level=[354:0x42]} // Darkrai
StaticPokemon{}={Species=[302:0x39, 302:0x48], Level=[302:0x4A]} // Shaymin
StaticPokemon{}={Species=[232:0x45, 232:0x53, 232:0x62], Level=[232:0x64]} // Arceus
StaticPokemon{}={Species=[112:0xB5], Level=[112:0xB7]} // Eevee
StaticPokemon{}={Species=[90:0x568]} // Happiny (egg)
StaticPokemon{}={Species=[321:0x332]} // Riolu (egg)
StaticPokemon{}={Species=[210:0x1C5, 210:0x1D6], Level=[210:0x1D8]} // Drifloon
StaticPokemon{}={Species=[329:0x74, 329:0x80], Level=[329:0x82]} // Rotom
StaticPokemon{}={Species=[406:0x153, 406:0x160], Level=[406:0x162]} // Spiritomb
RoamingPokemon{}={Species=[0x60580], Level=[0x604B6], Script=[344:0x1A, 344:0x24], Gender=[344:0x1C]} // Mesprit
RoamingPokemon{}={Species=[0x60584], Level=[0x604BE], Script=[274:0x18, 274:0x22], Gender=[274:0x1A]} // Cresselia
RoamingPokemonFunctionStartOffset=0x60490
ShopCount=28
SkipShops=[12,13,15,16,17,18,19,20,21,22,23]
MainGameShops=[0,1,2,3,4,5,6,7,8,9,10,11,14,24,25,26,27]
ShopDataPrefix=391104027511040285AF0302A5AF0302
StaticEggPokemonOffsets=[11, 12]
MainGameLegendaries=[483]
TCMCopyingPrefix=111011157D7005001EFF2FE11EFF2FE1
NewIndexToMusicPrefix=142003E00E2001E01220FFE702BC0847
Arm9ExtensionSize=168 // 168 for music
SpecialMusicStatics=[479,480,481,482,483,484,485,486,487,488,491,492,493]
DoubleBattleFlagReturnPrefix=08B5092131F020FB
DoubleBattleWalkingPrefix1=16B00020F8BD1498
DoubleBattleWalkingPrefix2=36FB16B00120F8BD
DoubleBattleTextBoxPrefix=F7F71AFEF7F734FE
TrainerEndFileNumber=4
TrainerEndTextBoxOffset=0xD8
TMText{}={42=[538:1], 48=[54:2], 56=[466:1, 466:2], 63=[135:35], 66=[453:1], 67=[90:0], 76=[60:4], 77=[437:2], 78=[419:2], 88=[518:2], 92=[449:1]}
Arm9CRC32=08E0337C
OverlayCRC32<6>=0AE6A693
OverlayCRC32<11>=3DCCA476
OverlayCRC32<59>=8CEA8C3C
OverlayCRC32<64>=727963E2

[Pearl (U)]
Game=APAE
Type=DP
Version=5
CopyText=1
CopyStaticPokemon=1
CopyRoamingPokemon=1
CopyFrom=Diamond (U)
File<PokedexAreaData>=<application/zukanlist/zkn_data/zukan_enc_pearl.narc, 14FDDFE6>
File<WildPokemon>=<fielddata/encountdata/p_enc_data.narc, 5AB5DD4F>
File<PokemonStats>=<poketool/personal_pearl/personal.narc, 075AD59A>
FastestTextTweak=instant_text/dp_instant_text
NewIndexToMusicTweak=musicfix/diamond_musicfix
NationalDexAtStartTweak=national_dex/dp_national_dex
HoneyTreeOffsets=[5, 6, 7]
MainGameLegendaries=[484]
Arm9CRC32=D80458A5
OverlayCRC32<6>=F7C193D2
OverlayCRC32<11>=0DD7691D
OverlayCRC32<59>=8CEA8C3C
OverlayCRC32<64>=525F49E6

[Platinum (U)]
Game=CPUE
Type=Plat
Version=0
CopyFrom=Diamond (U)
File<PokedexAreaData>=<application/zukanlist/zkn_data/zukan_enc_platinum.narc, 2F58506E>
File<ExtraEncounters>=<arc/encdata_ex.narc, 03FB3F28>
File<BattleSkillSubSeq>=<battle/skill/sub_seq.narc, B02FF213>
File<WildPokemon>=<fielddata/encountdata/pl_enc_data.narc, 188084F5>
File<Events>=<fielddata/eventdata/zone_event.narc, D8BB2D84>
File<MapTableFile>=<fielddata/maptable/mapname.bin, 530174B6>
File<InGameTrades>=<fielddata/pokemon_trade/fld_trade.narc, B98CC1AF>
File<Scripts>=<fielddata/script/scr_seq.narc, 6D482515>
File<ItemData>=<itemtool/itemdata/pl_item_data.narc, AAE44533>
File<Text>=<msgdata/pl_msg.narc, 5666AAEC>
File<PokemonEvolutions>=<poketool/personal/evo.narc, 6711E4B4>
File<PokemonStats>=<poketool/personal/pl_personal.narc, B16A8A01>
File<BabyPokemon>=<poketool/personal/pms.narc, C457991B>
File<PokemonMovesets>=<poketool/personal/wotbl.narc, 977997B9>
File<PokemonGraphics>=<poketool/pokegra/pl_pokegra.narc, 03BBC013>
File<TrainerData>=<poketool/trainer/trdata.narc, 2EE9B886>
File<TrainerPokemon>=<poketool/trainer/trpoke.narc, 739A02EE>
File<MoveData>=<poketool/waza/pl_waza_tbl.narc, B2D18230>
FieldOvlNumber=5
MoveTutorCompatOvlNumber=5
BattleOvlNumber=16
IntroOvlNumber=73
StarterPokemonOvlNumber=78
NewRoamerSubroutineTweak=hardcoded_statics/roamers/plat_roamers
FastestTextTweak=instant_text/plat_instant_text
NewIndexToMusicTweak=musicfix/plat_musicfix
NationalDexAtStartTweak=national_dex/plat_national_dex
StarterPokemonOffset=0x1BC0
StarterPokemonGraphicsPrefix=000222402104120C
StarterPokemonGraphicsPrefixInner=0290039002200002
StarterPokemonScriptOffset=427
StarterPokemonHeldItemOffset=0x460
MoveTutorMovesOffset=0x2FF64
MoveTutorCount=38
MoveTutorBytesCount=12
MoveTutorCompatOffset=0x3012C
MoveTutorCompatBytesCount=5
PokemonNamesTextOffset=412
TrainerNamesTextOffset=618
TrainerClassesTextOffset=619
MoveDescriptionsTextOffset=646
MoveNamesTextOffset=647
AbilityNamesTextOffset=610
ItemDescriptionsTextOffset=391
ItemNamesTextOffset=392
StarterScreenTextOffset=360
PokedexSpeciesTextOffset=711
StarterLocationTextOffset=466
IngameTradesTextOffset=370
IngameTradePersonTextOffsets=[74,97,180,643]
HiddenItemTableOffset=0xEA378
HiddenItemCount=257
ItemBallsScriptOffset=404
ItemBallsSkip=[25, 238, 321, 325, 326]
MapTableARM9Offset=0xE601C
MapTableNameIndexSize=1
MapNamesTextOffset=433
CatchingTutorialOpponentMonOffset=0x520A0
HoneyTreeOffsets=[2, 3, 4]
FossilTableOffset=0xEBFFC
FossilLevelScriptNumber=65
FossilLevelOffset=0x426
NationalDexScriptOffset=1064
StaticPokemonSupport=1
StaticPokemon{}={Species=[291:0x43, 291:0x52, 389:0xCC, 389:0xDD], Level=[291:0x54, 389:0xDF]} // Giratina
StaticPokemon{}={Species=[361:0x39, 361:0x48], Level=[361:0x4A]} // Uxie
StaticPokemon{}={Species=[357:0x81, 357:0x90], Level=[357:0x92]} // Azelf
StaticPokemon{}={Species=[239:0xAB, 239:0xB8], Level=[239:0xBA]} // Dialga
StaticPokemon{}={Species=[240:0xAB, 240:0xB8], Level=[240:0xBA]} // Palkia
StaticPokemon{}={Species=[286:0x103, 286:0x112], Level=[286:0x114]} // Heatran
StaticPokemon{}={Species=[317:0x88, 317:0x94], Level=[317:0x96]} // Regigigas
StaticPokemon{}={Species=[392:0xB0, 392:0xBD], Level=[392:0xBF]} // Registeel
StaticPokemon{}={Species=[394:0xB0, 394:0xBD], Level=[394:0xBF]} // Regice
StaticPokemon{}={Species=[396:0xB0, 396:0xBD], Level=[396:0xBF]} // Regirock
StaticPokemon{}={Species=[363:0x8E], Level=[363:0x90]} // Darkrai
StaticPokemon{}={Species=[310:0x87, 310:0x96], Level=[310:0x98]} // Shaymin
StaticPokemon{}={Species=[238:0x6C, 238:0x7A, 238:0x89], Level=[238:0x8B]} // Arceus
StaticPokemon{}={Species=[71:0xE5B]} // Togepi (egg)
StaticPokemon{}={Species=[117:0x79], Level=[117:0x7B]} // Eevee
StaticPokemon{}={Species=[153:0x7D], Level=[153:0x7F]} // Porygon
StaticPokemon{}={Species=[329:0x338]} // Riolu (egg)
StaticPokemon{}={Species=[216:0x1C9, 216:0x1DA], Level=[216:0x1DC]} // Drifloon
StaticPokemon{}={Species=[337:0x51, 337:0x5D], Level=[337:0x5F]} // Rotom
StaticPokemon{}={Species=[441:0x153, 441:0x160], Level=[441:0x162]} // Spiritomb
RoamingPokemon{}={Species=[0x1024B0, 0x102514], Level=[0x10248A], Script=[353:0x1A, 353:0x24], Gender=[353:0x1C]} // Mesprit
RoamingPokemon{}={Species=[0x1024B4, 0x102518], Level=[0x102490], Script=[282:0x16, 282:0x20], Gender=[282:0x18]} // Cresselia
RoamingPokemon{}={Species=[0x1024C4, 0x102524], Level=[0x1024A8]} // Articuno
RoamingPokemon{}={Species=[0x1024C0, 0x102520], Level=[0x1024A2]} // Zapdos
RoamingPokemon{}={Species=[0x1024BC, 0x10251C], Level=[0x10249C]} // Moltres
ShopCount=29
SkipShops=[13,15,16,17,18,19,20,21,22,23,24]
MainGameShops=[0,1,2,3,4,5,6,7,8,9,10,11,12,14,25,26,27,28]
ShopDataPrefix=ED7F0402258004026180040281800402
StaticEggPokemonOffsets=[13, 16]
MainGameLegendaries=[487]
TCMCopyingPrefix=111011157D7005001EFF2FE11EFF2FE1
NewIndexToMusicPrefix=142003E00E2001E01220FFE702BC0847
Arm9ExtensionSize=424 // 240 for music, 184 for roamers
SpecialMusicStatics=[144,145,146,377,378,379,479,480,481,482,483,484,485,486,487,488,491,492,493]
DoubleBattleFlagReturnPrefix=08B5092139F0C6FF
DoubleBattleWalkingPrefix1=16B00020F8BD1498
DoubleBattleWalkingPrefix2=C4FE16B00120F8BD
DoubleBattleTextBoxPrefix=F6F792FCF6F7ACFC
TMText{}={27=[561:10], 42=[594:1], 48=[61:1], 56=[517:0, 517:1], 63=[143:51], 66=[504:1, 504:2], 67=[98:0], 76=[67:4], 77=[488:2], 78=[470:2], 88=[574:2], 92=[500:1]}
FastDistortionWorldTweak=pt_fast_distortion_world
Arm9CRC32=4D104949
OverlayCRC32<5>=3E286491
OverlayCRC32<6>=E6C5F31B
OverlayCRC32<16>=25EBE8C1
OverlayCRC32<73>=C003DED1
OverlayCRC32<78>=091E8E97

[HeartGold (U)]
Game=IPKE
Type=HGSS
Version=0
File<BattleSkillSubSeq>=<a/0/0/1, 6644F919>
File<PokemonStats>=<a/0/0/2, DE8C5CAF>
File<PokemonGraphics>=<a/0/0/4, 373BBE93>
File<MoveData>=<a/0/1/1, 284AEE7A>
File<Scripts>=<a/0/1/2, A7E3F740>
File<ItemData>=<a/0/1/7, 40823EDE>
File<Text>=<a/0/2/7, 123DEB0B>
File<Events>=<a/0/3/2, 3AC93851>
File<PokemonMovesets>=<a/0/3/3, F0F9B169>
File<PokemonEvolutions>=<a/0/3/4, 6711E4B4>
File<WildPokemon>=<a/0/3/7, 9B861BD5>
File<TrainerData>=<a/0/5/5, 8B9BD9CE>
File<TrainerPokemon>=<a/0/5/6, E30BC8BE>
File<InGameTrades>=<a/1/1/2, 24561BD6>
File<PokedexAreaData>=<a/1/3/3, 4B9696A6>
File<EggMoves>=<a/2/2/9, 67123F30>
File<HeadbuttPokemon>=<a/2/5/2, 82A55C45>
File<BCCWilds>=<data/mushi/mushi_encount.bin, 17CA15C5>
File<MapTableFile>=<fielddata/maptable/mapname.bin, 5547F5BE>
File<MoveTutorCompat>=<fielddata/wazaoshie/waza_oshie.bin, 1DFC77A1>
File<BabyPokemon>=<poketool/personal/pms.narc, C457991B>
FieldOvlNumber=1
BattleOvlNumber=12
FossilTableOvlNumber=21
StarterPokemonOvlNumber=61
NewRoamerSubroutineTweak=hardcoded_statics/roamers/hgss_roamers
NewCatchingTutorialSubroutineTweak=hgss_catching_tutorialfix
FastestTextTweak=instant_text/hgss_instant_text
NationalDexAtStartTweak=national_dex/hgss_national_dex
MoveTutorMovesOffset=0x23AE0
MoveTutorCount=52
MoveTutorBytesCount=4
MoveTutorCompatOffset=0
MoveTutorCompatBytesCount=8
HasExtraPokemonNames=Yes
PokemonNamesTextOffset=237
TrainerNamesTextOffset=729
TrainerClassesTextOffset=730
DoublesTrainerClasses=[8, 121, 122]
EliteFourIndices=[244, 245, 246, 247, 418]
MoveDescriptionsTextOffset=749
MoveNamesTextOffset=750
AbilityNamesTextOffset=720
ItemDescriptionsTextOffset=221
ItemNamesTextOffset=222
StarterScreenTextOffset=190
IngameTradesTextOffset=200
IngameTradePersonTextOffsets=[562,596,608,634,0,0,344,463,535,47,537]
HiddenItemTableOffset=0xFA558
HiddenItemCount=231
ItemBallsScriptOffset=141
ItemBallsSkip=[58]
MapTableARM9Offset=0xF6BE0
MapTableNameIndexSize=1
MapNamesTextOffset=279
FossilTableOffset=0x130
FossilLevelScriptNumber=755
FossilLevelOffset=0x58D
NationalDexScriptOffset=229
PokedexAreaDataDungeonIndex=2
PokedexAreaDataOverworldIndex=1487
PokedexAreaDataDungeonSpecialIndex=2972
PokedexAreaDataOverworldSpecialIndex=3467
StaticPokemonSupport=1
StaticPokemon{}={Species=[104:0x108], Level=[104:0x138, 104:0x12C]} // Lugia
StaticPokemon{}={Species=[21:0xD1], Level=[21:0xF5, 21:0x101]} // Ho-oh
StaticPokemon{}={Species=[216:0x58F, 216:0x6E8, 216:0x708, 24:0x67, 24:0xB4, 24:0x314, 24:0x320, 24:0xD4], Level=[216:0x70A, 24:0x322]} // Suicune
StaticPokemon{}={Species=[14:0x2F, 14:0x3B], Level=[14:0x3D]} // Articuno
StaticPokemon{}={Species=[191:0x26B, 191:0x277], Level=[191:0x279]} // Zapdos
StaticPokemon{}={Species=[106:0x2F, 106:0x3B], Level=[106:0x3D]} // Moltres
StaticPokemon{}={Species=[11:0x2F, 11:0x3B], Level=[11:0x3D]} // Mewtwo
StaticPokemon{}={Species=[134:0xA3, 134:0xB4], Level=[134:0xB6]} // Kyogre
StaticPokemon{}={Species=[133:0xA3, 133:0xB4], Level=[133:0xB6]} // Groudon
StaticPokemon{}={Species=[135:0xDA, 135:0xEB, 135:0x62, 135:0x98], Level=[135:0xED]} // Rayquaza
StaticPokemon{}={Species=[131:0x43A, 131:0x67C, 131:0x872, 131:0x8E4, 131:0x958, 131:0x963], Level=[131:0x965]} // Dialga
StaticPokemon{}={Species=[131:0x4A2, 131:0x695, 131:0x88D, 131:0x8FA, 131:0x97F, 131:0x98A], Level=[131:0x98C]} // Palkia
StaticPokemon{}={Species=[131:0x50A, 131:0x9A4], Level=[131:0x9A6], Forme=[131:0x9AA]} // Giratina-O
StaticPokemon{}={Species=[750:0x4CC], Level=[750:0x4E3]} // Latias
StaticPokemon{}={Species=[750:0x4B7], Level=[750:0x4E3]} // Latios
StaticPokemon{}={Species=[243:0x2FD, 243:0x14B], Level=[243:0x2FF, 243:0x14D]} // Sudowoodo
StaticPokemon{}={Species=[58:0x61, 58:0x6D], Level=[58:0x6F]} // Lapras
StaticPokemon{}={Species=[938:0x3CD, 938:0x3DE], Level=[938:0x3E0]} // Red Gyarados
StaticPokemon{}={Species=[197:0x6C, 197:0x7D, 199:0x26A, 199:0x27B], Level=[197:0x7F, 199:0x27D]} // Snorlax
StaticPokemon{}={Species=[89:0xF3D, 89:0x1078, 89:0x10A5, 89:0x112C, 89:0x11B3], Level=[89:0xF3F, 89:0x107A, 89:0x10A7, 89:0x112E, 89:0x11B5]} // Koffing @ Rocket Base
StaticPokemon{}={Species=[89:0xF6A, 89:0xFC4, 89:0x101E, 89:0x104B, 89:0x1159, 89:0x1186], Level=[89:0xF6C, 89:0xFC6, 89:0x1020, 89:0x104D, 89:0x115B, 89:0x1188]} // Voltorb @ Rocket Base
StaticPokemon{}={Species=[89:0xF97, 89:0xFF1, 89:0x10D2, 89:0x10FF, 89:0x11E0], Level=[89:0xF99, 89:0xFF3, 89:0x10D4, 89:0x1101, 89:0x11E2]} // Geodude @ Rocket Base
StaticPokemon{}={Species=[90:0x784], Level=[90:0x786]} // Electrode @ Rocket Base (1)
StaticPokemon{}={Species=[90:0x7E8], Level=[90:0x7EA]} // Electrode @ Rocket Base (2)
StaticPokemon{}={Species=[90:0x84C], Level=[90:0x84E]} // Electrode @ Rocket Base (3)
StaticPokemon{}={Species=[892:0x61], Level=[892:0x63]} // Eevee
StaticPokemon{}={Species=[98:0x71], Level=[98:0x73]} // Tyrogue
StaticPokemon{}={Species=[112:0x4D1], Level=[112:0x4D3]} // Dratini
StaticPokemon{}={Species=[740:0x66F, 740:0x675, 740:0x695, 740:0x818, 740:0x8BC], Level=[740:0x86D]} // Bulbasaur
StaticPokemon{}={Species=[740:0x71D, 740:0x723, 740:0x743, 740:0x833, 740:0x8D7], Level=[740:0x86D]} // Squirtle
StaticPokemon{}={Species=[740:0x7CB, 740:0x7D1, 740:0x7F1], Level=[740:0x86D]} // Charmander
StaticPokemon{}={Species=[837:0x28F], Level=[837:0x2D1]} // Treecko
StaticPokemon{}={Species=[837:0x2A8], Level=[837:0x2D1]} // Torchic
StaticPokemon{}={Species=[837:0x2B4], Level=[837:0x2D1]} // Mudkip
StaticPokemon{}={Species=[860:0x146, 860:0x14D]]} // Primo's Mareep Egg
StaticPokemon{}={Species=[860:0x180, 860:0x187]]} // Primo's Wooper Egg
StaticPokemon{}={Species=[860:0x1BA, 860:0x1C1]]} // Primo's Slugma Egg
StaticPokemon{}={Species=[878:0x90], Level=[878:0x92]} // Secret Tentacool
StaticPokemonGameCorner{}={Species=[910:0x9A9, 910:0xA38, 910:0xAD8], Level=[910:0xABC], Text=[603:0x18]} // Abra
StaticPokemonGameCorner{}={Species=[910:0x9B5, 910:0xA5C, 910:0xAEF], Level=[910:0xABC], Text=[603:0x19]} // Ekans
StaticPokemonGameCorner{}={Species=[910:0x9CD, 910:0xA80, 910:0xB06], Level=[910:0xABC], Text=[603:0x1B]} // Sandshrew
StaticPokemonGameCorner{}={Species=[910:0x9C1], Level=[910:0xABC], Text=[603:0x1A]} // Dratini
StaticPokemonGameCorner{}={Species=[804:0x875, 804:0x8DB, 804:0x957], Level=[804:0x93B], Text=[509:0x21]} // Mr. Mime
StaticPokemonGameCorner{}={Species=[804:0x881, 804:0x8FF, 804:0x96E], Level=[804:0x93B], Text=[509:0x22]} // Eevee
StaticPokemonGameCorner{}={Species=[804:0x88D], Level=[804:0x93B], Text=[509:0x23]} // Porygon
RoamingPokemon{}={Species=[0x111F08, 0x111F4C], Level=[0x111EEE], Script=[24:0x2F]} // Raikou
RoamingPokemon{}={Species=[0x111F0C, 0x111F50], Level=[0x111EF4], Script=[24:0x4B]} // Entei
RoamingPokemon{}={Species=[0x111F10, 0x111F54], Level=[0x111EFA], Script=[776:0xD5], Gender=[776:0xD7]} // Latias
RoamingPokemon{}={Species=[0x111F14, 0x111F58], Level=[0x111F00], Script=[776:0x10B], Gender=[776:0x10D]} // Latios
StaticPokemonTrades=[6,7] // Shuckie & Kenya
StaticPokemonTradeScripts=[880,241]
StaticPokemonTradeLevelOffsets=[0x80,0xA7]
KenyaTextOffset=388
MysteryEggOffset=0x1C80E // Togepi Mystery Egg
ShopCount=40
SkipShops=[17,18,22,23,24,25,26,27,28,29,30,37,39]
MainGameShops=[0,2,5,6,7,12,14,16,19,31,33,34,36]
ShopDataPrefix=298E0402618E0402998E0402B98E0402
StaticEggPokemonOffsets=[34, 35, 36]
MainGameLegendaries=[250]
IndexToMusicPrefix=905F010084030000A0860100E803000000010203040000000001020304000000
SpecialMusicStatics=[243,244,245,249,250,383,382,384,150,381,380]
MarillCryScripts=[93:0x66, 225:0x1B8, 842:0x16C6, 849:0x1AE]
MarillTextFiles=[115, 379, 542, 545, 549]
TCMCopyingPrefix=111011157D7005001EFF2FE11EFF2FE1
Arm9ExtensionSize=220 // 92 for catching tutorial, 128 for roamers
CatchingTutorialMonTablePrefix=4EFA04B070BD
DoubleBattleFlagReturnPrefix=08B5092132F0B4FF
DoubleBattleWalkingPrefix1=16B00020F8BD1498
DoubleBattleWalkingPrefix2=9CFE16B00120F8BD
DoubleBattleTextBoxPrefix=F6F794FEF6F7AEFE
TrainerEndFileNumber=4
TrainerEndTextBoxOffset=0xD8
TMTextGameCorner{}={90=[603:14], 75=[603:15], 44=[603:16], 35=[603:17], 13=[603:18], 24=[603:19]} // Goldenrod
TMTextGameCorner{}={58=[509:23], 32=[509:24], 10=[509:25], 29=[509:26], 74=[509:27], 68=[509:28]} // Celadon
TMText{}={01=[574:6], 07=[622:4], 23=[606:5], 30=[614:5], 45=[582:7], 51=[558:4], 59=[129:3, 631:7], 89=[567:5]} // Johto Gym Leaders
TMText{}={03=[469:12], 19=[492:3], 34=[485:4], 48=[531:4], 50=[53:3], 80=[462:3, 462:4], 84=[514:3], 92=[454:4]} // Kanto Gym Leaders
TMText{}={05=[380:10, 380:12], 10=[627:2], 11=[67:7], 12=[386:2], 29=[534:1], 36=[403:6], 37=[370:2], 44=[378:8], 47=[372:2], 57=[345:17], 70=[56:4, 56:5], 83=[397:10], 85=[452:6]} // Everything else
FrontierScriptNumber=76
FrontierScriptTMOffsets{}={40=0xC5C, 31=0xC7A, 89=0xC98, 81=0xCB6, 71=0xCD4, 26=0xCF2, 30=0xD88, 53=0xDA6, 36=0xDC4, 59=0xDE2, 06=0xEA2, 73=0xEC0, 61=0xEDE, 45=0xEFC, 08=0xF1A, 04=0xF38}
MiscUITextOffset=191
FrontierTMText{}={40=380, 31=381, 89=382, 81=383, 71=384, 26=385, 30=386, 53=387, 36=388, 59=389, 06=390, 73=391, 61=392, 45=393, 08=394, 04=395}
Arm9CRC32=99A30D93
OverlayCRC32<1>=21F7A855
OverlayCRC32<12>=90D2AF3E
OverlayCRC32<21>=A6363D04
OverlayCRC32<61>=EE849CB4

[SoulSilver (U)]
Game=IPGE
Type=HGSS
Version=0
CopyText=1
CopyStaticPokemon=1
CopyRoamingPokemon=1
CopyFrom=HeartGold (U)
File<PokedexAreaData>=<a/1/3/3, D6CA84B4>
File<WildPokemon>=<a/1/3/6, BB578A64>
File<HeadbuttPokemon>=<a/2/5/2, 58826D1E>
FastestTextTweak=instant_text/hgss_instant_text
NationalDexAtStartTweak=national_dex/hgss_national_dex
NewCatchingTutorialSubroutineTweak=hgss_catching_tutorialfix
NewRoamerSubroutineTweak=hardcoded_statics/roamers/hgss_roamers
MainGameLegendaries=[249]
Arm9CRC32=8711C90D
OverlayCRC32<1>=172E4E62
OverlayCRC32<12>=7AFCE42A
OverlayCRC32<21>=A6363D04
OverlayCRC32<61>=EE849CB4

[Diamond (E)]
Game=ADAE
Type=DP
Version=13
CopyText=1
CopyStaticPokemon=1
CopyRoamingPokemon=1
CopyFrom=Diamond (U)
File<Text>=<msgdata/msg.narc, 35C0495C>
FastestTextTweak=instant_text/dp_instant_text
NewIndexToMusicTweak=musicfix/diamond_musicfix
NationalDexAtStartTweak=national_dex/dp_national_dex
Arm9CRC32=08E0337C
OverlayCRC32<6>=0AE6A693
OverlayCRC32<11>=3DCCA476
OverlayCRC32<59>=8CEA8C3C
OverlayCRC32<64>=727963E2

[Pearl (E)]
Game=APAE
Type=DP
Version=13
CopyText=1
CopyStaticPokemon=1
CopyRoamingPokemon=1
CopyFrom=Pearl (U)
File<Text>=<msgdata/msg.narc, 35C0495C>
FastestTextTweak=instant_text/dp_instant_text
NewIndexToMusicTweak=musicfix/diamond_musicfix
NationalDexAtStartTweak=national_dex/dp_national_dex
Arm9CRC32=D80458A5
OverlayCRC32<6>=F7C193D2
OverlayCRC32<11>=0DD7691D
OverlayCRC32<59>=8CEA8C3C
OverlayCRC32<64>=525F49E6

[Platinum (U Rev 1)]
Game=CPUE
Type=Plat
Version=1
CopyText=1
CopyStaticPokemon=1
CopyRoamingPokemon=1
CopyFrom=Platinum (U)
NewRoamerSubroutineTweak=hardcoded_statics/roamers/plat_roamers
FastestTextTweak=instant_text/plat_instant_text
NewIndexToMusicTweak=musicfix/plat_musicfix
NationalDexAtStartTweak=national_dex/plat_national_dex
FastDistortionWorldTweak=pt_fast_distortion_world
Arm9CRC32=4D104949
OverlayCRC32<5>=3E286491
OverlayCRC32<6>=E6C5F31B
OverlayCRC32<16>=25EBE8C1
OverlayCRC32<73>=C003DED1
OverlayCRC32<78>=091E8E97

[Platinum (E)]
Game=CPUE
Type=Plat
Version=10
CopyText=1
CopyStaticPokemon=1
CopyRoamingPokemon=1
CopyFrom=Platinum (U)
File<Scripts>=<fielddata/script/scr_seq.narc, FFAB46E4>
File<Text>=<msgdata/pl_msg.narc, B2ECC558>
NewRoamerSubroutineTweak=hardcoded_statics/roamers/plat_roamers
FastestTextTweak=instant_text/plat_instant_text
NewIndexToMusicTweak=musicfix/plat_musicfix
NationalDexAtStartTweak=national_dex/plat_national_dex
FastDistortionWorldTweak=pt_fast_distortion_world
Arm9CRC32=4D104949
OverlayCRC32<5>=3E286491
OverlayCRC32<6>=E6C5F31B
OverlayCRC32<16>=25EBE8C1
OverlayCRC32<73>=C003DED1
OverlayCRC32<78>=091E8E97

[HeartGold (E)]
Game=IPKE
Type=HGSS
Version=10
CopyText=1
CopyStaticPokemon=1
CopyRoamingPokemon=1
CopyFrom=HeartGold (U)
NewRoamerSubroutineTweak=hardcoded_statics/roamers/hgss_roamers
NewCatchingTutorialSubroutineTweak=hgss_catching_tutorialfix
FastestTextTweak=instant_text/hgss_instant_text
NationalDexAtStartTweak=national_dex/hgss_national_dex
Arm9CRC32=99A30D93
OverlayCRC32<1>=21F7A855
OverlayCRC32<12>=90D2AF3E
OverlayCRC32<21>=A6363D04
OverlayCRC32<61>=EE849CB4

[SoulSilver (E)]
Game=IPGE
Type=HGSS
Version=10
CopyText=1
CopyStaticPokemon=1
CopyRoamingPokemon=1
CopyFrom=SoulSilver (U)
FastestTextTweak=instant_text/hgss_instant_text
NationalDexAtStartTweak=national_dex/hgss_national_dex
NewCatchingTutorialSubroutineTweak=hgss_catching_tutorialfix
NewRoamerSubroutineTweak=hardcoded_statics/roamers/hgss_roamers
Arm9CRC32=8711C90D
OverlayCRC32<1>=172E4E62
OverlayCRC32<12>=7AFCE42A
OverlayCRC32<21>=A6363D04
OverlayCRC32<61>=EE849CB4

[Pearl (J)]
Game=APAJ
Type=DP
Version=0
CopyStaticPokemon=1
CopyFrom=Pearl (U)
File<BattleSkillSubSeq>=<battle/skill/sub_seq.narc, E4519D76>
File<Events>=<fielddata/eventdata/zone_event.narc, D7363FA7>
File<Scripts>=<fielddata/script/scr_seq.narc, E814B448>
File<Text>=<msgdata/msg.narc, F39D072F>
StarterPokemonOffset=0x30
HiddenItemTableOffset=0xF4C14
HasExtraPokemonNames=No
PokemonNamesTextOffset=356
TrainerNamesTextOffset=550
TrainerClassesTextOffset=551
MoveDescriptionsTextOffset=574
MoveNamesTextOffset=575
AbilityNamesTextOffset=544
ItemDescriptionsTextOffset=340
ItemNamesTextOffset=341
StarterScreenTextOffset=318
PokedexSpeciesTextOffset=607
StarterLocationTextOffset=269
IngameTradesTextOffset=324
IngameTradePersonTextOffsets=[66,88,170,571]
MapTableARM9Offset=0xF0C2C
MapNamesTextOffset=374
CatchingTutorialOpponentMonOffset=0x4AB34
FossilTableOffset=0xF6334
ShopDataPrefix=F11A040249BD030219BD0302FDBC0302
Arm9CRC32=B1A9B403
OverlayCRC32<6>=AD2BA4AF
OverlayCRC32<11>=4F5D2535
OverlayCRC32<59>=52AAB459
OverlayCRC32<64>=6CC01D0F

[Diamond (J)]
Game=ADAJ
Type=DP
Version=0
CopyStaticPokemon=1
CopyFrom=Diamond (U)
File<BattleSkillSubSeq>=<battle/skill/sub_seq.narc, E4519D76>
File<Events>=<fielddata/eventdata/zone_event.narc, D7363FA7>
File<Scripts>=<fielddata/script/scr_seq.narc, E814B448>
File<Text>=<msgdata/msg.narc, F39D072F>
StarterPokemonOffset=0x30
HiddenItemTableOffset=0xF4C10
HasExtraPokemonNames=No
PokemonNamesTextOffset=356
TrainerNamesTextOffset=550
TrainerClassesTextOffset=551
MoveDescriptionsTextOffset=574
MoveNamesTextOffset=575
AbilityNamesTextOffset=544
ItemDescriptionsTextOffset=340
ItemNamesTextOffset=341
StarterScreenTextOffset=318
PokedexSpeciesTextOffset=607
StarterLocationTextOffset=269
IngameTradesTextOffset=324
IngameTradePersonTextOffsets=[66,88,170,571]
MapTableARM9Offset=0xF0C28
MapNamesTextOffset=374
CatchingTutorialOpponentMonOffset=0x4AB34
FossilTableOffset=0xF6330
ShopDataPrefix=F11A040249BD030219BD0302FDBC0302
Arm9CRC32=2624AED0
OverlayCRC32<6>=90564DAF
OverlayCRC32<11>=87D3D888
OverlayCRC32<59>=189D13CA
OverlayCRC32<64>=6F2480C9

[Pearl (J Rev 5)]
Game=APAJ
Type=DP
Version=5
CopyText=1
CopyStaticPokemon=1
CopyFrom=Pearl (J)
Arm9CRC32=B1A9B403
OverlayCRC32<6>=AD2BA4AF
OverlayCRC32<11>=4F5D2535
OverlayCRC32<59>=52AAB459
OverlayCRC32<64>=6CC01D0F

[Pearl (G)]
Game=APAD
Type=DP
Version=5
CopyStaticPokemon=1
CopyFrom=Pearl (U)
File<BattleSkillSubSeq>=<battle/skill/sub_seq.narc, A2B73706>
File<Scripts>=<fielddata/script/scr_seq_release.narc, 81E4C75B>
File<Text>=<msgdata/msg.narc, 4A2EDAAE>
File<PokemonGraphics>=<poketool/pokegra/pokegra.narc, 99FC6C2E>
File<InGameTrades>=<resource/ger/pokemon_trade/fld_trade.narc, EA64A7F9>
NationalDexAtStartTweak=national_dex/dp_national_dex
HiddenItemTableOffset=0xF2DC4
MapTableARM9Offset=0xEEDCC
CatchingTutorialOpponentMonOffset=0x479D0
FossilTableOffset=0xF4520
DoubleBattleFlagReturnPrefix=08B5092131F02AFB
DoubleBattleWalkingPrefix2=22FB16B00120F8BD
DoubleBattleTextBoxPrefix=F7F706FEF7F720FE
ShopDataPrefix=********************************
Arm9CRC32=672E6E4B
OverlayCRC32<6>=6E45EC08
OverlayCRC32<11>=A1F171CB
OverlayCRC32<59>=96DC349C
OverlayCRC32<64>=CD34E846

[Diamond (G)]
Game=ADAD
Type=DP
Version=5
CopyStaticPokemon=1
CopyFrom=Diamond (U)
File<BattleSkillSubSeq>=<battle/skill/sub_seq.narc, A2B73706>
File<Scripts>=<fielddata/script/scr_seq_release.narc, 81E4C75B>
File<Text>=<msgdata/msg.narc, 4A2EDAAE>
File<PokemonGraphics>=<poketool/pokegra/pokegra.narc, 99FC6C2E>
File<InGameTrades>=<resource/ger/pokemon_trade/fld_trade.narc, EA64A7F9>
NationalDexAtStartTweak=national_dex/dp_national_dex
HiddenItemTableOffset=0xF2DC4
MapTableARM9Offset=0xEEDCC
CatchingTutorialOpponentMonOffset=0x479D0
FossilTableOffset=0xF4520
DoubleBattleFlagReturnPrefix=08B5092131F02AFB
DoubleBattleWalkingPrefix2=22FB16B00120F8BD
DoubleBattleTextBoxPrefix=F7F706FEF7F720FE
ShopDataPrefix=********************************
Arm9CRC32=6534A1F1
OverlayCRC32<6>=93FB792F
OverlayCRC32<11>=0C8681F0
OverlayCRC32<59>=96DC349C
OverlayCRC32<64>=65244084

[Pearl (S)]
Game=APAS
Type=DP
Version=5
CopyStaticPokemon=1
CopyFrom=Pearl (U)
File<BattleSkillSubSeq>=<battle/skill/sub_seq.narc, A2B73706>
File<Scripts>=<fielddata/script/scr_seq_release.narc, 8FADF14D>
File<Text>=<msgdata/msg.narc, 011962EF>
File<PokemonGraphics>=<poketool/pokegra/pokegra.narc, 99FC6C2E>
File<InGameTrades>=<resource/spa/pokemon_trade/fld_trade.narc, B0A25B9D>
NationalDexAtStartTweak=national_dex/dp_national_dex
HiddenItemTableOffset=0xF2E00
MapTableARM9Offset=0xEEE08
CatchingTutorialOpponentMonOffset=0x479D0
FossilTableOffset=0xF455C
DoubleBattleFlagReturnPrefix=08B5092131F02AFB
DoubleBattleWalkingPrefix2=22FB16B00120F8BD
DoubleBattleTextBoxPrefix=F7F706FEF7F720FE
ShopDataPrefix=********************************
Arm9CRC32=F84C375A
OverlayCRC32<6>=E2BF78A0
OverlayCRC32<11>=22C6C9CA
OverlayCRC32<59>=B77DAB96
OverlayCRC32<64>=A92907D8

[Diamond (S)]
Game=ADAS
Type=DP
Version=5
CopyStaticPokemon=1
CopyFrom=Diamond (U)
File<BattleSkillSubSeq>=<battle/skill/sub_seq.narc, A2B73706>
File<Scripts>=<fielddata/script/scr_seq_release.narc, 8FADF14D>
File<Text>=<msgdata/msg.narc, 011962EF>
File<PokemonGraphics>=<poketool/pokegra/pokegra.narc, 99FC6C2E>
File<InGameTrades>=<resource/spa/pokemon_trade/fld_trade.narc, B0A25B9D>
NationalDexAtStartTweak=national_dex/dp_national_dex
HiddenItemTableOffset=0xF2E00
MapTableARM9Offset=0xEEE08
CatchingTutorialOpponentMonOffset=0x479D0
FossilTableOffset=0xF455C
DoubleBattleFlagReturnPrefix=08B5092131F02AFB
DoubleBattleWalkingPrefix2=22FB16B00120F8BD
DoubleBattleTextBoxPrefix=F7F706FEF7F720FE
ShopDataPrefix=********************************
Arm9CRC32=6A024910
OverlayCRC32<6>=34FF651E
OverlayCRC32<11>=17D68A91
OverlayCRC32<59>=B77DAB96
OverlayCRC32<64>=5CE1EE3C

[Pearl (I)]
Game=APAI
Type=DP
Version=5
CopyStaticPokemon=1
CopyFrom=Pearl (U)
File<BattleSkillSubSeq>=<battle/skill/sub_seq.narc, A2B73706>
File<Scripts>=<fielddata/script/scr_seq_release.narc, 3596DF31>
File<Text>=<msgdata/msg.narc, 9809787C>
File<PokemonGraphics>=<poketool/pokegra/pokegra.narc, 99FC6C2E>
File<InGameTrades>=<resource/ita/pokemon_trade/fld_trade.narc, 0168D04C>
NationalDexAtStartTweak=national_dex/dp_national_dex
HiddenItemTableOffset=0xF2D68
MapTableARM9Offset=0xEED70
CatchingTutorialOpponentMonOffset=0x479D0
FossilTableOffset=0xF44C4
DoubleBattleFlagReturnPrefix=08B5092131F01AFB
DoubleBattleWalkingPrefix2=22FB16B00120F8BD
DoubleBattleTextBoxPrefix=F7F706FEF7F720FE
ShopDataPrefix=********************************
Arm9CRC32=6C854C5F
OverlayCRC32<6>=45958341
OverlayCRC32<11>=2E88B408
OverlayCRC32<59>=65BC0057
OverlayCRC32<64>=AEC1D5A4

[Diamond (I)]
Game=ADAI
Type=DP
Version=5
CopyStaticPokemon=1
CopyFrom=Diamond (U)
HiddenItemTableOffset=0xF2D68
File<BattleSkillSubSeq>=<battle/skill/sub_seq.narc, A2B73706>
File<Scripts>=<fielddata/script/scr_seq_release.narc, 3596DF31>
File<Text>=<msgdata/msg.narc, 9809787C>
File<PokemonGraphics>=<poketool/pokegra/pokegra.narc, 99FC6C2E>
File<InGameTrades>=<resource/ita/pokemon_trade/fld_trade.narc, 0168D04C>
NationalDexAtStartTweak=national_dex/dp_national_dex
MapTableARM9Offset=0xEED70
CatchingTutorialOpponentMonOffset=0x479D0
FossilTableOffset=0xF44C4
DoubleBattleFlagReturnPrefix=08B5092131F01AFB
DoubleBattleWalkingPrefix2=22FB16B00120F8BD
DoubleBattleTextBoxPrefix=F7F706FEF7F720FE
ShopDataPrefix=********************************
Arm9CRC32=569504C4
OverlayCRC32<6>=DEAC5AEB
OverlayCRC32<11>=8C0E7676
OverlayCRC32<59>=65BC0057
OverlayCRC32<64>=5B093C40

[Pearl (F)]
Game=APAF
Type=DP
Version=5
CopyStaticPokemon=1
CopyFrom=Pearl (U)
File<BattleSkillSubSeq>=<battle/skill/sub_seq.narc, A2B73706>
File<Scripts>=<fielddata/script/scr_seq_release.narc, 8FADF14D>
File<Text>=<msgdata/msg.narc, 2A61CC12>
File<PokemonGraphics>=<poketool/pokegra/pokegra.narc, 99FC6C2E>
File<InGameTrades>=<resource/fra/pokemon_trade/fld_trade.narc, D6CAB8E0>
NationalDexAtStartTweak=national_dex/dp_national_dex
HiddenItemTableOffset=0xF2DF4
MapTableARM9Offset=0xEEDFC
CatchingTutorialOpponentMonOffset=0x479D0
FossilTableOffset=0xF4550
DoubleBattleFlagReturnPrefix=08B5092131F02AFB
DoubleBattleWalkingPrefix2=22FB16B00120F8BD
DoubleBattleTextBoxPrefix=F7F706FEF7F720FE
ShopDataPrefix=********************************
Arm9CRC32=AD992311
OverlayCRC32<6>=8934EF2F
OverlayCRC32<11>=53F5A4B0
OverlayCRC32<59>=95817DEA
OverlayCRC32<64>=143FB16B

[Diamond (F)]
Game=ADAF
Type=DP
Version=5
CopyStaticPokemon=1
CopyFrom=Diamond (U)
File<BattleSkillSubSeq>=<battle/skill/sub_seq.narc, A2B73706>
File<Scripts>=<fielddata/script/scr_seq_release.narc, 8FADF14D>
File<Text>=<msgdata/msg.narc, 2A61CC12>
File<PokemonGraphics>=<poketool/pokegra/pokegra.narc, 99FC6C2E>
File<InGameTrades>=<resource/fra/pokemon_trade/fld_trade.narc, D6CAB8E0>
NationalDexAtStartTweak=national_dex/dp_national_dex
HiddenItemTableOffset=0xF2DF4
MapTableARM9Offset=0xEEDFC
CatchingTutorialOpponentMonOffset=0x479D0
FossilTableOffset=0xF4550
DoubleBattleFlagReturnPrefix=08B5092131F02AFB
DoubleBattleWalkingPrefix2=22FB16B00120F8BD
DoubleBattleTextBoxPrefix=F7F706FEF7F720FE
ShopDataPrefix=********************************
Arm9CRC32=B8EE141C
OverlayCRC32<6>=A5AD136A
OverlayCRC32<11>=A0CA9F36
OverlayCRC32<59>=95817DEA
OverlayCRC32<64>=34199B6F

[Pearl (K)]
Game=APAK
Type=DP
Version=0
CopyStaticPokemon=1
CopyFrom=Pearl (U)
File<BattleSkillSubSeq>=<battle/skill/sub_seq.narc, FE6A1BDA>
File<Scripts>=<fielddata/script/scr_seq_release.narc, C39FCE21>
File<Text>=<msgdata/msg.narc, 1C515BDD>
File<PokemonGraphics>=<poketool/pokegra/pokegra.narc, 99FC6C2E>
File<InGameTrades>=<resource/kor/pokemon_trade/fld_trade.narc, ABD1F5CB>
NationalDexAtStartTweak=national_dex/dp_national_dex
HiddenItemTableOffset=0xEE400
HasExtraPokemonNames=No
PokemonNamesTextOffset=357
TrainerNamesTextOffset=552
TrainerClassesTextOffset=553
MoveDescriptionsTextOffset=576
MoveNamesTextOffset=577
AbilityNamesTextOffset=546
ItemDescriptionsTextOffset=341
ItemNamesTextOffset=342
StarterScreenTextOffset=319
PokedexSpeciesTextOffset=609
StarterLocationTextOffset=269
IngameTradesTextOffset=325
IngameTradePersonTextOffsets=[66,88,170,573]
MapTableARM9Offset=0xEA408
MapNamesTextOffset=376
CatchingTutorialOpponentMonOffset=0x47E2C
FossilTableOffset=0xEFB5C
DoubleBattleFlagReturnPrefix=08B5092131F04AFB
DoubleBattleWalkingPrefix2=02FB16B00120F8BD
DoubleBattleTextBoxPrefix=F7F708FEF7F722FE
ShopDataPrefix=********************************
Arm9CRC32=E317C09B
OverlayCRC32<6>=37ECE0C0
OverlayCRC32<11>=A41BD6FC
OverlayCRC32<59>=E3B4A7FF
OverlayCRC32<64>=4431AA3B

[Diamond (K)]
Game=ADAK
Type=DP
Version=0
CopyStaticPokemon=1
CopyFrom=Diamond (U)
File<BattleSkillSubSeq>=<battle/skill/sub_seq.narc, FE6A1BDA>
File<Scripts>=<fielddata/script/scr_seq_release.narc, C39FCE21>
File<Text>=<msgdata/msg.narc, 1C515BDD>
File<PokemonGraphics>=<poketool/pokegra/pokegra.narc, 99FC6C2E>
File<InGameTrades>=<resource/kor/pokemon_trade/fld_trade.narc, ABD1F5CB>
NationalDexAtStartTweak=national_dex/dp_national_dex
HiddenItemTableOffset=0xEE400
HasExtraPokemonNames=No
PokemonNamesTextOffset=357
TrainerNamesTextOffset=552
TrainerClassesTextOffset=553
MoveDescriptionsTextOffset=576
MoveNamesTextOffset=577
AbilityNamesTextOffset=546
ItemDescriptionsTextOffset=341
ItemNamesTextOffset=342
StarterScreenTextOffset=319
PokedexSpeciesTextOffset=609
StarterLocationTextOffset=269
IngameTradesTextOffset=325
IngameTradePersonTextOffsets=[66,88,170,573]
MapTableARM9Offset=0xEA408
MapNamesTextOffset=376
CatchingTutorialOpponentMonOffset=0x47E2C
FossilTableOffset=0xEFB5C
DoubleBattleFlagReturnPrefix=08B5092131F04AFB
DoubleBattleWalkingPrefix2=02FB16B00120F8BD
DoubleBattleTextBoxPrefix=F7F708FEF7F722FE
ShopDataPrefix=********************************
Arm9CRC32=E50BF4B5
OverlayCRC32<6>=4681ECC3
OverlayCRC32<11>=3D2D9752
OverlayCRC32<59>=E3B4A7FF
OverlayCRC32<64>=6417803F

[Platinum (J)]
Game=CPUJ
Type=Plat
Version=0
CopyStaticPokemon=1
CopyFrom=Platinum (U)
File<InGameTrades>=<fielddata/pokemon_trade/fld_trade.narc, 08464A7E>
File<Scripts>=<fielddata/script/scr_seq.narc, F121799E>
File<Text>=<msgdata/pl_msg.narc, A131FC08>
File<PokemonGraphics>=<poketool/pokegra/pl_pokegra.narc, 6C318437>
HiddenItemTableOffset=0xE9A4C
MoveTutorMovesOffset=0x2FD54
MoveTutorCompatOffset=0x2FF1C
StarterPokemonOffset=0x1BAC
HasExtraPokemonNames=No
PokemonNamesTextOffset=408
TrainerNamesTextOffset=611
TrainerClassesTextOffset=612
MoveDescriptionsTextOffset=635
MoveNamesTextOffset=636
AbilityNamesTextOffset=604
ItemDescriptionsTextOffset=389
ItemNamesTextOffset=390
StarterScreenTextOffset=359
PokedexSpeciesTextOffset=698
StarterLocationTextOffset=460
IngameTradesTextOffset=369
IngameTradePersonTextOffsets=[73,96,179,632]
MapTableARM9Offset=0xE56F0
MapNamesTextOffset=427
CatchingTutorialOpponentMonOffset=0x51980
FossilTableOffset=0xEB68C
DoubleBattleFlagReturnPrefix=08B5092139F054FE
DoubleBattleWalkingPrefix2=3CF816B00120F8BD
DoubleBattleTextBoxPrefix=F6F7FEFDF6F718FE
ShopDataPrefix=********************************
FastDistortionWorldTweak=pt_fast_distortion_world
Arm9CRC32=9370B1BD
OverlayCRC32<5>=D045CE6A
OverlayCRC32<6>=D5C60661
OverlayCRC32<16>=13CDEC92
OverlayCRC32<73>=8FB18796
OverlayCRC32<78>=ABCE5F9F

[Platinum (G)]
Game=CPUD
Type=Plat
Version=0
CopyStaticPokemon=1
CopyFrom=Platinum (U)
File<Scripts>=<fielddata/script/scr_seq.narc, 01DF1412>
File<Text>=<msgdata/pl_msg.narc, 96C8829B>
File<InGameTrades>=<resource/ger/pokemon_trade/fld_trade.narc, EA64A7F9>
NationalDexAtStartTweak=national_dex/plat_national_dex
HiddenItemTableOffset=0xEA3D0
MoveTutorMovesOffset=0x2FF80
MoveTutorCompatOffset=0x30148
MapTableARM9Offset=0xE6074
CatchingTutorialOpponentMonOffset=0x52144
FossilTableOffset=0xEC054
DoubleBattleFlagReturnPrefix=08B5092139F0C4FF
ShopDataPrefix=91800402C98004020581040225810402
FastDistortionWorldTweak=pt_fast_distortion_world
Arm9CRC32=14AC281F
OverlayCRC32<5>=9ABB1B3D
OverlayCRC32<6>=531E0103
OverlayCRC32<16>=81FBB5A9
OverlayCRC32<73>=78321B58
OverlayCRC32<78>=BB90F646

[Platinum (F)]
Game=CPUF
Type=Plat
Version=0
CopyStaticPokemon=1
CopyFrom=Platinum (U)
File<Scripts>=<fielddata/script/scr_seq.narc, D81D0712>
File<Text>=<msgdata/pl_msg.narc, 541B73AE>
File<InGameTrades>=<resource/fra/pokemon_trade/fld_trade.narc, D6CAB8E0>
NationalDexAtStartTweak=national_dex/plat_national_dex
HiddenItemTableOffset=0xEA400
MoveTutorMovesOffset=0x2FF6C
MoveTutorCompatOffset=0x30134
MapTableARM9Offset=0xE60A4
CatchingTutorialOpponentMonOffset=0x52144
FossilTableOffset=0xEC084
DoubleBattleFlagReturnPrefix=08B5092139F0C4FF
ShopDataPrefix=91800402C98004020581040225810402
FastDistortionWorldTweak=pt_fast_distortion_world
Arm9CRC32=C0B29D1E
OverlayCRC32<5>=7157FFE7
OverlayCRC32<6>=0FCDB778
OverlayCRC32<16>=495B8746
OverlayCRC32<73>=CD2E3918
OverlayCRC32<78>=5181F86B

[Platinum (S)]
Game=CPUS
Type=Plat
Version=0
CopyStaticPokemon=1
CopyFrom=Platinum (U)
File<Scripts>=<fielddata/script/scr_seq.narc, D81D0712>
File<Text>=<msgdata/pl_msg.narc, 8DE5119D>
File<InGameTrades>=<resource/spa/pokemon_trade/fld_trade.narc, B0A25B9D>
NationalDexAtStartTweak=national_dex/plat_national_dex
HiddenItemTableOffset=0xEA40C
MoveTutorMovesOffset=0x2FF6C
MoveTutorCompatOffset=0x30134
MapTableARM9Offset=0xE60B0
CatchingTutorialOpponentMonOffset=0x52144
FossilTableOffset=0xEC090
DoubleBattleFlagReturnPrefix=08B5092139F0C4FF
ShopDataPrefix=91800402C98004020581040225810402
FastDistortionWorldTweak=pt_fast_distortion_world
Arm9CRC32=D3F8273F
OverlayCRC32<5>=C4A31B48
OverlayCRC32<6>=0E93E266
OverlayCRC32<16>=1BBB41F1
OverlayCRC32<73>=0F84AAEE
OverlayCRC32<78>=07F2C593

[Platinum (I)]
Game=CPUI
Type=Plat
Version=0
CopyStaticPokemon=1
CopyFrom=Platinum (U)
File<Scripts>=<fielddata/script/scr_seq.narc, D12BBD3C>
File<Text>=<msgdata/pl_msg.narc, 641AA93B>
File<InGameTrades>=<resource/ita/pokemon_trade/fld_trade.narc, 0168D04C>
NationalDexAtStartTweak=national_dex/plat_national_dex
HiddenItemTableOffset=0xEA394
MoveTutorMovesOffset=0x2FF74
MoveTutorCompatOffset=0x3013C
MapTableARM9Offset=0xE6038
CatchingTutorialOpponentMonOffset=0x52144
FossilTableOffset=0xEC018
DoubleBattleFlagReturnPrefix=08B5092139F0C4FF
ShopDataPrefix=91800402C98004020581040225810402
FastDistortionWorldTweak=pt_fast_distortion_world
Arm9CRC32=EDD15660
OverlayCRC32<5>=DAD8DD1C
OverlayCRC32<6>=5664CD24
OverlayCRC32<16>=3528E1D6
OverlayCRC32<73>=03562E3A
OverlayCRC32<78>=A99B6322

[Platinum (K)]
Game=CPUK
Type=Plat
Version=0
CopyStaticPokemon=1
CopyFrom=Platinum (U)
File<Scripts>=<fielddata/script/scr_seq.narc, CC45B7B9>
File<Text>=<msgdata/pl_msg.narc, 158E7FDB>
File<InGameTrades>=<resource/kor/pokemon_trade/fld_trade.narc, ABD1F5CB>
NationalDexAtStartTweak=national_dex/plat_national_dex
HiddenItemTableOffset=0xEAE00
MoveTutorMovesOffset=0x2FF5C
MoveTutorCompatOffset=0x30124
HasExtraPokemonNames=No
PokemonNamesTextOffset=408
TrainerNamesTextOffset=612
TrainerClassesTextOffset=613
MoveDescriptionsTextOffset=636
MoveNamesTextOffset=637
AbilityNamesTextOffset=605
ItemDescriptionsTextOffset=389
ItemNamesTextOffset=390
StarterScreenTextOffset=359
PokedexSpeciesTextOffset=701
StarterLocationTextOffset=461
IngameTradesTextOffset=369
IngameTradePersonTextOffsets=[73,96,179,633]
MapTableARM9Offset=0xE6AA4
MapNamesTextOffset=428
CatchingTutorialOpponentMonOffset=0x52594
FossilTableOffset=0xECA84
DoubleBattleFlagReturnPrefix=08B5092139F0C4FF
DoubleBattleWalkingPrefix2=C0FE16B00120F8BD
DoubleBattleTextBoxPrefix=F6F790FCF6F7AAFC
ShopDataPrefix=********************************
FastDistortionWorldTweak=pt_fast_distortion_world
Arm9CRC32=BAE2AD4B
OverlayCRC32<5>=CADD3A64
OverlayCRC32<6>=CFF5136D
OverlayCRC32<16>=14CC7DEA
OverlayCRC32<73>=D891EA37
OverlayCRC32<78>=923ACAED

[HeartGold (J)]
Game=IPKJ
Type=HGSS
Version=0
CopyFrom=HeartGold (U)
File<Scripts>=<a/0/1/2, 98F75402>
File<Text>=<a/0/2/7, 7026E193>
File<Events>=<a/0/3/2, BB5D8229>
File<InGameTrades>=<a/1/1/2, 76DCB3F5>
NationalDexAtStartTweak=national_dex/hgss_national_dex
HiddenItemTableOffset=0xF9D08
MoveTutorMovesOffset=0x23954
HasExtraPokemonNames=No
PokemonNamesTextOffset=232
TrainerNamesTextOffset=719
TrainerClassesTextOffset=720
MoveDescriptionsTextOffset=738
MoveNamesTextOffset=739
AbilityNamesTextOffset=711
ItemDescriptionsTextOffset=218
ItemNamesTextOffset=219
StarterScreenTextOffset=188
IngameTradesTextOffset=198
IngameTradePersonTextOffsets=[554,588,599,625,0,0,337,456,527,45,529]
MapTableARM9Offset=0xF6390
MapNamesTextOffset=272
CatchingTutorialPlayerMonOffset=0x51610
CatchingTutorialPlayerLevelOffset=0x51612
CatchingTutorialOpponentMonOffset=0x51632
FossilLevelScriptNumber=753
StaticPokemonSupport=1
StaticPokemon{}={Species=[104:0x108], Level=[104:0x138, 104:0x12C]} // Lugia
StaticPokemon{}={Species=[21:0xD1], Level=[21:0xF5, 21:0x101]} // Ho-oh
StaticPokemon{}={Species=[216:0x58F, 216:0x6E8, 216:0x708, 24:0x67, 24:0xB4, 24:0x314, 24:0x320, 24:0xD4], Level=[216:0x70A, 24:0x322]} // Suicune
StaticPokemon{}={Species=[14:0x2F, 14:0x3B], Level=[14:0x3D]} // Articuno
StaticPokemon{}={Species=[191:0x26B, 191:0x277], Level=[191:0x279]} // Zapdos
StaticPokemon{}={Species=[106:0x2F, 106:0x3B], Level=[106:0x3D]} // Moltres
StaticPokemon{}={Species=[11:0x2F, 11:0x3B], Level=[11:0x3D]} // Mewtwo
StaticPokemon{}={Species=[134:0xA3, 134:0xB4], Level=[134:0xB6]} // Kyogre
StaticPokemon{}={Species=[133:0xA3, 133:0xB4], Level=[133:0xB6]} // Groudon
StaticPokemon{}={Species=[135:0xDA, 135:0xEB, 135:0x62, 135:0x98], Level=[135:0xED]} // Rayquaza
StaticPokemon{}={Species=[131:0x43A, 131:0x67C, 131:0x872, 131:0x8E4, 131:0x958, 131:0x963], Level=[131:0x965]} // Dialga
StaticPokemon{}={Species=[131:0x4A2, 131:0x695, 131:0x88D, 131:0x8FA, 131:0x97F, 131:0x98A], Level=[131:0x98C]} // Palkia
StaticPokemon{}={Species=[131:0x50A, 131:0x9A4], Level=[131:0x9A6], Forme=[131:0x9AA]} // Giratina-O
StaticPokemon{}={Species=[748:0x4CC], Level=[748:0x4E3]} // Latias
StaticPokemon{}={Species=[748:0x4B7], Level=[748:0x4E3]} // Latios
StaticPokemon{}={Species=[243:0x310, 243:0x14B], Level=[243:0x312, 243:0x14D]} // Sudowoodo
StaticPokemon{}={Species=[58:0x61, 58:0x6D], Level=[58:0x6F]} // Lapras
StaticPokemon{}={Species=[934:0x3CD, 934:0x3DE], Level=[934:0x3E0]} // Red Gyarados
StaticPokemon{}={Species=[197:0x6C, 197:0x7D, 199:0x26A, 199:0x27B], Level=[197:0x7F, 199:0x27D]} // Snorlax
StaticPokemon{}={Species=[89:0xF3D, 89:0x1078, 89:0x10A5, 89:0x112C, 89:0x11B3], Level=[89:0xF3F, 89:0x107A, 89:0x10A7, 89:0x112E, 89:0x11B5]} // Koffing @ Rocket Base
StaticPokemon{}={Species=[89:0xF6A, 89:0xFC4, 89:0x101E, 89:0x104B, 89:0x1159, 89:0x1186], Level=[89:0xF6C, 89:0xFC6, 89:0x1020, 89:0x104D, 89:0x115B, 89:0x1188]} // Voltorb @ Rocket Base
StaticPokemon{}={Species=[89:0xF97, 89:0xFF1, 89:0x10D2, 89:0x10FF, 89:0x11E0], Level=[89:0xF99, 89:0xFF3, 89:0x10D4, 89:0x1101, 89:0x11E2]} // Geodude @ Rocket Base
StaticPokemon{}={Species=[90:0x770], Level=[90:0x772]} // Electrode @ Rocket Base (1)
StaticPokemon{}={Species=[90:0x7D4], Level=[90:0x7D6]} // Electrode @ Rocket Base (2)
StaticPokemon{}={Species=[90:0x838], Level=[90:0x83A]} // Electrode @ Rocket Base (3)
StaticPokemon{}={Species=[889:0x61], Level=[889:0x63]} // Eevee
StaticPokemon{}={Species=[98:0x71], Level=[98:0x73]} // Tyrogue
StaticPokemon{}={Species=[112:0x4D1], Level=[112:0x4D3]} // Dratini
StaticPokemon{}={Species=[738:0x66F, 738:0x675, 738:0x695, 738:0x818, 738:0x8BC], Level=[738:0x86D]} // Bulbasaur
StaticPokemon{}={Species=[738:0x71D, 738:0x723, 738:0x743, 738:0x833, 738:0x8D7], Level=[738:0x86D]} // Squirtle
StaticPokemon{}={Species=[738:0x7CB, 738:0x7D1, 738:0x7F1], Level=[738:0x86D]} // Charmander
StaticPokemon{}={Species=[834:0x272], Level=[834:0x2B4]} // Treecko
StaticPokemon{}={Species=[834:0x28B], Level=[834:0x2B4]} // Torchic
StaticPokemon{}={Species=[834:0x297], Level=[834:0x2B4]} // Mudkip
StaticPokemon{}={Species=[857:0x146, 857:0x14D]]} // Primo's Mareep Egg
StaticPokemon{}={Species=[857:0x180, 857:0x187]]} // Primo's Wooper Egg
StaticPokemon{}={Species=[857:0x1BA, 857:0x1C1]]} // Primo's Slugma Egg
StaticPokemon{}={Species=[875:0x90], Level=[875:0x92]} // Secret Tentacool
StaticPokemonGameCorner{}={Species=[903:0xF8B, 903:0x101A, 903:0x10BA], Level=[903:0x109E], Text=[591:0x31]} // Abra
StaticPokemonGameCorner{}={Species=[903:0xF97, 903:0x103E, 903:0x10D1], Level=[903:0x109E], Text=[591:0x32]} // Ekans
StaticPokemonGameCorner{}={Species=[903:0xFAF, 903:0x1062, 903:0x10E8], Level=[903:0x109E], Text=[591:0x34]} // Sandshrew
StaticPokemonGameCorner{}={Species=[903:0xFA3], Level=[903:0x109E], Text=[591:0x33]} // Dratini
StaticPokemonGameCorner{}={Species=[802:0x875, 802:0x8DB, 802:0x957], Level=[802:0x93B], Text=[502:0x21]} // Mr. Mime
StaticPokemonGameCorner{}={Species=[802:0x881, 802:0x8FF, 802:0x96E], Level=[802:0x93B], Text=[502:0x22]} // Eevee
StaticPokemonGameCorner{}={Species=[802:0x88D], Level=[802:0x93B], Text=[502:0x23]} // Porygon
StaticPokemonTrades=[6,7] // Shuckie & Kenya
StaticPokemonTradeScripts=[877,241]
StaticPokemonTradeLevelOffsets=[0x80,0xA7]
KenyaTextOffset=381
MysteryEggOffset=0x1C692 // Togepi Mystery Egg
StaticEggPokemonOffsets=[34, 35, 36]
MarillCryScripts=[93:0x66, 225:0x1B8, 839:0x16C6, 846:0x1AE]
MarillTextFiles=[]
DoubleBattleFlagReturnPrefix=08B5092132F058FE
DoubleBattleWalkingPrefix2=14F816B00120F8BD
DoubleBattleTextBoxPrefix=F6F7F0FFF7F70AF8
ShopDataPrefix=********************************
TMTextGameCorner{}={90=[591:39], 75=[591:40], 44=[591:41], 35=[591:42], 13=[591:43], 24=[591:44]} // Goldenrod
TMTextGameCorner{}={58=[502:23], 32=[502:24], 10=[502:25], 29=[502:26], 74=[502:27], 68=[502:28]} // Celadon
Arm9CRC32=BA386530
OverlayCRC32<1>=513BF822
OverlayCRC32<12>=C95025DF
OverlayCRC32<21>=7874DA2E
OverlayCRC32<61>=F45FB204

[SoulSilver (J)]
Game=IPGJ
Type=HGSS
Version=0
CopyStaticPokemon=1
CopyFrom=HeartGold (J)
File<PokedexAreaData>=<a/1/3/3, D6CA84B4>
File<WildPokemon>=<a/1/3/6, BB578A64>
File<HeadbuttPokemon>=<a/2/5/2, 58826D1E>
NationalDexAtStartTweak=national_dex/hgss_national_dex
Arm9CRC32=C537A4E3
OverlayCRC32<1>=00255396
OverlayCRC32<12>=AA71062F
OverlayCRC32<21>=7874DA2E
OverlayCRC32<61>=F45FB204

[HeartGold (K)]
Game=IPKK
Type=HGSS
Version=0
IgnoreGameCornerStatics=1
CopyStaticPokemon=1
CopyFrom=HeartGold (U)
File<Text>=<a/0/2/7, 6C096A9F>
File<InGameTrades>=<a/1/1/2, 649D61C3>
NationalDexAtStartTweak=national_dex/hgss_national_dex
HasExtraPokemonNames=No
HiddenItemTableOffset=0xFAC04
PokemonNamesTextOffset=233
TrainerNamesTextOffset=723
TrainerClassesTextOffset=724
MoveDescriptionsTextOffset=742
MoveNamesTextOffset=743
AbilityNamesTextOffset=715
ItemDescriptionsTextOffset=219
ItemNamesTextOffset=220
StarterScreenTextOffset=189
IngameTradesTextOffset=199
IngameTradePersonTextOffsets=[557,591,603,629,0,0,339,458,530,46,532]
MapTableARM9Offset=0xF728C
MapNamesTextOffset=274
CatchingTutorialPlayerMonOffset=0x51C74
CatchingTutorialPlayerLevelOffset=0x51C76
CatchingTutorialOpponentMonOffset=0x51C96
MarillTextFiles=[]
DoubleBattleFlagReturnPrefix=08B5092132F0E6FE
DoubleBattleWalkingPrefix2=6AFF16B00120F8BD
DoubleBattleTextBoxPrefix=F6F762FFF6F77CFF
ShopDataPrefix=********************************
Arm9CRC32=DD15025F
OverlayCRC32<1>=485A49F3
OverlayCRC32<12>=926F3029
OverlayCRC32<21>=A2FA11EE
OverlayCRC32<61>=53119D58

[SoulSilver (K)]
Game=IPGK
Type=HGSS
Version=0
IgnoreGameCornerStatics=1
CopyStaticPokemon=1
CopyFrom=SoulSilver (U)
File<Text>=<a/0/2/7, 6C096A9F>
File<InGameTrades>=<a/1/1/2, 649D61C3>
NationalDexAtStartTweak=national_dex/hgss_national_dex
HasExtraPokemonNames=No
HiddenItemTableOffset=0xFABFC
PokemonNamesTextOffset=233
TrainerNamesTextOffset=723
TrainerClassesTextOffset=724
MoveDescriptionsTextOffset=742
MoveNamesTextOffset=743
AbilityNamesTextOffset=715
ItemDescriptionsTextOffset=219
ItemNamesTextOffset=220
StarterScreenTextOffset=189
IngameTradesTextOffset=199
IngameTradePersonTextOffsets=[557,591,603,629,0,0,339,458,530,46,532]
MapTableARM9Offset=0xF7284
MapNamesTextOffset=274
CatchingTutorialPlayerMonOffset=0x51C6C
CatchingTutorialPlayerLevelOffset=0x51C6E
CatchingTutorialOpponentMonOffset=0x51C8E
MarillTextFiles=[]
DoubleBattleFlagReturnPrefix=08B5092132F0E6FE
DoubleBattleWalkingPrefix2=6AFF16B00120F8BD
DoubleBattleTextBoxPrefix=F6F762FFF6F77CFF
ShopDataPrefix=********************************
Arm9CRC32=F1C4716F
OverlayCRC32<1>=AAC2EFA7
OverlayCRC32<12>=EE7F9555
OverlayCRC32<21>=D2DAA298
OverlayCRC32<61>=F1C15D1F

[HeartGold (F)]
Game=IPKF
Type=HGSS
Version=0
CopyText=1
CopyStaticPokemon=1
CopyFrom=HeartGold (U)
File<Text>=<a/0/2/7, F8323397>
File<InGameTrades>=<a/1/1/2, 37DEEDE2>
NationalDexAtStartTweak=national_dex/hgss_national_dex
HiddenItemTableOffset=0xFA53C
MapTableARM9Offset=0xF6BC4
CatchingTutorialPlayerMonOffset=0x51B78
CatchingTutorialPlayerLevelOffset=0x51B7A
CatchingTutorialOpponentMonOffset=0x51B9A
ShopDataPrefix=298E0402618E0402998E0402B98E0402
Arm9CRC32=590080BF
OverlayCRC32<1>=6DD56808
OverlayCRC32<12>=43574EA6
OverlayCRC32<21>=5045E946
OverlayCRC32<61>=62BC379B

[SoulSilver (F)]
Game=IPGF
Type=HGSS
Version=0
CopyText=1
CopyStaticPokemon=1
CopyFrom=SoulSilver (U)
File<Text>=<a/0/2/7, F8323397>
File<InGameTrades>=<a/1/1/2, 37DEEDE2>
NationalDexAtStartTweak=national_dex/hgss_national_dex
HiddenItemTableOffset=0xFA53C
MapTableARM9Offset=0xF6BC4
CatchingTutorialPlayerMonOffset=0x51B78
CatchingTutorialPlayerLevelOffset=0x51B7A
CatchingTutorialOpponentMonOffset=0x51B9A
ShopDataPrefix=298E0402618E0402998E0402B98E0402
Arm9CRC32=A55C566F
OverlayCRC32<1>=5B0C8E3F
OverlayCRC32<12>=BDF17AFF
OverlayCRC32<21>=5045E946
OverlayCRC32<61>=62BC379B

[HeartGold (G)]
Game=IPKD
Type=HGSS
Version=0
CopyText=1
CopyStaticPokemon=1
CopyFrom=HeartGold (U)
File<Text>=<a/0/2/7, 5325ECF3>
File<InGameTrades>=<a/1/1/2, F4D80FDB>
NationalDexAtStartTweak=national_dex/hgss_national_dex
HiddenItemTableOffset=0xFA50C
MapTableARM9Offset=0xF6B94
CatchingTutorialPlayerMonOffset=0x51B78
CatchingTutorialPlayerLevelOffset=0x51B7A
CatchingTutorialOpponentMonOffset=0x51B9A
ShopDataPrefix=298E0402618E0402998E0402B98E0402
Arm9CRC32=010DE166
OverlayCRC32<1>=03A114D2
OverlayCRC32<12>=8F59BA1A
OverlayCRC32<21>=8B953722
OverlayCRC32<61>=09E99828

[SoulSilver (G)]
Game=IPGD
Type=HGSS
Version=0
CopyText=1
CopyStaticPokemon=1
CopyFrom=SoulSilver (U)
File<Text>=<a/0/2/7, 5325ECF3>
File<InGameTrades>=<a/1/1/2, F4D80FDB>
NationalDexAtStartTweak=national_dex/hgss_national_dex
HiddenItemTableOffset=0xFA50C
MapTableARM9Offset=0xF6B94
CatchingTutorialPlayerMonOffset=0x51B78
CatchingTutorialPlayerLevelOffset=0x51B7A
CatchingTutorialOpponentMonOffset=0x51B9A
ShopDataPrefix=298E0402618E0402998E0402B98E0402
Arm9CRC32=7133E536
OverlayCRC32<1>=3578F2E5
OverlayCRC32<12>=71FF8E43
OverlayCRC32<21>=8B953722
OverlayCRC32<61>=09E99828

[HeartGold (S)]
Game=IPKS
Type=HGSS
Version=0
CopyText=1
CopyStaticPokemon=1
CopyFrom=HeartGold (U)
File<Text>=<a/0/2/7, BFAE82BF>
File<InGameTrades>=<a/1/1/2, B5DA51CC>
NationalDexAtStartTweak=national_dex/hgss_national_dex
HiddenItemTableOffset=0xFA540
MapTableARM9Offset=0xF6BC8
CatchingTutorialPlayerMonOffset=0x51B70
CatchingTutorialPlayerLevelOffset=0x51B72
CatchingTutorialOpponentMonOffset=0x51B92
ShopDataPrefix=218E0402598E0402918E0402B18E0402
Arm9CRC32=E44F2901
OverlayCRC32<1>=76637802
OverlayCRC32<12>=1BE62592
OverlayCRC32<21>=0788415E
OverlayCRC32<61>=27EDB088

[SoulSilver (S)]
Game=IPGS
Type=HGSS
Version=0
CopyText=1
CopyStaticPokemon=1
CopyFrom=SoulSilver (U)
File<Text>=<a/0/2/7, BFAE82BF>
File<InGameTrades>=<a/1/1/2, B5DA51CC>
NationalDexAtStartTweak=national_dex/hgss_national_dex
HiddenItemTableOffset=0xFA548
MapTableARM9Offset=0xF6BD0
CatchingTutorialPlayerMonOffset=0x51B78
CatchingTutorialPlayerLevelOffset=0x51B7A
CatchingTutorialOpponentMonOffset=0x51B9A
ShopDataPrefix=298E0402618E0402998E0402B98E0402
Arm9CRC32=E705FE48
OverlayCRC32<1>=05057EF4
OverlayCRC32<12>=DD0D85AD
OverlayCRC32<21>=7FD40F84
OverlayCRC32<61>=88DA5446

[HeartGold (I)]
Game=IPKI
Type=HGSS
Version=0
CopyText=1
CopyStaticPokemon=1
CopyFrom=HeartGold (U)
File<Text>=<a/0/2/7, 5FD94A88>
File<InGameTrades>=<a/1/1/2, 39E1A3F0>
NationalDexAtStartTweak=national_dex/hgss_national_dex
HiddenItemTableOffset=0xFA4D0
MapTableARM9Offset=0xF6B58
CatchingTutorialPlayerMonOffset=0x51B78
CatchingTutorialPlayerLevelOffset=0x51B7A
CatchingTutorialOpponentMonOffset=0x51B9A
ShopDataPrefix=298E0402618E0402998E0402B98E0402
Arm9CRC32=A200E7D3
OverlayCRC32<1>=51DB5337
OverlayCRC32<12>=B91C4DD4
OverlayCRC32<21>=F71A0EFA
OverlayCRC32<61>=C4A4AED5

[SoulSilver (I)]
Game=IPGI
Type=HGSS
Version=0
CopyText=1
CopyStaticPokemon=1
CopyFrom=SoulSilver (U)
File<Text>=<a/0/2/7, 5FD94A88>
File<InGameTrades>=<a/1/1/2, 39E1A3F0>
NationalDexAtStartTweak=national_dex/hgss_national_dex
HiddenItemTableOffset=0xFA4D0
MapTableARM9Offset=0xF6B58
CatchingTutorialPlayerMonOffset=0x51B78
CatchingTutorialPlayerLevelOffset=0x51B7A
CatchingTutorialOpponentMonOffset=0x51B9A
ShopDataPrefix=298E0402618E0402998E0402B98E0402
Arm9CRC32=9A50D8E0
OverlayCRC32<1>=6702B500
OverlayCRC32<12>=47BA798D
OverlayCRC32<21>=F71A0EFA
OverlayCRC32<61>=C4A4AED5
