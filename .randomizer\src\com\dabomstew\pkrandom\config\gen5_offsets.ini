[Black (U)]
Game=IRBO
Type=BW1
Version=0
File<TextStrings>=<a/0/0/2, C66141BE>
File<TextStory>=<a/0/0/3, A1D418F5>
File<PokemonGraphics>=<a/0/0/4, 227031B2>
File<MapTableFile>=<a/0/1/2, EC1CB686>
File<PokemonStats>=<a/0/1/6, 3A3FDA3E>
File<PokemonMovesets>=<a/0/1/8, A03DCC02>
File<PokemonEvolutions>=<a/0/1/9, 494DF5A2>
File<BabyPokemon>=<a/0/2/0, E7432896>
File<MoveData>=<a/0/2/1, A62E835C>
File<ItemData>=<a/0/2/4, 49779F3A>
File<Scripts>=<a/0/5/7, 0D4E6B38>
File<TrainerTextBoxes>=<a/0/9/0, DB72FD1A>
File<TrainerData>=<a/0/9/2, BF203832>
File<TrainerPokemon>=<a/0/9/3, A0C7D342>
File<EggMoves>=<a/1/2/3, 784B2D4F>
File<MapFiles>=<a/1/2/5, 6B00C87E>
File<WildPokemon>=<a/1/2/6, 4B3B1CF6>
File<InGameTrades>=<a/1/6/5, 42F5F462>
File<PokedexAreaData>=<a/1/7/8, 872D98C4>
File<StarterGraphics>=<a/2/0/5, 2AE60524>
RoamerOvlNumber=10
FieldOvlNumber=21
ShopItemOvlNumber=21
IntroCryOvlNumber=88
PickupOvlNumber=92
BattleOvlNumber=93
LowHealthMusicOvlNumber=94
EvolutionOvlNumber=195
IntroGraphicOvlNumber=204
StarterCryOvlNumber=223
FastestTextTweak=instant_text/b1_instant_text
NewIndexToMusicTweak=musicfix/black_musicfix
NewIndexToMusicOvlTweak=musicfix/black_ovl21_musicfix
ShedinjaEvolutionTweak=shedinja/black_shedinja
ShedinjaEvolutionOvlTweak=shedinja/black_ovl195_shedinja
NationalDexAtStartTweak=national_dex/bw1_national_dex
TradesUnused=[1,3,7,8,9,10,11,12]
StarterOffsets1=[782:639, 782:644, 782:0x361, 782:0x5FD, 304:0xF9, 304:0x19C]
StarterOffsets2=[782:687, 782:692, 782:0x356, 782:0x5F2, 304:0x11C, 304:0x1C4]
StarterOffsets3=[782:716, 782:721, 782:0x338, 782:0x5D4, 304:0x12C, 304:0x1D9]
StarterLocationTextOffset=430
StarterCryTablePrefix=080A0700080000
PokedexGivenFileOffset=792
PokemonNamesTextOffset=70
TrainerNamesTextOffset=190
TrainerClassesTextOffset=191
DoublesTrainerClasses=[16, 63, 68, 69]
EliteFourIndices=[228, 229, 230, 231, 232, 587]
TrainerMugshotsTextOffset=176
MoveDescriptionsTextOffset=202
MoveNamesTextOffset=203
AbilityNamesTextOffset=182
ItemDescriptionsTextOffset=53
ItemNamesTextOffset=54
IngameTradesTextOffset=35
LuckyEggScriptOffset=390
ItemBallsScriptOffset=864
HiddenItemsScriptOffset=865
MapNamesTextOffset=89
ShopItemOffsets=[0x51538,0x5153C,0x51546,0x5154C,0x51564,0x51590,0x515E4,0x515F2,0x51600,0x51610,0x51620,0x51630,0x51640,0x51650,0x51662,0x51674,0x51686,0x5169C,0x516B2,0x516C8,0x516F8,0x51714,0x51734,0x51774,0x51796,0x517BA]
ShopItemSizes=[2,2,3,4,4,6,7,7,8,8,8,8,8,9,9,9,11,11,11,11,14,16,16,17,18,19]
ShopCount=26
TMShops=[1,2,4,7]
RegularShops=[0,16,20,23,24,25]
ItemBallsSkip=[]
HiddenItemsSkip=[]
NationalDexScriptOffset=792
StaticPokemonSupport=1
StaticPokemon{}={Species=[304:0x121, 304:0x1C9, 304:0x299], Level=[304:0x29D]} // Pansage
StaticPokemon{}={Species=[304:0x131, 304:0x1DE, 304:0x2B7], Level=[304:0x2BB]} // Pansear
StaticPokemon{}={Species=[304:0xFE, 304:0x1A1, 304:0x268], Level=[304:0x26C]} // Panpour
StaticPokemon{}={Species=[526:0x758], Level=[526:0x75C]} // Magikarp
StaticPokemon{}={Species=[94:0x810, 94:0x64, 94:0xB4, 94:0x44B, 94:0x7AB, 94:0x7D0, 94:0x7DC], Level=[94:0x44F]} // Zorua
StaticPokemon{}={Species=[776:0x85, 776:0xB2]} // Larvesta (egg)
StaticPokemon{}={Species=[316:0x369], Level=[316:0x36B]} // Darmanitan 1
StaticPokemon{}={Species=[316:0x437], Level=[316:0x439]} // Darmanitan 2
StaticPokemon{}={Species=[316:0x505], Level=[316:0x507]} // Darmanitan 3
StaticPokemon{}={Species=[316:0x5D3], Level=[316:0x5D5]} // Darmanitan 4
StaticPokemon{}={Species=[316:0x6A1], Level=[316:0x6A3]} // Darmanitan 5
StaticPokemon{}={Species=[306:0x65, 306:0x8F], Level=[306:0x91]} // Musharna
StaticPokemon{}={Species=[770:0x2F8, 770:0x353], Level=[770:0x355]} // Zoroark
StaticPokemon{}={Species=[364:0xE, 364:0x1F], Level=[364:0x21]} // Volcarona
StaticPokemon{}={Species=[474:0x1CE, 474:0x20A], Level=[474:0x20C]} // Victini
StaticPokemon{}={Species=[426:0x133, 426:0x15B, 556:0x1841, 556:0xCFC, 556:0x1878, 556:0x18EA], Level=[426:0x15D, 556:0xCFE]} // Reshiram
StaticPokemon{}={Species=[426:0x127, 426:0x174, 556:0x184D, 556:0x186C, 556:0xD15, 556:0x18DE], Level=[426:0x176, 556:0xD17]} // Zekrom
StaticPokemon{}={Species=[670:0x415, 670:0x426, 692:0x1E2], Level=[670:0x428]} // Cobalion
StaticPokemon{}={Species=[458:0x10, 458:0x21, 692:0x203], Level=[458:0x23]} // Terrakion
StaticPokemon{}={Species=[312:0x10, 312:0x21, 692:0x224], Level=[312:0x23]} // Virizion
StaticPokemon{}={Species=[752:0x66D, 752:0x6CC, 752:0x6DD], Level=[752:0x6DF]} // Landorus
StaticPokemon{}={Species=[464:0x10, 468:0x4F, 468:0x60], Level=[468:0x62]} // Kyurem
StaticPokemon{}={Species=[877:0x601], Level=[877:0x3F7]} // Cranidos
StaticPokemon{}={Species=[877:0x620], Level=[877:0x3F7]} // Shieldon
StaticPokemon{}={Species=[877:0x63F], Level=[877:0x3F7]} // Omanyte
StaticPokemon{}={Species=[877:0x65E], Level=[877:0x3F7]} // Kabuto
StaticPokemon{}={Species=[877:0x67D], Level=[877:0x3F7]} // Aerodactyl
StaticPokemon{}={Species=[877:0x69C], Level=[877:0x3F7]} // Anorith
StaticPokemon{}={Species=[877:0x6BB], Level=[877:0x3F7]} // Lileep
StaticPokemon{}={Species=[877:0x6DA], Level=[877:0x3F7]} // Tirtouga
StaticPokemon{}={Species=[877:0x6F9], Level=[877:0x3F7]} // Archen
StaticPokemonFakeBall{}={Species=[897:0x45], Level=[331:0x2AA, 331:0x2CE, 355:0x196, 355:0x2DA]} // Foongus
StaticPokemonFakeBall{}={Species=[897:0xC1], Level=[355:0x24A, 355:0x26E]} // Amoonguss
RoamingPokemon{}={Species=[0x95D8, 0x940C], Level=[0x930E], Script=[674:0x57E, 674:0x5F1]} // Thundurus
RoamingPokemon{}={Species=[0x95DC, 0x9410], Level=[0x930E], Script=[674:0x572, 674:0x5DC]} // Tornadus
GetRoamerFlagOffsetStartOffset=0x95C4
BoxLegendaryOffset=15
IsBlack=1
TradeScript[]=[46:0x81:0x86, 46:0x97:0x9C] // Cottonee/Petilil
TradeScript[]=[202:0x224:0x21F] // Minccino/Basculin
TradeScript[]=[686:0x76:0x71] // Boldore/Emolga
TradeScript[]=[830:0xB3:0xAE, 830:0xEA:0xE5, 830:0x114:0x10F] // Cinccino/Munchlax
TradeScript[]=[764:0x43:0x3E] // Ditto/Rotom
StaticEggPokemonOffsets=[5]
MainGameLegendaries=[643,644]
Arm9ExtensionSize=300 // 252 for music, 48 for Shedinja
TCMCopyingPrefix=1030A0E3013053E2FDFFFF1AF8FFFFEA
NewIndexToMusicPrefix=208020202860FFE7012002BC08470000
SpecialMusicStatics=[494,571,637,638,639,640,641,643,645,646]
ShedinjaSpeciesOffset=0xA67C8
TrainerOverworldTextBoxPrefix=0004000C03D10320
DoubleBattleLimitPrefix=321C26E0012E17D1
DoubleBattleGetPointerPrefix=0A9906980904090C
BeqToSingleTrainerNumber=0xD0D1
TextBoxChoicePrefix=6088B0420AD1A088
Arm9CRC32=69BC03D6
OverlayCRC32<10>=F8D7B6CA
OverlayCRC32<21>=F2B9DC42
OverlayCRC32<88>=26A8C2E1
OverlayCRC32<92>=D4CF0B58
OverlayCRC32<93>=A36EECA9
OverlayCRC32<94>=58394A4F
OverlayCRC32<195>=E24CC7A8
OverlayCRC32<204>=798061E6
OverlayCRC32<223>=4FFD3D7F

[White (U)]
Game=IRAO
Type=BW1
Version=0
CopyStaticPokemon=1
CopyRoamingPokemon=1
CopyTradeScripts=1
CopyFrom=IRBO
File<WildPokemon>=<a/1/2/6, 01FCA7BB>
File<PokedexAreaData>=<a/1/7/8, ED259CD8>
FastestTextTweak=instant_text/w1_instant_text
NewIndexToMusicTweak=musicfix/white_musicfix
NewIndexToMusicOvlTweak=musicfix/white_ovl21_musicfix
ShedinjaEvolutionTweak=shedinja/white_shedinja
ShedinjaEvolutionOvlTweak=shedinja/white_ovl195_shedinja
NationalDexAtStartTweak=national_dex/bw1_national_dex
TradesUnused=[0,2,7,8,9,10,11,12]
ShopItemOffsets=[0x51530,0x51534,0x5153E,0x51544,0x5155C,0x51588,0x515DC,0x515EA,0x515F8,0x51608,0x51618,0x51628,0x51638,0x51648,0x5165A,0x5166C,0x5167E,0x51694,0x516AA,0x516C0,0x516F0,0x5170C,0x5172C,0x5176C,0x5178E,0x517B2]
BoxLegendaryOffset=16
EliteFourIndices=[228, 229, 230, 231, 232, 586]
IsBlack=0
SpecialMusicStatics=[494,571,637,638,639,640,642,644,645,646]
ShedinjaSpeciesOffset=0xA67E8
Arm9CRC32=A6BA89D8
OverlayCRC32<10>=40E9CEEE
OverlayCRC32<21>=EAC5BFE0
OverlayCRC32<88>=6728568B
OverlayCRC32<92>=F597B809
OverlayCRC32<93>=167CBE37
OverlayCRC32<94>=AC9CECAC
OverlayCRC32<195>=6DF520CE
OverlayCRC32<204>=A9A7377A
OverlayCRC32<223>=09541F48

[Black 2 (U)]
Game=IREO
Type=BW2
Version=0
CopyFrom=IRBO
File<TextStrings>=<a/0/0/2, 0AAFBD81>
File<TextStory>=<a/0/0/3, 579FA304>
File<PokemonGraphics>=<a/0/0/4, F7FF84DC>
File<MapTableFile>=<a/0/1/2, C6B5EB86>
File<PokemonStats>=<a/0/1/6, 01A12D6A>
File<PokemonMovesets>=<a/0/1/8, 57FBEC75>
File<PokemonEvolutions>=<a/0/1/9, E7A49FB7>
File<BabyPokemon>=<a/0/2/0, 5AAA0D1E>
File<ItemData>=<a/0/2/4, D68F2EF6>
File<Scripts>=<a/0/5/6, EA1E0890>
File<TrainerTextBoxes>=<a/0/8/9, C73441C5>
File<TrainerData>=<a/0/9/1, 0852918C>
File<TrainerPokemon>=<a/0/9/2, 9435BD3C>
File<EggMoves>=<a/1/2/4, 784B2D4F>
File<MapFiles>=<a/1/2/6, FE1792B9>
File<WildPokemon>=<a/1/2/7, 7EAF56BE>
File<InGameTrades>=<a/1/6/3, 15ED8FFA>
File<PokedexAreaData>=<a/1/7/6, 13132CA5>
File<StarterGraphics>=<a/2/0/2, 83EB4FF0>
File<DriftveilPokemon>=<a/2/5/0, 60CE675E>
File<HiddenHollows>=<a/2/7/3, A4DB2F91>
File<ShopItems>=<a/2/8/2, 52CABC05>
File<HabitatList>=<a/2/9/6, 72A983AD>
FieldOvlNumber=36
MoveTutorOvlNumber=36
IntroCryOvlNumber=162
PickupOvlNumber=166
BattleOvlNumber=167
LowHealthMusicOvlNumber=168
EvolutionOvlNumber=284
IntroGraphicOvlNumber=294
StarterCryOvlNumber=316
FastestTextTweak=instant_text/b2_instant_text
NewIndexToMusicTweak=musicfix/black2_musicfix
NewIndexToMusicOvlTweak=musicfix/black2_ovl36_musicfix
ShedinjaEvolutionTweak=shedinja/black2_shedinja
ShedinjaEvolutionOvlTweak=shedinja/black2_ovl284_shedinja
NationalDexAtStartTweak=national_dex/bw2_national_dex
HiddenHollowIndex=1
ShopCount=32
TMShops=[7,10,12,17,24]
RegularShops=[0,1,2,3,4,5]
TradesUnused=[25]
StarterOffsets1=[854:0x58B, 854:0x590, 854:0x595]
StarterOffsets2=[854:0x5C0, 854:0x5C5, 854:0x5CA]
StarterOffsets3=[854:0x5E2, 854:0x5E7, 854:0x5EC]
StarterLocationTextOffset=169
StarterCryTablePrefix=080A070000080000
PokedexGivenFileOffset=854
MoveTutorDataOffset=0x51538
PokemonNamesTextOffset=90
TrainerMugshotsTextOffset=368
TrainerNamesTextOffset=382
TrainerClassesTextOffset=383
PWTTrainerNamesTextOffset=409
PWTTrainerClassesTextOffset=410
EliteFourIndices=[38, 39, 40, 41, 341]
ChallengeModeEliteFourIndices=[772,773,774,775,776]
MoveDescriptionsTextOffset=402
MoveNamesTextOffset=403
AbilityNamesTextOffset=374
ItemDescriptionsTextOffset=63
ItemNamesTextOffset=64
IngameTradesTextOffset=37
LuckyEggScriptOffset=676
ItemBallsScriptOffset=1240
HiddenItemsScriptOffset=1241
MapNamesTextOffset=109
ItemBallsSkip=[]
HiddenItemsSkip=[]
NationalDexScriptOffset=854
StaticPokemonSupport=1
StaticPokemon{}={Species=[662:0x1DE, 662:0x240, 740:0xCD, 740:0xFC, 740:0x12C, 740:0x14C], Level=[740:0x12E, 740:0x14E]} // Cobalion
StaticPokemon{}={Species=[730:0x13A, 730:0x15F, 730:0x19B, 730:0x1BB], Level=[730:0x19D, 730:0x1BD]} // Virizion
StaticPokemon{}={Species=[948:0x45D, 948:0x48D, 948:0x4AD], Level=[948:0x48F, 948:0x4AF]} // Terrakion
StaticPokemon{}={Species=[426:0x38A, 426:0x39B, 556:0x367, 556:0x568, 556:0x5E6, 556:0x6E1, 1208:0x3A4, 1208:0xA6A, 1208:0x717], Level=[426:0x39D]} // Reshiram
StaticPokemon{}={Species=[426:0x36B, 426:0x37C, 556:0x350, 556:0x551, 556:0x5C7, 556:0x6C3, 1208:0x38D, 1208:0xA53, 1208:0x706], Level=[426:0x37E]} // Zekrom
StaticPokemon{}={Species=[1112:0x133, 1122:0x2BA, 1122:0x311, 1128:0x37A, 1128:0x3D1, 1208:0x1B7, 1208:0x1F8, 1208:0x723, 1208:0xF3D, 1208:0xF4E], Level=[1208:0xF50]} // Kyurem
StaticPokemon{}={Species=[1208:0xD8B, 1208:0xD97], Level=[1208:0xD99], Forme=[1208:0xD8D, 1208:0xD9B]} // Kyurem-Black
StaticPokemon{}={Species=[1208:0xDB6, 1208:0xDC2], Level=[1208:0xDC4], Forme=[1208:0xDB8, 1208:0xDC6]} // Kyurem-White
StaticPokemon{}={Species=[304:0xCC, 304:0x14B, 304:0x1BC, 304:0x237, 304:0x327, 304:0x3E6, 304:0x4A1, 304:0x54A, 304:0x5BD, 304:0x5CE], Level=[304:0x5D0]} // Latias
StaticPokemon{}={Species=[304:0xB5, 304:0x134, 304:0x1A5, 304:0x220, 304:0x310, 304:0x3CF, 304:0x48A, 304:0x533, 304:0x59E, 304:0x5AF], Level=[304:0x5B1]} // Latios
StaticPokemon{}={Species=[32:0x247, 32:0x2B0, 32:0x2C1, 1034:0x12A], Level=[32:0x2C3]} // Uxie
StaticPokemon{}={Species=[684:0x136, 684:0x1C2, 684:0x1D3, 1034:0x169], Level=[684:0x1D5]} // Mesprit
StaticPokemon{}={Species=[950:0xA1, 950:0x10A, 950:0x11B, 1034:0x1BE], Level=[950:0x11D]} // Azelf
StaticPokemon{}={Species=[1222:0x134, 1222:0x145, 1018:0x32], Level=[1222:0x147]} // Regirock
StaticPokemon{}={Species=[1224:0x134, 1224:0x145, 1018:0x2C], Level=[1224:0x147]} // Regice
StaticPokemon{}={Species=[1226:0x134, 1226:0x145, 1018:0x38], Level=[1226:0x147]} // Registeel
StaticPokemon{}={Species=[1018:0x97, 1018:0xA8], Level=[1018:0xAA]} // Regigigas
StaticPokemon{}={Species=[526:0x48D, 526:0x512, 526:0x523], Level=[526:0x525]} // Cresselia
StaticPokemon{}={Species=[1068:0x193, 1068:0x1D6, 1068:0x1E7, 1080:0x193, 1080:0x1D6, 1080:0x1E7], Level=[1068:0x1E9, 1080:0x1E9]} // Heatran
StaticPokemon{}={Species=[652:0x5C6, 652:0x5E9], Level=[652:0x5EB]} // Mandibuzz
StaticPokemon{}={Species=[1102:0x592, 1102:0x5B5], Level=[1102:0x5B7]} // Braviary
StaticPokemon{}={Species=[364:0xE, 364:0x32, 364:0x40], Level=[364:0x34, 364:0x42]} // Volcarona
StaticPokemon{}={Species=[1030:0x290, 1030:0x2A1], Level=[1030:0x2A3]} // Crustle
StaticPokemon{}={Species=[480:0xE1, 480:0x10A, 480:0x131, 480:0x15A], Level=[480:0x10C, 480:0x15C]} // Jellicent
StaticPokemon{}={Species=[1168:0x2C, 1168:0x4F], Level=[1168:0x51]} // Shiny Haxorus
StaticPokemon{}={Species=[988:0x382], Level=[988:0x386]} // Eevee
StaticPokemon{}={Species=[664:0x3B5, 664:0x3E2, 664:0x40F, 664:0x43C], Level=[664:0x3B9, 664:0x3E6, 664:0x413, 664:0x440], Forme=[664:0x3B7, 664:0x3E4, 664:0x411, 664:0x43E]} // Deerling
StaticPokemon{}={Species=[880:0xAB4, 880:0xAC7], Level=[880:0xAB8]} // Shiny Gible
StaticPokemon{}={Species=[880:0xAD3, 880:0xAE6], Level=[880:0xAD7]} // Shiny Dratini
StaticPokemon{}={Species=[54:0xDD]} // Happiny (egg)
StaticPokemon{}={Species=[526:0x27E], Level=[526:0x282]} // Magikarp
StaticPokemon{}={Species=[1253:0x5E0], Level=[1253:0x3D6]} // Cranidos
StaticPokemon{}={Species=[1253:0x5FF], Level=[1253:0x3D6]} // Shieldon
StaticPokemon{}={Species=[1253:0x61E], Level=[1253:0x3D6]} // Omanyte
StaticPokemon{}={Species=[1253:0x63D], Level=[1253:0x3D6]} // Kabuto
StaticPokemon{}={Species=[1253:0x65C], Level=[1253:0x3D6]} // Aerodactyl
StaticPokemon{}={Species=[1253:0x67B], Level=[1253:0x3D6]} // Anorith
StaticPokemon{}={Species=[1253:0x69A], Level=[1253:0x3D6]} // Lileep
StaticPokemon{}={Species=[1253:0x6B9], Level=[1253:0x3D6]} // Tirtouga
StaticPokemon{}={Species=[1253:0x6D8], Level=[1253:0x3D6]} // Archen
StaticPokemon{}={Species=[208:0x5A6], Level=[208:0x5A8]} // Zorua
StaticPokemonFakeBall{}={Species=[1273:0x45], Level=[500:0x46E, 500:0x492, 500:0x4B6, 506:0x42A, 506:0x44E]} // Foongus
StaticPokemonFakeBall{}={Species=[1273:0xC7], Level=[534:0x2F2, 534:0x316, 562:0x3FE, 562:0x422, 563:0x742, 563:0x766, 563:0x78A]} // Amoonguss
IngameTradePersonTextOffsets=[529, 555, 193, 594, 628, 628]
StaticEggPokemonOffsets=[29]
MainGameLegendaries=[638,639,640]
Arm9ExtensionSize=496 // 448 for music, 48 for Shedinja
TCMCopyingPrefix=1030A0E3013053E2FDFFFF1AF8FFFFEA
NewIndexToMusicPrefix=2648288021203060FFE7012002BC0847
SpecialMusicStatics=[377,378,379,381,480,481,482,485,486,488,494,571,612,637,638,639,640,644,646,669]
ShedinjaSpeciesOffset=0x9ACCC
TrainerOverworldTextBoxPrefix=0004000C03D10320
DoubleBattleLimitPrefix=0224E3E7012817D1
DoubleBattleGetPointerPrefix=0A9906980904090C
BeqToSingleTrainerNumber=0xD0C7
TextBoxChoicePrefix=6088A8420AD1A088
Arm9CRC32=FAF9D733
OverlayCRC32<36>=4CF872D1
OverlayCRC32<162>=8D54F1B3
OverlayCRC32<166>=4CB7622C
OverlayCRC32<167>=26B6E3C0
OverlayCRC32<168>=0439E88C
OverlayCRC32<284>=957DBCBA
OverlayCRC32<294>=F3F8525A
OverlayCRC32<316>=D4756D45

[White 2 (U)]
Game=IRDO
Type=BW2
Version=0
CopyStaticPokemon=1
CopyFrom=IREO
File<WildPokemon>=<a/1/2/7, A35466D9>
File<PokedexAreaData>=<a/1/7/6, C5BE8838>
File<HabitatList>=<a/2/9/6, E578C751>
FastestTextTweak=instant_text/w2_instant_text
NewIndexToMusicTweak=musicfix/white2_musicfix
NewIndexToMusicOvlTweak=musicfix/white2_ovl36_musicfix
ShedinjaEvolutionTweak=shedinja/white2_shedinja
ShedinjaEvolutionOvlTweak=shedinja/white2_ovl284_shedinja
NationalDexAtStartTweak=national_dex/bw2_national_dex
HiddenHollowIndex=0
MoveTutorDataOffset=0x5152C
TradesUnused=[24]
IngameTradePersonTextOffsets=[537, 555, 193, 594, 628, 628]
SpecialMusicStatics=[377,378,379,380,480,481,482,485,486,488,494,571,612,637,638,639,640,643,646,668]
ShedinjaSpeciesOffset=0x9AD0C
Arm9CRC32=DB3843F0
OverlayCRC32<36>=E3CF3D36
OverlayCRC32<162>=CCA4CB78
OverlayCRC32<166>=759F8A03
OverlayCRC32<167>=3E3CA5FA
OverlayCRC32<168>=EDE00D9D
OverlayCRC32<284>=0E276134
OverlayCRC32<294>=717E4E01
OverlayCRC32<316>=D7EED8D1

[Black (F)]
Game=IRBF
Type=BW1
Version=0
CopyStaticPokemon=1
CopyFrom=IRBO
File<TextStrings>=<a/0/0/2, 5DF991B3>
File<TextStory>=<a/0/0/3, 14FC4F5C>
File<ItemData>=<a/0/2/4, 5F5B003D>
File<Scripts>=<a/0/5/7, FDE0C66A>
File<InGameTrades>=<a/1/6/5, C76D0CBA>
NationalDexAtStartTweak=national_dex/bw1_national_dex
ShopItemOffsets=[0x51530,0x51534,0x5153E,0x51544,0x5155C,0x51588,0x515DC,0x515EA,0x515F8,0x51608,0x51618,0x51628,0x51638,0x51648,0x5165A,0x5166C,0x5167E,0x51694,0x516AA,0x516C0,0x516F0,0x5170C,0x5172C,0x5176C,0x5178E,0x517B2]
Arm9CRC32=A4F262BE
OverlayCRC32<10>=8FC77ABE
OverlayCRC32<21>=054897E8
OverlayCRC32<88>=55593929
OverlayCRC32<92>=57E1FE0E
OverlayCRC32<93>=D568F722
OverlayCRC32<94>=3A8E21BE
OverlayCRC32<195>=6B6112D6
OverlayCRC32<204>=3E6F6775
OverlayCRC32<223>=F01B4C69

[White (F)]
Game=IRAF
Type=BW1
Version=0
CopyStaticPokemon=1
CopyFrom=IRAO
File<TextStrings>=<a/0/0/2, 5DF991B3>
File<TextStory>=<a/0/0/3, 14FC4F5C>
File<ItemData>=<a/0/2/4, 5F5B003D>
File<Scripts>=<a/0/5/7, FDE0C66A>
File<InGameTrades>=<a/1/6/5, C76D0CBA>
NationalDexAtStartTweak=national_dex/bw1_national_dex
ShopItemOffsets=[0x51528,0x5152C,0x51536,0x5153C,0x51554,0x51580,0x515D4,0x515E2,0x515F0,0x51600,0x51610,0x51620,0x51630,0x51640,0x51652,0x51664,0x51676,0x5168C,0x516A2,0x516B8,0x516E8,0x51704,0x51724,0x51764,0x51786,0x517AA]
Arm9CRC32=961E0C7B
OverlayCRC32<10>=97A78C5C
OverlayCRC32<21>=14BA0CDE
OverlayCRC32<88>=F4EB70CD
OverlayCRC32<92>=5ACBE24B
OverlayCRC32<93>=B185132E
OverlayCRC32<94>=985B99B1
OverlayCRC32<195>=F5FB3F97
OverlayCRC32<204>=B391C71E
OverlayCRC32<223>=935717C1

[Black (G)]
Game=IRBD
Type=BW1
Version=0
CopyStaticPokemon=1
CopyFrom=IRBO
File<TextStrings>=<a/0/0/2, 088A95B9>
File<TextStory>=<a/0/0/3, 5064AD57>
File<ItemData>=<a/0/2/4, 756DD6C3>
File<Scripts>=<a/0/5/7, 471456A0>
File<InGameTrades>=<a/1/6/5, 9BB550BD>
NationalDexAtStartTweak=national_dex/bw1_national_dex
ShopItemOffsets=[0x51530,0x51534,0x5153E,0x51544,0x5155C,0x51588,0x515DC,0x515EA,0x515F8,0x51608,0x51618,0x51628,0x51638,0x51648,0x5165A,0x5166C,0x5167E,0x51694,0x516AA,0x516C0,0x516F0,0x5170C,0x5172C,0x5176C,0x5178E,0x517B2]
Arm9CRC32=5189F435
OverlayCRC32<10>=A74BA01F
OverlayCRC32<21>=7E6E109F
OverlayCRC32<88>=E2E86BAF
OverlayCRC32<92>=C684D133
OverlayCRC32<93>=5DAB79AA
OverlayCRC32<94>=5F094FDE
OverlayCRC32<195>=CACEF977
OverlayCRC32<204>=6CBA741B
OverlayCRC32<223>=787E48BF

[White (G)]
Game=IRAD
Type=BW1
Version=0
CopyStaticPokemon=1
CopyFrom=IRAO
File<TextStrings>=<a/0/0/2, 088A95B9>
File<TextStory>=<a/0/0/3, 5064AD57>
File<ItemData>=<a/0/2/4, 756DD6C3>
File<Scripts>=<a/0/5/7, 471456A0>
File<InGameTrades>=<a/1/6/5, 9BB550BD>
NationalDexAtStartTweak=national_dex/bw1_national_dex
ShopItemOffsets=[0x51528,0x5152C,0x51536,0x5153C,0x51554,0x51580,0x515D4,0x515E2,0x515F0,0x51600,0x51610,0x51620,0x51630,0x51640,0x51652,0x51664,0x51676,0x5168C,0x516A2,0x516B8,0x516E8,0x51704,0x51724,0x51764,0x51786,0x517AA]
Arm9CRC32=23C9DFD2
OverlayCRC32<10>=452DD9DB
OverlayCRC32<21>=61B4BE9C
OverlayCRC32<88>=805834D4
OverlayCRC32<92>=1BC6EC0E
OverlayCRC32<93>=FEDB5C26
OverlayCRC32<94>=DC4FBCB9
OverlayCRC32<195>=BD27975F
OverlayCRC32<204>=4CB70AB9
OverlayCRC32<223>=B1CA7124

[Black (S)]
Game=IRBS
Type=BW1
Version=0
CopyStaticPokemon=1
CopyFrom=IRBO
File<TextStrings>=<a/0/0/2, DCFE58C9>
File<TextStory>=<a/0/0/3, 074DEAC6>
File<ItemData>=<a/0/2/4, F3B3E026>
File<Scripts>=<a/0/5/7, 7638D5C9>
File<InGameTrades>=<a/1/6/5, 192D997F>
NationalDexAtStartTweak=national_dex/bw1_national_dex
ShopItemOffsets=[0x5153C,0x51540,0x5154A,0x51550,0x51568,0x51594,0x515E8,0x515F6,0x51604,0x51614,0x51624,0x51634,0x51644,0x51654,0x51666,0x51678,0x5168A,0x516A0,0x516B6,0x516CC,0x516FC,0x51718,0x51738,0x51778,0x5179A,0x517BE]
Arm9CRC32=368F8E04
OverlayCRC32<10>=94D5A1A8
OverlayCRC32<21>=A6539D64
OverlayCRC32<88>=51BB12C3
OverlayCRC32<92>=414ADA7A
OverlayCRC32<93>=F1905C6B
OverlayCRC32<94>=63537E6B
OverlayCRC32<195>=38859ADE
OverlayCRC32<204>=8ED54FBE
OverlayCRC32<223>=187C0D03

[White (S)]
Game=IRAS
Type=BW1
Version=0
CopyStaticPokemon=1
CopyFrom=IRAO
File<TextStrings>=<a/0/0/2, DCFE58C9>
File<TextStory>=<a/0/0/3, 074DEAC6>
File<ItemData>=<a/0/2/4, F3B3E026>
File<Scripts>=<a/0/5/7, 7638D5C9>
File<InGameTrades>=<a/1/6/5, 192D997F>
NationalDexAtStartTweak=national_dex/bw1_national_dex
ShopItemOffsets=[0x51534,0x51538,0x51542,0x51548,0x51560,0x5158C,0x515E0,0x515EE,0x515FC,0x5160C,0x5161C,0x5162C,0x5163C,0x5164C,0x5165E,0x51670,0x51682,0x51698,0x516AE,0x516C4,0x516F4,0x51710,0x51730,0x51770,0x51792,0x517B6]
Arm9CRC32=D48F02E1
OverlayCRC32<10>=5DB2D539
OverlayCRC32<21>=3882585C
OverlayCRC32<88>=54B7C88D
OverlayCRC32<92>=8043EB67
OverlayCRC32<93>=73422427
OverlayCRC32<94>=325AA919
OverlayCRC32<195>=6637307A
OverlayCRC32<204>=DD127FDF
OverlayCRC32<223>=2F3ADC12

[Black (I)]
Game=IRBI
Type=BW1
Version=0
CopyStaticPokemon=1
CopyFrom=IRBO
File<TextStrings>=<a/0/0/2, 0B493D3F>
File<TextStory>=<a/0/0/3, 340F01AD>
File<ItemData>=<a/0/2/4, B25CF947>
File<Scripts>=<a/0/5/7, 7638D5C9>
File<InGameTrades>=<a/1/6/5, DAF9345C>
NationalDexAtStartTweak=national_dex/bw1_national_dex
ShopItemOffsets=[0x51520,0x51524,0x51532,0x51548,0x51538,0x515AC,0x515D0,0x515DE,0x515EC,0x515FC,0x5160C,0x5161C,0x5162C,0x5163C,0x5164E,0x51660,0x516B4,0x51672,0x51688,0x5169E,0x516E4,0x51700,0x51720,0x51760,0x51782,0x517A6]
Arm9CRC32=5A9924F2
OverlayCRC32<10>=C15D1FAD
OverlayCRC32<21>=4F9C6CB1
OverlayCRC32<88>=02AE885C
OverlayCRC32<92>=598C95C4
OverlayCRC32<93>=E0F9359C
OverlayCRC32<94>=2F85DA79
OverlayCRC32<195>=70F4039C
OverlayCRC32<204>=AE28DAEC
OverlayCRC32<223>=DC1D15FC

[White (I)]
Game=IRAI
Type=BW1
Version=0
CopyStaticPokemon=1
CopyFrom=IRAO
File<TextStrings>=<a/0/0/2, 0B493D3F>
File<TextStory>=<a/0/0/3, 340F01AD>
File<ItemData>=<a/0/2/4, B25CF947>
File<Scripts>=<a/0/5/7, 7638D5C9>
File<InGameTrades>=<a/1/6/5, DAF9345C>
NationalDexAtStartTweak=national_dex/bw1_national_dex
ShopItemOffsets=[0x51518,0x5151C,0x5152A,0x51540,0x51530,0x515A4,0x515C8,0x515D6,0x515E4,0x515F4,0x51604,0x51614,0x51624,0x51634,0x51646,0x51658,0x516AC,0x5166A,0x51680,0x51696,0x516DC,0x516F8,0x51718,0x51758,0x5177A,0x5179E]
Arm9CRC32=8DC1C9C0
OverlayCRC32<10>=9921B1F7
OverlayCRC32<21>=5999FAA1
OverlayCRC32<88>=2B0077F8
OverlayCRC32<92>=F578AD4B
OverlayCRC32<93>=1C399052
OverlayCRC32<94>=0F25AD49
OverlayCRC32<195>=0C9EEB6B
OverlayCRC32<204>=12FF46BB
OverlayCRC32<223>=0F78FCA2

[Black (J)]
Game=IRBJ
Type=BW1
Version=0
CopyStaticPokemon=1
CopyFrom=IRBO
File<TextStrings>=<a/0/0/2, D9800767>
File<TextStory>=<a/0/0/3, 64371924>
File<PokemonGraphics>=<a/0/0/4, 482B6A4F>
File<MapTableFile>=<a/0/1/2, 5CA68F9B>
File<ItemData>=<a/0/2/4, 11542F36>
File<Scripts>=<a/0/5/7, 3340FFA8>
File<InGameTrades>=<a/1/6/5, 56619FAA>
MoveDescriptionsTextOffset=203
MoveNamesTextOffset=204
ShopItemOffsets=[0x51244,0x51248,0x51252,0x51268,0x51270,0x512B4,0x512F0,0x512FE,0x5131C,0x5134C,0x5132C,0x5133C,0x5130C,0x5135C,0x5136E,0x51380,0x51392,0x513A8,0x513BE,0x513D4,0x51404,0x51420,0x51440,0x51480,0x514A2,0x514C6]
Arm9CRC32=6E7444E9
OverlayCRC32<10>=E91DA62C
OverlayCRC32<21>=2FEEC460
OverlayCRC32<88>=DC1300B9
OverlayCRC32<92>=0EC1245E
OverlayCRC32<93>=65349A4A
OverlayCRC32<94>=31CB4E7A
OverlayCRC32<195>=CA203F2C
OverlayCRC32<204>=127CF38D
OverlayCRC32<223>=973F0880

[White (J)]
Game=IRAJ
Type=BW1
Version=0
CopyStaticPokemon=1
CopyFrom=IRAO
File<TextStrings>=<a/0/0/2, D9800767>
File<TextStory>=<a/0/0/3, 64371924>
File<PokemonGraphics>=<a/0/0/4, 482B6A4F>
File<MapTableFile>=<a/0/1/2, 5CA68F9B>
File<ItemData>=<a/0/2/4, 11542F36>
File<Scripts>=<a/0/5/7, 3340FFA8>
File<InGameTrades>=<a/1/6/5, 56619FAA>
MoveDescriptionsTextOffset=203
MoveNamesTextOffset=204
ShopItemOffsets=[0x5123C,0x51240,0x5124A,0x51260,0x51268,0x512AC,0x512E8,0x512F6,0x51314,0x51344,0x51324,0x51334,0x51304,0x51354,0x51366,0x51378,0x5138A,0x513A0,0x513B6,0x513CC,0x513FC,0x51418,0x51438,0x51478,0x5149A,0x514BE]
Arm9CRC32=1A99A22E
OverlayCRC32<10>=95D97B6C
OverlayCRC32<21>=9311D75D
OverlayCRC32<88>=2983855B
OverlayCRC32<92>=41E0F3F4
OverlayCRC32<93>=CBF347C4
OverlayCRC32<94>=4DD90D5E
OverlayCRC32<195>=63636362
OverlayCRC32<204>=88CB9615
OverlayCRC32<223>=57871763

[Black (K)]
Game=IRBK
Type=BW1
Version=0
CopyStaticPokemon=1
CopyFrom=IRBO
File<TextStrings>=<a/0/0/2, E5A5DB10>
File<TextStory>=<a/0/0/3, 0F4C32EC>
File<ItemData>=<a/0/2/4, 27AE983B>
File<InGameTrades>=<a/1/6/5, 63498C52>
NationalDexAtStartTweak=national_dex/bw1_national_dex
ShopItemOffsets=[0x51524,0x51528,0x51536,0x5154C,0x5153C,0x515B0,0x515D4,0x515E2,0x515F0,0x51600,0x51610,0x51620,0x51630,0x51640,0x51652,0x51664,0x516B8,0x51676,0x5168C,0x516A2,0x516E8,0x51704,0x51724,0x51764,0x51786,0x517AA]
Arm9CRC32=37794F95
OverlayCRC32<10>=9C041821
OverlayCRC32<21>=E79FDDB3
OverlayCRC32<88>=8A835ABF
OverlayCRC32<92>=27B75A9A
OverlayCRC32<93>=3F071EF5
OverlayCRC32<94>=113D4CDF
OverlayCRC32<195>=FFEF510C
OverlayCRC32<204>=05A0D977
OverlayCRC32<223>=90F700C2

[White (K)]
Game=IRAK
Type=BW1
Version=0
CopyStaticPokemon=1
CopyFrom=IRAO
File<TextStrings>=<a/0/0/2, E5A5DB10>
File<TextStory>=<a/0/0/3, 0F4C32EC>
File<ItemData>=<a/0/2/4, 27AE983B>
File<InGameTrades>=<a/1/6/5, 63498C52>
NationalDexAtStartTweak=national_dex/bw1_national_dex
ShopItemOffsets=[0x5151C,0x51520,0x5152E,0x51544,0x51534,0x515A8,0x515CC,0x515DA,0x515E8,0x515F8,0x51608,0x51618,0x51628,0x51638,0x5164A,0x5165C,0x516B0,0x5166E,0x51684,0x5169A,0x516E0,0x516FC,0x5171C,0x5175C,0x5177E,0x517A2]
Arm9CRC32=16AA3F19
OverlayCRC32<10>=7A4C40D4
OverlayCRC32<21>=DD795DB3
OverlayCRC32<88>=43A68763
OverlayCRC32<92>=4410A4AC
OverlayCRC32<93>=61307C75
OverlayCRC32<94>=1AB592F2
OverlayCRC32<195>=8D5E5514
OverlayCRC32<204>=70E175AF
OverlayCRC32<223>=DF2D29AC

[Black 2 (F)]
Game=IREF
Type=BW2
Version=0
CopyStaticPokemon=1
CopyFrom=IREO
File<TextStrings>=<a/0/0/2, 05DE563F>
File<TextStory>=<a/0/0/3, E87B0632>
File<ItemData>=<a/0/2/4, 06C04911>
File<Scripts>=<a/0/5/6, 8BE998B3>
File<InGameTrades>=<a/1/6/3, 001B6DE5>
NationalDexAtStartTweak=national_dex/bw2_national_dex
MoveTutorDataOffset=0x5152C
Arm9CRC32=98ACB21A
OverlayCRC32<36>=7314EA5A
OverlayCRC32<162>=47279894
OverlayCRC32<166>=DCD888DE
OverlayCRC32<167>=D23B80B4
OverlayCRC32<168>=F6639242
OverlayCRC32<284>=112A8880
OverlayCRC32<294>=07A19A52
OverlayCRC32<316>=9A87B407

[White 2 (F)]
Game=IRDF
Type=BW2
Version=0
CopyStaticPokemon=1
CopyFrom=IRDO
File<TextStrings>=<a/0/0/2, 05DE563F>
File<TextStory>=<a/0/0/3, E87B0632>
File<ItemData>=<a/0/2/4, 06C04911>
File<Scripts>=<a/0/5/6, 8BE998B3>
File<InGameTrades>=<a/1/6/3, 001B6DE5>
NationalDexAtStartTweak=national_dex/bw2_national_dex
MoveTutorDataOffset=0x51520
Arm9CRC32=61194710
OverlayCRC32<36>=2936E726
OverlayCRC32<162>=268CD440
OverlayCRC32<166>=4375ED33
OverlayCRC32<167>=99AA7857
OverlayCRC32<168>=77FD23BD
OverlayCRC32<284>=34FAD991
OverlayCRC32<294>=8E753885
OverlayCRC32<316>=A680DF32

[Black 2 (G)]
Game=IRED
Type=BW2
Version=0
CopyStaticPokemon=1
CopyFrom=IREO
File<TextStrings>=<a/0/0/2, 0A612F1E>
File<TextStory>=<a/0/0/3, F9003A04>
File<ItemData>=<a/0/2/4, 9F69E2CB>
File<Scripts>=<a/0/5/6, 1DD429E8>
File<InGameTrades>=<a/1/6/3, 7C2D21A7>
NationalDexAtStartTweak=national_dex/bw2_national_dex
MoveTutorDataOffset=0x5155C
Arm9CRC32=8DC7E4A6
OverlayCRC32<36>=A1F1DD09
OverlayCRC32<162>=A1A6458A
OverlayCRC32<166>=A06010C3
OverlayCRC32<167>=40392337
OverlayCRC32<168>=3B1F041B
OverlayCRC32<284>=946AE3C4
OverlayCRC32<294>=4E16F546
OverlayCRC32<316>=699BD8B0

[White 2 (G)]
Game=IRDD
Type=BW2
Version=0
CopyStaticPokemon=1
CopyFrom=IRDO
File<TextStrings>=<a/0/0/2, 0A612F1E>
File<TextStory>=<a/0/0/3, F9003A04>
File<ItemData>=<a/0/2/4, 9F69E2CB>
File<Scripts>=<a/0/5/6, 1DD429E8>
File<InGameTrades>=<a/1/6/3, 7C2D21A7>
NationalDexAtStartTweak=national_dex/bw2_national_dex
MoveTutorDataOffset=0x51550
Arm9CRC32=EB769B6A
OverlayCRC32<36>=400EE8B2
OverlayCRC32<162>=A55A3E8E
OverlayCRC32<166>=B8770300
OverlayCRC32<167>=6ECDC783
OverlayCRC32<168>=D18B4E73
OverlayCRC32<284>=42022F46
OverlayCRC32<294>=AA708716
OverlayCRC32<316>=D35EA355

[Black 2 (I)]
Game=IREI
Type=BW2
Version=0
CopyStaticPokemon=1
CopyFrom=IREO
File<TextStrings>=<a/0/0/2, D2B9BE00>
File<TextStory>=<a/0/0/3, 86BD7F36>
File<ItemData>=<a/0/2/4, 454688E8>
File<Scripts>=<a/0/5/6, 76B6F0D5>
File<InGameTrades>=<a/1/6/3, 69DBC3B8>
NationalDexAtStartTweak=national_dex/bw2_national_dex
MoveTutorDataOffset=0x51554
Arm9CRC32=266EA831
OverlayCRC32<36>=96CCDDA7
OverlayCRC32<162>=49534761
OverlayCRC32<166>=2003AFAA
OverlayCRC32<167>=FC14A53F
OverlayCRC32<168>=E1C34887
OverlayCRC32<284>=122E79F7
OverlayCRC32<294>=8E83C586
OverlayCRC32<316>=96A98A1D

[White 2 (I)]
Game=IRDI
Type=BW2
Version=0
CopyStaticPokemon=1
CopyFrom=IRDO
File<TextStrings>=<a/0/0/2, D2B9BE00>
File<TextStory>=<a/0/0/3, 86BD7F36>
File<ItemData>=<a/0/2/4, 454688E8>
File<Scripts>=<a/0/5/6, 76B6F0D5>
File<InGameTrades>=<a/1/6/3, 69DBC3B8>
NationalDexAtStartTweak=national_dex/bw2_national_dex
MoveTutorDataOffset=0x51548
Arm9CRC32=35A924E0
OverlayCRC32<36>=5DF5BFD5
OverlayCRC32<162>=5C2290A5
OverlayCRC32<166>=C426EC98
OverlayCRC32<167>=5C87223F
OverlayCRC32<168>=815FC8BA
OverlayCRC32<284>=D13AF02C
OverlayCRC32<294>=0839E1F1
OverlayCRC32<316>=2D552545

[Black 2 (S)]
Game=IRES
Type=BW2
Version=0
CopyStaticPokemon=1
CopyFrom=IREO
File<TextStrings>=<a/0/0/2, A2235039>
File<TextStory>=<a/0/0/3, 149162A9>
File<ItemData>=<a/0/2/4, 08230E2F>
File<Scripts>=<a/0/5/6, 702CB68A>
File<InGameTrades>=<a/1/6/3, 57C0E599>
NationalDexAtStartTweak=national_dex/bw2_national_dex
MoveTutorDataOffset=0x5153C
Arm9CRC32=914ECB95
OverlayCRC32<36>=B7A8FDD4
OverlayCRC32<162>=8A291DF2
OverlayCRC32<166>=56E6C955
OverlayCRC32<167>=D87994A8
OverlayCRC32<168>=334A52E5
OverlayCRC32<284>=12547755
OverlayCRC32<294>=B6F92135
OverlayCRC32<316>=3585F11B

[White 2 (S)]
Game=IRDS
Type=BW2
Version=0
CopyStaticPokemon=1
CopyFrom=IRDO
File<TextStrings>=<a/0/0/2, A2235039>
File<TextStory>=<a/0/0/3, 149162A9>
File<ItemData>=<a/0/2/4, 08230E2F>
File<Scripts>=<a/0/5/6, 702CB68A>
File<InGameTrades>=<a/1/6/3, 57C0E599>
NationalDexAtStartTweak=national_dex/bw2_national_dex
MoveTutorDataOffset=0x51530
Arm9CRC32=73D0095B
OverlayCRC32<36>=D8CE4272
OverlayCRC32<162>=C3F7B172
OverlayCRC32<166>=3328105B
OverlayCRC32<167>=56AB1872
OverlayCRC32<168>=B0723FF9
OverlayCRC32<284>=49386511
OverlayCRC32<294>=4087E658
OverlayCRC32<316>=49765281

[Black 2 (J Rev 1)]
Game=IREJ
Type=BW2
Version=1
CopyFrom=IREO
File<TextStrings>=<a/0/0/2, 4E6EB3FC>
File<TextStory>=<a/0/0/3, 1C71B826>
File<PokemonGraphics>=<a/0/0/4, 56C617F3>
File<MapTableFile>=<a/0/1/2, 22F25322>
File<PokemonStats>=<a/0/1/6, 4E3C7E9C>
File<ItemData>=<a/0/2/4, C9AE07F1>
File<Scripts>=<a/0/5/6, C9C2BAF4>
File<MapFiles>=<a/1/2/6, AE4DBBE1>
File<InGameTrades>=<a/1/6/3, 2BF6A9DB>
MoveTutorDataOffset=0x512DC
StaticPokemonSupport=1
StaticPokemon{}={Species=[662:0x1DE, 662:0x240, 740:0xCD, 740:0xFC, 740:0x12C, 740:0x14C], Level=[740:0x12E, 740:0x14E]} // Cobalion
StaticPokemon{}={Species=[730:0x13A, 730:0x15F, 730:0x19B, 730:0x1BB], Level=[730:0x19D, 730:0x1BD]} // Virizion
StaticPokemon{}={Species=[948:0x45D, 948:0x48D, 948:0x4AD], Level=[948:0x48F, 948:0x4AF]} // Terrakion
StaticPokemon{}={Species=[426:0x38A, 426:0x39B, 556:0x367, 556:0x568, 556:0x5E6, 556:0x6E1, 1208:0x3A4, 1208:0xA6A, 1208:0x717], Level=[426:0x39D]} // Reshiram
StaticPokemon{}={Species=[426:0x36B, 426:0x37C, 556:0x350, 556:0x551, 556:0x5C7, 556:0x6C3, 1208:0x38D, 1208:0xA53, 1208:0x706], Level=[426:0x37E]} // Zekrom
StaticPokemon{}={Species=[1112:0x133, 1122:0x2BA, 1122:0x311, 1128:0x37A, 1128:0x3D1, 1208:0x1B7, 1208:0x1F8, 1208:0x723, 1208:0xF3D, 1208:0xF4E], Level=[1208:0xF50]} // Kyurem
StaticPokemon{}={Species=[1208:0xD8B, 1208:0xD97], Level=[1208:0xD99], Forme=[1208:0xD8D, 1208:0xD9B]} // Kyurem-Black
StaticPokemon{}={Species=[1208:0xDB6, 1208:0xDC2], Level=[1208:0xDC4], Forme=[1208:0xDB8, 1208:0xDC6]} // Kyurem-White
StaticPokemon{}={Species=[304:0xCC, 304:0x14B, 304:0x1B8, 304:0x22F, 304:0x31B, 304:0x3D6, 304:0x491, 304:0x536, 304:0x5A9, 304:0x5BA], Level=[304:0x5BC]} // Latias
StaticPokemon{}={Species=[304:0xB5, 304:0x134, 304:0x1A1, 304:0x218, 304:0x304, 304:0x3BF, 304:0x47A, 304:0x51F, 304:0x58A, 304:0x59B], Level=[304:0x59D]} // Latios
StaticPokemon{}={Species=[32:0x247, 32:0x2B0, 32:0x2C1, 1034:0x12A], Level=[32:0x2C3]} // Uxie
StaticPokemon{}={Species=[684:0x136, 684:0x1C2, 684:0x1D3, 1034:0x169], Level=[684:0x1D5]} // Mesprit
StaticPokemon{}={Species=[950:0xA1, 950:0x10A, 950:0x11B, 1034:0x1BE], Level=[950:0x11D]} // Azelf
StaticPokemon{}={Species=[1222:0x134, 1222:0x145, 1018:0x32], Level=[1222:0x147]} // Regirock
StaticPokemon{}={Species=[1224:0x134, 1224:0x145, 1018:0x2C], Level=[1224:0x147]} // Regice
StaticPokemon{}={Species=[1226:0x134, 1226:0x145, 1018:0x38], Level=[1226:0x147]} // Registeel
StaticPokemon{}={Species=[1018:0x97, 1018:0xA8], Level=[1018:0xAA]} // Regigigas
StaticPokemon{}={Species=[526:0x48D, 526:0x512, 526:0x523], Level=[526:0x525]} // Cresselia
StaticPokemon{}={Species=[1068:0x171, 1068:0x1B4, 1068:0x1C5, 1080:0x171, 1080:0x1B4, 1080:0x1C5], Level=[1068:0x1C7, 1080:0x1C7]} // Heatran
StaticPokemon{}={Species=[652:0x5C6, 652:0x5E9], Level=[652:0x5EB]} // Mandibuzz
StaticPokemon{}={Species=[1102:0x592, 1102:0x5B5], Level=[1102:0x5B7]} // Braviary
StaticPokemon{}={Species=[364:0xE, 364:0x32, 364:0x40], Level=[364:0x34, 364:0x42]} // Volcarona
StaticPokemon{}={Species=[1030:0x290, 1030:0x2A1], Level=[1030:0x2A3]} // Crustle
StaticPokemon{}={Species=[480:0xE1, 480:0x10A, 480:0x131, 480:0x15A], Level=[480:0x10C, 480:0x15C]} // Jellicent
StaticPokemon{}={Species=[1168:0x2C, 1168:0x4F], Level=[1168:0x51]} // Shiny Haxorus
StaticPokemon{}={Species=[988:0x382], Level=[988:0x386]} // Eevee
StaticPokemon{}={Species=[664:0x3B5, 664:0x3E2, 664:0x40F, 664:0x43C], Level=[664:0x3B9, 664:0x3E6, 664:0x413, 664:0x440], Forme=[664:0x3B7, 664:0x3E4, 664:0x411, 664:0x43E]} // Deerling
StaticPokemon{}={Species=[880:0xAB4, 880:0xAC7], Level=[880:0xAB8]} // Shiny Gible
StaticPokemon{}={Species=[880:0xAD3, 880:0xAE6], Level=[880:0xAD7]} // Shiny Dratini
StaticPokemon{}={Species=[54:0xDD]} // Happiny (egg)
StaticPokemon{}={Species=[526:0x27E], Level=[526:0x282]} // Magikarp
StaticPokemon{}={Species=[1253:0x5E0], Level=[1253:0x3D6]} // Cranidos
StaticPokemon{}={Species=[1253:0x5FF], Level=[1253:0x3D6]} // Shieldon
StaticPokemon{}={Species=[1253:0x61E], Level=[1253:0x3D6]} // Omanyte
StaticPokemon{}={Species=[1253:0x63D], Level=[1253:0x3D6]} // Kabuto
StaticPokemon{}={Species=[1253:0x65C], Level=[1253:0x3D6]} // Aerodactyl
StaticPokemon{}={Species=[1253:0x67B], Level=[1253:0x3D6]} // Anorith
StaticPokemon{}={Species=[1253:0x69A], Level=[1253:0x3D6]} // Lileep
StaticPokemon{}={Species=[1253:0x6B9], Level=[1253:0x3D6]} // Tirtouga
StaticPokemon{}={Species=[1253:0x6D8], Level=[1253:0x3D6]} // Archen
StaticPokemon{}={Species=[208:0x5A6], Level=[208:0x5A8]} // Zorua
StaticPokemonFakeBall{}={Species=[1273:0x45], Level=[500:0x46E, 500:0x492, 500:0x4B6, 506:0x42A, 506:0x44E]} // Foongus
StaticPokemonFakeBall{}={Species=[1273:0xC7], Level=[534:0x2F2, 534:0x316, 562:0x3FE, 562:0x422, 563:0x742, 563:0x766, 563:0x78A]} // Amoonguss
StaticEggPokemonOffsets=[29]
Arm9CRC32=F53E7838
OverlayCRC32<36>=4BF19607
OverlayCRC32<162>=35DA9C90
OverlayCRC32<166>=EBDBC1CB
OverlayCRC32<167>=0D1C66F1
OverlayCRC32<168>=CDD99E97
OverlayCRC32<284>=E1504E5D
OverlayCRC32<294>=1B490894
OverlayCRC32<316>=074A22B1

[White 2 (J Rev 1)]
Game=IRDJ
Type=BW2
Version=1
CopyStaticPokemon=1
CopyFrom=IREJ
File<WildPokemon>=<a/1/2/7, A35466D9>
File<PokedexAreaData>=<a/1/7/6, C5BE8838>
File<HabitatList>=<a/2/9/6, E578C751>
MoveTutorDataOffset=0x512D0
Arm9CRC32=6B69DD50
OverlayCRC32<36>=896946FC
OverlayCRC32<162>=DD3707A4
OverlayCRC32<166>=083CFC05
OverlayCRC32<167>=27978D30
OverlayCRC32<168>=094F1D74
OverlayCRC32<284>=337744D2
OverlayCRC32<294>=FC3CEB37
OverlayCRC32<316>=0F57993F

[Black 2 (K)]
Game=IREK
Type=BW2
Version=0
CopyStaticPokemon=1
CopyFrom=IREO
File<TextStrings>=<a/0/0/2, 88364F58>
File<TextStory>=<a/0/0/3, 52356EAD>
File<ItemData>=<a/0/2/4, E093E0EA>
File<Scripts>=<a/0/5/6, 388040F2>
File<InGameTrades>=<a/1/6/3, 91B75B3C>
NationalDexAtStartTweak=national_dex/bw2_national_dex
MoveTutorDataOffset=0x5160C
Arm9CRC32=18ED174C
OverlayCRC32<36>=AC0E0C5A
OverlayCRC32<162>=195919EB
OverlayCRC32<166>=C0D5AB8C
OverlayCRC32<167>=6FAC8636
OverlayCRC32<168>=0E04A907
OverlayCRC32<284>=4621EF7E
OverlayCRC32<294>=A678C4C6
OverlayCRC32<316>=04E41D23

[White 2 (K)]
Game=IRDK
Type=BW2
Version=0
CopyStaticPokemon=1
CopyFrom=IRDO
File<TextStrings>=<a/0/0/2, 88364F58>
File<TextStory>=<a/0/0/3, 52356EAD>
File<ItemData>=<a/0/2/4, E093E0EA>
File<Scripts>=<a/0/5/6, 388040F2>
File<InGameTrades>=<a/1/6/3, 91B75B3C>
NationalDexAtStartTweak=national_dex/bw2_national_dex
MoveTutorDataOffset=0x51600
Arm9CRC32=47B7A7F6
OverlayCRC32<36>=EACE4819
OverlayCRC32<162>=D9FE03A6
OverlayCRC32<166>=A9F8ADA1
OverlayCRC32<167>=B94A6A8C
OverlayCRC32<168>=5E4912F4
OverlayCRC32<284>=99975230
OverlayCRC32<294>=28B2F1D4
OverlayCRC32<316>=08076F8F
