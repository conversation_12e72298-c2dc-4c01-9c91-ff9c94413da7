[X]
Game=CTR-P-EKJA
TitleId=0004000000055D00
Type=XY
Acronym=X
File<WildPokemon>=<a/0/1/2, [F0617248, 52071B78]>
File<Scripts>=<a/0/3/1, [AF19B42A, AF19B42A]>
File<TrainerData>=<a/0/3/8, [7C3D4F79, 7C3D4F79]>
File<TrainerPokemon>=<a/0/4/0, [9CC8CE76, 9CC8CE76]>
File<TextStringsJaKana>=<a/0/7/2, [9BC3A6FF, 080E6174]>
File<TextStringsJaKanji>=<a/0/7/3, [F8DBB596, B6D6061D]>
File<TextStrings>=<a/0/7/4, [D82C84F1, 37523125]>
File<TextStringsFr>=<a/0/7/5, [C87137F9, 63E3E296]>
File<TextStringsIt>=<a/0/7/6, [786B8CD7, 95AC37C5]>
File<TextStringsDe>=<a/0/7/7, [BD2D89DF, B6B19DBC]>
File<TextStringsEs>=<a/0/7/8, [3DF7E17C, 74BB078A]>
File<TextStringsKo>=<a/0/7/9, [9065EEB5, 3989926C]>
File<StoryTextJaKana>=<a/0/8/0, [C4254B39, C4254B39]>
File<StoryTextJaKanji>=<a/0/8/1, [D986593F, D986593F]>
File<StoryText>=<a/0/8/2, [EFD12B9E, EFD12B9E]>
File<StoryTextFr>=<a/0/8/3, [838EBFA4, 838EBFA4]>
File<StoryTextIt>=<a/0/8/4, [44E43DB3, 44E43DB3]>
File<StoryTextDe>=<a/0/8/5, [CAF14DBA, CAF14DBA]>
File<StoryTextEs>=<a/0/8/6, [A35ADBD4, A35ADBD4]>
File<StoryTextKo>=<a/0/8/7, [370DB5E5, 370DB5E5]>
File<PokemonGraphics>=<a/0/9/3, [F739084C, F739084C]>
File<PokedexAreaData>=<a/2/0/2, [76467D4C, 76467D4C]>
File<MoveData>=<a/2/1/2, [974DAD20, 974DAD20]>
File<EggMoves>=<a/2/1/3, [DA076D1B, DA076D1B]>
File<PokemonMovesets>=<a/2/1/4, [165E653E, 165E653E]>
File<PokemonEvolutions>=<a/2/1/5, [B6F5DFA9, B6F5DFA9]>
File<MegaEvolutions>=<a/2/1/6, [FE88377C, FE88377C]>
File<PokemonStats>=<a/2/1/8, [7834E6AE, 7834E6AE]>
File<BabyPokemon>=<a/2/1/9, [8219BA02, 8219BA02]>
File<ItemData>=<a/2/2/0, [C013D028, C013D028]>
File<Battle>=<DllBattle.cro, [E0AAA768, 721F8FAC]>
File<Field>=<DllField.cro, [9ECEE09B, 555BBB72]>
File<StaticPokemon>=<DllField.cro, [9ECEE09B, 555BBB72]>
File<Intro>=<DllIntro.cro, [6C0EE4DD, 21DBFF0B]>
File<StarterDisplay>=<DllPoke3Select.cro, [2A592492, A7F697A3]>
File<Evolution>=<DllShinkaDemo.cro, [43C33CF5, B2CD2FBA]>
PokemonNamesTextOffset=80
AbilityNamesTextOffset=34
MoveNamesTextOffset=13
MoveDescriptionsTextOffset=15
ItemNamesTextOffset=96
ItemDescriptionsTextOffset=99
MapTableFileOffset=360
MapNamesTextOffset=72
StaticPokemonOffset=0xEE46C
GiftPokemonOffset=0xF805C
StarterIndices=[0,1,2,3,4,5]
StarterOffsetOffset=0xB8
StarterExtraOffset=0x10
StarterTextOffset=63
SpecificStarterTextOffsets=[1,2,3]
TrainerNamesTextOffset=21
TrainerClassesTextOffset=20
TrainerMugshotsTextOffset=22
DoublesTrainerClasses=[8, 26, 46, 52, 71, 72, 97, 99, 106]
EliteFourIndices=[187, 269, 270, 271, 276]
FieldItemsScriptNumber=17
FieldItemsOffset=0xB04
HiddenItemsScriptNumber=26
HiddenItemsOffset=0xB18
ShopItemSizes=[2, 11, 14, 17, 18, 19, 19, 19, 19, 1, 4, 10, 3, 9, 1, 1, 3, 3, 5, 5, 6, 7, 5, 5, 8, 3]
ShopCount=26
TMShops=[18,19,22,23]
RegularShops=[0,1,2,3,4,5,6,7,8,14,15]
IngameTradesTextOffset=113
IngameTradesTextExtraOffset=0
IngameTradeCount=9
TitleScreenTextOffset=85
UpdateStringOffset=25
BoxLegendaryOffsets=[2, 12]
BoxLegendaryScriptOffsets=[4658, 5430, 16798]
LinkedStaticEncounterOffsets=[1:3, 2:12]
MainGameLegendaries=[716]
RoamingLegendaryOffsets=[6, 7, 8]
FullyUpdatedVersionNumber=5232
CodeCRC32=[3778C475, BA7B14F7]

[Y]
Game=CTR-P-EK2A
TitleId=0004000000055E00
Type=XY
Acronym=Y
CopyFrom=CTR-P-EKJA
File<WildPokemon>=<a/0/1/2, [C4BF77F6, 82D947B5]>
File<PokedexAreaData>=<a/2/0/2, [B94C21A8, B94C21A8]>
File<Field>=<DllField.cro, [9575E9CD, E174F557]>
File<StaticPokemon>=<DllField.cro, [9575E9CD, E174F557]>
BoxLegendaryOffsets=[1, 3]
BoxLegendaryScriptOffsets=[4670, 5456, 17266]
MainGameLegendaries=[717]
FullyUpdatedVersionNumber=5216
CodeCRC32=[46BDAD57, 3E056C1E]

[Omega Ruby]
Game=CTR-P-ECRA
TitleId=000400000011C400
Type=ORAS
Acronym=OR
File<WildPokemon>=<a/0/1/3, [BD3DF991, BD3DF991]>
File<Scripts>=<a/0/2/9, [3F312B82, 3F312B82]>
File<TrainerData>=<a/0/3/6, [F2F2FCFE, F2F2FCFE]>
File<TrainerPokemon>=<a/0/3/8, [9C55C332, 9C55C332]>
File<TextStringsJaKana>=<a/0/7/1, [3012FCBF, 3E8C9E72]>
File<TextStringsJaKanji>=<a/0/7/2, [75638696, A64AF164]>
File<TextStrings>=<a/0/7/3, [0EF80A2A, 5ABFB959]>
File<TextStringsFr>=<a/0/7/4, [63BC908F, A6C2AFAC]>
File<TextStringsIt>=<a/0/7/5, [79E211CF, A842AC5F]>
File<TextStringsDe>=<a/0/7/6, [631C8EAA, 928D7E3B]>
File<TextStringsEs>=<a/0/7/7, [3467A16A, 0733AE30]>
File<TextStringsKo>=<a/0/7/8, [29518B34, 7B6DDFC4]>
File<StoryTextJaKana>=<a/0/7/9, [FCC62512, FCC62512]>
File<StoryTextJaKanji>=<a/0/8/0, [B5DD59B9, B5DD59B9]>
File<StoryText>=<a/0/8/1, [23B7F22E, 23B7F22E]>
File<StoryTextFr>=<a/0/8/2, [BE187B12, BE187B12]>
File<StoryTextIt>=<a/0/8/3, [F9B2A3A0, F9B2A3A0]>
File<StoryTextDe>=<a/0/8/4, [5C6D7C5B, 5C6D7C5B]>
File<StoryTextEs>=<a/0/8/5, [4F20E0AD, 4F20E0AD]>
File<StoryTextKo>=<a/0/8/6, [AFF7DD4F, AFF7DD4F]>
File<PokemonGraphics>=<a/0/9/1, [2AA511AA, 2AA511AA]>
File<PokedexAreaData>=<a/1/8/1, [0C1F0B7B, 0C1F0B7B]>
File<MoveData>=<a/1/8/9, [D17DC884, D17DC884]>
File<EggMoves>=<a/1/9/0, [243ED81A, 243ED81A]>
File<PokemonMovesets>=<a/1/9/1, [C03B6E88, C03B6E88]>
File<PokemonEvolutions>=<a/1/9/2, [21DCE891, 21DCE891]>
File<MegaEvolutions>=<a/1/9/3, [43D2B7D0, 43D2B7D0]>
File<PokemonStats>=<a/1/9/5, [ABB02044, ABB02044]>
File<BabyPokemon>=<a/1/9/6, [8219BA02, 8219BA02]>
File<ItemData>=<a/1/9/7, [06267B89, 06267B89]>
File<Battle>=<DllBattle.cro, [E78107DF, 1457FEBB]>
File<StaticPokemon>=<DllField.cro, [F929EB93, 999A83FA]>
File<StarterDisplay>=<DllPoke3Select.cro, [F4A1F22E, D8E1FE18]>
File<Evolution>=<DllShinkaDemo.cro, [53AC2B89, 5794CE31]>
PokemonNamesTextOffset=98
AbilityNamesTextOffset=37
MoveNamesTextOffset=14
MoveDescriptionsTextOffset=16
ItemNamesTextOffset=114
ItemDescriptionsTextOffset=117
MapTableFileOffset=536
MapNamesTextOffset=90
StaticPokemonOffset=0xF1B20
GiftPokemonOffset=0xF906C
StarterIndices=[0,1,2,28,29,30,31,32,33,34,35,36]
StarterOffsetOffset=0xB8
StarterExtraOffset=0
StarterTextOffset=77
SpecificStarterTextOffsets=[1,2,3]
TrainerNamesTextOffset=22
TrainerClassesTextOffset=21
TrainerMugshotsTextOffset=23
DoublesTrainerClasses=[8, 26, 46, 52, 71, 72, 97, 99, 106, 133, 173, 188, 193, 206, 210, 211, 223, 226, 231]
EliteFourIndices=[553, 554, 555, 556, 557]
FieldItemsScriptNumber=39
FieldItemsOffset=0xB64
ShopItemSizes=[3, 10, 14, 17, 18, 19, 19, 19, 19, 1, 9, 6, 4, 3, 8, 8, 3, 3, 4, 3, 6, 8, 7, 4]
ShopCount=24
TMShops=[12,15,22,23]
RegularShops=[0,1,2,3,4,5,6,7,8,9]
IngameTradesTextOffset=132
IngameTradesTextExtraOffset=18
IngameTradeCount=3
TitleScreenTextOffset=38
UpdateStringOffset=22
RayquazaEncounterNumber=28
RayquazaEncounterScriptNumber=31
LinkedStaticEncounterOffsets=[26:49, 27:50, 29:58, 80:56, 81:57, 75:76]
MainGameLegendaries=[381,383]
MegaStoneItemScriptNumber=57
FullyUpdatedVersionNumber=7280
CodeCRC32=[4D9BBCE3, B0AB0BFE]

[Alpha Sapphire]
Game=CTR-P-ECLA
TitleId=000400000011C500
Type=ORAS
Acronym=AS
CopyFrom=CTR-P-ECRA
File<WildPokemon>=<a/0/1/3, [6C80A38D, 6C80A38D]>
File<PokedexAreaData>=<a/1/8/1, [61BE522C, 61BE522C]>
File<Battle>=<DllBattle.cro, [065E1414, 40BCBB0F]>
File<StaticPokemon>=<DllField.cro, [90583160, 4273CB4D]>
File<StarterDisplay>=<DllPoke3Select.cro, [B3DA67CF, 149128E8]>
File<Evolution>=<DllShinkaDemo.cro, [9D0D192A, 0164805D]>
MainGameLegendaries=[380,382]
CodeCRC32=[72B62B7F, DDC6E452]
