[Sun]
Game=CTR-P-BNDA
TitleId=0004000000164800
Type=SM
Acronym=Su
File<MoveData>=<a/0/1/1, [1E3831AE, 1E3831AE]>
File<EggMoves>=<a/0/1/2, [0F68D82A, 9D2577C3]>
File<PokemonMovesets>=<a/0/1/3, [4DA9DF72, 4DA9DF72]>
File<PokemonEvolutions>=<a/0/1/4, [4732336F, 4732336F]>
File<MegaEvolutions>=<a/0/1/5, [4001BBAB, 4001BBAB]>
File<PokemonStats>=<a/0/1/7, [81A28B5B, 81A28B5B]>
File<BabyPokemon>=<a/0/1/8, [5929BB30, 5929BB30]>
File<ItemData>=<a/0/1/9, [E2968714, E2968714]>
File<TextStringsJaKana>=<a/0/3/0, [2F51F38A, DFE73E53]>
File<TextStringsJaKanji>=<a/0/3/1, [F7CF2BDC, C1CAF30F]>
File<TextStrings>=<a/0/3/2, [7C6240E6, A04DCEDE]>
File<TextStringsFr>=<a/0/3/3, [8D0E205C, 9035F6C6]>
File<TextStringsIt>=<a/0/3/4, [B4E7E4AC, C40DDCEA]>
File<TextStringsDe>=<a/0/3/5, [0D5BA0F6, D6DC181D]>
File<TextStringsEs>=<a/0/3/6, [8C0D486A, D84CD267]>
File<TextStringsKo>=<a/0/3/7, [9ACCE086, 8A668378]>
File<TextStringsZhSimplified>=<a/0/3/8, [F65F8202, 0F7415D1]>
File<TextStringsZhTraditional>=<a/0/3/9, [E0110F4D, 82901798]>
File<StoryText>=<a/0/4/2, [B651F900, B651F900]>
File<PokemonGraphics>=<a/0/6/2, [7B1D8B2A, 7B1D8B2A]>
File<ZoneData>=<a/0/7/7, [76C0FCE0, 76C0FCE0]>
File<WildPokemon>=<a/0/8/2, [C25E1A15, C25E1A15]>
File<WorldData>=<a/0/9/1, [8B6B447D, 8B6B447D]>
File<Scripts>=<a/0/9/2, [40809C49, 40809C49]>
File<TrainerData>=<a/1/0/5, [51FC0767, 51FC0767]>
File<TrainerPokemon>=<a/1/0/6, [2F5EFBF1, 2F5EFBF1]>
File<StaticPokemon>=<a/1/5/5, [7E3092FF, 7E3092FF]>
File<PickupData>=<a/2/6/7, [CC060249, CC060249]>
File<Battle>=<Battle.cro, [D582C507, 9EE998A6]>
File<ShopsAndTutors>=<Shop.cro, [0D4AD07B, B9244730]>
PokemonNamesTextOffset=55
AbilityNamesTextOffset=96
MoveNamesTextOffset=113
MoveDescriptionsTextOffset=112
ItemNamesTextOffset=36
ItemDescriptionsTextOffset=35
MapNamesTextOffset=67
TrainerNamesTextOffset=105
TrainerClassesTextOffset=106
StarterTextOffset=41
IngameTradesTextOffset=12
TitleScreenTextOffset=94
UpdateStringOffset=20
TotemPokemonIndices=[1,4,9,14,19,24,33,39,45]
TotemPokemonUnusedIndices=[]
AllyPokemonIndices=[8,13,17,18,23,31,32,37,38,43,44,48,49]
ShopItemSizes=[9, 11, 13, 15, 17, 19, 20, 21, 9, 4, 8, 12, 5, 4, 11, 3, 10, 6, 10, 6, 4, 5, 7, 1]
ShopCount=24
TMShops=[12,13,16,18,19]
RegularShops=[0,1,2,3,4,5,6,7]
DoublesTrainerClasses=[172, 173, 174, 174, 175, 176, 177, 178, 179, 181, 182]
EliteFourIndices=[149, 152, 153, 156, 129, 413, 414]
CosmoemEvolutionNumber=791
LinkedStaticEncounterOffsets=[112:113, 120:131, 124:130] // UBs probably need to be added to this too
MainGameLegendaries=[791]
ZygardeScriptLevelOffsets=[0x19D6, 0x19F8]
FullyUpdatedVersionNumber=2112
CodeCRC32=[DE47EF73, 2A28CFAD]

[Moon]
Game=CTR-P-BNEA
TitleId=0004000000175E00
Type=SM
Acronym=Mo
CopyFrom=CTR-P-BNDA
File<WildPokemon>=<a/0/8/3, [3F8DF023, 3F8DF023]>
CosmoemEvolutionNumber=792
MainGameLegendaries=[792]
CodeCRC32=[02948BA5, 8E4AE24C]

[Ultra Sun]
Game=CTR-P-A2AA
TitleId=00040000001B5000
Type=USUM
Acronym=US
File<MoveData>=<a/0/1/1, [E8CC46AA, E8CC46AA]>
File<EggMoves>=<a/0/1/2, [7C24766C, 7C24766C]>
File<PokemonMovesets>=<a/0/1/3, [428BEF17, 428BEF17]>
File<PokemonEvolutions>=<a/0/1/4, [14B009D5, 14B009D5]>
File<MegaEvolutions>=<a/0/1/5, [AA8FA0AF, AA8FA0AF]>
File<PokemonStats>=<a/0/1/7, [7D4D7A45, 7D4D7A45]>
File<BabyPokemon>=<a/0/1/8, [A26BE810, A26BE810]>
File<ItemData>=<a/0/1/9, [4EF71745, 4EF71745]>
File<TextStringsJaKana>=<a/0/3/0, [BB54E5FD, A23BF0A5]>
File<TextStringsJaKanji>=<a/0/3/1, [88B0CBA9, 44AE64FE]>
File<TextStrings>=<a/0/3/2, [0FB62104, C2FEF549]>
File<TextStringsFr>=<a/0/3/3, [A1B4CBA9, 78107DCE]>
File<TextStringsIt>=<a/0/3/4, [A0816130, 9B63661D]>
File<TextStringsDe>=<a/0/3/5, [739E8D75, AA481B6C]>
File<TextStringsEs>=<a/0/3/6, [FF633A6D, 643565E3]>
File<TextStringsKo>=<a/0/3/7, [09CE93FB, E15E94A2]>
File<TextStringsZhSimplified>=<a/0/3/8, [0A51E183, E00CE669]>
File<TextStringsZhTraditional>=<a/0/3/9, [09D78158, 33C386F5]>
File<StoryText>=<a/0/4/2, [2D66B8A4, 2D66B8A4]>
File<PokemonGraphics>=<a/0/6/2, [82273273, 82273273]>
File<ZoneData>=<a/0/7/7, [37A8FE41, 37A8FE41]>
File<WildPokemon>=<a/0/8/2, [7D0E8107, 7D0E8107]>
File<WorldData>=<a/0/9/1, [5869B140, 5869B140]>
File<Scripts>=<a/0/9/2, [E34345E0, 139607BF]>
File<TrainerData>=<a/1/0/6, [95274370, 95274370]>
File<TrainerPokemon>=<a/1/0/7, [D0C7992F, D0C7992F]>
File<StaticPokemon>=<a/1/5/9, [82A75234, 82A75234]>
File<PickupData>=<a/2/7/1, [CC060249, CC060249]>
File<Battle>=<Battle.cro, [1025191A, B0F91C1A]>
File<ShopsAndTutors>=<Shop.cro, [24E3E8B9, 4EE3C879]>
PokemonNamesTextOffset=60
AbilityNamesTextOffset=101
MoveNamesTextOffset=118
MoveDescriptionsTextOffset=117
ItemNamesTextOffset=40
ItemDescriptionsTextOffset=39
MapNamesTextOffset=72
TrainerNamesTextOffset=110
TrainerClassesTextOffset=111
StarterTextOffset=39
IngameTradesTextOffset=14
TitleScreenTextOffset=99
UpdateStringOffset=20
TotemPokemonIndices=[1,4,9,14,19,24,33,39,45,137,146,158,159,160,162,229,231,249]
TotemPokemonUnusedIndices=[14,19,33]
AllyPokemonIndices=[8,13,17,18,23,31,32,37,38,43,44,48,49,140,141,147,148,163,164,230,232,250]
ShopItemSizes=[9, 12, 14, 16, 18, 20, 21, 22, 9, 4, 8, 12, 5, 4, 11, 3, 5, 6, 10, 5, 4, 5, 7, 5, 8, 8, 8, 8]
ShopCount=28
TMShops=[12,13,16,18,19]
RegularShops=[0,1,2,3,4,5,6,7]
DoublesTrainerClasses=[172, 173, 174, 174, 175, 176, 177, 178, 179, 181, 182, 211, 212, 213, 214, 215]
EliteFourIndices=[149, 153, 156, 489, 494, 495, 496]
CosmoemEvolutionNumber=791
LinkedStaticEncounterOffsets=[127:128, 135:146, 139:145] // Unused SM UBs need to be added to this, probably other stuff too
MainGameLegendaries=[800]
ZygardeScriptLevelOffsets=[0x1B3E, 0x1B60]
FullyUpdatedVersionNumber=2080
CodeCRC32=[6DBB9B4D, CE281B2B]

[Ultra Moon]
Game=CTR-P-A2BA
TitleId=00040000001B5100
Type=USUM
Acronym=UM
CopyFrom=CTR-P-A2AA
File<WildPokemon>=<a/0/8/3, [0FB10C58, 0FB10C58]>
CosmoemEvolutionNumber=792
CodeCRC32=[1C3F1290, 061A17D2]
