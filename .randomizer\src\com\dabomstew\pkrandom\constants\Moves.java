package com.dabomstew.pkrandom.constants;

/*----------------------------------------------------------------------------*/
/*--  Moves.java - defines an index number constant for every Move.         --*/
/*--                                                                        --*/
/*--  Part of "Universal Pokemon Randomizer ZX" by the UPR-ZX team          --*/
/*--  Pokemon and any associated names and the like are                     --*/
/*--  trademark and (C) Nintendo 1996-2020.                                 --*/
/*--                                                                        --*/
/*--  The custom code written here is licensed under the terms of the GPL:  --*/
/*--                                                                        --*/
/*--  This program is free software: you can redistribute it and/or modify  --*/
/*--  it under the terms of the GNU General Public License as published by  --*/
/*--  the Free Software Foundation, either version 3 of the License, or     --*/
/*--  (at your option) any later version.                                   --*/
/*--                                                                        --*/
/*--  This program is distributed in the hope that it will be useful,       --*/
/*--  but WITHOUT ANY WARRANTY; without even the implied warranty of        --*/
/*--  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the          --*/
/*--  GNU General Public License for more details.                          --*/
/*--                                                                        --*/
/*--  You should have received a copy of the GNU General Public License     --*/
/*--  along with this program. If not, see <http://www.gnu.org/licenses/>.  --*/
/*----------------------------------------------------------------------------*/

public class Moves {
    // https://bulbapedia.bulbagarden.net/wiki/List_of_moves
    public static final int pound = 1;
    public static final int karateChop = 2;
    public static final int doubleSlap = 3;
    public static final int cometPunch = 4;
    public static final int megaPunch = 5;
    public static final int payDay = 6;
    public static final int firePunch = 7;
    public static final int icePunch = 8;
    public static final int thunderPunch = 9;
    public static final int scratch = 10;
    public static final int viseGrip = 11;
    public static final int guillotine = 12;
    public static final int razorWind = 13;
    public static final int swordsDance = 14;
    public static final int cut = 15;
    public static final int gust = 16;
    public static final int wingAttack = 17;
    public static final int whirlwind = 18;
    public static final int fly = 19;
    public static final int bind = 20;
    public static final int slam = 21;
    public static final int vineWhip = 22;
    public static final int stomp = 23;
    public static final int doubleKick = 24;
    public static final int megaKick = 25;
    public static final int jumpKick = 26;
    public static final int rollingKick = 27;
    public static final int sandAttack = 28;
    public static final int headbutt = 29;
    public static final int hornAttack = 30;
    public static final int furyAttack = 31;
    public static final int hornDrill = 32;
    public static final int tackle = 33;
    public static final int bodySlam = 34;
    public static final int wrap = 35;
    public static final int takeDown = 36;
    public static final int thrash = 37;
    public static final int doubleEdge = 38;
    public static final int tailWhip = 39;
    public static final int poisonSting = 40;
    public static final int twineedle = 41;
    public static final int pinMissile = 42;
    public static final int leer = 43;
    public static final int bite = 44;
    public static final int growl = 45;
    public static final int roar = 46;
    public static final int sing = 47;
    public static final int supersonic = 48;
    public static final int sonicBoom = 49;
    public static final int disable = 50;
    public static final int acid = 51;
    public static final int ember = 52;
    public static final int flamethrower = 53;
    public static final int mist = 54;
    public static final int waterGun = 55;
    public static final int hydroPump = 56;
    public static final int surf = 57;
    public static final int iceBeam = 58;
    public static final int blizzard = 59;
    public static final int psybeam = 60;
    public static final int bubbleBeam = 61;
    public static final int auroraBeam = 62;
    public static final int hyperBeam = 63;
    public static final int peck = 64;
    public static final int drillPeck = 65;
    public static final int submission = 66;
    public static final int lowKick = 67;
    public static final int counter = 68;
    public static final int seismicToss = 69;
    public static final int strength = 70;
    public static final int absorb = 71;
    public static final int megaDrain = 72;
    public static final int leechSeed = 73;
    public static final int growth = 74;
    public static final int razorLeaf = 75;
    public static final int solarBeam = 76;
    public static final int poisonPowder = 77;
    public static final int stunSpore = 78;
    public static final int sleepPowder = 79;
    public static final int petalDance = 80;
    public static final int stringShot = 81;
    public static final int dragonRage = 82;
    public static final int fireSpin = 83;
    public static final int thunderShock = 84;
    public static final int thunderbolt = 85;
    public static final int thunderWave = 86;
    public static final int thunder = 87;
    public static final int rockThrow = 88;
    public static final int earthquake = 89;
    public static final int fissure = 90;
    public static final int dig = 91;
    public static final int toxic = 92;
    public static final int confusion = 93;
    public static final int psychic = 94;
    public static final int hypnosis = 95;
    public static final int meditate = 96;
    public static final int agility = 97;
    public static final int quickAttack = 98;
    public static final int rage = 99;
    public static final int teleport = 100;
    public static final int nightShade = 101;
    public static final int mimic = 102;
    public static final int screech = 103;
    public static final int doubleTeam = 104;
    public static final int recover = 105;
    public static final int harden = 106;
    public static final int minimize = 107;
    public static final int smokescreen = 108;
    public static final int confuseRay = 109;
    public static final int withdraw = 110;
    public static final int defenseCurl = 111;
    public static final int barrier = 112;
    public static final int lightScreen = 113;
    public static final int haze = 114;
    public static final int reflect = 115;
    public static final int focusEnergy = 116;
    public static final int bide = 117;
    public static final int metronome = 118;
    public static final int mirrorMove = 119;
    public static final int selfDestruct = 120;
    public static final int eggBomb = 121;
    public static final int lick = 122;
    public static final int smog = 123;
    public static final int sludge = 124;
    public static final int boneClub = 125;
    public static final int fireBlast = 126;
    public static final int waterfall = 127;
    public static final int clamp = 128;
    public static final int swift = 129;
    public static final int skullBash = 130;
    public static final int spikeCannon = 131;
    public static final int constrict = 132;
    public static final int amnesia = 133;
    public static final int kinesis = 134;
    public static final int softBoiled = 135;
    public static final int highJumpKick = 136;
    public static final int glare = 137;
    public static final int dreamEater = 138;
    public static final int poisonGas = 139;
    public static final int barrage = 140;
    public static final int leechLife = 141;
    public static final int lovelyKiss = 142;
    public static final int skyAttack = 143;
    public static final int transform = 144;
    public static final int bubble = 145;
    public static final int dizzyPunch = 146;
    public static final int spore = 147;
    public static final int flash = 148;
    public static final int psywave = 149;
    public static final int splash = 150;
    public static final int acidArmor = 151;
    public static final int crabhammer = 152;
    public static final int explosion = 153;
    public static final int furySwipes = 154;
    public static final int bonemerang = 155;
    public static final int rest = 156;
    public static final int rockSlide = 157;
    public static final int hyperFang = 158;
    public static final int sharpen = 159;
    public static final int conversion = 160;
    public static final int triAttack = 161;
    public static final int superFang = 162;
    public static final int slash = 163;
    public static final int substitute = 164;
    public static final int struggle = 165;
    public static final int sketch = 166;
    public static final int tripleKick = 167;
    public static final int thief = 168;
    public static final int spiderWeb = 169;
    public static final int mindReader = 170;
    public static final int nightmare = 171;
    public static final int flameWheel = 172;
    public static final int snore = 173;
    public static final int curse = 174;
    public static final int flail = 175;
    public static final int conversion2 = 176;
    public static final int aeroblast = 177;
    public static final int cottonSpore = 178;
    public static final int reversal = 179;
    public static final int spite = 180;
    public static final int powderSnow = 181;
    public static final int protect = 182;
    public static final int machPunch = 183;
    public static final int scaryFace = 184;
    public static final int feintAttack = 185;
    public static final int sweetKiss = 186;
    public static final int bellyDrum = 187;
    public static final int sludgeBomb = 188;
    public static final int mudSlap = 189;
    public static final int octazooka = 190;
    public static final int spikes = 191;
    public static final int zapCannon = 192;
    public static final int foresight = 193;
    public static final int destinyBond = 194;
    public static final int perishSong = 195;
    public static final int icyWind = 196;
    public static final int detect = 197;
    public static final int boneRush = 198;
    public static final int lockOn = 199;
    public static final int outrage = 200;
    public static final int sandstorm = 201;
    public static final int gigaDrain = 202;
    public static final int endure = 203;
    public static final int charm = 204;
    public static final int rollout = 205;
    public static final int falseSwipe = 206;
    public static final int swagger = 207;
    public static final int milkDrink = 208;
    public static final int spark = 209;
    public static final int furyCutter = 210;
    public static final int steelWing = 211;
    public static final int meanLook = 212;
    public static final int attract = 213;
    public static final int sleepTalk = 214;
    public static final int healBell = 215;
    public static final int returnTheMoveNotTheKeyword = 216;
    public static final int present = 217;
    public static final int frustration = 218;
    public static final int safeguard = 219;
    public static final int painSplit = 220;
    public static final int sacredFire = 221;
    public static final int magnitude = 222;
    public static final int dynamicPunch = 223;
    public static final int megahorn = 224;
    public static final int dragonBreath = 225;
    public static final int batonPass = 226;
    public static final int encore = 227;
    public static final int pursuit = 228;
    public static final int rapidSpin = 229;
    public static final int sweetScent = 230;
    public static final int ironTail = 231;
    public static final int metalClaw = 232;
    public static final int vitalThrow = 233;
    public static final int morningSun = 234;
    public static final int synthesis = 235;
    public static final int moonlight = 236;
    public static final int hiddenPower = 237;
    public static final int crossChop = 238;
    public static final int twister = 239;
    public static final int rainDance = 240;
    public static final int sunnyDay = 241;
    public static final int crunch = 242;
    public static final int mirrorCoat = 243;
    public static final int psychUp = 244;
    public static final int extremeSpeed = 245;
    public static final int ancientPower = 246;
    public static final int shadowBall = 247;
    public static final int futureSight = 248;
    public static final int rockSmash = 249;
    public static final int whirlpool = 250;
    public static final int beatUp = 251;
    public static final int fakeOut = 252;
    public static final int uproar = 253;
    public static final int stockpile = 254;
    public static final int spitUp = 255;
    public static final int swallow = 256;
    public static final int heatWave = 257;
    public static final int hail = 258;
    public static final int torment = 259;
    public static final int flatter = 260;
    public static final int willOWisp = 261;
    public static final int memento = 262;
    public static final int facade = 263;
    public static final int focusPunch = 264;
    public static final int smellingSalts = 265;
    public static final int followMe = 266;
    public static final int naturePower = 267;
    public static final int charge = 268;
    public static final int taunt = 269;
    public static final int helpingHand = 270;
    public static final int trick = 271;
    public static final int rolePlay = 272;
    public static final int wish = 273;
    public static final int assist = 274;
    public static final int ingrain = 275;
    public static final int superpower = 276;
    public static final int magicCoat = 277;
    public static final int recycle = 278;
    public static final int revenge = 279;
    public static final int brickBreak = 280;
    public static final int yawn = 281;
    public static final int knockOff = 282;
    public static final int endeavor = 283;
    public static final int eruption = 284;
    public static final int skillSwap = 285;
    public static final int imprison = 286;
    public static final int refresh = 287;
    public static final int grudge = 288;
    public static final int snatch = 289;
    public static final int secretPower = 290;
    public static final int dive = 291;
    public static final int armThrust = 292;
    public static final int camouflage = 293;
    public static final int tailGlow = 294;
    public static final int lusterPurge = 295;
    public static final int mistBall = 296;
    public static final int featherDance = 297;
    public static final int teeterDance = 298;
    public static final int blazeKick = 299;
    public static final int mudSport = 300;
    public static final int iceBall = 301;
    public static final int needleArm = 302;
    public static final int slackOff = 303;
    public static final int hyperVoice = 304;
    public static final int poisonFang = 305;
    public static final int crushClaw = 306;
    public static final int blastBurn = 307;
    public static final int hydroCannon = 308;
    public static final int meteorMash = 309;
    public static final int astonish = 310;
    public static final int weatherBall = 311;
    public static final int aromatherapy = 312;
    public static final int fakeTears = 313;
    public static final int airCutter = 314;
    public static final int overheat = 315;
    public static final int odorSleuth = 316;
    public static final int rockTomb = 317;
    public static final int silverWind = 318;
    public static final int metalSound = 319;
    public static final int grassWhistle = 320;
    public static final int tickle = 321;
    public static final int cosmicPower = 322;
    public static final int waterSpout = 323;
    public static final int signalBeam = 324;
    public static final int shadowPunch = 325;
    public static final int extrasensory = 326;
    public static final int skyUppercut = 327;
    public static final int sandTomb = 328;
    public static final int sheerCold = 329;
    public static final int muddyWater = 330;
    public static final int bulletSeed = 331;
    public static final int aerialAce = 332;
    public static final int icicleSpear = 333;
    public static final int ironDefense = 334;
    public static final int block = 335;
    public static final int howl = 336;
    public static final int dragonClaw = 337;
    public static final int frenzyPlant = 338;
    public static final int bulkUp = 339;
    public static final int bounce = 340;
    public static final int mudShot = 341;
    public static final int poisonTail = 342;
    public static final int covet = 343;
    public static final int voltTackle = 344;
    public static final int magicalLeaf = 345;
    public static final int waterSport = 346;
    public static final int calmMind = 347;
    public static final int leafBlade = 348;
    public static final int dragonDance = 349;
    public static final int rockBlast = 350;
    public static final int shockWave = 351;
    public static final int waterPulse = 352;
    public static final int doomDesire = 353;
    public static final int psychoBoost = 354;
    public static final int roost = 355;
    public static final int gravity = 356;
    public static final int miracleEye = 357;
    public static final int wakeUpSlap = 358;
    public static final int hammerArm = 359;
    public static final int gyroBall = 360;
    public static final int healingWish = 361;
    public static final int brine = 362;
    public static final int naturalGift = 363;
    public static final int feint = 364;
    public static final int pluck = 365;
    public static final int tailwind = 366;
    public static final int acupressure = 367;
    public static final int metalBurst = 368;
    public static final int uTurn = 369;
    public static final int closeCombat = 370;
    public static final int payback = 371;
    public static final int assurance = 372;
    public static final int embargo = 373;
    public static final int fling = 374;
    public static final int psychoShift = 375;
    public static final int trumpCard = 376;
    public static final int healBlock = 377;
    public static final int wringOut = 378;
    public static final int powerTrick = 379;
    public static final int gastroAcid = 380;
    public static final int luckyChant = 381;
    public static final int meFirst = 382;
    public static final int copycat = 383;
    public static final int powerSwap = 384;
    public static final int guardSwap = 385;
    public static final int punishment = 386;
    public static final int lastResort = 387;
    public static final int worrySeed = 388;
    public static final int suckerPunch = 389;
    public static final int toxicSpikes = 390;
    public static final int heartSwap = 391;
    public static final int aquaRing = 392;
    public static final int magnetRise = 393;
    public static final int flareBlitz = 394;
    public static final int forcePalm = 395;
    public static final int auraSphere = 396;
    public static final int rockPolish = 397;
    public static final int poisonJab = 398;
    public static final int darkPulse = 399;
    public static final int nightSlash = 400;
    public static final int aquaTail = 401;
    public static final int seedBomb = 402;
    public static final int airSlash = 403;
    public static final int xScissor = 404;
    public static final int bugBuzz = 405;
    public static final int dragonPulse = 406;
    public static final int dragonRush = 407;
    public static final int powerGem = 408;
    public static final int drainPunch = 409;
    public static final int vacuumWave = 410;
    public static final int focusBlast = 411;
    public static final int energyBall = 412;
    public static final int braveBird = 413;
    public static final int earthPower = 414;
    public static final int switcheroo = 415;
    public static final int gigaImpact = 416;
    public static final int nastyPlot = 417;
    public static final int bulletPunch = 418;
    public static final int avalanche = 419;
    public static final int iceShard = 420;
    public static final int shadowClaw = 421;
    public static final int thunderFang = 422;
    public static final int iceFang = 423;
    public static final int fireFang = 424;
    public static final int shadowSneak = 425;
    public static final int mudBomb = 426;
    public static final int psychoCut = 427;
    public static final int zenHeadbutt = 428;
    public static final int mirrorShot = 429;
    public static final int flashCannon = 430;
    public static final int rockClimb = 431;
    public static final int defog = 432;
    public static final int trickRoom = 433;
    public static final int dracoMeteor = 434;
    public static final int discharge = 435;
    public static final int lavaPlume = 436;
    public static final int leafStorm = 437;
    public static final int powerWhip = 438;
    public static final int rockWrecker = 439;
    public static final int crossPoison = 440;
    public static final int gunkShot = 441;
    public static final int ironHead = 442;
    public static final int magnetBomb = 443;
    public static final int stoneEdge = 444;
    public static final int captivate = 445;
    public static final int stealthRock = 446;
    public static final int grassKnot = 447;
    public static final int chatter = 448;
    public static final int judgment = 449;
    public static final int bugBite = 450;
    public static final int chargeBeam = 451;
    public static final int woodHammer = 452;
    public static final int aquaJet = 453;
    public static final int attackOrder = 454;
    public static final int defendOrder = 455;
    public static final int healOrder = 456;
    public static final int headSmash = 457;
    public static final int doubleHit = 458;
    public static final int roarOfTime = 459;
    public static final int spacialRend = 460;
    public static final int lunarDance = 461;
    public static final int crushGrip = 462;
    public static final int magmaStorm = 463;
    public static final int darkVoid = 464;
    public static final int seedFlare = 465;
    public static final int ominousWind = 466;
    public static final int shadowForce = 467;
    public static final int honeClaws = 468;
    public static final int wideGuard = 469;
    public static final int guardSplit = 470;
    public static final int powerSplit = 471;
    public static final int wonderRoom = 472;
    public static final int psyshock = 473;
    public static final int venoshock = 474;
    public static final int autotomize = 475;
    public static final int ragePowder = 476;
    public static final int telekinesis = 477;
    public static final int magicRoom = 478;
    public static final int smackDown = 479;
    public static final int stormThrow = 480;
    public static final int flameBurst = 481;
    public static final int sludgeWave = 482;
    public static final int quiverDance = 483;
    public static final int heavySlam = 484;
    public static final int synchronoise = 485;
    public static final int electroBall = 486;
    public static final int soak = 487;
    public static final int flameCharge = 488;
    public static final int coil = 489;
    public static final int lowSweep = 490;
    public static final int acidSpray = 491;
    public static final int foulPlay = 492;
    public static final int simpleBeam = 493;
    public static final int entrainment = 494;
    public static final int afterYou = 495;
    public static final int round = 496;
    public static final int echoedVoice = 497;
    public static final int chipAway = 498;
    public static final int clearSmog = 499;
    public static final int storedPower = 500;
    public static final int quickGuard = 501;
    public static final int allySwitch = 502;
    public static final int scald = 503;
    public static final int shellSmash = 504;
    public static final int healPulse = 505;
    public static final int hex = 506;
    public static final int skyDrop = 507;
    public static final int shiftGear = 508;
    public static final int circleThrow = 509;
    public static final int incinerate = 510;
    public static final int quash = 511;
    public static final int acrobatics = 512;
    public static final int reflectType = 513;
    public static final int retaliate = 514;
    public static final int finalGambit = 515;
    public static final int bestow = 516;
    public static final int inferno = 517;
    public static final int waterPledge = 518;
    public static final int firePledge = 519;
    public static final int grassPledge = 520;
    public static final int voltSwitch = 521;
    public static final int struggleBug = 522;
    public static final int bulldoze = 523;
    public static final int frostBreath = 524;
    public static final int dragonTail = 525;
    public static final int workUp = 526;
    public static final int electroweb = 527;
    public static final int wildCharge = 528;
    public static final int drillRun = 529;
    public static final int dualChop = 530;
    public static final int heartStamp = 531;
    public static final int hornLeech = 532;
    public static final int sacredSword = 533;
    public static final int razorShell = 534;
    public static final int heatCrash = 535;
    public static final int leafTornado = 536;
    public static final int steamroller = 537;
    public static final int cottonGuard = 538;
    public static final int nightDaze = 539;
    public static final int psystrike = 540;
    public static final int tailSlap = 541;
    public static final int hurricane = 542;
    public static final int headCharge = 543;
    public static final int gearGrind = 544;
    public static final int searingShot = 545;
    public static final int technoBlast = 546;
    public static final int relicSong = 547;
    public static final int secretSword = 548;
    public static final int glaciate = 549;
    public static final int boltStrike = 550;
    public static final int blueFlare = 551;
    public static final int fieryDance = 552;
    public static final int freezeShock = 553;
    public static final int iceBurn = 554;
    public static final int snarl = 555;
    public static final int icicleCrash = 556;
    public static final int vCreate = 557;
    public static final int fusionFlare = 558;
    public static final int fusionBolt = 559;
    public static final int flyingPress = 560;
    public static final int matBlock = 561;
    public static final int belch = 562;
    public static final int rototiller = 563;
    public static final int stickyWeb = 564;
    public static final int fellStinger = 565;
    public static final int phantomForce = 566;
    public static final int trickOrTreat = 567;
    public static final int nobleRoar = 568;
    public static final int ionDeluge = 569;
    public static final int parabolicCharge = 570;
    public static final int forestsCurse = 571;
    public static final int petalBlizzard = 572;
    public static final int freezeDry = 573;
    public static final int disarmingVoice = 574;
    public static final int partingShot = 575;
    public static final int topsyTurvy = 576;
    public static final int drainingKiss = 577;
    public static final int craftyShield = 578;
    public static final int flowerShield = 579;
    public static final int grassyTerrain = 580;
    public static final int mistyTerrain = 581;
    public static final int electrify = 582;
    public static final int playRough = 583;
    public static final int fairyWind = 584;
    public static final int moonblast = 585;
    public static final int boomburst = 586;
    public static final int fairyLock = 587;
    public static final int kingsShield = 588;
    public static final int playNice = 589;
    public static final int confide = 590;
    public static final int diamondStorm = 591;
    public static final int steamEruption = 592;
    public static final int hyperspaceHole = 593;
    public static final int waterShuriken = 594;
    public static final int mysticalFire = 595;
    public static final int spikyShield = 596;
    public static final int aromaticMist = 597;
    public static final int eerieImpulse = 598;
    public static final int venomDrench = 599;
    public static final int powder = 600;
    public static final int geomancy = 601;
    public static final int magneticFlux = 602;
    public static final int happyHour = 603;
    public static final int electricTerrain = 604;
    public static final int dazzlingGleam = 605;
    public static final int celebrate = 606;
    public static final int holdHands = 607;
    public static final int babyDollEyes = 608;
    public static final int nuzzle = 609;
    public static final int holdBack = 610;
    public static final int infestation = 611;
    public static final int powerUpPunch = 612;
    public static final int oblivionWing = 613;
    public static final int thousandArrows = 614;
    public static final int thousandWaves = 615;
    public static final int landsWrath = 616;
    public static final int lightOfRuin = 617;
    public static final int originPulse = 618;
    public static final int precipiceBlades = 619;
    public static final int dragonAscent = 620;
    public static final int hyperspaceFury = 621;
    public static final int breakneckBlitzPhysical = 622;
    public static final int breakneckBlitzSpecial = 623;
    public static final int allOutPummelingPhysical = 624;
    public static final int allOutPummelingSpecial = 625;
    public static final int supersonicSkystrikePhysical = 626;
    public static final int supersonicSkystrikeSpecial = 627;
    public static final int acidDownpourPhysical = 628;
    public static final int acidDownpourSpecial = 629;
    public static final int tectonicRagePhysical = 630;
    public static final int tectonicRageSpecial = 631;
    public static final int continentalCrushPhysical = 632;
    public static final int continentalCrushSpecial = 633;
    public static final int savageSpinOutPhysical = 634;
    public static final int savageSpinOutSpecial = 635;
    public static final int neverEndingNightmarePhysical = 636;
    public static final int neverEndingNightmareSpecial = 637;
    public static final int corkscrewCrashPhysical = 638;
    public static final int corkscrewCrashSpecial = 639;
    public static final int infernoOverdrivePhysical = 640;
    public static final int infernoOverdriveSpecial = 641;
    public static final int hydroVortexPhysical = 642;
    public static final int hydroVortexSpecial = 643;
    public static final int bloomDoomPhysical = 644;
    public static final int bloomDoomSpecial = 645;
    public static final int gigavoltHavocPhysical = 646;
    public static final int gigavoltHavocSpecial = 647;
    public static final int shatteredPsychePhysical = 648;
    public static final int shatteredPsycheSpecial = 649;
    public static final int subzeroSlammerPhysical = 650;
    public static final int subzeroSlammerSpecial = 651;
    public static final int devastatingDrakePhysical = 652;
    public static final int devastatingDrakeSpecial = 653;
    public static final int blackHoleEclipsePhysical = 654;
    public static final int blackHoleEclipseSpecial = 655;
    public static final int twinkleTacklePhysical = 656;
    public static final int twinkleTackleSpecial = 657;
    public static final int catastropika = 658;
    public static final int shoreUp = 659;
    public static final int firstImpression = 660;
    public static final int banefulBunker = 661;
    public static final int spiritShackle = 662;
    public static final int darkestLariat = 663;
    public static final int sparklingAria = 664;
    public static final int iceHammer = 665;
    public static final int floralHealing = 666;
    public static final int highHorsepower = 667;
    public static final int strengthSap = 668;
    public static final int solarBlade = 669;
    public static final int leafage = 670;
    public static final int spotlight = 671;
    public static final int toxicThread = 672;
    public static final int laserFocus = 673;
    public static final int gearUp = 674;
    public static final int throatChop = 675;
    public static final int pollenPuff = 676;
    public static final int anchorShot = 677;
    public static final int psychicTerrain = 678;
    public static final int lunge = 679;
    public static final int fireLash = 680;
    public static final int powerTrip = 681;
    public static final int burnUp = 682;
    public static final int speedSwap = 683;
    public static final int smartStrike = 684;
    public static final int purify = 685;
    public static final int revelationDance = 686;
    public static final int coreEnforcer = 687;
    public static final int tropKick = 688;
    public static final int instruct = 689;
    public static final int beakBlast = 690;
    public static final int clangingScales = 691;
    public static final int dragonHammer = 692;
    public static final int brutalSwing = 693;
    public static final int auroraVeil = 694;
    public static final int sinisterArrowRaid = 695;
    public static final int maliciousMoonsault = 696;
    public static final int oceanicOperetta = 697;
    public static final int guardianOfAlola = 698;
    public static final int soulStealing7StarStrike = 699;
    public static final int stokedSparksurfer = 700;
    public static final int pulverizingPancake = 701;
    public static final int extremeEvoboost = 702;
    public static final int genesisSupernova = 703;
    public static final int shellTrap = 704;
    public static final int fleurCannon = 705;
    public static final int psychicFangs = 706;
    public static final int stompingTantrum = 707;
    public static final int shadowBone = 708;
    public static final int accelerock = 709;
    public static final int liquidation = 710;
    public static final int prismaticLaser = 711;
    public static final int spectralThief = 712;
    public static final int sunsteelStrike = 713;
    public static final int moongeistBeam = 714;
    public static final int tearfulLook = 715;
    public static final int zingZap = 716;
    public static final int naturesMadness = 717;
    public static final int multiAttack = 718;
    public static final int tenMillionVoltThunderbolt = 719;
    public static final int mindBlown = 720;
    public static final int plasmaFists = 721;
    public static final int photonGeyser = 722;
    public static final int lightThatBurnsTheSky = 723;
    public static final int searingSunrazeSmash = 724;
    public static final int menacingMoonrazeMaelstrom = 725;
    public static final int letsSnuggleForever = 726;
    public static final int splinteredStormshards = 727;
    public static final int clangorousSoulblaze = 728;
    public static final int zippyZap = 729;
    public static final int splishySplash = 730;
    public static final int floatyFall = 731;
    public static final int pikaPapow = 732;
    public static final int bouncyBubble = 733;
    public static final int buzzyBuzz = 734;
    public static final int sizzlySlide = 735;
    public static final int glitzyGlow = 736;
    public static final int baddyBad = 737;
    public static final int sappySeed = 738;
    public static final int freezyFrost = 739;
    public static final int sparklySwirl = 740;
    public static final int veeveeVolley = 741;
    public static final int doubleIronBash = 742;
    public static final int maxGuard = 743;
    public static final int dynamaxCannon = 744;
    public static final int snipeShot = 745;
    public static final int jawLock = 746;
    public static final int stuffCheeks = 747;
    public static final int noRetreat = 748;
    public static final int tarShot = 749;
    public static final int magicPowder = 750;
    public static final int dragonDarts = 751;
    public static final int teatime = 752;
    public static final int octolock = 753;
    public static final int boltBeak = 754;
    public static final int fishiousRend = 755;
    public static final int courtChange = 756;
    public static final int maxFlare = 757;
    public static final int maxFlutterby = 758;
    public static final int maxLightning = 759;
    public static final int maxStrike = 760;
    public static final int maxKnuckle = 761;
    public static final int maxPhantasm = 762;
    public static final int maxHailstorm = 763;
    public static final int maxOoze = 764;
    public static final int maxGeyser = 765;
    public static final int maxAirstream = 766;
    public static final int maxStarfall = 767;
    public static final int maxWyrmwind = 768;
    public static final int maxMindstorm = 769;
    public static final int maxRockfall = 770;
    public static final int maxQuake = 771;
    public static final int maxDarkness = 772;
    public static final int maxOvergrowth = 773;
    public static final int maxSteelspike = 774;
    public static final int clangorousSoul = 775;
    public static final int bodyPress = 776;
    public static final int decorate = 777;
    public static final int drumBeating = 778;
    public static final int snapTrap = 779;
    public static final int pyroBall = 780;
    public static final int behemothBlade = 781;
    public static final int behemothBash = 782;
    public static final int auraWheel = 783;
    public static final int breakingSwipe = 784;
    public static final int branchPoke = 785;
    public static final int overdrive = 786;
    public static final int appleAcid = 787;
    public static final int gravApple = 788;
    public static final int spiritBreak = 789;
    public static final int strangeSteam = 790;
    public static final int lifeDew = 791;
    public static final int obstruct = 792;
    public static final int falseSurrender = 793;
    public static final int meteorAssault = 794;
    public static final int eternabeam = 795;
    public static final int steelBeam = 796;
    public static final int expandingForce = 797;
    public static final int steelRoller = 798;
    public static final int scaleShot = 799;
    public static final int meteorBeam = 800;
    public static final int shellSideArm = 801;
    public static final int mistyExplosion = 802;
    public static final int grassyGlide = 803;
    public static final int risingVoltage = 804;
    public static final int terrainPulse = 805;
    public static final int skitterSmack = 806;
    public static final int burningJealousy = 807;
    public static final int lashOut = 808;
    public static final int poltergeist = 809;
    public static final int corrosiveGas = 810;
    public static final int coaching = 811;
    public static final int flipTurn = 812;
    public static final int tripleAxel = 813;
    public static final int dualWingbeat = 814;
    public static final int scorchingSands = 815;
    public static final int jungleHealing = 816;
    public static final int wickedBlow = 817;
    public static final int surgingStrikes = 818;
    public static final int thunderCage = 819;
    public static final int dragonEnergy = 820;
    public static final int freezingGlare = 821;
    public static final int fieryWrath = 822;
    public static final int thunderousKick = 823;
    public static final int glacialLance = 824;
    public static final int astralBarrage = 825;
    public static final int eerieSpell = 826;
}