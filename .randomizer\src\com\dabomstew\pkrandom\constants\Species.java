package com.dabomstew.pkrandom.constants;

/*----------------------------------------------------------------------------*/
/*--  Species.java - defines a species number constant for every Pokemon.   --*/
/*--                                                                        --*/
/*--  Part of "Universal Pokemon Randomizer ZX" by the UPR-ZX team          --*/
/*--  Pokemon and any associated names and the like are                     --*/
/*--  trademark and (C) Nintendo 1996-2020.                                 --*/
/*--                                                                        --*/
/*--  The custom code written here is licensed under the terms of the GPL:  --*/
/*--                                                                        --*/
/*--  This program is free software: you can redistribute it and/or modify  --*/
/*--  it under the terms of the GNU General Public License as published by  --*/
/*--  the Free Software Foundation, either version 3 of the License, or     --*/
/*--  (at your option) any later version.                                   --*/
/*--                                                                        --*/
/*--  This program is distributed in the hope that it will be useful,       --*/
/*--  but WITHOUT ANY WARRANTY; without even the implied warranty of        --*/
/*--  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the          --*/
/*--  GNU General Public License for more details.                          --*/
/*--                                                                        --*/
/*--  You should have received a copy of the GNU General Public License     --*/
/*--  along with this program. If not, see <http://www.gnu.org/licenses/>.  --*/
/*----------------------------------------------------------------------------*/

public class Species {
    public static final int bulbasaur = 1;
    public static final int ivysaur = 2;
    public static final int venusaur = 3;
    public static final int charmander = 4;
    public static final int charmeleon = 5;
    public static final int charizard = 6;
    public static final int squirtle = 7;
    public static final int wartortle = 8;
    public static final int blastoise = 9;
    public static final int caterpie = 10;
    public static final int metapod = 11;
    public static final int butterfree = 12;
    public static final int weedle = 13;
    public static final int kakuna = 14;
    public static final int beedrill = 15;
    public static final int pidgey = 16;
    public static final int pidgeotto = 17;
    public static final int pidgeot = 18;
    public static final int rattata = 19;
    public static final int raticate = 20;
    public static final int spearow = 21;
    public static final int fearow = 22;
    public static final int ekans = 23;
    public static final int arbok = 24;
    public static final int pikachu = 25;
    public static final int raichu = 26;
    public static final int sandshrew = 27;
    public static final int sandslash = 28;
    public static final int nidoranFemale = 29;
    public static final int nidorina = 30;
    public static final int nidoqueen = 31;
    public static final int nidoranMale = 32;
    public static final int nidorino = 33;
    public static final int nidoking = 34;
    public static final int clefairy = 35;
    public static final int clefable = 36;
    public static final int vulpix = 37;
    public static final int ninetales = 38;
    public static final int jigglypuff = 39;
    public static final int wigglytuff = 40;
    public static final int zubat = 41;
    public static final int golbat = 42;
    public static final int oddish = 43;
    public static final int gloom = 44;
    public static final int vileplume = 45;
    public static final int paras = 46;
    public static final int parasect = 47;
    public static final int venonat = 48;
    public static final int venomoth = 49;
    public static final int diglett = 50;
    public static final int dugtrio = 51;
    public static final int meowth = 52;
    public static final int persian = 53;
    public static final int psyduck = 54;
    public static final int golduck = 55;
    public static final int mankey = 56;
    public static final int primeape = 57;
    public static final int growlithe = 58;
    public static final int arcanine = 59;
    public static final int poliwag = 60;
    public static final int poliwhirl = 61;
    public static final int poliwrath = 62;
    public static final int abra = 63;
    public static final int kadabra = 64;
    public static final int alakazam = 65;
    public static final int machop = 66;
    public static final int machoke = 67;
    public static final int machamp = 68;
    public static final int bellsprout = 69;
    public static final int weepinbell = 70;
    public static final int victreebel = 71;
    public static final int tentacool = 72;
    public static final int tentacruel = 73;
    public static final int geodude = 74;
    public static final int graveler = 75;
    public static final int golem = 76;
    public static final int ponyta = 77;
    public static final int rapidash = 78;
    public static final int slowpoke = 79;
    public static final int slowbro = 80;
    public static final int magnemite = 81;
    public static final int magneton = 82;
    public static final int farfetchd = 83;
    public static final int doduo = 84;
    public static final int dodrio = 85;
    public static final int seel = 86;
    public static final int dewgong = 87;
    public static final int grimer = 88;
    public static final int muk = 89;
    public static final int shellder = 90;
    public static final int cloyster = 91;
    public static final int gastly = 92;
    public static final int haunter = 93;
    public static final int gengar = 94;
    public static final int onix = 95;
    public static final int drowzee = 96;
    public static final int hypno = 97;
    public static final int krabby = 98;
    public static final int kingler = 99;
    public static final int voltorb = 100;
    public static final int electrode = 101;
    public static final int exeggcute = 102;
    public static final int exeggutor = 103;
    public static final int cubone = 104;
    public static final int marowak = 105;
    public static final int hitmonlee = 106;
    public static final int hitmonchan = 107;
    public static final int lickitung = 108;
    public static final int koffing = 109;
    public static final int weezing = 110;
    public static final int rhyhorn = 111;
    public static final int rhydon = 112;
    public static final int chansey = 113;
    public static final int tangela = 114;
    public static final int kangaskhan = 115;
    public static final int horsea = 116;
    public static final int seadra = 117;
    public static final int goldeen = 118;
    public static final int seaking = 119;
    public static final int staryu = 120;
    public static final int starmie = 121;
    public static final int mrMime = 122;
    public static final int scyther = 123;
    public static final int jynx = 124;
    public static final int electabuzz = 125;
    public static final int magmar = 126;
    public static final int pinsir = 127;
    public static final int tauros = 128;
    public static final int magikarp = 129;
    public static final int gyarados = 130;
    public static final int lapras = 131;
    public static final int ditto = 132;
    public static final int eevee = 133;
    public static final int vaporeon = 134;
    public static final int jolteon = 135;
    public static final int flareon = 136;
    public static final int porygon = 137;
    public static final int omanyte = 138;
    public static final int omastar = 139;
    public static final int kabuto = 140;
    public static final int kabutops = 141;
    public static final int aerodactyl = 142;
    public static final int snorlax = 143;
    public static final int articuno = 144;
    public static final int zapdos = 145;
    public static final int moltres = 146;
    public static final int dratini = 147;
    public static final int dragonair = 148;
    public static final int dragonite = 149;
    public static final int mewtwo = 150;
    public static final int mew = 151;
    public static final int chikorita = 152;
    public static final int bayleef = 153;
    public static final int meganium = 154;
    public static final int cyndaquil = 155;
    public static final int quilava = 156;
    public static final int typhlosion = 157;
    public static final int totodile = 158;
    public static final int croconaw = 159;
    public static final int feraligatr = 160;
    public static final int sentret = 161;
    public static final int furret = 162;
    public static final int hoothoot = 163;
    public static final int noctowl = 164;
    public static final int ledyba = 165;
    public static final int ledian = 166;
    public static final int spinarak = 167;
    public static final int ariados = 168;
    public static final int crobat = 169;
    public static final int chinchou = 170;
    public static final int lanturn = 171;
    public static final int pichu = 172;
    public static final int cleffa = 173;
    public static final int igglybuff = 174;
    public static final int togepi = 175;
    public static final int togetic = 176;
    public static final int natu = 177;
    public static final int xatu = 178;
    public static final int mareep = 179;
    public static final int flaaffy = 180;
    public static final int ampharos = 181;
    public static final int bellossom = 182;
    public static final int marill = 183;
    public static final int azumarill = 184;
    public static final int sudowoodo = 185;
    public static final int politoed = 186;
    public static final int hoppip = 187;
    public static final int skiploom = 188;
    public static final int jumpluff = 189;
    public static final int aipom = 190;
    public static final int sunkern = 191;
    public static final int sunflora = 192;
    public static final int yanma = 193;
    public static final int wooper = 194;
    public static final int quagsire = 195;
    public static final int espeon = 196;
    public static final int umbreon = 197;
    public static final int murkrow = 198;
    public static final int slowking = 199;
    public static final int misdreavus = 200;
    public static final int unown = 201;
    public static final int wobbuffet = 202;
    public static final int girafarig = 203;
    public static final int pineco = 204;
    public static final int forretress = 205;
    public static final int dunsparce = 206;
    public static final int gligar = 207;
    public static final int steelix = 208;
    public static final int snubbull = 209;
    public static final int granbull = 210;
    public static final int qwilfish = 211;
    public static final int scizor = 212;
    public static final int shuckle = 213;
    public static final int heracross = 214;
    public static final int sneasel = 215;
    public static final int teddiursa = 216;
    public static final int ursaring = 217;
    public static final int slugma = 218;
    public static final int magcargo = 219;
    public static final int swinub = 220;
    public static final int piloswine = 221;
    public static final int corsola = 222;
    public static final int remoraid = 223;
    public static final int octillery = 224;
    public static final int delibird = 225;
    public static final int mantine = 226;
    public static final int skarmory = 227;
    public static final int houndour = 228;
    public static final int houndoom = 229;
    public static final int kingdra = 230;
    public static final int phanpy = 231;
    public static final int donphan = 232;
    public static final int porygon2 = 233;
    public static final int stantler = 234;
    public static final int smeargle = 235;
    public static final int tyrogue = 236;
    public static final int hitmontop = 237;
    public static final int smoochum = 238;
    public static final int elekid = 239;
    public static final int magby = 240;
    public static final int miltank = 241;
    public static final int blissey = 242;
    public static final int raikou = 243;
    public static final int entei = 244;
    public static final int suicune = 245;
    public static final int larvitar = 246;
    public static final int pupitar = 247;
    public static final int tyranitar = 248;
    public static final int lugia = 249;
    public static final int hoOh = 250;
    public static final int celebi = 251;
    public static final int treecko = 252;
    public static final int grovyle = 253;
    public static final int sceptile = 254;
    public static final int torchic = 255;
    public static final int combusken = 256;
    public static final int blaziken = 257;
    public static final int mudkip = 258;
    public static final int marshtomp = 259;
    public static final int swampert = 260;
    public static final int poochyena = 261;
    public static final int mightyena = 262;
    public static final int zigzagoon = 263;
    public static final int linoone = 264;
    public static final int wurmple = 265;
    public static final int silcoon = 266;
    public static final int beautifly = 267;
    public static final int cascoon = 268;
    public static final int dustox = 269;
    public static final int lotad = 270;
    public static final int lombre = 271;
    public static final int ludicolo = 272;
    public static final int seedot = 273;
    public static final int nuzleaf = 274;
    public static final int shiftry = 275;
    public static final int taillow = 276;
    public static final int swellow = 277;
    public static final int wingull = 278;
    public static final int pelipper = 279;
    public static final int ralts = 280;
    public static final int kirlia = 281;
    public static final int gardevoir = 282;
    public static final int surskit = 283;
    public static final int masquerain = 284;
    public static final int shroomish = 285;
    public static final int breloom = 286;
    public static final int slakoth = 287;
    public static final int vigoroth = 288;
    public static final int slaking = 289;
    public static final int nincada = 290;
    public static final int ninjask = 291;
    public static final int shedinja = 292;
    public static final int whismur = 293;
    public static final int loudred = 294;
    public static final int exploud = 295;
    public static final int makuhita = 296;
    public static final int hariyama = 297;
    public static final int azurill = 298;
    public static final int nosepass = 299;
    public static final int skitty = 300;
    public static final int delcatty = 301;
    public static final int sableye = 302;
    public static final int mawile = 303;
    public static final int aron = 304;
    public static final int lairon = 305;
    public static final int aggron = 306;
    public static final int meditite = 307;
    public static final int medicham = 308;
    public static final int electrike = 309;
    public static final int manectric = 310;
    public static final int plusle = 311;
    public static final int minun = 312;
    public static final int volbeat = 313;
    public static final int illumise = 314;
    public static final int roselia = 315;
    public static final int gulpin = 316;
    public static final int swalot = 317;
    public static final int carvanha = 318;
    public static final int sharpedo = 319;
    public static final int wailmer = 320;
    public static final int wailord = 321;
    public static final int numel = 322;
    public static final int camerupt = 323;
    public static final int torkoal = 324;
    public static final int spoink = 325;
    public static final int grumpig = 326;
    public static final int spinda = 327;
    public static final int trapinch = 328;
    public static final int vibrava = 329;
    public static final int flygon = 330;
    public static final int cacnea = 331;
    public static final int cacturne = 332;
    public static final int swablu = 333;
    public static final int altaria = 334;
    public static final int zangoose = 335;
    public static final int seviper = 336;
    public static final int lunatone = 337;
    public static final int solrock = 338;
    public static final int barboach = 339;
    public static final int whiscash = 340;
    public static final int corphish = 341;
    public static final int crawdaunt = 342;
    public static final int baltoy = 343;
    public static final int claydol = 344;
    public static final int lileep = 345;
    public static final int cradily = 346;
    public static final int anorith = 347;
    public static final int armaldo = 348;
    public static final int feebas = 349;
    public static final int milotic = 350;
    public static final int castform = 351;
    public static final int kecleon = 352;
    public static final int shuppet = 353;
    public static final int banette = 354;
    public static final int duskull = 355;
    public static final int dusclops = 356;
    public static final int tropius = 357;
    public static final int chimecho = 358;
    public static final int absol = 359;
    public static final int wynaut = 360;
    public static final int snorunt = 361;
    public static final int glalie = 362;
    public static final int spheal = 363;
    public static final int sealeo = 364;
    public static final int walrein = 365;
    public static final int clamperl = 366;
    public static final int huntail = 367;
    public static final int gorebyss = 368;
    public static final int relicanth = 369;
    public static final int luvdisc = 370;
    public static final int bagon = 371;
    public static final int shelgon = 372;
    public static final int salamence = 373;
    public static final int beldum = 374;
    public static final int metang = 375;
    public static final int metagross = 376;
    public static final int regirock = 377;
    public static final int regice = 378;
    public static final int registeel = 379;
    public static final int latias = 380;
    public static final int latios = 381;
    public static final int kyogre = 382;
    public static final int groudon = 383;
    public static final int rayquaza = 384;
    public static final int jirachi = 385;
    public static final int deoxys = 386;
    public static final int turtwig = 387;
    public static final int grotle = 388;
    public static final int torterra = 389;
    public static final int chimchar = 390;
    public static final int monferno = 391;
    public static final int infernape = 392;
    public static final int piplup = 393;
    public static final int prinplup = 394;
    public static final int empoleon = 395;
    public static final int starly = 396;
    public static final int staravia = 397;
    public static final int staraptor = 398;
    public static final int bidoof = 399;
    public static final int bibarel = 400;
    public static final int kricketot = 401;
    public static final int kricketune = 402;
    public static final int shinx = 403;
    public static final int luxio = 404;
    public static final int luxray = 405;
    public static final int budew = 406;
    public static final int roserade = 407;
    public static final int cranidos = 408;
    public static final int rampardos = 409;
    public static final int shieldon = 410;
    public static final int bastiodon = 411;
    public static final int burmy = 412;
    public static final int wormadam = 413;
    public static final int mothim = 414;
    public static final int combee = 415;
    public static final int vespiquen = 416;
    public static final int pachirisu = 417;
    public static final int buizel = 418;
    public static final int floatzel = 419;
    public static final int cherubi = 420;
    public static final int cherrim = 421;
    public static final int shellos = 422;
    public static final int gastrodon = 423;
    public static final int ambipom = 424;
    public static final int drifloon = 425;
    public static final int drifblim = 426;
    public static final int buneary = 427;
    public static final int lopunny = 428;
    public static final int mismagius = 429;
    public static final int honchkrow = 430;
    public static final int glameow = 431;
    public static final int purugly = 432;
    public static final int chingling = 433;
    public static final int stunky = 434;
    public static final int skuntank = 435;
    public static final int bronzor = 436;
    public static final int bronzong = 437;
    public static final int bonsly = 438;
    public static final int mimeJr = 439;
    public static final int happiny = 440;
    public static final int chatot = 441;
    public static final int spiritomb = 442;
    public static final int gible = 443;
    public static final int gabite = 444;
    public static final int garchomp = 445;
    public static final int munchlax = 446;
    public static final int riolu = 447;
    public static final int lucario = 448;
    public static final int hippopotas = 449;
    public static final int hippowdon = 450;
    public static final int skorupi = 451;
    public static final int drapion = 452;
    public static final int croagunk = 453;
    public static final int toxicroak = 454;
    public static final int carnivine = 455;
    public static final int finneon = 456;
    public static final int lumineon = 457;
    public static final int mantyke = 458;
    public static final int snover = 459;
    public static final int abomasnow = 460;
    public static final int weavile = 461;
    public static final int magnezone = 462;
    public static final int lickilicky = 463;
    public static final int rhyperior = 464;
    public static final int tangrowth = 465;
    public static final int electivire = 466;
    public static final int magmortar = 467;
    public static final int togekiss = 468;
    public static final int yanmega = 469;
    public static final int leafeon = 470;
    public static final int glaceon = 471;
    public static final int gliscor = 472;
    public static final int mamoswine = 473;
    public static final int porygonZ = 474;
    public static final int gallade = 475;
    public static final int probopass = 476;
    public static final int dusknoir = 477;
    public static final int froslass = 478;
    public static final int rotom = 479;
    public static final int uxie = 480;
    public static final int mesprit = 481;
    public static final int azelf = 482;
    public static final int dialga = 483;
    public static final int palkia = 484;
    public static final int heatran = 485;
    public static final int regigigas = 486;
    public static final int giratina = 487;
    public static final int cresselia = 488;
    public static final int phione = 489;
    public static final int manaphy = 490;
    public static final int darkrai = 491;
    public static final int shaymin = 492;
    public static final int arceus = 493;
    public static final int victini = 494;
    public static final int snivy = 495;
    public static final int servine = 496;
    public static final int serperior = 497;
    public static final int tepig = 498;
    public static final int pignite = 499;
    public static final int emboar = 500;
    public static final int oshawott = 501;
    public static final int dewott = 502;
    public static final int samurott = 503;
    public static final int patrat = 504;
    public static final int watchog = 505;
    public static final int lillipup = 506;
    public static final int herdier = 507;
    public static final int stoutland = 508;
    public static final int purrloin = 509;
    public static final int liepard = 510;
    public static final int pansage = 511;
    public static final int simisage = 512;
    public static final int pansear = 513;
    public static final int simisear = 514;
    public static final int panpour = 515;
    public static final int simipour = 516;
    public static final int munna = 517;
    public static final int musharna = 518;
    public static final int pidove = 519;
    public static final int tranquill = 520;
    public static final int unfezant = 521;
    public static final int blitzle = 522;
    public static final int zebstrika = 523;
    public static final int roggenrola = 524;
    public static final int boldore = 525;
    public static final int gigalith = 526;
    public static final int woobat = 527;
    public static final int swoobat = 528;
    public static final int drilbur = 529;
    public static final int excadrill = 530;
    public static final int audino = 531;
    public static final int timburr = 532;
    public static final int gurdurr = 533;
    public static final int conkeldurr = 534;
    public static final int tympole = 535;
    public static final int palpitoad = 536;
    public static final int seismitoad = 537;
    public static final int throh = 538;
    public static final int sawk = 539;
    public static final int sewaddle = 540;
    public static final int swadloon = 541;
    public static final int leavanny = 542;
    public static final int venipede = 543;
    public static final int whirlipede = 544;
    public static final int scolipede = 545;
    public static final int cottonee = 546;
    public static final int whimsicott = 547;
    public static final int petilil = 548;
    public static final int lilligant = 549;
    public static final int basculin = 550;
    public static final int sandile = 551;
    public static final int krokorok = 552;
    public static final int krookodile = 553;
    public static final int darumaka = 554;
    public static final int darmanitan = 555;
    public static final int maractus = 556;
    public static final int dwebble = 557;
    public static final int crustle = 558;
    public static final int scraggy = 559;
    public static final int scrafty = 560;
    public static final int sigilyph = 561;
    public static final int yamask = 562;
    public static final int cofagrigus = 563;
    public static final int tirtouga = 564;
    public static final int carracosta = 565;
    public static final int archen = 566;
    public static final int archeops = 567;
    public static final int trubbish = 568;
    public static final int garbodor = 569;
    public static final int zorua = 570;
    public static final int zoroark = 571;
    public static final int minccino = 572;
    public static final int cinccino = 573;
    public static final int gothita = 574;
    public static final int gothorita = 575;
    public static final int gothitelle = 576;
    public static final int solosis = 577;
    public static final int duosion = 578;
    public static final int reuniclus = 579;
    public static final int ducklett = 580;
    public static final int swanna = 581;
    public static final int vanillite = 582;
    public static final int vanillish = 583;
    public static final int vanilluxe = 584;
    public static final int deerling = 585;
    public static final int sawsbuck = 586;
    public static final int emolga = 587;
    public static final int karrablast = 588;
    public static final int escavalier = 589;
    public static final int foongus = 590;
    public static final int amoonguss = 591;
    public static final int frillish = 592;
    public static final int jellicent = 593;
    public static final int alomomola = 594;
    public static final int joltik = 595;
    public static final int galvantula = 596;
    public static final int ferroseed = 597;
    public static final int ferrothorn = 598;
    public static final int klink = 599;
    public static final int klang = 600;
    public static final int klinklang = 601;
    public static final int tynamo = 602;
    public static final int eelektrik = 603;
    public static final int eelektross = 604;
    public static final int elgyem = 605;
    public static final int beheeyem = 606;
    public static final int litwick = 607;
    public static final int lampent = 608;
    public static final int chandelure = 609;
    public static final int axew = 610;
    public static final int fraxure = 611;
    public static final int haxorus = 612;
    public static final int cubchoo = 613;
    public static final int beartic = 614;
    public static final int cryogonal = 615;
    public static final int shelmet = 616;
    public static final int accelgor = 617;
    public static final int stunfisk = 618;
    public static final int mienfoo = 619;
    public static final int mienshao = 620;
    public static final int druddigon = 621;
    public static final int golett = 622;
    public static final int golurk = 623;
    public static final int pawniard = 624;
    public static final int bisharp = 625;
    public static final int bouffalant = 626;
    public static final int rufflet = 627;
    public static final int braviary = 628;
    public static final int vullaby = 629;
    public static final int mandibuzz = 630;
    public static final int heatmor = 631;
    public static final int durant = 632;
    public static final int deino = 633;
    public static final int zweilous = 634;
    public static final int hydreigon = 635;
    public static final int larvesta = 636;
    public static final int volcarona = 637;
    public static final int cobalion = 638;
    public static final int terrakion = 639;
    public static final int virizion = 640;
    public static final int tornadus = 641;
    public static final int thundurus = 642;
    public static final int reshiram = 643;
    public static final int zekrom = 644;
    public static final int landorus = 645;
    public static final int kyurem = 646;
    public static final int keldeo = 647;
    public static final int meloetta = 648;
    public static final int genesect = 649;
    public static final int chespin = 650;
    public static final int quilladin = 651;
    public static final int chesnaught = 652;
    public static final int fennekin = 653;
    public static final int braixen = 654;
    public static final int delphox = 655;
    public static final int froakie = 656;
    public static final int frogadier = 657;
    public static final int greninja = 658;
    public static final int bunnelby = 659;
    public static final int diggersby = 660;
    public static final int fletchling = 661;
    public static final int fletchinder = 662;
    public static final int talonflame = 663;
    public static final int scatterbug = 664;
    public static final int spewpa = 665;
    public static final int vivillon = 666;
    public static final int litleo = 667;
    public static final int pyroar = 668;
    public static final int flabébé = 669;
    public static final int floette = 670;
    public static final int florges = 671;
    public static final int skiddo = 672;
    public static final int gogoat = 673;
    public static final int pancham = 674;
    public static final int pangoro = 675;
    public static final int furfrou = 676;
    public static final int espurr = 677;
    public static final int meowstic = 678;
    public static final int honedge = 679;
    public static final int doublade = 680;
    public static final int aegislash = 681;
    public static final int spritzee = 682;
    public static final int aromatisse = 683;
    public static final int swirlix = 684;
    public static final int slurpuff = 685;
    public static final int inkay = 686;
    public static final int malamar = 687;
    public static final int binacle = 688;
    public static final int barbaracle = 689;
    public static final int skrelp = 690;
    public static final int dragalge = 691;
    public static final int clauncher = 692;
    public static final int clawitzer = 693;
    public static final int helioptile = 694;
    public static final int heliolisk = 695;
    public static final int tyrunt = 696;
    public static final int tyrantrum = 697;
    public static final int amaura = 698;
    public static final int aurorus = 699;
    public static final int sylveon = 700;
    public static final int hawlucha = 701;
    public static final int dedenne = 702;
    public static final int carbink = 703;
    public static final int goomy = 704;
    public static final int sliggoo = 705;
    public static final int goodra = 706;
    public static final int klefki = 707;
    public static final int phantump = 708;
    public static final int trevenant = 709;
    public static final int pumpkaboo = 710;
    public static final int gourgeist = 711;
    public static final int bergmite = 712;
    public static final int avalugg = 713;
    public static final int noibat = 714;
    public static final int noivern = 715;
    public static final int xerneas = 716;
    public static final int yveltal = 717;
    public static final int zygarde = 718;
    public static final int diancie = 719;
    public static final int hoopa = 720;
    public static final int volcanion = 721;
    public static final int rowlet = 722;
    public static final int dartrix = 723;
    public static final int decidueye = 724;
    public static final int litten = 725;
    public static final int torracat = 726;
    public static final int incineroar = 727;
    public static final int popplio = 728;
    public static final int brionne = 729;
    public static final int primarina = 730;
    public static final int pikipek = 731;
    public static final int trumbeak = 732;
    public static final int toucannon = 733;
    public static final int yungoos = 734;
    public static final int gumshoos = 735;
    public static final int grubbin = 736;
    public static final int charjabug = 737;
    public static final int vikavolt = 738;
    public static final int crabrawler = 739;
    public static final int crabominable = 740;
    public static final int oricorio = 741;
    public static final int cutiefly = 742;
    public static final int ribombee = 743;
    public static final int rockruff = 744;
    public static final int lycanroc = 745;
    public static final int wishiwashi = 746;
    public static final int mareanie = 747;
    public static final int toxapex = 748;
    public static final int mudbray = 749;
    public static final int mudsdale = 750;
    public static final int dewpider = 751;
    public static final int araquanid = 752;
    public static final int fomantis = 753;
    public static final int lurantis = 754;
    public static final int morelull = 755;
    public static final int shiinotic = 756;
    public static final int salandit = 757;
    public static final int salazzle = 758;
    public static final int stufful = 759;
    public static final int bewear = 760;
    public static final int bounsweet = 761;
    public static final int steenee = 762;
    public static final int tsareena = 763;
    public static final int comfey = 764;
    public static final int oranguru = 765;
    public static final int passimian = 766;
    public static final int wimpod = 767;
    public static final int golisopod = 768;
    public static final int sandygast = 769;
    public static final int palossand = 770;
    public static final int pyukumuku = 771;
    public static final int typeNull = 772;
    public static final int silvally = 773;
    public static final int minior = 774;
    public static final int komala = 775;
    public static final int turtonator = 776;
    public static final int togedemaru = 777;
    public static final int mimikyu = 778;
    public static final int bruxish = 779;
    public static final int drampa = 780;
    public static final int dhelmise = 781;
    public static final int jangmoO = 782;
    public static final int hakamoO = 783;
    public static final int kommoO = 784;
    public static final int tapuKoko = 785;
    public static final int tapuLele = 786;
    public static final int tapuBulu = 787;
    public static final int tapuFini = 788;
    public static final int cosmog = 789;
    public static final int cosmoem = 790;
    public static final int solgaleo = 791;
    public static final int lunala = 792;
    public static final int nihilego = 793;
    public static final int buzzwole = 794;
    public static final int pheromosa = 795;
    public static final int xurkitree = 796;
    public static final int celesteela = 797;
    public static final int kartana = 798;
    public static final int guzzlord = 799;
    public static final int necrozma = 800;
    public static final int magearna = 801;
    public static final int marshadow = 802;
    public static final int poipole = 803;
    public static final int naganadel = 804;
    public static final int stakataka = 805;
    public static final int blacephalon = 806;
    public static final int zeraora = 807;
    public static final int meltan = 808;
    public static final int melmetal = 809;
    public static final int grookey = 810;
    public static final int thwackey = 811;
    public static final int rillaboom = 812;
    public static final int scorbunny = 813;
    public static final int raboot = 814;
    public static final int cinderace = 815;
    public static final int sobble = 816;
    public static final int drizzile = 817;
    public static final int inteleon = 818;
    public static final int skwovet = 819;
    public static final int greedent = 820;
    public static final int rookidee = 821;
    public static final int corvisquire = 822;
    public static final int corviknight = 823;
    public static final int blipbug = 824;
    public static final int dottler = 825;
    public static final int orbeetle = 826;
    public static final int nickit = 827;
    public static final int thievul = 828;
    public static final int gossifleur = 829;
    public static final int eldegoss = 830;
    public static final int wooloo = 831;
    public static final int dubwool = 832;
    public static final int chewtle = 833;
    public static final int drednaw = 834;
    public static final int yamper = 835;
    public static final int boltund = 836;
    public static final int rolycoly = 837;
    public static final int carkol = 838;
    public static final int coalossal = 839;
    public static final int applin = 840;
    public static final int flapple = 841;
    public static final int appletun = 842;
    public static final int silicobra = 843;
    public static final int sandaconda = 844;
    public static final int cramorant = 845;
    public static final int arrokuda = 846;
    public static final int barraskewda = 847;
    public static final int toxel = 848;
    public static final int toxtricity = 849;
    public static final int sizzlipede = 850;
    public static final int centiskorch = 851;
    public static final int clobbopus = 852;
    public static final int grapploct = 853;
    public static final int sinistea = 854;
    public static final int polteageist = 855;
    public static final int hatenna = 856;
    public static final int hattrem = 857;
    public static final int hatterene = 858;
    public static final int impidimp = 859;
    public static final int morgrem = 860;
    public static final int grimmsnarl = 861;
    public static final int obstagoon = 862;
    public static final int perrserker = 863;
    public static final int cursola = 864;
    public static final int sirfetchd = 865;
    public static final int mrRime = 866;
    public static final int runerigus = 867;
    public static final int milcery = 868;
    public static final int alcremie = 869;
    public static final int falinks = 870;
    public static final int pincurchin = 871;
    public static final int snom = 872;
    public static final int frosmoth = 873;
    public static final int stonjourner = 874;
    public static final int eiscue = 875;
    public static final int indeedee = 876;
    public static final int morpeko = 877;
    public static final int cufant = 878;
    public static final int copperajah = 879;
    public static final int dracozolt = 880;
    public static final int arctozolt = 881;
    public static final int dracovish = 882;
    public static final int arctovish = 883;
    public static final int duraludon = 884;
    public static final int dreepy = 885;
    public static final int drakloak = 886;
    public static final int dragapult = 887;
    public static final int zacian = 888;
    public static final int zamazenta = 889;
    public static final int eternatus = 890;
    public static final int kubfu = 891;
    public static final int urshifu = 892;
    public static final int zarude = 893;
    public static final int regieleki = 894;
    public static final int regidrago = 895;
    public static final int glastrier = 896;
    public static final int spectrier = 897;
    public static final int calyrex = 898;

    public static final class Gen4Formes {
        public static final int deoxysA = 494;
        public static final int deoxysD = 495;
        public static final int deoxysS = 496;
        public static final int wormadamS = 497;
        public static final int wormadamT = 498;
        public static final int giratinaO = 499;
        public static final int shayminS = 500;
        public static final int rotomH = 501;
        public static final int rotomW = 502;
        public static final int rotomFr = 503;
        public static final int rotomFa = 504;
        public static final int rotomM = 505;
    }

    public static final class Gen5Formes {
        public static final int deoxysA = 650;
        public static final int deoxysD = 651;
        public static final int deoxysS = 652;
        public static final int wormadamS = 653;
        public static final int wormadamT = 654;
        public static final int shayminS = 655;
        public static final int giratinaO = 656;
        public static final int rotomH = 657;
        public static final int rotomW = 658;
        public static final int rotomFr = 659;
        public static final int rotomFa = 660;
        public static final int rotomM = 661;
        public static final int castformF = 662;
        public static final int castformW = 663;
        public static final int castformI = 664;
        public static final int basculinB = 665;
        public static final int darmanitanZ = 666;
        public static final int meloettaP = 667;
        public static final int kyuremW = 668;
        public static final int kyuremB = 669;
        public static final int keldeoCosmetic1 = 670;
        public static final int tornadusT = 671;
        public static final int thundurusT = 672;
        public static final int landorusT = 673;
    }

    public static final class Gen6Formes {
        public static final int deoxysA = 722;
        public static final int deoxysD = 723;
        public static final int deoxysS = 724;
        public static final int wormadamS = 725;
        public static final int wormadamT = 726;
        public static final int shayminS = 727;
        public static final int giratinaO = 728;
        public static final int rotomH = 729;
        public static final int rotomW = 730;
        public static final int rotomFr = 731;
        public static final int rotomFa = 732;
        public static final int rotomM = 733;
        public static final int castformF = 734;
        public static final int castformW = 735;
        public static final int castformI = 736;
        public static final int cherrimCosmetic1 = 737;
        public static final int basculinB = 738;
        public static final int darmanitanZ = 739;
        public static final int meloettaP = 740;
        public static final int kyuremW = 741;
        public static final int kyuremB = 742;
        public static final int keldeoCosmetic1= 743;
        public static final int tornadusT = 744;
        public static final int thundurusT = 745;
        public static final int landorusT = 746;
        public static final int gengarMega = 747;
        public static final int meowsticF = 748;
        public static final int furfrouCosmetic1 = 749;
        public static final int furfrouCosmetic2 = 750;
        public static final int furfrouCosmetic3 = 751;
        public static final int furfrouCosmetic4 = 752;
        public static final int furfrouCosmetic5 = 753;
        public static final int furfrouCosmetic6 = 754;
        public static final int furfrouCosmetic7 = 755;
        public static final int furfrouCosmetic8 = 756;
        public static final int furfrouCosmetic9 = 757;
        public static final int gardevoirMega = 758;
        public static final int ampharosMega = 759;
        public static final int venusaurMega = 760;
        public static final int charizardMegaX = 761;
        public static final int charizardMegaY = 762;
        public static final int mewtwoMegaX = 763;
        public static final int mewtwoMegaY = 764;
        public static final int blazikenMega = 765;
        public static final int medichamMega = 766;
        public static final int houndoomMega = 767;
        public static final int aggronMega = 768;
        public static final int banetteMega = 769;
        public static final int tyranitarMega = 770;
        public static final int scizorMega = 771;
        public static final int pinsirMega = 772;
        public static final int aerodactylMega = 773;
        public static final int lucarioMega = 774;
        public static final int abomasnowMega = 775;
        public static final int aegislashB = 776;
        public static final int blastoiseMega = 777;
        public static final int kangaskhanMega = 778;
        public static final int gyaradosMega = 779;
        public static final int absolMega = 780;
        public static final int alakazamMega = 781;
        public static final int heracrossMega = 782;
        public static final int mawileMega = 783;
        public static final int manectricMega = 784;
        public static final int garchompMega = 785;
        public static final int latiosMega = 786;
        public static final int latiasMega = 787;
        public static final int pumpkabooCosmetic1 = 788;
        public static final int pumpkabooCosmetic2 = 789;
        public static final int pumpkabooCosmetic3 = 790;
        public static final int gourgeistCosmetic1 = 791;
        public static final int gourgeistCosmetic2 = 792;
        public static final int gourgeistCosmetic3 = 793;
        public static final int floetteCosmetic1 = 794;
        public static final int floetteCosmetic2 = 795;
        public static final int floetteCosmetic3 = 796;
        public static final int floetteCosmetic4 = 797;
        public static final int floetteE = 798;
        public static final int swampertMega = 799;
        public static final int sceptileMega = 800;
        public static final int sableyeMega = 801;
        public static final int altariaMega = 802;
        public static final int galladeMega = 803;
        public static final int audinoMega = 804;
        public static final int sharpedoMega = 805;
        public static final int slowbroMega = 806;
        public static final int steelixMega = 807;
        public static final int pidgeotMega = 808;
        public static final int glalieMega = 809;
        public static final int diancieMega = 810;
        public static final int metagrossMega = 811;
        public static final int kyogreP = 812;
        public static final int groudonP = 813;
        public static final int rayquazaMega = 814;
        public static final int pikachuCosmetic1 = 815;
        public static final int pikachuCosmetic2 = 816;
        public static final int pikachuCosmetic3 = 817;
        public static final int pikachuCosmetic4 = 818;
        public static final int pikachuCosmetic5 = 819;
        public static final int pikachuCosmetic6 = 820;
        public static final int hoopaU = 821;
        public static final int cameruptMega = 822;
        public static final int lopunnyMega = 823;
        public static final int salamenceMega = 824;
        public static final int beedrillMega = 825;
    }

    public static final class SMFormes {
        public static final int deoxysA = 803;
        public static final int deoxysD = 804;
        public static final int deoxysS = 805;
        public static final int wormadamS = 806;
        public static final int wormadamT = 807;
        public static final int shayminS = 808;
        public static final int giratinaO = 809;
        public static final int rotomH = 810;
        public static final int rotomW = 811;
        public static final int rotomFr = 812;
        public static final int rotomFa = 813;
        public static final int rotomM = 814;
        public static final int castformF = 815;
        public static final int castformW = 816;
        public static final int castformI = 817;
        public static final int cherrimCosmetic1 = 818;
        public static final int shellosCosmetic1 = 819;
        public static final int gastrodonCosmetic1 = 820;
        public static final int basculinB = 821;
        public static final int darmanitanZ = 822;
        public static final int meloettaP = 823;
        public static final int kyuremW = 824;
        public static final int kyuremB = 825;
        public static final int keldeoCosmetic1 = 826;
        public static final int tornadusT = 827;
        public static final int thundurusT = 828;
        public static final int landorusT = 829;
        public static final int gengarMega = 830;
        public static final int meowsticF = 831;
        public static final int furfrouCosmetic1 = 832;
        public static final int furfrouCosmetic2 = 833;
        public static final int furfrouCosmetic3 = 834;
        public static final int furfrouCosmetic4 = 835;
        public static final int furfrouCosmetic5 = 836;
        public static final int furfrouCosmetic6 = 837;
        public static final int furfrouCosmetic7 = 838;
        public static final int furfrouCosmetic8 = 839;
        public static final int furfrouCosmetic9 = 840;
        public static final int gardevoirMega = 841;
        public static final int ampharosMega = 842;
        public static final int venusaurMega = 843;
        public static final int charizardMegaX = 844;
        public static final int charizardMegaY = 845;
        public static final int mewtwoMegaX = 846;
        public static final int mewtwoMegaY = 847;
        public static final int blazikenMega = 848;
        public static final int medichamMega = 849;
        public static final int houndoomMega = 850;
        public static final int aggronMega = 851;
        public static final int banetteMega = 852;
        public static final int tyranitarMega = 853;
        public static final int scizorMega = 854;
        public static final int pinsirMega = 855;
        public static final int aerodactylMega = 856;
        public static final int lucarioMega = 857;
        public static final int abomasnowMega = 858;
        public static final int aegislashB = 859;
        public static final int blastoiseMega = 860;
        public static final int kangaskhanMega = 861;
        public static final int gyaradosMega = 862;
        public static final int absolMega = 863;
        public static final int alakazamMega = 864;
        public static final int heracrossMega = 865;
        public static final int mawileMega = 866;
        public static final int manectricMega = 867;
        public static final int garchompMega = 868;
        public static final int latiosMega = 869;
        public static final int latiasMega = 870;
        public static final int pumpkabooCosmetic1 = 871;
        public static final int pumpkabooCosmetic2 = 872;
        public static final int pumpkabooCosmetic3 = 873;
        public static final int gourgeistCosmetic1 = 874;
        public static final int gourgeistCosmetic2 = 875;
        public static final int gourgeistCosmetic3 = 876;
        public static final int floetteCosmetic1 = 877;
        public static final int floetteCosmetic2 = 878;
        public static final int floetteCosmetic3 = 879;
        public static final int floetteCosmetic4 = 880;
        public static final int floetteE = 881;
        public static final int swampertMega = 882;
        public static final int sceptileMega = 883;
        public static final int sableyeMega = 884;
        public static final int altariaMega = 885;
        public static final int galladeMega = 886;
        public static final int audinoMega = 887;
        public static final int sharpedoMega = 888;
        public static final int slowbroMega = 889;
        public static final int steelixMega = 890;
        public static final int pidgeotMega = 891;
        public static final int glalieMega = 892;
        public static final int diancieMega = 893;
        public static final int metagrossMega = 894;
        public static final int kyogreP = 895;
        public static final int groudonP = 896;
        public static final int rayquazaMega = 897;
        public static final int hoopaU = 898;
        public static final int cameruptMega = 899;
        public static final int lopunnyMega = 900;
        public static final int salamenceMega = 901;
        public static final int beedrillMega = 902;
        public static final int wishiwashiS = 903;
        public static final int oricorioE = 904;
        public static final int oricorioP = 905;
        public static final int oricorioG = 906;
        public static final int lycanrocM = 907;
        public static final int rattataA = 908;
        public static final int raticateA = 909;
        public static final int raticateACosmetic1 = 910;
        public static final int raichuA = 911;
        public static final int sandshrewA = 912;
        public static final int sandslashA = 913;
        public static final int vulpixA = 914;
        public static final int ninetalesA = 915;
        public static final int meowthA = 916;
        public static final int persianA = 917;
        public static final int geodudeA = 918;
        public static final int gravelerA = 919;
        public static final int golemA = 920;
        public static final int grimerA = 921;
        public static final int mukA = 922;
        public static final int exeggutorA = 923;
        public static final int marowakA = 924;
        public static final int greninjaCosmetic1 = 925;
        public static final int greninjaA = 926;
        public static final int zygarde10 = 927;
        public static final int zygarde10Cosmetic1 = 928;
        public static final int zygardeCosmetic1 = 929;
        public static final int zygardeC = 930;
        public static final int miniorCosmetic1 = 931;
        public static final int miniorCosmetic2 = 932;
        public static final int miniorCosmetic3 = 933;
        public static final int miniorCosmetic4 = 934;
        public static final int miniorCosmetic5 = 935;
        public static final int miniorCosmetic6 = 936;
        public static final int miniorC = 937;
        public static final int miniorCCosmetic1 = 938;
        public static final int miniorCCosmetic2 = 939;
        public static final int miniorCCosmetic3 = 940;
        public static final int miniorCCosmetic4 = 941;
        public static final int miniorCCosmetic5 = 942;
        public static final int miniorCCosmetic6 = 943;
        public static final int diglettA = 944;
        public static final int dugtrioA = 945;
        public static final int mimikyuCosmetic1 = 946;
        public static final int mimikyuCosmetic2 = 947;
        public static final int mimikyuCosmetic3 = 948;
        public static final int magearnaCosmetic1 = 949;
        public static final int pikachuCosmetic1 = 950;
        public static final int pikachuCosmetic2 = 951;
        public static final int pikachuCosmetic3 = 952;
        public static final int pikachuCosmetic4 = 953;
        public static final int pikachuCosmetic5 = 954;
        public static final int pikachuCosmetic6 = 955;
        public static final int gumshoosCosmetic1 = 956;
        public static final int vikavoltCosmetic1 = 957;
        public static final int lurantisCosmetic1 = 958;
        public static final int salazzleCosmetic1 = 959;
        public static final int kommoOCosmetic1 = 960;
    }

    public static final class USUMFormes {
        public static final int deoxysA = 808;
        public static final int deoxysD = 809;
        public static final int deoxysS = 810;
        public static final int wormadamS = 811;
        public static final int wormadamT = 812;
        public static final int shayminS = 813;
        public static final int giratinaO = 814;
        public static final int rotomH = 815;
        public static final int rotomW = 816;
        public static final int rotomFr = 817;
        public static final int rotomFa = 818;
        public static final int rotomM = 819;
        public static final int castformF = 820;
        public static final int castformW = 821;
        public static final int castformI = 822;
        public static final int cherrimCosmetic1 = 823;
        public static final int shellosCosmetic1 = 824;
        public static final int gastrodonCosmetic1 = 825;
        public static final int basculinB = 826;
        public static final int darmanitanZ = 827;
        public static final int meloettaP = 828;
        public static final int kyuremW = 829;
        public static final int kyuremB = 830;
        public static final int keldeoCosmetic1 = 831;
        public static final int tornadusT = 832;
        public static final int thundurusT = 833;
        public static final int landorusT = 834;
        public static final int gengarMega = 835;
        public static final int meowsticF = 836;
        public static final int furfrouCosmetic1 = 837;
        public static final int furfrouCosmetic2 = 838;
        public static final int furfrouCosmetic3 = 839;
        public static final int furfrouCosmetic4 = 840;
        public static final int furfrouCosmetic5 = 841;
        public static final int furfrouCosmetic6 = 842;
        public static final int furfrouCosmetic7 = 843;
        public static final int furfrouCosmetic8 = 844;
        public static final int furfrouCosmetic9 = 845;
        public static final int gardevoirMega = 846;
        public static final int ampharosMega = 847;
        public static final int venusaurMega = 848;
        public static final int charizardMegaX = 849;
        public static final int charizardMegaY = 850;
        public static final int mewtwoMegaX = 851;
        public static final int mewtwoMegaY = 852;
        public static final int blazikenMega = 853;
        public static final int medichamMega = 854;
        public static final int houndoomMega = 855;
        public static final int aggronMega = 856;
        public static final int banetteMega = 857;
        public static final int tyranitarMega = 858;
        public static final int scizorMega = 859;
        public static final int pinsirMega = 860;
        public static final int aerodactylMega = 861;
        public static final int lucarioMega = 862;
        public static final int abomasnowMega = 863;
        public static final int aegislashB = 864;
        public static final int blastoiseMega = 865;
        public static final int kangaskhanMega = 866;
        public static final int gyaradosMega = 867;
        public static final int absolMega = 868;
        public static final int alakazamMega = 869;
        public static final int heracrossMega = 870;
        public static final int mawileMega = 871;
        public static final int manectricMega = 872;
        public static final int garchompMega = 873;
        public static final int latiosMega = 874;
        public static final int latiasMega = 875;
        public static final int pumpkabooCosmetic1 = 876;
        public static final int pumpkabooCosmetic2 = 877;
        public static final int pumpkabooCosmetic3 = 878;
        public static final int gourgeistCosmetic1 = 879;
        public static final int gourgeistCosmetic2 = 880;
        public static final int gourgeistCosmetic3 = 881;
        public static final int floetteCosmetic1 = 882;
        public static final int floetteCosmetic2 = 883;
        public static final int floetteCosmetic3 = 884;
        public static final int floetteCosmetic4 = 885;
        public static final int floetteE = 886;
        public static final int swampertMega = 887;
        public static final int sceptileMega = 888;
        public static final int sableyeMega = 889;
        public static final int altariaMega = 890;
        public static final int galladeMega = 891;
        public static final int audinoMega = 892;
        public static final int sharpedoMega = 893;
        public static final int slowbroMega = 894;
        public static final int steelixMega = 895;
        public static final int pidgeotMega = 896;
        public static final int glalieMega = 897;
        public static final int diancieMega = 898;
        public static final int metagrossMega = 899;
        public static final int kyogreP = 900;
        public static final int groudonP = 901;
        public static final int rayquazaMega = 902;
        public static final int hoopaU = 903;
        public static final int cameruptMega = 904;
        public static final int lopunnyMega = 905;
        public static final int salamenceMega = 906;
        public static final int beedrillMega = 907;
        public static final int wishiwashiS = 908;
        public static final int oricorioE = 909;
        public static final int oricorioP = 910;
        public static final int oricorioG = 911;
        public static final int lycanrocM = 912;
        public static final int lycanrocD = 913;
        public static final int rattataA = 914;
        public static final int raticateA = 915;
        public static final int raticateACosmetic1 = 916;
        public static final int raichuA = 917;
        public static final int sandshrewA = 918;
        public static final int sandslashA = 919;
        public static final int vulpixA = 920;
        public static final int ninetalesA = 921;
        public static final int meowthA = 922;
        public static final int persianA = 923;
        public static final int geodudeA = 924;
        public static final int gravelerA = 925;
        public static final int golemA = 926;
        public static final int grimerA = 927;
        public static final int mukA = 928;
        public static final int exeggutorA = 929;
        public static final int marowakA = 930;
        public static final int marowakACosmetic1 = 931;
        public static final int greninjaCosmetic1 = 932;
        public static final int greninjaA = 933;
        public static final int zygarde10 = 934;
        public static final int zygarde10Cosmetic1 = 935;
        public static final int zygardeCosmetic1 = 936;
        public static final int zygardeC = 937;
        public static final int miniorCosmetic1 = 938;
        public static final int miniorCosmetic2 = 939;
        public static final int miniorCosmetic3 = 940;
        public static final int miniorCosmetic4 = 941;
        public static final int miniorCosmetic5 = 942;
        public static final int miniorCosmetic6 = 943;
        public static final int miniorC = 944;
        public static final int miniorCCosmetic1 = 945;
        public static final int miniorCCosmetic2 = 946;
        public static final int miniorCCosmetic3 = 947;
        public static final int miniorCCosmetic4 = 948;
        public static final int miniorCCosmetic5 = 949;
        public static final int miniorCCosmetic6 = 950;
        public static final int diglettA = 951;
        public static final int dugtrioA = 952;
        public static final int mimikyuCosmetic1 = 953;
        public static final int mimikyuCosmetic2 = 954;
        public static final int mimikyuCosmetic3 = 955;
        public static final int magearnaCosmetic1 = 956;
        public static final int pikachuCosmetic1 = 957;
        public static final int pikachuCosmetic2 = 958;
        public static final int pikachuCosmetic3 = 959;
        public static final int pikachuCosmetic4 = 960;
        public static final int pikachuCosmetic5 = 961;
        public static final int pikachuCosmetic6 = 962;
        public static final int pikachuCosmetic7 = 963;
        public static final int gumshoosCosmetic1 = 964;
        public static final int vikavoltCosmetic1 = 965;
        public static final int lurantisCosmetic1 = 966;
        public static final int salazzleCosmetic1 = 967;
        public static final int kommoOCosmetic1 = 968;
        public static final int necrozmaDM = 969;
        public static final int necrozmaDW = 970;
        public static final int necrozmaU = 971;
        public static final int araquanidCosmetic1 = 972;
        public static final int togedemaruCosmetic1 = 973;
        public static final int ribombeeCosmetic1 = 974;
        public static final int rockruffCosmetic1 = 975;

    }
}
