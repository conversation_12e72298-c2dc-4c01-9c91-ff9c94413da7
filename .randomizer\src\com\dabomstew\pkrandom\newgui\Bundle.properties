# To change this template, choose Too<PERSON> | Templates
# and open the template in the editor.

GUI.versionLabel.text=Randomizer ZX Version %s
GUI.generalOptionsPanel.title=General Options
GUI.limitPokemonCheckBox.text=Limit Pokemon
GUI.limitPokemonCheckBox.toolTipText=<html>Select this to allow yourself to limit the Pokemon used by the randomization.<br /><b>If this box isn't checked all Pokemon will be allowed.</b>
GUI.raceModeCheckBox.toolTipText=<html>Select this to enable certain things which are useful for a speedrun race of the ROM you create.<br />The ability to save a log file will be disabled, and a check value will be generated.<br />You can send this value around with the preset file to ensure that everyone has the same ROM to race with.
GUI.raceModeCheckBox.text=Race Mode
GUI.noIrregularAltFormesCheckBox.text=No Irregular Alt Formes
GUI.noIrregularAltFormesCheckBox.toolTipText=<html>Bans "irregular" alternate formes from the Pokemon pool when "Allow Alternate Formes" is selected for Wild Pokemon, Trainer Pokemon, Evolutions, etc.<br />Irregular formes are those that normally cannot exist outside of battle (such as Mega Evolutions and other in-battle transformations like Darmanitan-Z, Zygarde-C, etc),<br />as well as the "Fusion" Pokemon (Kyurem-B/W and Necrozma-DM/DW).<br />These alternate formes still exist and will get randomized base stats, abilities and so on (according to your settings),<br />but they can only be accessed through their regular means (such as by Mega Evolving or transforming because of an Ability).
GUI.romInformationPanel.title=ROM Information
GUI.noRomLoaded=NO ROM LOADED
GUI.openROMButton.toolTipText=
GUI.openROMButton.text=Open ROM
GUI.randomizeSaveButton.text=Randomize (Save)
GUI.premadeSeedButton.text=Premade Seed
GUI.settingsButton.toolTipText=
GUI.settingsButton.text=Settings
GUI.loadSettingsButton.toolTipText=<html>Clicking this button allows you to load predefined settings from a file.<br />If the file was created with a game which has less randomization options<br />than the one you are randomizing now, those options will be set to "Unchanged"/Off.<br />If you load from a newer game, everything that isn't supported will be ignored.
GUI.loadSettingsButton.text=Load Settings
GUI.saveSettingsButton.toolTipText=<html>Clicking this will allow you to save the current randomization settings as a file.<br />You can then load these settings when you are randomizing any ROM.<br />The way in which ROMs with more or less features than the current one<br />  are handled is described when you hover over the "Load Settings" button.
GUI.saveSettingsButton.text=Save Settings
GUI.websiteLinkLabel.text=<html><a href="https://github.com/Ajarmar/universal-pokemon-randomizer-zx/releases">Release page (latest version: %s)</a>
GUI.pokemonTraitsPanel.title=Pokemon Traits
GUI.pbsPanel.title=Pokemon Base Statistics
GUI.pbsUnchangedRadioButton.toolTipText=Don't change Pokemon stats from the base at all.
GUI.pbsUnchangedRadioButton.text=Unchanged
GUI.pbsShuffleRadioButton.toolTipText=<html>Shuffle each Pokemon's stats.<br />For example, its base Attack may be swapped with its base Special Attack, etc.<br />This does not make any Pokemon stronger or weaker.
GUI.pbsShuffleRadioButton.text=Shuffle
GUI.pbsRandomRadioButton.toolTipText=<html>Randomizes each Pokemon's stats, as long as they fall within the original base stat total.<br />This could make Pokemon stronger or weaker if they get unlucky or lucky rolls on stats they need.<br />Evolutions of a Pokemon will be treated separately from that Pokemon unless you check Follow Evolutions.
GUI.pbsRandomRadioButton.text=Random
GUI.pbsLegendariesSlowRadioButton.toolTipText=All legendaries get the "Slow" EXP Curve.
GUI.pbsLegendariesSlowRadioButton.text=Legendaries: Slow
GUI.pbsStrongLegendariesSlowRadioButton.toolTipText=<html>Strong legendaries get the "Slow" EXP Curve.<br />This includes all legendaries with >600 BST.
GUI.pbsStrongLegendariesSlowRadioButton.text=Strong Legendaries: Slow
GUI.pbsAllMediumFastRadioButton.toolTipText=All Pokemon get the selected EXP Curve.
GUI.pbsAllMediumFastRadioButton.text=All Pokemon
GUI.pbsStandardizeEXPCurvesCheckBox.toolTipText=<html>When this is selected, every Pokemon's EXP curve will be changed to the selected EXP curve below,<br />except for the Pokemon forced to use the "Slow" EXP curve, chosen with the settings to the right.<br />This will cause Pokemon to be better or worse based more on their stats/moves/type rather than the difficulty of leveling them;<br />it will also allow for more potential evolutions if you randomize evolutions, since a Pokemon only can evolve into a Pokemon with the same EXP curve.<br /><br />If you're not sure which EXP Curve to pick, Medium Fast and Medium Slow are generally good curves (most Pokemon use these in the regular games).
GUI.pbsStandardizeEXPCurvesCheckBox.text=Standardize EXP Curves to:
GUI.pbsFollowEvolutionsCheckBox.toolTipText=<html>When this is selected and base stats are shuffled or randomized, evolutions of a Pokemon will inherit from their base form's rolls.<br />Shuffle: The same new ordering of stats will be used.<br />Randomized: The evolution will have the same proportion of stats as the base form.
GUI.pbsFollowEvolutionsCheckBox.text=Follow Evolutions
GUI.pbsUpdateBaseStatsCheckBox.toolTipText=<html>Select this to update the base stats of Pokemon in the loaded game to their values in the generation chosen to the right.<br />This does NOT:<br />* Add any new Pokemon to the game<br />* Split Special Attack and Special Defense in Gen 1.
GUI.pbsUpdateBaseStatsCheckBox.text=Update Base Stats to Generation:
GUI.ptPanel.title=Pokemon Types
GUI.ptUnchangedRadioButton.toolTipText=Don't change Pokemon types at all.
GUI.ptUnchangedRadioButton.text=Unchanged
GUI.ptRandomFollowEvolutionsRadioButton.toolTipText=<html>Randomize the types of each Pokemon, but make most evolutions copy the base Pokemon (except perhaps adding an extra secondary type).<br />Evolutions that don't copy types are the Eeveelutions, among others.
GUI.ptRandomFollowEvolutionsRadioButton.text=Random (follow evolutions)
GUI.ptRandomCompletelyRadioButton.toolTipText=<html>Randomize Pokemon types completely.<br />Evolutions of a Pokemon are completely separate from that Pokemon, so the type of a Pokemon will likely completely change every evolution.
GUI.ptRandomCompletelyRadioButton.text=Random (completely)
GUI.ptIsDualTypeCheckBox.text=Force Dual Typing
GUI.paPanel.title=Pokemon Abilities
GUI.paUnchangedRadioButton.toolTipText=Don't change Pokemon abilities from the base at all.
GUI.paUnchangedRadioButton.text=Unchanged
GUI.paRandomRadioButton.toolTipText=<html>Give each Pokemon new abilities.<br />Each Pokemon will have a base ability, and a 50% chance of having a 2nd ability different from the first.<br />(50% of the species will have this ability if it is made).<br />In Generation 5 games, each Pokemon will also receive a new random "Dream World" ability.<br />Pokemon such as Shedinja which have Wonder Guard as their ability will keep it to maintain balance.
GUI.paRandomRadioButton.text=Random
GUI.paAllowWonderGuardCheckBox.toolTipText=<html>If this is checked, Wonder Guard will be able to be chosen as any Pokemon's ability.<br />This can lead to some very overpowered/broken Pokemon.<br />USE WITH CAUTION!
GUI.paAllowWonderGuardCheckBox.text=Allow Wonder Guard
GUI.paFollowEvolutionsCheckBox.toolTipText=<html>When this is selected and abilities are randomized, non-split evolutions of a Pokemon will inherit that Pokemon's random abilities.
GUI.paFollowEvolutionsCheckBox.text=Follow Evolutions
GUI.paBanLabel.text=Ban...
GUI.paTrappingAbilitiesCheckBox.toolTipText=<html>When abilities are randomized, ban abilities that can prevent the opponent from fleeing/switching out from being selected.<br />\nThis bans Arena Trap, Magnet Pull and Shadow Tag.<br />\nHas no effect if abilities are not randomized.<br />\nPokemon that have a trapping ability to begin with will still have their abilities randomized, unlike how Wonder Guard is treated.
GUI.paTrappingAbilitiesCheckBox.text=Trapping Abilities
GUI.paNegativeAbilitiesCheckBox.toolTipText=<html>When abilities are randomized, ban abilities that are purely negative in nature from being chosen.<br />\nThis bans Defeatist, Slow Start, Truant, Klutz, Stall.<br />\nHas no effect if abilities are not randomized.<br />\nPokemon that have a negative ability to begin with will still have their abilities randomized, unlike how Wonder Guard is treated.
GUI.paNegativeAbilitiesCheckBox.text=Negative Abilities
GUI.paBadAbilitiesCheckBox.toolTipText=<html>When abilities are randomized, ban bad abilities.<br />\nThis bans Minus, Plus, Anticipation, Forewarn, Frisk, Honey Gather, Aura Break, Receiver, Power of Alchemy.<br />If "Double Battle Mode" for Trainers is not selected, it also bans Friend Guard, Healer, Telepathy, Symbiosis, Battery.<br />\nHas no effect if abilities are not randomized.
GUI.paBadAbilitiesCheckBox.text=Bad Abilities
GUI.paEnsureTwoAbilitiesCheckbox.toolTipText=<html>When abilities are randomized, ensure each Pokemon has two abilities.
GUI.paEnsureTwoAbilitiesCheckbox.text=Ensure Two Abilities
GUI.pePanel.title=Pokemon Evolutions
GUI.peUnchangedRadioButton.toolTipText=Don't randomize Pokemon evolutions.
GUI.peUnchangedRadioButton.text=Unchanged
GUI.peRandomRadioButton.toolTipText=<html>Randomize which species each Pokemon evolves into.<br />\nEvolutions will be mostly unrestricted unless you check some boxes to the right, but the EXP curves of the Pokemon must match.
GUI.peRandomRadioButton.text=Random
GUI.peRandomEveryLevelRadioButton.toolTipText=<html>Randomize which species each Pokemon evolves into while forcing them to evolve every single level.<br />All existing evolutions for a given Pokemon will be removed and replaced with a single Level 1 evolution.<br />Evolutions will be mostly unrestricted unless you check some boxes to the right, but the EXP curves of the Pokemon must match.<br />Evolution "loops" are expected and intended with this setting.
GUI.peRandomEveryLevelRadioButton.text=Random Every Level
GUI.peSimilarStrengthCheckBox.toolTipText=<html>When this is checked, random evolutions will prefer Pokemon with a similar BST to the original evolution target.<br />Has less precedence than all other modifiers below, so it may not always be strictly enforced.
GUI.peSimilarStrengthCheckBox.text=Similar Strength
GUI.peSameTypingCheckBox.toolTipText=<html>When selected, randomized evolutions will share at least one type between the source and target if possible.
GUI.peSameTypingCheckBox.text=Same Typing
GUI.peLimitEvolutionsToThreeCheckBox.toolTipText=<html>When selected, there will not be any evolution chains created that are longer than three stages.
GUI.peLimitEvolutionsToThreeCheckBox.text=Limit Evolutions to Three Stages
GUI.peForceChangeCheckBox.toolTipText=<html>When selected, every evolution of a Pokemon will be randomized to something different than what it was originally.
GUI.peForceChangeCheckBox.text=Force Change
GUI.peChangeImpossibleEvosCheckBox.toolTipText=<html>If this is checked, every evolution that isn't possible to do without trading in the current game will be changed.<br /><b>Takes effect regardless of whether evolutions are randomized or not.</b><br />Some of the types that will be changed are:<ul><li>"Normal" trade evolutions<li>Trade evolutions with another condition, such as held item or Pokemon traded for<li>Day/night evolutions if there isn't day/night in the game<li>Contest-stat evolutions if there aren't contests in the game<li>Location-based evolutions, if those locations don't exist in the game<li>Move-based evolutions, <b>only if you randomize movesets</b></ul>
GUI.peChangeImpossibleEvosCheckBox.text=Change Impossible Evolutions
GUI.peMakeEvolutionsEasierCheckBox.toolTipText=<html>If this is checked, Pokemon that evolve at a very high level will evolve at lower levels to make them more viable for shorter playthroughs.<br />Specifically, every Pokemon will evolve to its final stage by level 40, and three-stage evolutions will reach their middle stage by no later than level 30.<br />Additionally, Pokemon that normally evolve by having another specific Pokemon in the party will instead evolve at level 35.<br />Lastly, Pokemon that evolve via friendship will evolve at 160 happiness (down from 220), similar to how it works in the Switch Pokemon games.<br /><b>Takes effect regardless of whether evolutions are randomized or not.</b>
GUI.peMakeEvolutionsEasierCheckBox.text=Make Evolutions Easier
GUI.peRemoveTimeBasedEvolutions.toolTipText=<html>If this is checked, evolutions that require a certain time of day will now be possible regardless of the in-game time.<br />Split time-based evolutions (like Eevee => Espeon/Umbreon and Rockruff => Lycanroc) will be changed to stone evolutions instead.
GUI.peRemoveTimeBasedEvolutions.text=Remove Time-Based Evolutions
GUI.startersStaticsTradesPanel=Starters, Statics & Trades
GUI.spPanel.title=Starter Pokemon
GUI.spUnchangedRadioButton.toolTipText=Don't change the starter Pokemon.
GUI.spUnchangedRadioButton.text=Unchanged
GUI.spCustomRadioButton.toolTipText=Lets you pick the 3 starter pokemon you want to use, or "Random" at the top of the selector.
GUI.spCustomRadioButton.text=Custom
GUI.spRandomCompletelyRadioButton.toolTipText=Picks 3 random starter Pokemon to be used.
GUI.spRandomCompletelyRadioButton.text=Random (completely)
GUI.spRandomTwoEvosRadioButton.toolTipText=<html>Picks 3 random starter Pokemon to be used.<br />These Pokemon must have 2 evolution stages, e.g. be like the starters in the real games.
GUI.spRandomTwoEvosRadioButton.text=Random (basic Pokemon with 2 evolutions)
GUI.spRandomizeStarterHeldItemsCheckBox.toolTipText=<html>Checking this will randomize the items held by the starters where possible.<br />In Generation 2 games, each starter will get an individual random item.<br />In Generation 3 games, all the starters will get the same random item.
GUI.spRandomizeStarterHeldItemsCheckBox.text=Randomize Starter Held Items
GUI.spBanBadItemsCheckBox.toolTipText=<html>Checking this will remove "bad" items that don't do much from the set of possible random items for starter Pokemon, such as berries and mail.
GUI.spBanBadItemsCheckBox.text=Ban Bad Items
GUI.stpPanel.title=Static Pokemon
GUI.stpUnchangedRadioButton.toolTipText=Static Pokemon remain the same.
GUI.stpUnchangedRadioButton.text=Unchanged
GUI.stpSwapLegendariesSwapStandardsRadioButton.toolTipText=<html>Selecting this will replace every static Pokemon encounter, gift or purchase with another random one.<br />In this particular mode, legendary pokemon will always be swapped for other legendaries.<br />Also, normal non-legendary Pokemon will only be swapped for other non-legendaries.<br />In games where Ultra Beasts appear, they will be swapped out for other Ultra Beasts.
GUI.stpSwapLegendariesSwapStandardsRadioButton.text=Swap Legendaries && Swap Standards
GUI.stpRandomCompletelyRadioButton.toolTipText=<html>Selecting this will replace every static Pokemon encounter, gift or purchase with another random one.<br />In this particular mode, any Pokemon can replace any other Pokemon, so you could get a Mew in the Game Corner.<br />Or fight Magikarp instead of Mewtwo...
GUI.stpRandomCompletelyRadioButton.text=Random (completely)
GUI.stpRandomSimilarStrengthRadioButton.toolTipText=<html>Selecting this will replace every static Pokemon encounter with a Pokemon of similar strength.
GUI.stpRandomSimilarStrengthRadioButton.text=Random (similar strength)
GUI.stpLimitMainGameLegendariesCheckBox.toolTipText=<html>Selecting this will set an upper BST limit on what main-game Legendary Pokemon can be randomized into, and also expand the window for what counts <br />as "Similar Strength" for those Pokemon.<br /><br />This only applies to main-game Legendary Pokemon that are catchable and that are directly in your way during the main game, so some Pokemon<br />like Kyurem-B/Kyurem-W in BW2 (not catchable) and Rayquaza in Emerald (have to go out of your way to catch) are not included.
GUI.stpLimitMainGameLegendariesCheckBox.text=Limit Main-Game Legendaries
GUI.stpRandomize600BSTCheckBox.toolTipText=<html>Selecting this will enforce pure random on all static Pokemon with 600+ BST.
GUI.stpRandomize600BSTCheckBox.text=Randomize 600+ BST
GUI.igtPanel.title=In-Game Trades
GUI.igtUnchangedRadioButton.toolTipText=In-game trades remain the same.
GUI.igtUnchangedRadioButton.text=Unchanged
GUI.igtRandomizeGivenPokemonOnlyRadioButton.toolTipText=<html>Selecting this will randomize the Pokemon you receive from each in-game trade,<br />but the Pokemon requested by the NPC in exchange will remain the same.
GUI.igtRandomizeGivenPokemonOnlyRadioButton.text=Randomize Given Pokemon Only
GUI.igtRandomizeBothRequestedGivenRadioButton.toolTipText=<html>Selecting this will replace both the Pokemon you receive from an in-game trade and the Pokemon required to do the trade.
GUI.igtRandomizeBothRequestedGivenRadioButton.text=Randomize Both Requested && Given Pokemon
GUI.igtRandomizeNicknamesCheckBox.toolTipText=<html>Check this to randomize the nicknames of the Pokemon you receive.<br />The nicknames will be chosen from a predefined list.<br />If there are no more usable nicknames the original nicknames will be kept for the rest of the trades.
GUI.igtRandomizeNicknamesCheckBox.text=Randomize Nicknames
GUI.igtRandomizeOTsCheckBox.toolTipText=<html>Check this to randomize the Original Trainer (ID & name) of the Pokemon you receive from trades.<br />The names will be chosen from the same list as trainer names,<br />with names that are too long to be OT names excluded.
GUI.igtRandomizeOTsCheckBox.text=Randomize OTs
GUI.igtRandomizeIVsCheckBox.toolTipText=<html>Check this to randomize the IVs of the Pokemon you receive from ingame trades.<br />In most games these Pokemon have set IVs, so clicking this randomizes those set IVs.
GUI.igtRandomizeIVsCheckBox.text=Randomize IVs
GUI.igtRandomizeItemsCheckBox.toolTipText=<html>Check this to give each Pokemon you receive from an ingame trade a random held item.<br />This includes trades that do not normally have a held item on the Pokemon.
GUI.igtRandomizeItemsCheckBox.text=Randomize Items
GUI.movesMovesetsPanel=Moves & Movesets
GUI.mdPanel.title=Move Data
GUI.mdRandomizeMovePowerCheckBox.toolTipText=<html>Randomize the power of normal damaging moves.<br />Moves will get a new random power generally between 20 and 150 inclusive, with a very small chance of higher values appearing.<br />Non-damaging moves and moves with variable power will be unaffected.<br /><b>NOTE: There is no way to see the updated power of moves in-game in Gen 1!</b>
GUI.mdRandomizeMovePowerCheckBox.text=Randomize Move Power
GUI.mdRandomizeMoveAccuracyCheckBox.toolTipText=<html>Randomize the accuracy of most moves.<br />Some restrictions are enforced to reduce the chances of situations like 100% accurate OHKO moves happening.<br />Sure-hit moves are not changed.<br /><b>NOTE: There is no way to see the updated accuracy of moves in-game in Gen 1!</b>
GUI.mdRandomizeMoveAccuracyCheckBox.text=Randomize Move Accuracy
GUI.mdRandomizeMovePPCheckBox.toolTipText=<html>Randomize the PP of each move.<br />The maximum PP of each move (before PP Ups) will be randomized to a multiple of 5 between 5 and 40 inclusive.
GUI.mdRandomizeMovePPCheckBox.text=Randomize Move PP
GUI.mdRandomizeMoveTypesCheckBox.toolTipText=<html>Randomize the type of most moves.<br />The type of each move other than Struggle and ???-type moves will be re-rolled randomly.<br />This has no real effect on self-status moves, and may have odd side-effects in Gen 1.
GUI.mdRandomizeMoveTypesCheckBox.text=Randomize Move Types
GUI.mdRandomizeMoveCategoryCheckBox.toolTipText=<html>Randomize the category of damaging moves between Physical and Special.<br />This does not affect status moves.<br /><b>NOTE: This feature is not available for Gen 1-3 games. This randomizer does NOT add the Physical/Special split to games that don't have it!</b>
GUI.mdRandomizeMoveCategoryCheckBox.text=Randomize Move Category
GUI.mdUpdateMovesCheckBox.toolTipText=<html>If this is checked, moves will be updated to their stats (power, accuracy, etc) in the generation chosen to the right where possible.<br /><b>This does NOT add the Fairy type! Moves that were changed to Fairy type will keep their Gen 5 types.</b><br />Note that this only affects move stats, not any secondary effects that may have been added.
GUI.mdUpdateMovesCheckBox.text=Update Moves to Generation:
GUI.mdLegacyCheckBox.toolTipText=<html>Instead of updating moves to their Gen 6 stats, update them to their Gen 5 stats instead.<br />This is intended for people who used the "Update Moves" function in old randomizers but don't like the new changes.<br />This is available for every game except Gen 5 games where it would be pointless.
GUI.mdLegacyCheckBox.text=Legacy
GUI.pmsPanel.title=Pokemon Movesets
GUI.pmsUnchangedRadioButton.toolTipText=Don't change Pokemon movesets at all.
GUI.pmsUnchangedRadioButton.text=Unchanged
GUI.pmsRandomPreferringSameTypeRadioButton.toolTipText=<html>Randomize Pokemon movesets, preferring moves that are of the type (or one of the types) of the Pokemon.<br />Each Pokemon will get at least one reasonably accurate damaging move to begin with.
GUI.pmsRandomPreferringSameTypeRadioButton.text=Random (preferring same type)
GUI.pmsRandomCompletelyRadioButton.toolTipText=<html>Randomize Pokemon movesets, completely ignoring the type of the move and the Pokemon.<br />Each Pokemon will get at least one reasonably accurate damaging move to begin with.
GUI.pmsRandomCompletelyRadioButton.text=Random (completely)
GUI.pmsMetronomeOnlyModeRadioButton.toolTipText=<html>Where possible, every Pok\u00e9mon in the entire game will have Metronome as its only move,<br /> with the PP boosted to 40 to make it possible to complete fights without always using Struggle.<br />Does not currently apply to non-standard battles such as the Battle Tower/Frontier or the PWT.
GUI.pmsMetronomeOnlyModeRadioButton.text=Metronome Only Mode
GUI.pmsGuaranteedLevel1MovesCheckBox.toolTipText=<html>Check this to make sure every Pokemon gets a guaranteed amount of moves at level 1, instead of them keeping their original move count.<br />Set amount of guaranteed moves with the slider to the right.<br />Setting this to 4 ensures that every Pokemon you catch will have a full moveset.
GUI.pmsGuaranteedLevel1MovesCheckBox.text=Guaranteed Level 1 Moves
GUI.pmsReorderDamagingMovesCheckBox.toolTipText=<html>Reorders the randomized movesets so that less powerful damaging moves are learnt before those with high power.\n<br />The positions of non-damaging moves will not change.
GUI.pmsReorderDamagingMovesCheckBox.text=Reorder Damaging Moves
GUI.pmsNoGameBreakingMovesCheckBox.toolTipText=<html>Checking this checkbox will stop moves that can break early/late games being available in randomized movesets.<br />In first generation games, Dragon Rage, SonicBoom, Spore, and every OHKO move are banned.<br />In second generation onwards, only SonicBoom and Dragon Rage are banned (because OHKO moves and sleep are significantly less broken).
GUI.pmsNoGameBreakingMovesCheckBox.text=No Game-Breaking Moves
GUI.pmsForceGoodDamagingCheckBox.toolTipText=<html>Selecting this option will allow you to use the slider below to select a proportion of the randomized movesets to be forced to be good damaging moves.<br />Note that other moves can still be selected as good damaging moves randomly, this is simply an additional chance.
GUI.pmsForceGoodDamagingCheckBox.text=Force % of Good Damaging Moves:
GUI.pmsGuaranteedLevel1MovesSlider.toolTipText=<html>Set amount of guaranteed moves at level 1. Applies to all Pokemon.
GUI.pmsForceGoodDamagingSlider.toolTipText=<html>Use this slider to select the probability of good damaging moves for the option above.<br />Note that other moves can still be selected as good damaging moves randomly, this is simply an additional chance.
GUI.foePokemonPanel.title=Foe Pokemon
GUI.tpPanel.title=Trainer Pokemon
GUI.tpMain0Unchanged.toolTipText=Don't change trainer Pokemon at all.
GUI.tpMain0Unchanged.text=Unchanged
GUI.tpUnchangedRadioButton.toolTipText=Don't change trainer Pokemon at all.
GUI.tpUnchangedRadioButton.text=Unchanged
GUI.tpMain1Random.toolTipText=Randomize Trainers' Pokemon completely.
GUI.tpMain1Random.text=Random
GUI.tpRandomRadioButton.toolTipText=Randomize Trainers' Pokemon completely.
GUI.tpRandomRadioButton.text=Random
GUI.tpMain2RandomEvenDistribution.toolTipText=<html>For all trainers in the game (main-game and post-game), attempt to distribute Pokemon evenly.<br />Any other modifiers like similar strength take precedence, with the goal of reducing Pokemon from appearing too often.
GUI.tpMain2RandomEvenDistribution.text=Random (even distribution)
GUI.tpRandomEvenDistributionRadioButton.toolTipText=<html>For all trainers in the game (main-game and post-game), attempt to distribute Pokemon evenly.<br />Any other modifiers like similar strength take precedence, with the goal of reducing Pokemon from appearing too often.
GUI.tpRandomEvenDistributionRadioButton.text=Random (even distribution)
GUI.tpMain3RandomEvenDistributionMainGame.toolTipText=<html>For all trainers in the game (main-game trainers only), attempt to distribute Pokemon evenly.<br />Any other modifiers like similar strength take precedence, with the goal of reducing Pokemon from appearing too often.<br />Balancing among main game trainers causes evenness with a standard playthrough, and ignores post-game trainers (they are simply random).<br />This setting is for Gen 5 only.
GUI.tpMain3RandomEvenDistributionMainGame.text=Random (even distribution, main-game)
GUI.tpRandomEvenDistributionMainRadioButton.toolTipText=<html>For all trainers in the game (main-game trainers only), attempt to distribute Pokemon evenly.<br />Any other modifiers like similar strength take precedence, with the goal of reducing Pokemon from appearing too often.<br />Balancing among main game trainers causes evenness with a standard playthrough, and ignores post-game trainers (they are simply random).<br />This setting is for Gen 5 only.
GUI.tpRandomEvenDistributionMainRadioButton.text=Random (even distribution, main-game)
GUI.tpMain4TypeThemed.toolTipText=<html>Pick a type for each trainer and give them random Pokemon in that type.<br />Certain groups of trainers, such as the trainers in each gym, will all be given the same (random) type.
GUI.tpMain4TypeThemed.text=Type Themed
GUI.tpTypeThemedRadioButton.toolTipText=<html>Pick a type for each trainer and give them random Pokemon in that type.<br />Certain groups of trainers, such as the trainers in each gym, will all be given the same (random) type.
GUI.tpTypeThemedRadioButton.text=Type Themed
GUI.tpMain5TypeThemedEliteFourGyms.toolTipText=<html>Pick a type for each trainer and give them random Pokemon in that type.<br />Certain groups of trainers, such as the trainers in each gym, will all be given the same (random) type.<br />Only applies to Elite Four and Gym Trainers/Leaders.
GUI.tpMain5TypeThemedEliteFourGyms.text=Type Themed (Elite Four/Gyms Only)
GUI.tpRivalCarriesStarterCheckBox.toolTipText=<html>If this is selected, the rival will have their starter in every team they battle you with, evolved if it is far enough through.<br />The rest of their team will be chosen in the same way as every other trainer.
GUI.tpRivalCarriesStarterCheckBox.text=Rival Carries Starter Through Game
GUI.tpSimilarStrengthCheckBox.toolTipText=<html>If this is checked, the random Pokemon that replaces each Pokemon will be of similar power to the original.<br />However, preserving other rules such as type theming has precedence over this, so weaker or stronger Pokemon will be chosen if there are no other Pokemon available.
GUI.tpSimilarStrengthCheckBox.text=Try to Use Pokemon with Similar Strength
GUI.tpWeightTypesCheckBox.toolTipText=<html>If this is checked, the number of trainers with each type will roughly match up to the number of Pokemon with that type.<br />This should reduce repetition of Pokemon, but may lead to a lot of trainers with the same type in a row.
GUI.tpWeightTypesCheckBox.text=Weight Types by # of Pokemon
GUI.tpDontUseLegendariesCheckBox.text=Don't Use Legendaries
GUI.tpNoEarlyWonderGuardCheckBox.toolTipText=<html>Pokemon such as Shedinja, with the ability "Wonder Guard", are a pain to run into early on when you can't damage them.<br />Selecting this option will make sure that no trainers are given these Pokemon under level 20.<br />By the time you are fighting level 20 trainers you can reasonably be expected to have a counter for each type.
GUI.tpNoEarlyWonderGuardCheckBox.text=No Early Wonder Guard
GUI.tpRandomizeTrainerNamesCheckBox.toolTipText=<html>Check this to randomize trainers' names when you fight them.<br />For RBY, this only includes the Gym Leaders and Elite 4, as no-one else has names.<br />For every other game this will replace the names of each individual trainer.
GUI.tpRandomizeTrainerNamesCheckBox.text=Randomize Trainer Names
GUI.tpRandomizeTrainerClassNamesCheckBox.toolTipText=<html>Check this to randomize the class names to new names (e.g. "Youngster" could become "Misfit").
GUI.tpRandomizeTrainerClassNamesCheckBox.text=Randomize Trainer Class Names
GUI.tpForceFullyEvolvedAtCheckBox.toolTipText=<html>Checking this will force all trainer Pokemon at or above the level you select below to be fully evolved regardless of other settings.<br />Pokemon below the selected level will be randomly picked like normal.
GUI.tpForceFullyEvolvedAtCheckBox.text=Force Fully Evolved at Level:
GUI.tpForceFullyEvolvedAtSlider.toolTipText=Use this slider to select the minimum level to force fully evolved Pokemon at if said option is checked above.
GUI.tpPercentageLevelModifierSlider.toolTipText=<html>Use this slider to select the percentage to change trainer Pokemon levels by.<br />Negative percentages (-50 to 0) will decrease levels, and positive percentages will increase them.
GUI.tpPercentageLevelModifierCheckBox.toolTipText=<html>Checking this will enable a percentage-based level modifier for every enemy trainer Pokemon in the game.
GUI.tpPercentageLevelModifierCheckBox.text=Percentage Level Modifier:
GUI.tpAllowAlternateFormesCheckBox.toolTipText=<html>Allow trainers to have alternate formes of Pokemon, such as the various formes of Rotom or Deoxys.<br />Note that their traits are randomized/shuffled separately from their base formes.
GUI.tpAllowAlternateFormesCheckBox.text=Allow Alternate Formes
GUI.wildPokemonPanel.title=Wild Pokemon
GUI.wpPanel.title=Wild Pokemon
GUI.wpUnchangedRadioButton.toolTipText=Don't change Wild Pokemon at all.
GUI.wpUnchangedRadioButton.text=Unchanged
GUI.wpRandomRadioButton.toolTipText=<html>Completely randomize Wild Pokemon in every area.<br />This should mean that there are many different Pokemon in each area.
GUI.wpRandomRadioButton.text=Random
GUI.wpArea1To1RadioButton.toolTipText=<html>Each Pokemon in a given encounter area will be replaced by another Pokemon in every slot it appears in.<br />This will make each area have a handful of random Pokemon.
GUI.wpArea1To1RadioButton.text=Area 1-to-1 Mapping
GUI.wpGlobal1To1RadioButton.toolTipText=<html>Every place a certain Pokemon appears in, it will be replaced by another set Pokemon.<br />This mode doesn't support any other rules except similar strength because it is too restrictive on its own.
GUI.wpGlobal1To1RadioButton.text=Global 1-to-1 Mapping
GUI.wpARPanel.title=Additional Rule
GUI.wpARNoneRadioButton.toolTipText=Don't apply any other rules.
GUI.wpARNoneRadioButton.text=None
GUI.wpARSimilarStrengthRadioButton.toolTipText=<html>If this is checked, the random Pokemon that replaces each Pokemon will be of similar power to the original.<br />However, preserving other rules such as 1-1 maps has precedence over this, so weaker or stronger Pokemon will be chosen if there are no other Pokemon available.<br />This option is not available alongside Type Themes or Catch-em-All because the Pokemon pool would be too limited in some cases.
GUI.wpARSimilarStrengthRadioButton.text=Similar Strength
GUI.wpARCatchEmAllModeRadioButton.toolTipText=<html>If this is turned on, every random Pokemon chosen to replace an encounter will be one that hasn't been chosen before.<br />This should make sure that every Pokemon is catchable.<br />Once every Pokemon has been chosen the Pokemon choice list will start again from full.
GUI.wpARCatchEmAllModeRadioButton.text=Catch Em All Mode
GUI.wpARTypeThemeAreasRadioButton.toolTipText=<html>If this is chosen, every encounter area will have only Pokemon of a random type.<br />This may lead to a more realistic experience, or an odd one (e.g. if Fire pokemon are chosen to be in Surfing encounters).
GUI.wpARTypeThemeAreasRadioButton.text=Type Themed Areas
GUI.wpUseTimeBasedEncountersCheckBox.toolTipText=<html>This affects games that have either seasons or morning/day/night encounter sets.<br />If this is checked, each of these sets will be treated as a separate "area".<br />So you will have to visit each place in morning/day/night, or in each season, to collect the Pokemon.<br />If this isn't checked, all of morning/day/night and all seasons will use the same encounter data.
GUI.wpUseTimeBasedEncountersCheckBox.text=Use Time Based Encounters
GUI.wpDontUseLegendariesCheckBox.text=Don't Use Legendaries
GUI.wpSetMinimumCatchRateCheckBox.toolTipText=<html>If this is selected, every Pokemon in the game with a catch rate below a certain level will have its catch rate increased.\n<br />The exact minimum catch rate level can be set using the slider to the right.\n<br />The percentages below assume a normal Poke Ball is used while the wild Pokemon is at full health with no status condition.\n<br /><b>Level 1:</b> "Normal" minimum catch rate. ~10% (Gens 1-4)/~18% (Gens 5+) chance for normal Pokemon, ~5% (Gens 1-4)/~10% (Gens 5+) chance for legendary Pokemon.\n<br /><b>Level 2:</b> "Buffed" minimum catch rate. ~17% (Gens 1-4)/~26% (Gens 5+) chance for normal Pokemon, ~9% (Gens 1-4)/~16% (Gens 5+) chance for legendary Pokemon.\n<br /><b>Level 3:</b> "Super" minimum catch rate. ~27% (Gens 1-4)/~37% (Gens 5+) chance for normal Pokemon, ~14% (Gens 1-4)/~22% (Gens 5+) chance for legendary Pokemon.\n<br /><b>Level 4</b>: "Ultra" minimum catch rate. ~34% (Gens 1-4)/~44% (Gens 5+) chance for all Pokemon. \n<br /><b>Level 5:</b> Guaranteed catches (every Pokemon is guaranteed to be caught, so long as they are catchable in the first place).
GUI.wpSetMinimumCatchRateCheckBox.text=Set Minimum Catch Rate:
GUI.wpRandomizeHeldItemsCheckBox.toolTipText=<html>Checking this will randomize the items held by Pokemon in the wild, including whether they can have one at all or not.<br />In some cases, these item definitions will also apply to static encounters with legendaries and the like.
GUI.wpRandomizeHeldItemsCheckBox.text=Randomize Held Items
GUI.wpBanBadItemsCheckBox.toolTipText=<html>Checking this will remove "bad" items that don't do much from the set of possible random items for wild Pokemon, such as berries and mail.
GUI.wpBanBadItemsCheckBox.text=Ban Bad Items
GUI.wpBalanceShakingGrassPokemonCheckBox.toolTipText=<html>Checking this will tune down shaking grass Pokemon at lower levels.
GUI.wpBalanceShakingGrassPokemonCheckBox.text=Balance Shaking Grass Pokemon
GUI.wpPercentageLevelModifierCheckBox.toolTipText=<html>Checking this will enable a percentage-based level modifier for every wild Pokemon in the game.
GUI.wpPercentageLevelModifierCheckBox.text=Percentage Level Modifier:
GUI.wpPercentageLevelModifierSlider.toolTipText=<html>Use this slider to select the percentage to change wild Pokemon levels by.<br />Negative percentages (-50 to 0) will decrease levels, and positive percentages will increase them.
GUI.wpSetMinimumCatchRateSlider.toolTipText=<html>If Minimum Catch Rate is selected, allows you to set the level used. The percentages below assume a normal Poke Ball at full health.\n<br /><b>Level 1:</b> "Normal" minimum catch rate. ~10% (Gens 1-4)/~18% (Gens 5+) chance for normal Pokemon, ~5% (Gens 1-4)/~10% (Gens 5+) chance for legendary Pokemon.\n<br /><b>Level 2:</b> "Buffed" minimum catch rate. ~17% (Gens 1-4)/~26% (Gens 5+) chance for normal Pokemon, ~9% (Gens 1-4)/~16% (Gens 5+) chance for legendary Pokemon.\n<br /><b>Level 3:</b> "Super" minimum catch rate. ~27% (Gens 1-4)/~37% (Gens 5+) chance for normal Pokemon, ~14% (Gens 1-4)/~22% (Gens 5+) chance for legendary Pokemon.\n<br /><b>Level 4</b>: "Ultra" minimum catch rate. ~34% (Gens 1-4)/~44% (Gens 5+) chance for all Pokemon. \n<br /><b>Level 5:</b> Guaranteed catches (every Pokemon is guaranteed to be caught, so long as they are catchable in the first place).
GUI.tmsHMsTutorsPanel.title=TM/HMs & Tutors
GUI.tmPanel.title=TMs & HMs
GUI.tmMovesPanel.title=TM/HM Moves
GUI.tmUnchangedRadioButton.toolTipText=<html>Leave the moves in TMs as they are.<br />If Metronome Only Mode is selected, all TMs are changed to Metronome and this setting has no effect.
GUI.tmUnchangedRadioButton.text=Unchanged
GUI.tmRandomRadioButton.toolTipText=<html>Give each TM a new move.<br />HM moves are not affected, nor can they be selected to be put in TMs.<br />Each TM will still be unique.<br />If Metronome Only Mode is selected, all TMs are changed to Metronome and this setting has no effect.
GUI.tmRandomRadioButton.text=Random
GUI.tmFullHMCompatibilityCheckBox.toolTipText=<html>If you select this option, then every Pokemon will learn every HM, regardless of any other options you check.
GUI.tmFullHMCompatibilityCheckBox.text=Full HM Compatibility
GUI.tmLevelupMoveSanityCheckBox.toolTipText=<html>If you select this option, then Pokemon will be guaranteed to learn TMs of moves that they learn by levelup.<br />Otherwise, move compatibility will be left alone or decided randomly, depending on your other choices.
GUI.tmLevelupMoveSanityCheckBox.text=TM/Levelup Move Sanity
GUI.tmKeepFieldMoveTMsCheckBox.toolTipText=<html>If you select this, TMs that contain field-use moves will be left alone.<br />This includes things like Dig & Teleport, but not healing moves (Softboiled).
GUI.tmKeepFieldMoveTMsCheckBox.text=Keep Field Move TMs
GUI.tmForceGoodDamagingCheckBox.toolTipText=<html>Selecting this option will allow you to use the slider below to select a proportion of the randomized TMs to be forced to be good damaging moves.<br />Note that other TMs can still be selected as good damaging moves randomly, this is simply an additional chance.
GUI.tmForceGoodDamagingCheckBox.text=Force % of Good Damaging Moves:
GUI.tmForceGoodDamagingSlider.toolTipText=<html>Use this slider to select the probability of good damaging moves for the option above.<br />Note that other TMs can still be selected as good damaging moves randomly, this is simply an additional chance.
GUI.tmNoGameBreakingMovesCheckBox.toolTipText=<html>Checking this checkbox will stop moves that can break early/late games being available in randomized TMs.<br />In first generation games, Dragon Rage, SonicBoom, Spore, and every OHKO move are banned.<br />In second generation onwards, only SonicBoom and Dragon Rage are banned (because OHKO moves and sleep are significantly less broken).
GUI.tmNoGameBreakingMovesCheckBox.text=No Game-Breaking Moves
GUI.thcPanel.title=TM/HM Compatibility
GUI.thcUnchangedRadioButton.toolTipText=<html>Every Pokemon will be able to learn the same TMs that it could before.<br />Note that this applies even if you change the TM moves, which could lead to some odd combinations.
GUI.thcUnchangedRadioButton.text=Unchanged
GUI.thcRandomPreferSameTypeRadioButton.toolTipText=<html>Randomize the TMs and HMs that each Pokemon can learn.<br />Each TM or HM will have:<br />A 90% chance of being learnable if the Pokemon has it as (one of) its type(s).<br />A 50% chance of being learnable if the move is Normal and the Pokemon isn't.<br />A 25% chance otherwise.
GUI.thcRandomPreferSameTypeRadioButton.text=Random (prefer same type)
GUI.thcRandomCompletelyRadioButton.toolTipText=<html>Randomize the TMs and HMs that each Pokemon can learn.<br />Each TM or HM will have a 50% chance of being learnable regardless of type.
GUI.thcRandomCompletelyRadioButton.text=Random (completely)
GUI.thcFullCompatibilityRadioButton.toolTipText=<html>Select this option to allow every Pokemon to learn every TM/HM.<br />This can be fun to mess around with, but it might make the game too easy.
GUI.thcFullCompatibilityRadioButton.text=Full Compatibility
GUI.mtPanel.title=Move Tutors
GUI.mtMovesPanel.title=Move Tutor Moves
GUI.mtNoExistLabel.text=This game does not have any Move Tutors, or they are not randomizable yet.
GUI.mtUnchangedRadioButton.toolTipText=<html>Leave the moves taught by tutors as they are.<br />If Metronome Only Mode is selected, all Move Tutors are changed to Metronome and this setting has no effect.
GUI.mtUnchangedRadioButton.text=Unchanged
GUI.mtRandomRadioButton.toolTipText=<html>Give each move tutor slot a new move.<br />Each Move Tutor move will still be unique, and they will not overlap with TM/HM moves.<br />If Metronome Only Mode is selected, all Move Tutors are changed to Metronome and this setting has no effect.
GUI.mtRandomRadioButton.text=Random
GUI.mtLevelupMoveSanityCheckBox.toolTipText=<html>If you select this option, then Pokemon will be guaranteed to learn move tutors of moves that they learn by levelup.<br />Otherwise, move compatibility will be left alone or decided randomly, depending on your other choices.
GUI.mtLevelupMoveSanityCheckBox.text=Tutor/Levelup Move Sanity
GUI.mtKeepFieldMoveTutorsCheckBox.toolTipText=<html>If you select this, move tutors that contain field-use moves will be left alone.<br />This includes things like Headbutt, but not healing moves (Softboiled).
GUI.mtKeepFieldMoveTutorsCheckBox.text=Keep Field Move Tutors
GUI.mtForceGoodDamagingCheckBox.toolTipText=<html>Selecting this option will allow you to use the slider below to select a proportion of the randomized tutors to be forced to be good damaging moves.<br />Note that other tutors can still be selected as good damaging moves randomly, this is simply an additional chance.
GUI.mtForceGoodDamagingCheckBox.text=Force % of Good Damaging Moves:
GUI.mtForceGoodDamagingSlider.toolTipText=<html>Use this slider to select the probability of good damaging moves for the option above.<br />Note that other tutors can still be selected as good damaging moves randomly, this is simply an additional chance.
GUI.mtNoGameBreakingMovesCheckBox.toolTipText=<html>Checking this checkbox will stop moves that can break early/late games being available in randomized move tutors.<br />In first generation games, Dragon Rage, SonicBoom, Spore, and every OHKO move are banned.<br />In second generation onwards, only SonicBoom and Dragon Rage are banned (because OHKO moves and sleep are significantly less broken).
GUI.mtNoGameBreakingMovesCheckBox.text=No Game-Breaking Moves
GUI.mtcPanel.title=Move Tutor Compatibility
GUI.mtcUnchangedRadioButton.toolTipText=<html>Every Pokemon will be able to learn the same move tutor moves that it could before.<br />Note that this applies even if you randomize the moves, which could lead to some odd combinations.
GUI.mtcUnchangedRadioButton.text=Unchanged
GUI.mtcRandomPreferSameTypeRadioButton.toolTipText=<html>Randomize the Move Tutor moves that each Pokemon can learn.<br />Each move will have:<br />A 90% chance of being learnable if the Pokemon has it as (one of) its type(s).<br />A 50% chance of being learnable if the move is Normal and the Pokemon isn't.<br />A 25% chance otherwise.
GUI.mtcRandomPreferSameTypeRadioButton.text=Random (prefer same type)
GUI.mtcRandomCompletelyRadioButton.toolTipText=<html>Randomize the Move Tutor moves that each Pokemon can learn.<br />Each move will have a 50% chance of being learnable regardless of type.
GUI.mtcRandomCompletelyRadioButton.text=Random (completely)
GUI.mtcFullCompatibilityRadioButton.toolTipText=<html>Select this option to allow every Pokemon to learn every Move Tutor move.<br />This can be fun to mess around with, but it might make the game too easy.
GUI.mtcFullCompatibilityRadioButton.text=Full Compatibility
GUI.itemsPanel.title=Items
GUI.fiPanel.title=Field Items
GUI.fiUnchangedRadioButton.toolTipText=Items on the ground and hidden items remain the same.
GUI.fiUnchangedRadioButton.text=Unchanged
GUI.fiShuffleRadioButton.toolTipText=<html>Selecting this will take the full set of items that can be picked up from item balls, as well as hidden items,<br />and randomize their order so each item appears in a new place.<br />Key items are left in their original location and glitch items are excluded.<br />This stops item balls containing seriously overpowered items, but more powerful items may be available early on.<br />TMs will remain in the same item balls, but the numbers of the TMs in them will be shuffled among the set.
GUI.fiShuffleRadioButton.text=Shuffle
GUI.fiRandomRadioButton.toolTipText=<html>Selecting this will place a new random item in each item ball & hidden item slot.<br />Key items & glitch items are automatically excluded.<br />This means item balls can contain seriously overpowered items such as Master Balls.<br />TMs will remain in the same item balls, but the numbers of the TMs in them will be randomized, including TMs not usually available from item balls.<br />All TMs will still be available at least once in the game.
GUI.fiRandomRadioButton.text=Random
GUI.fiRandomEvenDistributionRadioButton.toolTipText=<html>This is the same as Random, except that the randomizer will control the number of times each item is placed. <br />This applies to the entire game, main- and post-game.
GUI.fiRandomEvenDistributionRadioButton.text=Random (even distribution)
GUI.fiBanBadItemsCheckBox.toolTipText=<html>Checking this will remove "bad" items that don't do much from the set of possible random items for field items, such as berries and mail.
GUI.fiBanBadItemsCheckBox.text=Ban Bad Items
GUI.shPanel.title=Special Shops
GUI.shUnchangedRadioButton.toolTipText=Items in shops remain the same.
GUI.shUnchangedRadioButton.text=Unchanged
GUI.shShuffleRadioButton.toolTipText=Items in non-main shops are shuffled.
GUI.shShuffleRadioButton.text=Shuffle
GUI.shRandomRadioButton.toolTipText=Items in non-main shops are randomized.
GUI.shRandomRadioButton.text=Random
GUI.shBanOverpoweredShopItemsCheckBox.toolTipText=<html>Checking this will remove overpowered shop items from the pool, such as the Lucky Egg and<br />items that sell for high prices to maniacs.
GUI.shBanOverpoweredShopItemsCheckBox.text=Ban Overpowered Shop Items
GUI.shBanBadItemsCheckBox.toolTipText=<html>Checking this will remove "bad" items that don't do much from the set of possible random items for shop items, such as berries and mail.
GUI.shBanBadItemsCheckBox.text=Ban Bad Items
GUI.shBanRegularShopItemsCheckBox.toolTipText=<html>Checking this will remove regular shop items from the possible random items for shop items.
GUI.shBanRegularShopItemsCheckBox.text=Ban Regular Shop Items
GUI.shBalanceShopItemPricesCheckBox.toolTipText=<html>Checking this will change prices for many items in order to make them more balanced.<br /><b>Be aware:</b> prices for some items are seriously imbalanced without this setting.
GUI.shBalanceShopItemPricesCheckBox.text=Balance Shop Item Prices
GUI.shGuaranteeEvolutionItemsCheckBox.toolTipText=<html>Checking this will ensure all evolution items appear in shops.
GUI.shGuaranteeEvolutionItemsCheckBox.text=Guarantee Evolution Items
GUI.shGuaranteeXItemsCheckbox.tooltipText=<html>Checking this will ensure all X items (including Guard Spec. and Dire Hit) appear in shops.
GUI.shGuaranteeXItemsCheckbox.text=Guarantee X Items
GUI.puPanel.title=Pickup Items
GUI.puUnchangedRadioButton.toolTipText=Items obtained via the Pickup ability remain the same.
GUI.puUnchangedRadioButton.text=Unchanged
GUI.puRandomRadioButton.toolTipText=Items obtained via the Pickup ability are randomized.
GUI.puRandomRadioButton.text=Random
GUI.puBanBadItemsCheckBox.toolTipText=<html>Checking this will remove "bad" items that don't do much from the set of possible random items for the Pickup item table, such as berries and mail.
GUI.puBanBadItemsCheckBox.text=Ban Bad Items
GUI.miscTweaksPanel.title=Misc. Tweaks
GUI.miscPanel.title=Misc. Tweaks
GUI.miscNoneAvailableLabel.text=There are no tweaks available for the currently loaded game.
GUI.miscBWExpPatchCheckBox.toolTipText=<html>Select this to patch the game you are randomizing to use the Black/White new XP gain system.<br />This system gives EXP in a different way, rewarding players for beating higher level Pokemon with lower levels.<br />This is currently available only for English R/B/Y and G/S/C.
GUI.miscBWExpPatchCheckBox.text=B/W Exp Patch
GUI.miscNerfXAccuracyCheckBox.toolTipText=<html>X Accuracy in Generation 1 games is a pretty broken item, given that it gives 100% accuracy to any and all moves used.<br />Apply this code tweak to stop X Accuracy from working on sleep moves, trapping moves & one-hit KO moves.<br />Doing this means that the player cannot use X Accuracy and lock the opponents into infinite sleep/Wrap, nor can they just use one-hit KO moves on everything.<br />Applying this patch will also remove the one-hit KO moves from the "broken moves" list if you use that option, since they aren't really broken without X Accuracy.<br /><b>Credit to Mountebank for this patch.</b>
GUI.miscNerfXAccuracyCheckBox.text=Nerf X Accuracy
GUI.miscFixCritRateCheckBox.toolTipText=<html>Selecting this option will "fix" Generation 1's critical hit rate to be the same as the other games (1/16), instead of being based on Speed.<br />Focus Energy and Dire Hit will also be fixed to increase crit rate instead of decreasing it.<br />"High crit rate" moves such as Slash will have double the normal crit chance, like in later games.
GUI.miscFixCritRateCheckBox.text="Fix" Crit Rate
GUI.miscFastestTextCheckBox.toolTipText=<html>Selecting this option will make all text boxes in the game show up with minimum delay, regardless of the text speed the player sets in Options.<br /><b>Credit to Dabomstew/Revo for Gen 3, Kaphotics for Gen 4, Ajarmar for Gen 5, and unknown cheat code makers for Gen 6/7</b>
GUI.miscFastestTextCheckBox.text=Fastest Text
GUI.miscRunningShoesIndoorsCheckBox.toolTipText=<html>Selecting this option will allow you to use the Running Shoes in any location, just like in later games.
GUI.miscRunningShoesIndoorsCheckBox.text=Running Shoes Indoors
GUI.miscRandomizePCPotionCheckBox.toolTipText=<html>Selecting this option will randomize the Potion that is in your item PC at the beginning of the game to any other "useful" item.
GUI.miscRandomizePCPotionCheckBox.text=Randomize PC Potion
GUI.miscAllowPikachuEvolutionCheckBox.toolTipText=<html>Selecting this option will let you evolve Pikachu into Raichu with a Thunder Stone like any other game.
GUI.miscAllowPikachuEvolutionCheckBox.text=Allow Pikachu Evolution
GUI.miscGiveNationalDexAtCheckBox.toolTipText=<html>If this is checked then the National Dex will be given to the player at the start of the game with the regular Pokedex.<br />It is useful for tracking Pokemon outside of the regional dex when using the Pokedex.<br /><b>Important caveats:</b><br /><b>For FRLG only:</b> this setting is <i>necessary</i> for Kanto Pokemon to evolve into Non-Kanto Pokemon, i.e. Golbat evolving into Crobat.<br /><b>For DPPt only:</b> the National Dex will instead be obtained after speaking to Professor Rowan once after obtaining the regular Pokedex.
GUI.miscGiveNationalDexAtCheckBox.text=Give National Dex at Start
GUI.miscUpdateTypeEffectivenessCheckBox.toolTipText=<html>If this is checked, the type weaknesses/strengths/immunities will be updated to what they are in Gen 6 and up. <b>This does NOT add the Fairy type.</b><br />For Gen 1, this changes the following:<br /><ul><li>Ice is now not very effective against Fire.<li>Poison is now neutral against Bug.<li>Bug is now not very effective against Poison.<li>Ghost is now super effective against Psychic.</ul>For Gens 2-5, this changes the following:<br /><ul><li>Ghost is now neutral against Steel.<li>Dark is now neutral against Steel.</ul>
GUI.miscUpdateTypeEffectivenessCheckBox.text=Update Type Effectiveness
GUI.forceChallengeMode.toolTipText=<html>If this is checked, then Challenge Mode will be forcibly enabled, bypassing the Key System entirely.<br /><b>You will <i>not</i> be able to access Easy or Normal Mode if you enable this setting.</b>
GUI.forceChallengeMode.text=Force Challenge Mode
GUI.miscLowerCasePokemonNamesCheckBox.toolTipText=<html>If this is selected, all Pokemon names will be made into Camel Case.<br />e.g. VENUSAUR becomes Venusaur.<br />This looks better in Gen3/Gen4 games, and OK in Gen1/Gen2 games.
GUI.miscLowerCasePokemonNamesCheckBox.text=Lower Case Pokemon Names
GUI.miscRandomizeCatchingTutorialCheckBox.toolTipText=<html>Selecting this option will randomize the Pokemon participating in the game's catching tutorial.
GUI.miscRandomizeCatchingTutorialCheckBox.text=Randomize Catching Tutorial
GUI.miscBanLuckyEggCheckBox.toolTipText=<html>Bans Lucky Egg from showing up as any kind of randomized item.<br />If the original game contains a Lucky Egg in any non-randomized place, that will still be the case.
GUI.miscBanLuckyEggCheckBox.text=Ban Lucky Egg
GUI.miscNoFreeLuckyEggCheckBox.toolTipText=<html>Causes Professor Juniper to not give you a Lucky Egg for free in the Chargestone Cave (BW)/Celestial Tower (BW2).
GUI.miscNoFreeLuckyEggCheckBox.text=No Free Lucky Egg
GUI.miscBanBigMoneyManiacCheckBox.toolTipText=<html>Bans maniac items worth more than $10,000 from showing up as any kind of randomized item.
GUI.miscBanBigMoneyManiacCheckBox.text=Ban Big Money Maniac Items
GUI.cantWriteConfigFile=WARNING: The randomizer is unable to write its config file to the directory it is in.\nThis means that it will probably be unable to save the randomized ROMs it creates.\nPlease run the randomizer from a directory where you can write files.\nYou can try to use the randomizer as-is, but it will probably not work.
GUI.copyNameFilesDialog.text=You appear to have customized name files in the config directory left over from an old version of the randomizer.\nWould you like these files to be copied to the main program directory so they are used in this version?
GUI.copyNameFilesDialog.title=Copy custom names?
GUI.convertNameFilesDialog.text=You appear to have customized name files in the randomizer directory left over from an old version of the randomizer.\nWould you like these files to be converted to the new custom names format so they are used in this version?
GUI.convertNameFilesDialog.title=Convert custom names?
GUI.copyNameFilesFailed=At least one file was not able to be copied.
GUI.convertNameFilesFailed=Could not convert custom names to the new format.
GUI.configFileMissing=The file %s is missing from the configuration and so this program cannot start.\nPlease make sure you extract the program from the ZIP file before running it.
GUI.loadingText=Loading...
GUI.savingText=Saving...
GUI.loadFailed=There was an unhandled exception trying to load your ROM.\nA log file containing some details has been saved to %s.\nPlease include this file in any bug reports you do.
GUI.loadFailedNoLog=There was an unhandled exception trying to load your ROM.
GUI.unreadableRom=Could not read %s from disk.\nPlease ensure you have read access to the ROM you're trying to open.
GUI.tooShortToBeARom=%s appears to be a blank or nearly blank file.\nCheck to make sure you're opening the right file.
GUI.openedZIPfile=%s is a ZIP archive, not a ROM.\nYou should extract it and try to randomize the actual ROM file inside.
GUI.openedRARfile=%s is a RAR archive, not a ROM.\nYou should extract it and try to randomize the actual ROM file inside.
GUI.openedIPSfile=%s is an IPS patch, not a ROM.\nYou should apply it to a ROM first before trying to randomize the result.
GUI.unsupportedRom=Could not load %s - it's not a supported ROM.
GUI.encryptedRom=<html>Could not load %s as it appears to be an encrypted ROM.<br />Universal Pokemon Randomizer ZX does not currently support encrypted 3DS ROMs.<br/>If you believe your ROM is actually decrypted, please try decrypting your ROM with a different tool or try using a different ROM.
GUI.romSupportPrefix=Support:
GUI.processFailed=There was an unhandled exception trying to process your ROM.\nA log file containing some details has been saved to %s.\nPlease include this file in any bug reports you do.
GUI.processFailedNoLog=There was an unhandled exception trying to process your ROM.
GUI.raceModeRequirements=You can't use Race Mode without randomizing either the wild Pokemon or the trainer Pokemon.\nReview this and try again.
GUI.batchRandomizationRequirements=You have enabled both Race Mode and Batch Randomization. These options can't be used together.\nDisable one of these options and try again.
GUI.pokeLimitNotChosen=You enabled the option to limit the Pokemon that appear, but didn't choose any to allow.\nSelect some by clicking on the "Limit Pokemon" button and try again.
GUI.presetFailTrainerClasses=Can't use this preset because you have a different set of random trainer class names to the creator.\nHave them make you a rndp file instead.
GUI.presetFailTrainerNames=Can't use this preset because you have a different set of random trainer names to the creator.\nHave them make you a rndp file instead.
GUI.presetFailNicknames=Can't use this preset because you have a different set of random nicknames to the creator.\nHave them make you a rndp file instead.
GUI.presetDifferentCustomNames=You don't have the same set of custom names as the creator. If you continue, you will have different custom names (if that setting is in use).\nIf you want to play with the same custom names, have them send you a preset file (.rndp) instead.
GUI.starterUnavailable=Could not set one of the custom starters from the settings file because it does not exist in this generation.
GUI.saveFailedMessage=Error during randomization: %s.\nIf this is the first time you've seen this message, try again.\nIf it keeps happening, try loosening any restrictive settings you've selected.\nA log file containing some details has been saved to %s.\nPlease include this file in any bug reports you do.
GUI.saveFailedMessageNoLog=Error during randomization: %s.\nIf this is the first time you've seen this message, try again.\nIf it keeps happening, try loosening any restrictive settings you've selected.
GUI.saveFailedIO=There was an unhandled exception trying to save your ROM to disk.\nA log file containing some details has been saved to %s.\nPlease include this file in any bug reports you do.
GUI.saveFailedIONoLog=There was an unhandled exception trying to save your ROM to disk.
GUI.cannotWriteToLocation=The randomizer is not allowed to write this file: %s.\nPlease try saving your ROM to a different location.
GUI.raceModeCheckValuePopup=Your check value for the race is:\n%08X\nDistribute this along with the preset file, if you're the race maker!\nIf you received this in a race, compare it to the value the race maker gave.
GUI.saveLogDialog.text=Do you want to save a log file of the randomization performed?\nThis may allow you to gain an unfair advantage, do not do so if you are doing something like a race.
GUI.saveLogDialog.title=Save Log?
GUI.logSaveFailed=Could not save log file!
GUI.logSaved=Log file saved to\n%s.log
GUI.randomizationDone=Randomization Complete. You can now play!
GUI.saveFailed=There was an unhandled exception trying to save your ROM.\nA log file containing some details has been saved to %s.\nPlease include this file in any bug reports you do.
GUI.saveFailedNoLog=There was an unhandled exception trying to save your ROM.
GUI.cantOverwriteDS=You cannot overwrite the original ROM when you save a DS randomization.\nPlease choose a different filename.
GUI.noUpdates=No new updates found.
GUI.settingsFileOlder=This settings file was created by an older randomizer version.\nThe randomizer will attempt to open it anyway, but you should look out for new options added since you made it.\nTo prevent this message from appearing every time you load this settings file, re-save your settings file.
GUI.settingsFileNewer=This settings file is for a newer randomizer version.\nYou should upgrade your randomizer.
GUI.invalidSettingsFile=Settings file is not valid.
GUI.settingsLoaded=Settings loaded from %s.
GUI.settingsLoadFailed=Settings file load failed. Please try again.
GUI.settingsSaveFailed=Settings file save failed. Please try again.
GUI.cantLoadCustomNames=Could not initialise custom names data.\nPlease redownload the randomizer and try again.
GUI.customNamesEditorMenuItem.text=Custom Names Editor
GUI.mtMovesPanel.toolTipText=
GUI.mtMovesPanel.text=
GUI.mtCompatPanel.toolTipText=
GUI.mtCompatPanel.text=
GUI.shopItemsPanel.toolTipText=
GUI.shopItemsPanel.text=
GUI.miscTweaksPanel.toolTipText=
GUI.miscTweaksPanel.text=
GUI.gameMascotLabel.toolTipText=
GUI.gameMascotLabel.text=
GUI.batchRandomizationMenuItem.text=Batch Randomization Settings
GUI.batchRandomizationProgress=Saving %d of %d
Log.InvalidRomLoaded=The ROM you loaded is not a clean, official ROM.\nRandomizing ROM hacks or bad ROM dumps is not supported and may cause issues.\n
GenerationLimitDialog.includePokemonHeader.text=Include Pokemon from:
GenerationLimitDialog.relatedPokemonHeader.text=... and related Pokemon from:
GenerationLimitDialog.gen1CB.text=Generation 1
GenerationLimitDialog.gen2CB.text=Generation 2
GenerationLimitDialog.gen3CB.text=Generation 3
GenerationLimitDialog.gen4CB.text=Generation 4
GenerationLimitDialog.gen5CB.text=Generation 5
GenerationLimitDialog.okButton.text=OK
GenerationLimitDialog.gen2Short=Gen 2
GenerationLimitDialog.gen4Short=Gen 4
GenerationLimitDialog.gen1Short=Gen 1
GenerationLimitDialog.gen3Short=Gen 3
GenerationLimitDialog.title=Choose Pokemon to allow
GenerationLimitDialog.cancelButton.text=Cancel
GenerationLimitDialog.warningRomHackLabel.text=<html><center>WARNING: This functionality will NOT work correctly with ROM hacks<br /> that change the available Pokemon or add new ones!</center>
GenerationLimitDialog.warningXYLabel.text=<html><center>To prevent X/Y from softlocking, you must<br />select at least one of Generations 1, 2, 3, or 4</center>
CodeTweaksDialog.headerLabel.text=Choose tweaks to enable...
CodeTweaksDialog.title=Code Tweaks
CodeTweaksDialog.okButton.text=OK
CodeTweaksDialog.cancelButton.text=Cancel
PresetMakeDialog.doneButton.text=Done
PresetMakeDialog.title=Randomization Completed - Seed Details
PresetMakeDialog.produceFileButton.text=Produce File
PresetMakeDialog.gameRandomizedLabel.text=Your game has been successfully randomized!
PresetMakeDialog.settingsToGiveLabel.text=Below are the settings you can give to other people to produce the same randomization as you just did.
PresetMakeDialog.seedFieldLabel.text=Random Seed:
PresetMakeDialog.configStringFieldLabel.text=Config String:
PresetMakeDialog.canProduceFileLabel.text=Alternatively you can produce a file which contains this data which you can then send to people.
PresetLoadDialog.romFileButton.text=...
PresetLoadDialog.presetFileButton.text=...
PresetLoadDialog.acceptButton.text=Apply Randomization Settings
PresetLoadDialog.presetFileLabel.text=Preset File:
PresetLoadDialog.cancelButton.text=Cancel
PresetLoadDialog.romRequiredLabel.text=ROM Required: Enter settings above first.
PresetLoadDialog.title=Use Preset
PresetLoadDialog.seedBoxLabel.text=Random Seed:
PresetLoadDialog.configStringBoxLabel.text=Config String:
PresetLoadDialog.orLabel.text=-OR-
PresetLoadDialog.romFileBoxLabel.text=Rom File:
PresetLoadDialog.romRequiredLabel.textWithROM=ROM Required: %s
PresetLoadDialog.invalidSeedFile=The seed file did not contain valid settings.
PresetLoadDialog.loadingSeedFileFailed=Could not load seed file.
PresetLoadDialog.notRequiredROM=This isn't the required ROM.\nRequired: %s\nThis ROM: %s
PresetLoadDialog.newerVersionRequired=The preset file was generated with a newer randomizer version. Try downloading the latest version.
PresetLoadDialog.olderVersionRequired=The preset file was generated with an older randomizer version. It can only be used with version %s.
CodeTweaks.bwPatch.name=B/W Exp Patch
CodeTweaks.bwPatch.toolTipText=<html>Select this to patch the game you are randomizing to use the Black/White new XP gain system.<br />This system gives EXP in a different way, rewarding players for beating higher level Pokemon with lower levels.<br />This is currently available only for English R/B/Y and G/S/C.
CodeTweaks.nerfXAcc.name=Nerf X Accuracy
CodeTweaks.nerfXAcc.toolTipText=<html>X Accuracy in Generation 1 games is a pretty broken item, given that it gives 100% accuracy to any and all moves used.<br />Apply this code tweak to stop X Accuracy from working on sleep moves, trapping moves & one-hit KO moves.<br />Doing this means that the player cannot use X Accuracy and lock the opponents into infinite sleep/Wrap, nor can they just use one-hit KO moves on everything.<br />Applying this patch will also remove the one-hit KO moves from the "broken moves" list if you use that option, since they aren't really broken without X Accuracy.<br /><b>Credit to Mountebank for this patch.</b>
CodeTweaks.critRateFix.name="Fix" Crit Rate
CodeTweaks.critRateFix.toolTipText=<html>Selecting this option will "fix" Generation 1's critical hit rate to be the same as the other games (1/16), instead of being based on Speed.<br />Focus Energy and Dire Hit will also be fixed to increase crit rate instead of decreasing it.<br />"High crit rate" moves such as Slash will have double the normal crit chance, like in later games.
CodeTweaks.fastestText.toolTipText=<html>Selecting this option will make all text boxes in the game show up with minimum delay, regardless of the text speed the player sets in Options.<br /><b>Credit to Dabomstew/Revo for Gen 3, Kaphotics for Gen 4, Ajarmar for Gen 5, and unknown cheat code makers for Gen 6/7</b>
CodeTweaks.fastestText.name=Fastest Text
CodeTweaks.runningShoes.toolTipText=<html>Selecting this option will allow you to use the Running Shoes in any location, just like in later games.
CodeTweaks.runningShoes.name=Running Shoes Indoors
CodeTweaks.pcPotion.toolTipText=<html>Selecting this option will randomize the Potion that is in your item PC at the beginning of the game to any other "useful" item.
CodeTweaks.pcPotion.name=Randomize PC Potion
CodeTweaks.pikachuEvo.toolTipText=<html>Selecting this option will let you evolve Pikachu into Raichu with a Thunder Stone like any other game.
CodeTweaks.pikachuEvo.name=Allow Pikachu Evolution
CodeTweaks.nationalDex.toolTipText=<html>If this is checked then the National Dex will be given to the player at the start of the game with the regular Pokedex.<br />This is only <i>necessary</i> for FRLG, where certain Pokemon can't evolve if you don't have it.<br />It is useful in other games for tracking Pokemon outside of the regional dex when using the Pokedex.
CodeTweaks.nationalDex.name=Give National Dex at Start
CodeTweaks.typeEffectiveness.toolTipText=<html>If this is checked, the type weakness/strengths/immunities will be updated to the current set.<br />For RBY, this means Ghost will be Super Effective against Psychic, among other changes.
CodeTweaks.typeEffectiveness.name=Update Type Effectiveness
CodeTweaks.forceChallengeMode.toolTipText=<html>If this is checked, then Challenge Mode will be forcibly enabled, bypassing the Key System entirely.<br /><b>You will <i>not</i> be able to access Easy or Normal Mode if you enable this setting.</b>
CodeTweaks.forceChallengeMode.name=Force Challenge Mode
CodeTweaks.lowerCaseNames.toolTipText=<html>If this is selected, all Pokemon names will be made into Camel Case.<br />e.g. VENUSAUR becomes Venusaur.<br />This looks better in Gen3/Gen4 games, and OK in Gen1/Gen2 games.
CodeTweaks.lowerCaseNames.name=Lower Case Pokemon Names
CodeTweaks.catchingTutorial.toolTipText=<html>Selecting this option will randomize the Pokemon participating in the game's catching tutorial.
CodeTweaks.catchingTutorial.name=Randomize Catching Tutorial
CodeTweaks.luckyEgg.toolTipText=<html>Bans Lucky Egg from showing up as any kind of randomized item.<br />If the original game contains a Lucky Egg in any non-randomized place, that will still be the case.
CodeTweaks.luckyEgg.name=Ban Lucky Egg
CodeTweaks.freeLuckyEgg.toolTipText=<html>Causes Professor Juniper to not give you a Lucky Egg for free in the Chargestone Cave (BW)/Celestial Tower (BW2).
CodeTweaks.freeLuckyEgg.name=No Free Lucky Egg
CodeTweaks.maniacItems.toolTipText=<html>Bans maniac items worth more than $10,000 from showing up as any kind of randomized item.
CodeTweaks.maniacItems.name=Ban Big Money Maniac Items
CodeTweaks.sosBattles.toolTipText=<html>Makes it possible for every Pokemon to call allies during wild Pokemon battles.<br />Without this setting, only Pokemon that could call allies in the base game will be able to call allies.
CodeTweaks.sosBattles.name=All Wild Pokemon Can Call Allies
CodeTweaks.balanceStaticLevels.toolTipText=<html>Changes levels of Static Pokemon to be more balanced. Currently applies only to fossil Pokemon in FRLG and BW1.
CodeTweaks.balanceStaticLevels.name=Balance Static Pokemon Levels
CodeTweaks.retainAltFormes.toolTipText=<html>Lets all party Pokemon that are in an alternate forme retain their alternate forme when the game is reset.<br />Without this setting, Mega Evolutions, Primal Reversions, Ash-Greninja, Zygarde Complete Forme, and Ultra Necrozma will revert to their base forme upon reset.<br />In ORAS and Gen 7, this setting also prevents certain alternate formes from being reverted after a battle if these formes are <i>not</i> the result of an in-battle transformation.<br />This includes Primal Groudon, Primal Kyogre, Wishiwashi School Form, and Minior.
CodeTweaks.retainAltFormes.name=Don't Revert Temporary Alt Formes
CodeTweaks.runWithoutRunningShoes.toolTipText=<html>Allows you to run before acquiring the Running Shoes.
CodeTweaks.runWithoutRunningShoes.name=Run Without Running Shoes
CodeTweaks.fasterHpAndExpBars.toolTipText=<html>Doubles the scrolling speed of the HP and EXP bars that appear in battle.<br />In practice, this makes them scroll at the same speed as in the Gen 3 games.
CodeTweaks.fasterHpAndExpBars.name=Faster HP and EXP Bars
CodeTweaks.fastDistortionWorld.name=Fast Distortion World
CodeTweaks.fastDistortionWorld.toolTipText=<html>Cuts out most of the Distortion World by instantly warping you to the Cyrus fight when you enter it.
CodeTweaks.updateRotomFormeTyping.name=Update Rotom Appliance Typings
CodeTweaks.updateRotomFormeTyping.toolTipText=<html>Updates the typings of Rotom's alternate formes (i.e., the appliances) to match the typings they have in Gen 5 and onwards.<br />For example, Wash Rotom will change from Electric/Ghost to Electric/Water.
CodeTweaks.disableLowHpMusic.name=Disable Low HP Music
CodeTweaks.disableLowHpMusic.toolTipText=<html>Disables the music that plays when one of the player's Pokemon is at low HP in battle, ensuring that the current song will continue playing no matter what.
CustomNamesEditorDialog.trainerNamesSP.TabConstraints.tabTitle=Trainer Names
CustomNamesEditorDialog.title=Custom Names Editor
CustomNamesEditorDialog.closeBtn.text=Close
CustomNamesEditorDialog.saveBtn.text=Save
CustomNamesEditorDialog.nicknamesSP.TabConstraints.tabTitle=Pokemon Nicknames
CustomNamesEditorDialog.doublesTrainerClassesSP.TabConstraints.tabTitle=Doubles Trainer Classes
CustomNamesEditorDialog.doublesTrainerNamesSP.TabConstraints.tabTitle=Doubles Trainer Names
CustomNamesEditorDialog.trainerClassesSP.TabConstraints.tabTitle=Trainer Classes
GUI.pbsFollowMegaEvosCheckBox.text=Follow Mega Evolutions
GUI.pbsFollowMegaEvosCheckBox.toolTipText=<html>When this is selected and base stats are shuffled or randomized, Mega Evolutions will inherit from their base form's rolls.<br />Shuffle: The same new ordering of stats will be used.<br />Randomized: The Mega Evolution will have the same proportion of stats as the base form.<br />Not recommended for use in Gen 7 unless you know what you're doing; see the Important Information page on the wiki for more info.
GUI.paFollowMegaEvosCheckBox.text=Follow Mega Evolutions
GUI.paFollowMegaEvosCheckBox.toolTipText=<html>When this is selected and abilities are randomized, non-split Mega Evolutions of a Pokemon will inherit that Pokemon's random abilities.<br />Not recommended for use in Gen 7 unless you know what you're doing; see the Important Information page on the wiki for more info.
GUI.ptFollowMegaEvosCheckBox.text=Follow Mega Evolutions
GUI.ptFollowMegaEvosCheckBox.toolTipText=<html>When this is selected and types are randomized, non-split Mega Evolutions of a Pokemon will inherit that Pokemon's random types (perhaps adding another type).<br />Not recommended for use in Gen 7 unless you know what you're doing; see the Important Information page on the wiki for more info.
GUI.spAllowAltFormesCheckBox.text=Allow Alternate Formes
GUI.spAllowAltFormesCheckBox.toolTipText=<html>Allow alternate formes of Pokemon, such as the various formes of Rotom or Deoxys, to appear as Starter Pokemon.<br />Note that their traits are randomized/shuffled separately from their base formes.
GUI.stpAllowAltFormesCheckBox.text=Allow Alternate Formes
GUI.stpAllowAltFormesCheckBox.toolTipText=<html>Allow alternate formes of Pokemon, such as the various formes of Rotom or Deoxys, to appear as Static Pokemon.<br />Note that their traits are randomized/shuffled separately from their base formes.
GUI.stpSwapMegaEvosCheckBox.text=Swap Mega Evolvables
GUI.stpSwapMegaEvosCheckBox.toolTipText=<html>Swap Statics capable of Mega Evolution with another Pokemon capable of Mega Evolution.<br />This affects Lucario in X/Y and Latios/Latias in OR/AS.
GUI.stpPercentageLevelModifierCheckBox.text=Percentage Level Modifier:
GUI.stpPercentageLevelModifierCheckBox.tooltipText=Checking this will enable a percentage-based level modifier for every Static Pokemon.
GUI.tpSwapMegaEvosCheckBox.text=Swap Mega Evolvables
GUI.tpSwapMegaEvosCheckBox.toolTipText=<html>Swap Trainer Pokemon capable of Mega Evolution with another Pokemon capable of Mega Evolution.<br />This only affects Trainer Pokemon that actually hold a Mega Stone (so, for example, it will affect Diantha's Gardevoir but not every Gardevoir)<br />If the "Limit Pokemon" general option is used to remove all Mega Evolutions from the game, this setting will be disabled.
GUI.ptForceDualTypeCheckBox.text=Force Dual Types
GUI.ptForceDualTypeCheckBox.toolTipText=Checking this will force all Pokemon to have two types when types are randomized
GUI.wpAllowAltFormesCheckBox.text=Allow Alternate Formes
GUI.wpAllowAltFormesCheckBox.toolTipText=<html>Allow alternate formes of Pokemon, such as the various formes of Rotom or Deoxys, to appear as Wild Pokemon.<br />Note that their traits are randomized/shuffled separately from their base formes.
GUI.tpDoubleBattleModeCheckBox.text=Double Battle Mode
GUI.tpDoubleBattleModeCheckBox.toolTipText=<html>Sets all Trainer battles to be double battles instead of single battles. The first rival battle is excluded.<br /><br /><b>Be aware that there are a few issues with this mode.</b> In particular: <br /><ul><li>Entering a double battle with a single Pokemon causes some generation-dependent issues:<br /><ul><li>Gen 3: The empty spot will be filled by a duplicate of the single Pokemon, and entering the "Pokémon" menu will softlock the game.<li>Gen 4: The empty spot will be filled by a garbled sprite that only can use Struggle.<li>Gen 6/7: The empty spot will be filled by a shiny Bulbasaur that cannot act.</ul><li>In Gen 5 games, Trainer pairs that are double battles in the regular game will have one incorrect text box if you let them spot you.<li>In Gen 6 games, regular Trainers will have some text issues if you talk to them directly.</ul>
GUI.tpAddMorePokemonForLabel.text=Additional Pokemon for...
GUI.tpBossTrainersCheckBox.text=Boss Trainers
GUI.tpImportantTrainersCheckBox.text=Important Trainers
GUI.tpRegularTrainersCheckBox.text=Regular Trainers
GUI.tpBossTrainersCheckBox.toolTipText=<html>Check this to add additional Pokemon to "Boss" Trainers (select amount of new Pokemon below). Trainers will not get more than 6 total Pokemon.<br />Boss Trainers include Gym Leaders, Kahunas, Team Leaders, Elite Four, and Champions.
GUI.tpImportantTrainersCheckBox.toolTipText=<html>Check this to add additional Pokemon to "Important" Trainers (select amount of new Pokemon below). Trainers will not get more than 6 total Pokemon.<br />Important Trainers include Rivals/Friends, Team Admins, and other important story battles (such as Colress in BW2 and Sycamore in XY).
GUI.tpRegularTrainersCheckBox.toolTipText=<html>Check this to add additional Pokemon to regular Trainers (select amount of new Pokemon below). Trainers will not get more than 6 total Pokemon.<br />Regular Trainers include all trainers that don't qualify as "Boss" or "Important" Trainers.
GUI.peAllowAltFormesCheckBox.text=Allow Alternate Formes
GUI.peAllowAltFormesCheckBox.toolTipText=<html>Allow Pokemon to evolve into alternate formes of Pokemon, including into regional variants.
GUI.tpRandomShinyTrainerPokemonCheckBox.text=Random Shiny Trainer Pokemon
GUI.tpRandomShinyTrainerPokemonCheckBox.toolTipText=Gives a 1/256 chance of Trainer Pokemon being shiny.
GUI.totpPanel.title=Totem Pokemon
GUI.totpAllyPanel.title=Ally Pokemon
GUI.totpAuraPanel.title=Auras
GUI.totpRandomizeHeldItemsCheckBox.text=Randomize Held Items
GUI.totpAllowAltFormesCheckBox.text=Allow Alternate Formes
GUI.totpPercentageLevelModifierCheckBox.text=Percentage Level Modifier:
GUI.totpUnchangedRadioButton.text=Unchanged
GUI.totpRandomRadioButton.text=Random
GUI.totpRandomSimilarStrengthRadioButton.text=Random (similar strength)
GUI.totpAllyUnchangedRadioButton.text=Unchanged
GUI.totpAllyRandomRadioButton.text=Random
GUI.totpAllyRandomSimilarStrengthRadioButton.text=Random (similar strength)
GUI.totpAuraUnchangedRadioButton.text=Unchanged
GUI.totpAuraRandomRadioButton.text=Random
GUI.totpAuraRandomRadioButton.toolTipText.=<html>Auras will be completely randomized.<br />The possible auras are +1/+2/+3 to a single stat or to every stat.
GUI.totpAuraRandomSameStrengthRadioButton.text=Random (same strength)
GUI.totpAuraRandomSameStrengthRadioButton.toolTipText=<html>Auras will be randomized to auras with the same net gain of stages.<br />For example, a Totem Pokemon that previously had +2 Speed could get +2 Defense instead.<br />Totem Pokemon with +1/+2 to every stat will not have their auras randomized, since those auras are the only ones with 5 and 10 stages respectively.
GUI.totpUnchangedRadioButton.toolTipText=Don't change Totem Pokemon species.
GUI.totpRandomRadioButton.toolTipText=Randomize Totem Pokemon species.
GUI.totpRandomSimilarStrengthRadioButton.toolTipText=Replace every Totem Pokemon with a Pokemon of similar strength.
GUI.totpAllyRandomRadioButton.toolTipText=Randomize Ally Pokemon completely.
GUI.totpAllyRandomSimilarStrengthRadioButton.toolTipText=Replace Ally Pokemon with Pokemon of similar strength.
GUI.totpAuraUnchangedRadioButton.toolTipText=Don't change auras.
GUI.totpRandomizeHeldItemsCheckBox.toolTipText=Replace Totem Pokemon's held items with different consumable items.
GUI.totpAllowAltFormesCheckBox.toolTipText=Allows alternate formes of Pokemon to appear as Totem/Ally Pokemon.
GUI.totpPercentageLevelModifierCheckBox.toolTipText=Checking this will enable a percentage-based level modifier for every Totem/Ally Pokemon.
GUI.totpAllyUnchangedRadioButton.toolTipText=Don't change Ally Pokemon.
GUI.pmsEvolutionMovesCheckBox.text=Evolution Moves for All Pokemon
GUI.pmsEvolutionMovesCheckBox.toolTipText=<html>Selecting this option will cause every Pokemon to learn a move upon evolution.<br /><br /><b>Use carefully with Sun/Moon 1.0 - </b>due to a glitch, if a Pokemon has an evolution move and also learns a move<br />on the level that it evolved at, it will only learn the evolution move upon evolving.
GUI.windowTitle=Universal Pokemon Randomizer ZX v%s
GUI.wikiLinkLabel.text=<html><a href="https://github.com/Ajarmar/universal-pokemon-randomizer-zx/wiki">Wiki</a>
GUI.paWeighDuplicatesTogetherCheckBox.text=Combine Duplicate Abilities
GUI.paWeighDuplicatesTogetherCheckBox.toolTipText=<html>This setting causes abilities that have the exact same effect to be considered as the same ability for randomization probability purposes. Every variation of the respective abilities can still appear.<br />This applies to:<br /><b>Gen 3+:</b> Insomnia/Vital Spirit, Clear Body/White Smoke, Huge Power/Pure Power, Battle Armor/Shell Armor, Cloud Nine/Air Lock<br /><b>Gen 4+:</b> Filter/Solid Rock<br /><b>Gen 5+:</b> Rough Skin/Iron Barbs, Mold Breaker/Turboblaze/Teravolt<br /><b>Gen 7+:</b> Wimp Out/Emergency Exit, Queenly Majesty/Dazzling, Gooey/Tangling Hair, Receiver/Power of Alchemy, Clear Body/White Smoke/Full Metal Body, Multiscale/Shadow Shield, Filter/Solid Rock/Prism Armor
GUI.applyGameUpdateMenuItem.text=Load Game Update
GUI.gameUpdateApplied=<html>Update saved for game: %s.<br />Note that if the update file is moved or renamed, you will have to reapply it.
GUI.nonMatchingGameUpdate=<html>Supplied file %s is not a valid game update for %s.<br />Please check your game updates and try again.
GUI.invalidGameUpdate=<html>Supplied file %s does not appear to be a valid game update.</br />Please check if the file is a valid game update.
GUI.savingWithGameUpdate=<html>You've supplied a game update, so your game can only be output as a LayeredFS directory.<br /><b>Make sure you have this same game update installed in Citra or on your 3DS!</b>
GUI.removeGameUpdateMenuItem.text=Unload Game Update
GUI.keepGameLoadedAfterRandomizingMenuItem.text=Keep Game Loaded After Randomizing
GUI.unloadGameAfterRandomizingMenuItem.text=Unload Game After Randomizing
GUI.keepGameLoadedAfterRandomizing=Your loaded game and settings will now remain after a successful randomization.
GUI.unloadGameAfterRandomizing=Your loaded game and settings will now be unloaded after a successful randomization (this is the default setting).
GUI.firstStart=Welcome to the Universal Pokemon Randomizer ZX! You are using version %s.
GUI.loadGetSettingsMenuItem.text=Get/Load Settings String
GUI.invalidSettingsString=The settings string you attempted to load is invalid.
GUI.settingsStringTooNew=The settings string was generated with a newer randomizer version. Try downloading the latest version.
GUI.settingsStringTooOld=The settings string was generated with an older randomizer version. It can only be used with version %s.
GUI.settingsStringLoaded=Settings loaded from string.
GUI.settingsStringOlder=This settings string was created by an older randomizer version.\nThe randomizer will attempt to open it anyway, but you should look out for new options added since you made it.
GUI.pleaseUseTheLauncher=<html>The randomizer was not started via the launcher. You will not be able to load 3DS games into the randomizer.<br /><br />To randomize 3DS games, please start the randomizer by double-clicking on the appropriate launcher<br/>for your operating system (e.g., use launcher_WINDOWS.bat on Windows).<br /><br />If you are seeing this message despite using the launcher, make sure that you are using the<br />launcher bundled with version 4.2.0 (or later) of the randomizer.
GUI.invalidRomMessage=The selected ROM does not appear to be a clean, official ROM.\nThe randomizer will still attempt to load it, but you may experience issues.
GUI.tmFollowEvolutionsCheckBox.text=Follow Evolutions
GUI.tmFollowEvolutionsCheckBox.toolTipText=<html>When this is selected and TM compatibility is randomized, evolutions of a Pokemon will inherit that Pokemon's TM compatibilities.<br />Additionally, when a Pokemon evolves, each TM will have:<br /><b>Random (prefer same type)</b>: A 90% chance of becoming learnable if the Pokemon just gained its type through evolution, a 10% chance otherwise.<br/><b>Random (completely)</b>: A 25% chance of becoming learnable regardless of type.
GUI.mtFollowEvolutionsCheckBox.text=Follow Evolutions
GUI.mtFollowEvolutionsCheckBox.toolTipText=<html>When this is selected and move tutor compatibility is randomized, evolutions of a Pokemon will inherit that Pokemon's move tutor compatibilities.<br />Additionally, when a Pokemon evolves, each move will have:<br /><b>Random (prefer same type)</b>: A 90% chance of becoming learnable if the Pokemon just gained its type through evolution, a 10% chance otherwise.<br/><b>Random (completely)</b>: A 25% chance of becoming learnable regardless of type.
GUI.stpFixMusicAllCheckBox.text=Fix Music
GUI.stpFixMusicAllCheckBox.toolTipText=<html>Fixes the music for all Static Pokemon encounters so that even when randomized, encounters that should have special music will play the correct song.<br />Note that in Gen 4/5, if special music is assigned to a Pokemon then it will <b>always</b> play when battling that Pokemon, even if you just find it normally in the wild.
GUI.tpHeldItemsLabel.text=Add Held Items to...
GUI.tpBossTrainersItemsCheckBox.text=Boss Trainers
GUI.tpBossTrainersItemsCheckBox.toolTipText=<html>Check this to add or replace held items for the Pokemon of "Boss" Trainers.<br />Boss Trainers include Gym Leaders, Kahunas, Team Leaders, Elite Four, and Champions.
GUI.tpImportantTrainersItemsCheckBox.text=Important Trainers
GUI.tpImportantTrainersItemsCheckBox.toolTipText=<html>Check this to add or replace held items for the Pokemon of "Important" Trainers.<br />Important Trainers include Rivals/Friends, Team Admins, and other important story battles (such as Colress in BW2 and Sycamore in XY).
GUI.tpRegularTrainersItemsCheckBox.text=Regular Trainers
GUI.tpRegularTrainersItemsCheckBox.toolTipText=<html>Check this to add or replace held items for the Pokemon of regular Trainers.<br />Regular Trainers include all trainers that don't qualify as "Boss" or "Important" Trainers.
GUI.tpConsumableItemsOnlyCheckBox.text=Consumable Only
GUI.tpConsumableItemsOnlyCheckBox.tooltip=<html>Items will be chosen randomly from all single-use held items that a Pokemon can eat/use on its own.<br />This is mostly berries, with a few other items like Focus Sash and White Herb.</html>
GUI.tpSensibleItemsCheckBox.text=Sensible Items
GUI.tpSensibleItemsCheckBox.tooltip=<html>Items will be chosen randomly from a subset of the held items that "make sense" for a given Pokemon.<br />For example, Silk Scarf won't be an option for a given Pokemon unless it knows at least one damaging normal move.<br />Another example is that a Yache berry will only be an option on Pokemon that are weak or double weak to ice.</html>
GUI.tpHighestLevelGetsItemCheckBox.text=Highest Level Only
GUI.tpHighestLevelGetsItemCheckBox.tooltip=<html>If this option is selected, a held item will be given only to a trainer's highest level Pokemon (or one of them, if there are multiple at that level).<br />If this is unchecked, all of a Trainer's Pokemon will be given items.</html>
GUI.pbsAssignEvoStatsRandomlyCheckBox.text=Randomize Added Stats on Evolution
GUI.pbsAssignEvoStatsRandomlyCheckBox.tooltipText=<html>When a Pokemon evolves, the additional BST it gains will be assigned randomly on top of the pre-evo's stats <br />instead of the evolved Pokemon having the exact same ratio between its stats as the pre-evo.<br /><br />Applies to both regular Evolutions and Mega Evolutions (only if "Follow Evolutions"/"Follow Mega Evolutions" is selected).
GUI.tpEliteFourUniquePokemonCheckBox.text=Pokemon League Has Unique Pokemon:
GUI.tpEliteFourUniquePokemonCheckBox.toolTipText=<html>Enabling this setting will ensure that Pokemon League Trainers (Elite Four, Champions and BW1 N/Ghetsis) <br />are given the specified number of unique Pokemon that will not appear on any other Trainer.<br />The Elite Four Trainers' highest level Pokemon are the ones that will be unique;<br/>in the case of a level tie, it will be the Pokemon further back in the party.<br />Does not apply to postgame Pokemon League battles.
GUI.tpBetterMovesetsCheckBox.text=Better Movesets
GUI.tpBetterMovesetsCheckBox.toolTipText=<html>Attempts to give Trainer Pokemon better movesets by including TM moves/tutor moves/egg moves/pre-evolution moves,<br />and picking moves that synergize with the Pokemon's ability/stats/other moves.
BatchRandomizationSettingsDialog.title=Batch Randomization Settings
BatchRandomizationSettingsDialog.enableCheckBox.text=Enable batch randomization
BatchRandomizationSettingsDialog.enableCheckBox.toolTipText=If selected, generate the number of randomized ROMs specified.
BatchRandomizationSettingsDialog.startingIndexLabel.text=Starting index
BatchRandomizationSettingsDialog.startingIndexSpinner.toolTipText=Starting index for the first generated ROM.
BatchRandomizationSettingsDialog.numberOfRandomizedROMsLabel.text=Number of randomized ROMs
BatchRandomizationSettingsDialog.numberOfRandomizedROMsSpinner.toolTipText=Number of ROMs to generate in a single batch.
BatchRandomizationSettingsDialog.outputDirectoryButton.text=Output directory...
BatchRandomizationSettingsDialog.outputDirectoryButton.toolTipText=Choose the output directory for your randomized ROMs.
BatchRandomizationSettingsDialog.fileNamePrefixLabel.text=File name prefix
BatchRandomizationSettingsDialog.fileNamePrefixTextBox.toolTipText=File name prefix for the generated ROMs.
BatchRandomizationSettingsDialog.autoAdvanceIndexCheckBox.text=Automatically advance index after batch
BatchRandomizationSettingsDialog.autoAdvanceIndexCheckBox.toolTipText=If selected, the starting index will be automatically updated to the next index after a batch is run.
BatchRandomizationSettingsDialog.generateLogFilesCheckBox.text=Generate log files
BatchRandomizationSettingsDialog.generateLogFilesCheckBox.toolTipText=If selected, save logs for the randomized ROMs.
BatchRandomizationSettingsDialog.okButton.text=OK
BatchRandomizationSettingsDialog.cancelButton.text=Cancel