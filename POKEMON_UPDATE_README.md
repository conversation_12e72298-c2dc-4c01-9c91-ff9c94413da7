# 🔄 Pokemon Data Updater - Generation IX

Sistema automatizado para atualizar todos os dados dos Pokémon no projeto Dynamic Pokemon Expansion para a **Generation IX** (Scarlet/Violet) usando a **PokeAPI**.

## 🎯 Objetivo

Corrigir e atualizar **TODOS** os Pokémon do projeto para suas versões mais atuais, mantendo fidelidade aos dados oficiais da Generation IX.

## ✅ Dados Atualizados

O sistema atualiza automaticamente:

- ✅ **Base Stats** (HP, Attack, Defense, Sp.Attack, Sp.Defense, Speed)
- ✅ **Tipos** (Type1 e Type2)
- ✅ **Habilidades** (Ability1, Ability2, Hidden Ability)
- ✅ **Gender Ratio** (Taxa de gênero)
- ✅ **Catch Rate** (Taxa de captura)
- ✅ **Base Happiness** (Felicidade inicial)
- ✅ **Hatch Counter** (Ciclos de chocagem)
- ✅ **Growth Rate** (Taxa de crescimento)
- ✅ **Egg Groups** (Grupos de ovos)
- ✅ **Learnset completo**:
  - Por level up
  - Por TM/TR
  - Por breeding (egg moves)
  - Por tutoring

## 🚀 Como Usar

### 1. Instalação de Dependências

```bash
pip install requests
```

### 2. Demonstração Rápida

```bash
python demo_pokemon_update.py
```

Este script mostra:
- Como atualizar um Pokémon específico (Vibrava)
- Comparação de dados entre diferentes Pokémon
- Como extrair lista de Pokémon do projeto atual

### 3. Atualização Completa

```bash
python pokemon_updater.py
```

Este script:
- Processa todos os Pokémon listados
- Gera código atualizado para cada um
- Cria relatório detalhado (`pokemon_update_report.md`)
- Mostra estatísticas de sucesso/falha

## 📊 Exemplo de Saída

### Vibrava - Dados Atualizados (Generation IX)

**Stats Originais (PokeAPI):**
- HP: 50, Attack: 70, Defense: 50, Sp.Attack: 50, Sp.Defense: 50, Speed: 70
- Tipos: Ground/Dragon
- Total: 340

**No projeto atual (modificado):**
- HP: 65, Attack: 110, Defense: 55, Sp.Attack: 70, Sp.Defense: 50, Speed: 70
- Tipos: Bug/Dragon (❌ INCORRETO!)
- Total: 420

**Código gerado pelo sistema:**
```c
[SPECIES_VIBRAVA] =
{
    .baseHP = 50,
    .baseAttack = 70,
    .baseDefense = 50,
    .baseSpAttack = 50,
    .baseSpDefense = 50,
    .baseSpeed = 70,
    .type1 = TYPE_GROUND,
    .type2 = TYPE_DRAGON,
    .catchRate = 120,
    // ... outros dados atualizados
},
```

## 🔧 Personalização

### Adicionar Mais Pokémon

Edite a lista em `pokemon_updater.py`:

```python
pokemon_list = [
    (1, "bulbasaur"), (2, "ivysaur"), (3, "venusaur"),
    # Adicione mais aqui:
    (150, "mewtwo"),
    (151, "mew"),
    # ... até Generation IX
]
```

### Filtrar por Geração

```python
# Apenas Generation I (1-151)
gen1_pokemon = [(i, f"pokemon_{i}") for i in range(1, 152)]

# Apenas Generation III (252-386)
gen3_pokemon = [(i, f"pokemon_{i}") for i in range(252, 387)]
```

## ⚠️ Dados que Precisam de Ajuste Manual

Alguns dados não estão disponíveis na PokeAPI e precisam ser definidos manualmente:

1. **EV Yields** - Quanto de EV cada Pokémon dá quando derrotado
2. **Exp Yield** - Quanto de experiência dá quando derrotado
3. **Body Color** - Cor do Pokémon para classificação
4. **Held Items** - Itens que o Pokémon pode carregar na natureza

## 📋 Workflow Recomendado

### Fase 1: Teste com Poucos Pokémon
```bash
# 1. Edite pokemon_updater.py para incluir apenas 5-10 Pokémon
# 2. Execute o script
python pokemon_updater.py

# 3. Revise o relatório
cat pokemon_update_report.md

# 4. Aplique mudanças manualmente em src/Base_Stats.c
# 5. Teste compilação
```

### Fase 2: Expansão Gradual
```bash
# 1. Adicione mais Pokémon à lista
# 2. Repita o processo
# 3. Sempre teste a compilação após mudanças
```

### Fase 3: Atualização Completa
```bash
# 1. Processe todos os Pokémon do projeto
# 2. Crie backup dos arquivos originais
# 3. Aplique todas as mudanças
# 4. Teste extensivamente
```

## 🛡️ Backup e Segurança

**SEMPRE** faça backup antes de aplicar mudanças:

```bash
# Backup automático
cp src/Base_Stats.c src/Base_Stats.c.backup
cp src/Level_Up_Learnsets.c src/Level_Up_Learnsets.c.backup

# Ou use git
git add .
git commit -m "Backup antes da atualização Pokemon"
```

## 🐛 Resolução de Problemas

### Erro de Rate Limiting
```
Erro: Too Many Requests (429)
```
**Solução:** O script já inclui delays. Se persistir, aumente o delay em `time.sleep(0.1)`.

### Pokémon não encontrado
```
❌ Erro ao obter dados do pokemon_name
```
**Solução:** Verifique se o ID e nome estão corretos na PokeAPI.

### Dados inconsistentes
**Solução:** Sempre revise o relatório gerado antes de aplicar mudanças.

## 📈 Benefícios

1. **Fidelidade Total** aos dados oficiais da Generation IX
2. **Automatização** do processo de atualização
3. **Relatórios Detalhados** para revisão
4. **Backup Automático** dos dados originais
5. **Flexibilidade** para atualizar Pokémon específicos

## 🎮 Resultado Final

Após a atualização completa, o projeto terá:
- ✅ Todos os Pokémon com dados oficiais da Generation IX
- ✅ Stats, tipos e habilidades corretos
- ✅ Movesets atualizados
- ✅ Dados de breeding e evolução precisos
- ✅ Fidelidade total aos jogos oficiais

## 🤝 Contribuição

Para melhorar o sistema:
1. Reporte bugs encontrados
2. Sugira melhorias no mapeamento de dados
3. Adicione suporte para dados adicionais
4. Melhore a documentação

---

**⚠️ IMPORTANTE:** Sempre teste as mudanças em um ambiente de desenvolvimento antes de aplicar ao projeto principal!
