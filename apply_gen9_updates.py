#!/usr/bin/env python3
"""
Script para aplicar os dados da Generation IX aos arquivos do projeto
"""

import os
import re
import glob
from typing import Dict, List, Tuple

class Gen9DataApplier:
    def __init__(self):
        self.base_stats_updates = {}
        self.learnset_updates = {}

    def load_gen9_data(self):
        """Carrega todos os dados da Generation IX"""

        print("📁 Carregando dados da Generation IX...")

        if not os.path.exists("pokemon_updates_gen9"):
            print("❌ Pasta pokemon_updates_gen9 não encontrada!")
            return False

        files = glob.glob("pokemon_updates_gen9/pokemon_*.txt")
        print(f"📊 Encontrados {len(files)} arquivos de dados")

        for file_path in files:
            # Extrai ID do Pokémon
            match = re.search(r'pokemon_(\d+)_\d+\.txt', file_path)
            if not match:
                continue

            pokemon_id = int(match.group(1))

            # Carrega dados do arquivo
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Extrai base stats
            base_stats = self.extract_base_stats_from_content(content)
            if base_stats:
                self.base_stats_updates[pokemon_id] = base_stats

            # Extrai learnset
            learnset = self.extract_learnset_from_content(content)
            if learnset:
                self.learnset_updates[pokemon_id] = learnset

        print(f"✅ Carregados {len(self.base_stats_updates)} base stats")
        print(f"✅ Carregados {len(self.learnset_updates)} learnsets")
        return True

    def extract_base_stats_from_content(self, content: str) -> Dict:
        """Extrai base stats do conteúdo do arquivo"""

        # Procura pelo bloco de base stats
        pattern = r'\[SPECIES_POKEMON_\d+\] =\s*\{([^}]+)\}'
        match = re.search(pattern, content, re.DOTALL)

        if not match:
            return None

        stats_block = match.group(1)

        # Extrai valores individuais
        stats = {}
        stat_patterns = {
            'baseHP': r'\.baseHP = (\d+)',
            'baseAttack': r'\.baseAttack = (\d+)',
            'baseDefense': r'\.baseDefense = (\d+)',
            'baseSpAttack': r'\.baseSpAttack = (\d+)',
            'baseSpDefense': r'\.baseSpDefense = (\d+)',
            'baseSpeed': r'\.baseSpeed = (\d+)',
            'type1': r'\.type1 = (TYPE_\w+)',
            'type2': r'\.type2 = (TYPE_\w+)',
            'ability1': r'\.ability1 = (ABILITY_\w+)',
            'ability2': r'\.ability2 = (ABILITY_\w+)',
            'hiddenAbility': r'\.hiddenAbility = (ABILITY_[\w-]+)',
            'catchRate': r'\.catchRate = (\d+)',
            'expYield': r'\.expYield = (\d+)',
            'genderRatio': r'\.genderRatio = PERCENT_FEMALE\(([\d.]+)\)',
            'eggCycles': r'\.eggCycles = (\d+)',
            'friendship': r'\.friendship = (\d+)'
        }

        for stat_name, pattern in stat_patterns.items():
            match = re.search(pattern, stats_block)
            if match:
                stats[stat_name] = match.group(1)

        return stats if stats else None

    def extract_learnset_from_content(self, content: str) -> List[Tuple[int, str]]:
        """Extrai learnset do conteúdo do arquivo"""

        # Procura pelo learnset (aceita qualquer nome)
        pattern = r'static const struct LevelUpMove s[^[]+LevelUpLearnset\[\] = \{([^}]+)\}'
        match = re.search(pattern, content, re.DOTALL)

        if not match:
            return None

        moves_block = match.group(1)
        moves = []

        # Extrai moves individuais
        move_pattern = r'LEVEL_UP_MOVE\(\s*(\d+),\s*(MOVE_\w+)\)'
        for move_match in re.finditer(move_pattern, moves_block):
            level = int(move_match.group(1))
            move = move_match.group(2)
            moves.append((level, move))

        return moves if moves else None

    def update_base_stats_file(self):
        """Atualiza o arquivo Base_Stats.c"""

        print("\n🔧 Atualizando Base_Stats.c...")

        if not os.path.exists("src/Base_Stats.c"):
            print("❌ Arquivo src/Base_Stats.c não encontrado!")
            return False

        # Backup
        with open("src/Base_Stats.c", 'r', encoding='utf-8') as f:
            original_content = f.read()

        with open("src/Base_Stats.c.backup_gen9", 'w', encoding='utf-8') as f:
            f.write(original_content)

        print("📋 Backup criado: src/Base_Stats.c.backup_gen9")

        # Atualiza conteúdo
        updated_content = original_content
        updates_applied = 0

        # Mapeia IDs para nomes de espécies
        species_map = self.get_species_mapping()

        for pokemon_id, stats in self.base_stats_updates.items():
            # Procura pelo entry existente usando o nome correto
            species_name = species_map.get(pokemon_id, f"SPECIES_POKEMON_{pokemon_id}")
            pattern = rf'\[{species_name}\] =\s*\{{[^}}]+\}}'

            match = re.search(pattern, updated_content, re.DOTALL)
            if match:
                old_entry = match.group(0)

                # Cria novo entry
                new_entry = self.create_base_stats_entry(pokemon_id, stats)

                # Substitui
                updated_content = updated_content.replace(old_entry, new_entry)
                updates_applied += 1

                if updates_applied <= 5:  # Mostra apenas os primeiros 5
                    print(f"✅ Atualizado Pokémon #{pokemon_id}")

        # Salva arquivo atualizado
        with open("src/Base_Stats.c", 'w', encoding='utf-8') as f:
            f.write(updated_content)

        print(f"📊 {updates_applied} base stats atualizados!")
        return True

    def get_species_mapping(self) -> Dict[int, str]:
        """Mapeia IDs de Pokémon para nomes de espécies"""

        # Primeiros 151 Pokémon com nomes específicos
        species_names = {
            1: "SPECIES_BULBASAUR", 2: "SPECIES_IVYSAUR", 3: "SPECIES_VENUSAUR",
            4: "SPECIES_CHARMANDER", 5: "SPECIES_CHARMELEON", 6: "SPECIES_CHARIZARD",
            7: "SPECIES_SQUIRTLE", 8: "SPECIES_WARTORTLE", 9: "SPECIES_BLASTOISE",
            10: "SPECIES_CATERPIE", 11: "SPECIES_METAPOD", 12: "SPECIES_BUTTERFREE",
            13: "SPECIES_WEEDLE", 14: "SPECIES_KAKUNA", 15: "SPECIES_BEEDRILL",
            16: "SPECIES_PIDGEY", 17: "SPECIES_PIDGEOTTO", 18: "SPECIES_PIDGEOT",
            19: "SPECIES_RATTATA", 20: "SPECIES_RATICATE", 21: "SPECIES_SPEAROW",
            22: "SPECIES_FEAROW", 23: "SPECIES_EKANS", 24: "SPECIES_ARBOK",
            25: "SPECIES_PIKACHU", 26: "SPECIES_RAICHU", 27: "SPECIES_SANDSHREW",
            28: "SPECIES_SANDSLASH", 29: "SPECIES_NIDORAN_F", 30: "SPECIES_NIDORINA",
            31: "SPECIES_NIDOQUEEN", 32: "SPECIES_NIDORAN_M", 33: "SPECIES_NIDORINO",
            34: "SPECIES_NIDOKING", 35: "SPECIES_CLEFAIRY", 36: "SPECIES_CLEFABLE",
            37: "SPECIES_VULPIX", 38: "SPECIES_NINETALES", 39: "SPECIES_JIGGLYPUFF",
            40: "SPECIES_WIGGLYTUFF", 41: "SPECIES_ZUBAT", 42: "SPECIES_GOLBAT",
            43: "SPECIES_ODDISH", 44: "SPECIES_GLOOM", 45: "SPECIES_VILEPLUME",
            46: "SPECIES_PARAS", 47: "SPECIES_PARASECT", 48: "SPECIES_VENONAT",
            49: "SPECIES_VENOMOTH", 50: "SPECIES_DIGLETT", 51: "SPECIES_DUGTRIO",
            52: "SPECIES_MEOWTH", 53: "SPECIES_PERSIAN", 54: "SPECIES_PSYDUCK",
            55: "SPECIES_GOLDUCK", 56: "SPECIES_MANKEY", 57: "SPECIES_PRIMEAPE",
            58: "SPECIES_GROWLITHE", 59: "SPECIES_ARCANINE", 60: "SPECIES_POLIWAG",
            61: "SPECIES_POLIWHIRL", 62: "SPECIES_POLIWRATH", 63: "SPECIES_ABRA",
            64: "SPECIES_KADABRA", 65: "SPECIES_ALAKAZAM", 66: "SPECIES_MACHOP",
            67: "SPECIES_MACHOKE", 68: "SPECIES_MACHAMP", 69: "SPECIES_BELLSPROUT",
            70: "SPECIES_WEEPINBELL", 71: "SPECIES_VICTREEBEL", 72: "SPECIES_TENTACOOL",
            73: "SPECIES_TENTACRUEL", 74: "SPECIES_GEODUDE", 75: "SPECIES_GRAVELER",
            76: "SPECIES_GOLEM", 77: "SPECIES_PONYTA", 78: "SPECIES_RAPIDASH",
            79: "SPECIES_SLOWPOKE", 80: "SPECIES_SLOWBRO", 81: "SPECIES_MAGNEMITE",
            82: "SPECIES_MAGNETON", 83: "SPECIES_FARFETCHD", 84: "SPECIES_DODUO",
            85: "SPECIES_DODRIO", 86: "SPECIES_SEEL", 87: "SPECIES_DEWGONG",
            88: "SPECIES_GRIMER", 89: "SPECIES_MUK", 90: "SPECIES_SHELLDER",
            91: "SPECIES_CLOYSTER", 92: "SPECIES_GASTLY", 93: "SPECIES_HAUNTER",
            94: "SPECIES_GENGAR", 95: "SPECIES_ONIX", 96: "SPECIES_DROWZEE",
            97: "SPECIES_HYPNO", 98: "SPECIES_KRABBY", 99: "SPECIES_KINGLER",
            100: "SPECIES_VOLTORB", 101: "SPECIES_ELECTRODE", 102: "SPECIES_EXEGGCUTE",
            103: "SPECIES_EXEGGUTOR", 104: "SPECIES_CUBONE", 105: "SPECIES_MAROWAK",
            106: "SPECIES_HITMONLEE", 107: "SPECIES_HITMONCHAN", 108: "SPECIES_LICKITUNG",
            109: "SPECIES_KOFFING", 110: "SPECIES_WEEZING", 111: "SPECIES_RHYHORN",
            112: "SPECIES_RHYDON", 113: "SPECIES_CHANSEY", 114: "SPECIES_TANGELA",
            115: "SPECIES_KANGASKHAN", 116: "SPECIES_HORSEA", 117: "SPECIES_SEADRA",
            118: "SPECIES_GOLDEEN", 119: "SPECIES_SEAKING", 120: "SPECIES_STARYU",
            121: "SPECIES_STARMIE", 122: "SPECIES_MR_MIME", 123: "SPECIES_SCYTHER",
            124: "SPECIES_JYNX", 125: "SPECIES_ELECTABUZZ", 126: "SPECIES_MAGMAR",
            127: "SPECIES_PINSIR", 128: "SPECIES_TAUROS", 129: "SPECIES_MAGIKARP",
            130: "SPECIES_GYARADOS", 131: "SPECIES_LAPRAS", 132: "SPECIES_DITTO",
            133: "SPECIES_EEVEE", 134: "SPECIES_VAPOREON", 135: "SPECIES_JOLTEON",
            136: "SPECIES_FLAREON", 137: "SPECIES_PORYGON", 138: "SPECIES_OMANYTE",
            139: "SPECIES_OMASTAR", 140: "SPECIES_KABUTO", 141: "SPECIES_KABUTOPS",
            142: "SPECIES_AERODACTYL", 143: "SPECIES_SNORLAX", 144: "SPECIES_ARTICUNO",
            145: "SPECIES_ZAPDOS", 146: "SPECIES_MOLTRES", 147: "SPECIES_DRATINI",
            148: "SPECIES_DRAGONAIR", 149: "SPECIES_DRAGONITE", 150: "SPECIES_MEWTWO",
            151: "SPECIES_MEW"
        }

        return species_names

    def create_base_stats_entry(self, pokemon_id: int, stats: Dict) -> str:
        """Cria entry de base stats formatado"""

        # Corrige nomes de abilities (remove hífens)
        for key in ['ability1', 'ability2', 'hiddenAbility']:
            if key in stats:
                stats[key] = stats[key].replace('-', '_')

        # Usa o nome correto da espécie
        species_map = self.get_species_mapping()
        species_name = species_map.get(pokemon_id, f"SPECIES_POKEMON_{pokemon_id}")

        entry = f"    [{species_name}] =\n"
        entry += "    {\n"
        entry += f"        .baseHP = {stats.get('baseHP', '50')},\n"
        entry += f"        .baseAttack = {stats.get('baseAttack', '50')},\n"
        entry += f"        .baseDefense = {stats.get('baseDefense', '50')},\n"
        entry += f"        .baseSpAttack = {stats.get('baseSpAttack', '50')},\n"
        entry += f"        .baseSpDefense = {stats.get('baseSpDefense', '50')},\n"
        entry += f"        .baseSpeed = {stats.get('baseSpeed', '50')},\n"
        entry += f"        .type1 = {stats.get('type1', 'TYPE_NORMAL')},\n"
        entry += f"        .type2 = {stats.get('type2', 'TYPE_NORMAL')},\n"
        entry += f"        .catchRate = {stats.get('catchRate', '45')},\n"
        entry += f"        .expYield = {stats.get('expYield', '100')},\n"
        entry += "        .evYield_HP = 0,\n"
        entry += "        .evYield_Attack = 0,\n"
        entry += "        .evYield_Defense = 0,\n"
        entry += "        .evYield_SpAttack = 0,\n"
        entry += "        .evYield_SpDefense = 0,\n"
        entry += "        .evYield_Speed = 0,\n"
        entry += "        .item1 = ITEM_NONE,\n"
        entry += "        .item2 = ITEM_NONE,\n"

        # Gender ratio
        gender_rate = float(stats.get('genderRatio', '50.0'))
        entry += f"        .genderRatio = PERCENT_FEMALE({gender_rate}),\n"

        entry += f"        .eggCycles = {stats.get('eggCycles', '20')},\n"
        entry += f"        .friendship = {stats.get('friendship', '50')},\n"
        entry += "        .growthRate = GROWTH_MEDIUM_SLOW,\n"
        entry += "        .eggGroup1 = EGG_GROUP_FIELD,\n"
        entry += "        .eggGroup2 = EGG_GROUP_FIELD,\n"
        entry += f"        .ability1 = {stats.get('ability1', 'ABILITY_NONE')},\n"
        entry += f"        .ability2 = {stats.get('ability2', 'ABILITY_NONE')},\n"
        entry += f"        .hiddenAbility = {stats.get('hiddenAbility', 'ABILITY_NONE')},\n"
        entry += "        .safariZoneFleeRate = 0,\n"
        entry += "        .bodyColor = BODY_COLOR_GREEN,\n"
        entry += "        .noFlip = FALSE,\n"
        entry += "    },"

        return entry

    def update_learnsets_file(self):
        """Atualiza o arquivo Learnsets.c"""

        print("\n🔧 Atualizando Learnsets.c...")

        if not os.path.exists("src/Learnsets.c"):
            print("❌ Arquivo src/Learnsets.c não encontrado!")
            return False

        # Backup
        with open("src/Learnsets.c", 'r', encoding='utf-8') as f:
            original_content = f.read()

        with open("src/Learnsets.c.backup_gen9", 'w', encoding='utf-8') as f:
            f.write(original_content)

        print("📋 Backup criado: src/Learnsets.c.backup_gen9")

        # Atualiza conteúdo
        updated_content = original_content
        updates_applied = 0

        # Mapeia IDs para nomes de espécies
        species_map = self.get_species_mapping()

        for pokemon_id, moves in self.learnset_updates.items():
            # Procura pelo learnset existente
            species_name = species_map.get(pokemon_id, f"SPECIES_POKEMON_{pokemon_id}")

            # Remove "SPECIES_" e converte para formato do learnset
            learnset_name = species_name.replace("SPECIES_", "").lower().title()
            learnset_name = f"s{learnset_name}LevelUpLearnset"

            pattern = rf'static const struct LevelUpMove {learnset_name}\[\] = \{{.*?\}};'

            match = re.search(pattern, updated_content, re.DOTALL)
            if match:
                old_learnset = match.group(0)

                # Cria novo learnset
                new_learnset = self.create_learnset_entry(learnset_name, moves)

                # Substitui
                updated_content = updated_content.replace(old_learnset, new_learnset)
                updates_applied += 1

                if updates_applied <= 5:  # Mostra apenas os primeiros 5
                    print(f"✅ Atualizado learnset #{pokemon_id}")

        # Salva arquivo atualizado
        with open("src/Learnsets.c", 'w', encoding='utf-8') as f:
            f.write(updated_content)

        print(f"📊 {updates_applied} learnsets atualizados!")
        return True

    def create_learnset_entry(self, learnset_name: str, moves: List[Tuple[int, str]]) -> str:
        """Cria entry de learnset formatado"""

        entry = f"static const struct LevelUpMove {learnset_name}[] = {{\n"

        for level, move in moves:
            # Corrige nomes dos moves (remove underscores extras, mas mantém MOVE_)
            if move.startswith('MOVE_'):
                # Remove underscores internos apenas se necessário
                move_corrected = move.replace('_', '')
                # Reconstrói com um underscore após MOVE
                if move_corrected.startswith('MOVE'):
                    move_corrected = 'MOVE_' + move_corrected[4:]
            else:
                move_corrected = move

            entry += f"    LEVEL_UP_MOVE({level:2d}, {move_corrected}),\n"

        entry += "    LEVEL_UP_END\n"
        entry += "};"

        return entry

def main():
    """Função principal"""

    print("🚀 APLICANDO DADOS DA GENERATION IX AO PROJETO")
    print("=" * 50)

    applier = Gen9DataApplier()

    # Carrega dados
    if not applier.load_gen9_data():
        return False

    # Aplica base stats
    if not applier.update_base_stats_file():
        return False

    # Aplica learnsets
    if not applier.update_learnsets_file():
        return False

    print("\n🎉 ATUALIZAÇÃO CONCLUÍDA!")
    print("📊 Próximos passos:")
    print("   1. Verificar se os dados foram aplicados corretamente")
    print("   2. Compilar o projeto: python scripts/make.py")
    print("   3. Testar o ROM atualizado")

    return True

if __name__ == "__main__":
    main()
