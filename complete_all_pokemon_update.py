#!/usr/bin/env python3
"""
Atualização Completa de TODOS os Pokémon - Generation IX
Sistema para atualizar TODOS os 1026 Pokémon do projeto (1-1025)
"""

from pokemon_updater import PokemonUpdater
import time

def get_pokemon_name_from_id(pokemon_id):
    """Obtém nome do Pokémon baseado no ID usando PokeAPI"""
    try:
        import requests
        response = requests.get(f"https://pokeapi.co/api/v2/pokemon/{pokemon_id}")
        if response.status_code == 200:
            data = response.json()
            return data['name']
        return None
    except:
        return None

def get_all_pokemon_list():
    """Retorna lista completa de TODOS os Pokémon para atualizar (1-1025)"""
    
    # Define todas as gerações (1 a 1025 - todos os Pokémon do projeto)
    generations = [
        (1, 151, "Generation I (Kanto)"),
        (152, 251, "Generation II (Johto)"),
        (252, 386, "Generation III (Hoenn)"),
        (387, 493, "Generation IV (Sinnoh)"),
        (494, 649, "Generation V (Unova)"),
        (650, 721, "Generation VI (Kalos)"),
        (722, 809, "Generation VII (Alola)"),
        (810, 905, "Generation VIII (Galar)"),
        (906, 1025, "Generation IX (Paldea)")
    ]
    
    all_pokemon = []
    generation_counts = {}
    
    print("🔍 COLETANDO DADOS DE TODOS OS POKÉMON...")
    print("=" * 60)
    
    for start_id, end_id, gen_name in generations:
        print(f"📋 Processando {gen_name}...")
        gen_pokemon = []
        
        for i in range(start_id, end_id + 1):
            pokemon_name = get_pokemon_name_from_id(i)
            if pokemon_name:
                gen_pokemon.append((i, pokemon_name))
                if i % 50 == 0:  # Progress indicator
                    print(f"   ... processado até #{i}")
        
        all_pokemon.extend(gen_pokemon)
        generation_counts[gen_name] = len(gen_pokemon)
        print(f"✅ {gen_name}: {len(gen_pokemon)} Pokémon")
    
    print("=" * 60)
    print(f"📊 TOTAL: {len(all_pokemon)} Pokémon (1-1025)")
    print("🎯 Todas as 9 gerações incluídas!")
    
    return all_pokemon

def update_pokemon_batch(updater, pokemon_batch, batch_number, total_batches):
    """Atualiza um lote de Pokémon"""
    print(f"\n🔄 LOTE {batch_number}/{total_batches} - {len(pokemon_batch)} Pokémon")
    print("=" * 60)
    
    successful_updates = []
    failed_updates = []
    pokemon_updates = []
    
    for i, (pokemon_id, pokemon_name) in enumerate(pokemon_batch, 1):
        print(f"[{i:2d}/{len(pokemon_batch)}] ", end="")
        
        try:
            # Obtém dados do Pokémon
            pokemon_data = updater.get_pokemon_data(pokemon_id)
            if pokemon_data:
                latest_data = updater.get_latest_generation_data(pokemon_data)
                
                # Gera entradas
                base_stats_entry = updater.generate_base_stats_entry(pokemon_id, pokemon_name, latest_data)
                moveset_code = updater.generate_level_up_moves(pokemon_name, latest_data['moves']['level_up'])
                
                # Adiciona à lista de atualizações
                pokemon_updates.append({
                    'pokemon_id': pokemon_id,
                    'pokemon_name': pokemon_name,
                    'base_stats_entry': base_stats_entry,
                    'level_up_moves': moveset_code,
                    'latest_data': latest_data
                })
                
                successful_updates.append((pokemon_id, pokemon_name))
                generation_used = latest_data['moves'].get('generation_used', 'unknown')
                print(f"✅ {pokemon_name.capitalize()} (#{pokemon_id}) - {generation_used}")
            else:
                failed_updates.append((pokemon_id, pokemon_name))
                print(f"❌ {pokemon_name.capitalize()} (#{pokemon_id}) - Dados não encontrados")
                
        except Exception as e:
            print(f"❌ Erro crítico ao processar {pokemon_name}: {e}")
            failed_updates.append((pokemon_id, pokemon_name))
        
        # Pausa a cada 10 Pokémon para evitar rate limiting
        if i % 10 == 0:
            print(f"\n⏸️  Pausa de 2 segundos...")
            time.sleep(2)
    
    return successful_updates, failed_updates, pokemon_updates

def main():
    """Função principal para atualização completa de TODOS os Pokémon"""
    updater = PokemonUpdater()
    
    print("🚀 ATUALIZAÇÃO COMPLETA DE TODOS OS POKÉMON - GENERATION IX")
    print("=" * 70)
    print("📊 PROCESSANDO TODOS OS 1026 POKÉMON DO PROJETO (1-1025)")
    print("🎯 Priorização: Generation IX → VIII → VII")
    print("🔄 Sistema de geração única (sem mistura)")
    print("=" * 70)
    
    # Obtém lista completa de Pokémon
    all_pokemon = get_all_pokemon_list()
    
    print(f"\n📋 Total de Pokémon para atualizar: {len(all_pokemon)}")
    print(f"📊 Todas as 9 gerações incluídas (I-IX)")
    
    # Pergunta confirmação
    confirm = input(f"\n🤖 Prosseguir com atualização de {len(all_pokemon)} Pokémon? (s/N): ")
    if not confirm.lower().startswith('s'):
        print("❌ Operação cancelada pelo usuário")
        return
    
    # Divide em lotes de 20 Pokémon (menor para evitar problemas)
    batch_size = 20
    batches = [all_pokemon[i:i + batch_size] for i in range(0, len(all_pokemon), batch_size)]
    
    print(f"\n📦 Dividindo em {len(batches)} lotes de até {batch_size} Pokémon cada")
    print(f"⏱️  Tempo estimado: ~{len(batches) * 2} minutos")
    
    # Processa cada lote
    all_successful = []
    all_failed = []
    all_updates = []
    
    for batch_num, batch in enumerate(batches, 1):
        successful, failed, updates = update_pokemon_batch(updater, batch, batch_num, len(batches))
        
        all_successful.extend(successful)
        all_failed.extend(failed)
        all_updates.extend(updates)
        
        # Aplica atualizações do lote
        if updates:
            print(f"\n🔧 Aplicando {len(updates)} atualizações do lote {batch_num}...")
            if updater.apply_updates_to_files(updates):
                print(f"✅ Lote {batch_num} aplicado com sucesso!")
            else:
                print(f"❌ Erro ao aplicar lote {batch_num}")
        
        # Pausa entre lotes
        if batch_num < len(batches):
            print(f"\n⏸️  Pausa de 3 segundos antes do próximo lote...")
            time.sleep(3)
    
    # Relatório final
    print("\n" + "=" * 70)
    print("📊 RELATÓRIO FINAL DA ATUALIZAÇÃO COMPLETA")
    print("=" * 70)
    
    print(f"✅ Sucessos: {len(all_successful)}")
    print(f"❌ Falhas: {len(all_failed)}")
    if len(all_successful) + len(all_failed) > 0:
        success_rate = (len(all_successful)/(len(all_successful)+len(all_failed))*100)
        print(f"📈 Taxa de sucesso: {success_rate:.1f}%")
    
    if all_failed:
        print(f"\n❌ Pokémon que falharam ({len(all_failed)}):")
        for pokemon_id, pokemon_name in all_failed[:20]:  # Mostra apenas os primeiros 20
            print(f"   - {pokemon_name} (#{pokemon_id})")
        if len(all_failed) > 20:
            print(f"   ... e mais {len(all_failed) - 20} Pokémon")
    
    print(f"\n🎉 ATUALIZAÇÃO COMPLETA FINALIZADA!")
    print(f"🔄 {len(all_successful)} Pokémon atualizados para Generation IX!")
    print(f"📊 Cobertura: {len(all_successful)}/1025 Pokémon ({len(all_successful)/1025*100:.1f}%)")

if __name__ == "__main__":
    main()
