#!/usr/bin/env python3
"""
Atualização Completa de Pokémon - Generation IX
Sistema para atualizar TODOS os Pokémon do projeto em lotes sequenciais
"""

from pokemon_updater import PokemonUpdater
import time

def get_all_pokemon_list():
    """Retorna lista completa de TODOS os Pokémon do projeto (1-1439)"""

    # Gera lista automática de todos os Pokémon baseada no NUM_SPECIES
    all_pokemon = []

    # Mapeamento de nomes conhecidos para IDs específicos
    known_names = {
        # Generation I (1-151)
        1: "bulbasaur", 2: "ivysaur", 3: "venusaur",
        4: "charmander", 5: "charmeleon", 6: "charizard",
        7: "squirtle", 8: "wartortle", 9: "blastoise",
        10: "caterpie", 11: "metapod", 12: "butterfree",
        13: "weedle", 14: "kakuna", 15: "beedrill",
        16: "pidgey", 17: "pidgeotto", 18: "pidgeot",
        19: "rattata", 20: "raticate", 21: "spearow", 22: "fearow",
        23: "ekans", 24: "arbok", 25: "pikachu", 26: "raichu",
        27: "sandshrew", 28: "sandslash", 29: "nidoran-f", 30: "nidorina", 31: "nidoqueen",
        32: "nidoran-m", 33: "nidorino", 34: "nidoking", 35: "clefairy", 36: "clefable",
        37: "vulpix", 38: "ninetales", 39: "jigglypuff", 40: "wigglytuff",
        41: "zubat", 42: "golbat", 43: "oddish", 44: "gloom", 45: "vileplume",
        46: "paras", 47: "parasect", 48: "venonat", 49: "venomoth",
        50: "diglett", 51: "dugtrio", 52: "meowth", 53: "persian",
        54: "psyduck", 55: "golduck", 56: "mankey", 57: "primeape",
        58: "growlithe", 59: "arcanine", 60: "poliwag", 61: "poliwhirl", 62: "poliwrath",
        63: "abra", 64: "kadabra", 65: "alakazam", 66: "machop", 67: "machoke", 68: "machamp",
        69: "bellsprout", 70: "weepinbell", 71: "victreebel", 72: "tentacool", 73: "tentacruel",
        74: "geodude", 75: "graveler", 76: "golem", 77: "ponyta", 78: "rapidash",
        79: "slowpoke", 80: "slowbro", 81: "magnemite", 82: "magneton", 83: "farfetchd",
        84: "doduo", 85: "dodrio", 86: "seel", 87: "dewgong", 88: "grimer", 89: "muk",
        90: "shellder", 91: "cloyster", 92: "gastly", 93: "haunter", 94: "gengar", 95: "onix",
        96: "drowzee", 97: "hypno", 98: "krabby", 99: "kingler", 100: "voltorb", 101: "electrode",
        102: "exeggcute", 103: "exeggutor", 104: "cubone", 105: "marowak",
        106: "hitmonlee", 107: "hitmonchan", 108: "lickitung", 109: "koffing", 110: "weezing",
        111: "rhyhorn", 112: "rhydon", 113: "chansey", 114: "tangela", 115: "kangaskhan",
        116: "horsea", 117: "seadra", 118: "goldeen", 119: "seaking", 120: "staryu", 121: "starmie",
        122: "mr-mime", 123: "scyther", 124: "jynx", 125: "electabuzz", 126: "magmar",
        127: "pinsir", 128: "tauros", 129: "magikarp", 130: "gyarados", 131: "lapras", 132: "ditto",
        133: "eevee", 134: "vaporeon", 135: "jolteon", 136: "flareon", 137: "porygon",
        138: "omanyte", 139: "omastar", 140: "kabuto", 141: "kabutops", 142: "aerodactyl",
        143: "snorlax", 144: "articuno", 145: "zapdos", 146: "moltres",
        147: "dratini", 148: "dragonair", 149: "dragonite", 150: "mewtwo", 151: "mew",

        # Generation II (152-251)
        152: "chikorita", 153: "bayleef", 154: "meganium",
        155: "cyndaquil", 156: "quilava", 157: "typhlosion",
        158: "totodile", 159: "croconaw", 160: "feraligatr",
        161: "sentret", 162: "furret", 163: "hoothoot", 164: "noctowl",
        165: "ledyba", 166: "ledian", 167: "spinarak", 168: "ariados",
        169: "crobat", 170: "chinchou", 171: "lanturn", 172: "pichu",
        173: "cleffa", 174: "igglybuff", 175: "togepi", 176: "togetic",
        177: "natu", 178: "xatu", 179: "mareep", 180: "flaaffy", 181: "ampharos",
        182: "bellossom", 183: "marill", 184: "azumarill", 185: "sudowoodo",
        186: "politoed", 187: "hoppip", 188: "skiploom", 189: "jumpluff",
        190: "aipom", 191: "sunkern", 192: "sunflora", 193: "yanma",
        194: "wooper", 195: "quagsire", 196: "espeon", 197: "umbreon",
        198: "murkrow", 199: "slowking", 200: "misdreavus", 201: "unown",
        202: "wobbuffet", 203: "girafarig", 204: "pineco", 205: "forretress",
        206: "dunsparce", 207: "gligar", 208: "steelix", 209: "snubbull", 210: "granbull",
        211: "qwilfish", 212: "scizor", 213: "shuckle", 214: "heracross",
        215: "sneasel", 216: "teddiursa", 217: "ursaring", 218: "slugma", 219: "magcargo",
        220: "swinub", 221: "piloswine", 222: "corsola", 223: "remoraid", 224: "octillery",
        225: "delibird", 226: "mantine", 227: "skarmory", 228: "houndour", 229: "houndoom",
        230: "kingdra", 231: "phanpy", 232: "donphan", 233: "porygon2",
        234: "stantler", 235: "smeargle", 236: "tyrogue", 237: "hitmontop",
        238: "smoochum", 239: "elekid", 240: "magby", 241: "miltank",
        242: "blissey", 243: "raikou", 244: "entei", 245: "suicune",
        246: "larvitar", 247: "pupitar", 248: "tyranitar", 249: "lugia",
        250: "ho-oh", 251: "celebi",

        # Generation III (252-386)
        252: "treecko", 253: "grovyle", 254: "sceptile",
        255: "torchic", 256: "combusken", 257: "blaziken",
        258: "mudkip", 259: "marshtomp", 260: "swampert",
        261: "poochyena", 262: "mightyena", 263: "zigzagoon", 264: "linoone",
        265: "wurmple", 266: "silcoon", 267: "beautifly", 268: "cascoon", 269: "dustox",
        270: "lotad", 271: "lombre", 272: "ludicolo", 273: "seedot", 274: "nuzleaf", 275: "shiftry",
        276: "taillow", 277: "swellow", 278: "wingull", 279: "pelipper",
        280: "ralts", 281: "kirlia", 282: "gardevoir", 283: "surskit", 284: "masquerain",
        285: "shroomish", 286: "breloom", 287: "slakoth", 288: "vigoroth", 289: "slaking",
        290: "nincada", 291: "ninjask", 292: "shedinja", 293: "whismur", 294: "loudred", 295: "exploud",
        296: "makuhita", 297: "hariyama", 298: "azurill", 299: "nosepass",
        300: "skitty", 301: "delcatty", 302: "sableye", 303: "mawile",
        304: "aron", 305: "lairon", 306: "aggron", 307: "meditite", 308: "medicham",
        309: "electrike", 310: "manectric", 311: "plusle", 312: "minun",
        313: "volbeat", 314: "illumise", 315: "roselia", 316: "gulpin", 317: "swalot",
        318: "carvanha", 319: "sharpedo", 320: "wailmer", 321: "wailord",
        322: "numel", 323: "camerupt", 324: "torkoal", 325: "spoink", 326: "grumpig",
        327: "spinda", 328: "trapinch", 329: "vibrava", 330: "flygon",
        331: "cacnea", 332: "cacturne", 333: "swablu", 334: "altaria",
        335: "zangoose", 336: "seviper", 337: "lunatone", 338: "solrock",
        339: "barboach", 340: "whiscash", 341: "corphish", 342: "crawdaunt",
        343: "baltoy", 344: "claydol", 345: "lileep", 346: "cradily",
        347: "anorith", 348: "armaldo", 349: "feebas", 350: "milotic",
        351: "castform", 352: "kecleon", 353: "shuppet", 354: "banette",
        355: "duskull", 356: "dusclops", 357: "tropius", 358: "chimecho",
        359: "absol", 360: "wynaut", 361: "snorunt", 362: "glalie",
        363: "spheal", 364: "sealeo", 365: "walrein", 366: "clamperl",
        367: "huntail", 368: "gorebyss", 369: "relicanth", 370: "luvdisc",
        371: "bagon", 372: "shelgon", 373: "salamence", 374: "beldum", 375: "metang", 376: "metagross",
        377: "regirock", 378: "regice", 379: "registeel", 380: "latias", 381: "latios",
        382: "kyogre", 383: "groudon", 384: "rayquaza", 385: "jirachi", 386: "deoxys",
    }

    # Gera lista completa de 1 a 1439 (NUM_SPECIES - 1)
    for pokemon_id in range(1, 1440):  # 1 a 1439
        if pokemon_id in known_names:
            pokemon_name = known_names[pokemon_id]
        else:
            # Para Pokémon sem nome conhecido, usa formato genérico
            pokemon_name = f"pokemon_{pokemon_id}"

        all_pokemon.append((pokemon_id, pokemon_name))

    return all_pokemon

def update_pokemon_batch(updater, pokemon_batch, batch_number, total_batches):
    """Atualiza um lote de Pokémon"""
    print(f"\n🔄 LOTE {batch_number}/{total_batches} - {len(pokemon_batch)} Pokémon")
    print("=" * 60)

    successful_updates = []
    failed_updates = []
    pokemon_updates = []

    for i, (pokemon_id, pokemon_name) in enumerate(pokemon_batch, 1):
        print(f"[{i:2d}/{len(pokemon_batch)}] ", end="")

        try:
            if updater.update_pokemon_in_project(pokemon_id, pokemon_name):
                successful_updates.append((pokemon_id, pokemon_name))

                # Coleta dados para aplicação
                pokemon_data = updater.get_pokemon_data(pokemon_id)
                if pokemon_data:
                    latest_data = updater.get_latest_generation_data(pokemon_data)
                    pokemon_updates.append({
                        'pokemon_id': pokemon_id,
                        'pokemon_name': pokemon_name,
                        'base_stats_entry': updater.generate_base_stats_entry(pokemon_id, pokemon_name, latest_data),
                        'level_up_moves': updater.generate_level_up_moves(pokemon_name, latest_data['moves']['level_up']),
                        'latest_data': latest_data
                    })
            else:
                failed_updates.append((pokemon_id, pokemon_name))
        except Exception as e:
            print(f"❌ Erro crítico ao processar {pokemon_name}: {e}")
            failed_updates.append((pokemon_id, pokemon_name))

        # Pausa a cada 10 Pokémon para evitar rate limiting
        if i % 10 == 0:
            print(f"\n⏸️  Pausa de 2 segundos...")
            time.sleep(2)

    return successful_updates, failed_updates, pokemon_updates

def main():
    """Função principal para atualização completa de TODOS os Pokémon"""
    print("🎮 ATUALIZAÇÃO COMPLETA DE POKÉMON - GENERATION IX")
    print("=" * 70)
    print("Este script atualizará TODOS os 1439 Pokémon com dados da Generation IX")
    print("Priorização: Gen IX > Gen VIII > Gen VII > Gen VI")
    print("Cobertura: Gerações I-IX completas (1-1439)")
    print("=" * 70)

    # Confirma execução
    response = input("\n🤖 Continuar com a atualização completa de 1439 Pokémon? (s/N): ").lower().strip()
    if response not in ['s', 'sim', 'y', 'yes']:
        print("❌ Atualização cancelada pelo usuário")
        return

    updater = PokemonUpdater()

    # Obtém lista completa de Pokémon (1-1439)
    all_pokemon = get_all_pokemon_list()
    total_pokemon = len(all_pokemon)

    print(f"\n📊 INICIANDO ATUALIZAÇÃO MASSIVA:")
    print(f"Total de Pokémon: {total_pokemon}")
    print(f"Gerações cobertas: I-IX (completas)")
    print(f"Processamento em lotes de 20 Pokémon")
    print(f"Tempo estimado: {total_pokemon * 1.5 // 60} minutos")
    print(f"Rate limiting: 3 segundos entre lotes")

    # Divide em lotes menores para melhor controle
    batch_size = 20
    batches = [all_pokemon[i:i + batch_size] for i in range(0, len(all_pokemon), batch_size)]
    total_batches = len(batches)

    all_updates = []
    successful_count = 0
    failed_count = 0

    start_time = time.time()

    print(f"\n🚀 Iniciando processamento de {total_batches} lotes...")

    # Processa cada lote
    for batch_num, batch in enumerate(batches, 1):
        batch_updates, batch_successful, batch_failed = update_pokemon_batch(
            updater, batch, batch_num, total_batches
        )

        all_updates.extend(batch_updates)
        successful_count += batch_successful
        failed_count += batch_failed

        # Mostra progresso a cada 10 lotes
        if batch_num % 10 == 0 or batch_num == total_batches:
            elapsed = time.time() - start_time
            progress = (batch_num / total_batches) * 100
            pokemon_processed = batch_num * batch_size
            rate = pokemon_processed / elapsed if elapsed > 0 else 0

            print(f"\n📈 PROGRESSO: {progress:.1f}% ({batch_num}/{total_batches} lotes)")
            print(f"   Pokémon processados: {pokemon_processed}/{total_pokemon}")
            print(f"   Taxa: {rate:.1f} Pokémon/minuto")
            print(f"   Sucessos: {successful_count}, Falhas: {failed_count}")

        # Pausa entre lotes para evitar rate limiting
        if batch_num < total_batches:
            time.sleep(3)

    # Estatísticas finais
    elapsed_time = time.time() - start_time
    success_rate = (successful_count / total_pokemon) * 100

    print(f"\n" + "=" * 70)
    print("📊 ESTATÍSTICAS FINAIS DA ATUALIZAÇÃO MASSIVA:")
    print("=" * 70)
    print(f"✅ Sucessos: {successful_count:,}/{total_pokemon:,} ({success_rate:.1f}%)")
    print(f"❌ Falhas: {failed_count:,}/{total_pokemon:,} ({(failed_count/total_pokemon)*100:.1f}%)")
    print(f"⏱️  Tempo total: {elapsed_time/60:.1f} minutos")
    print(f"🚀 Taxa média: {total_pokemon/(elapsed_time/60):.1f} Pokémon/minuto")
    print(f"📁 Dados individuais salvos em: pokemon_updates/")

    # Pergunta se deve aplicar atualizações
    if successful_count > 0:
        print(f"\n🔧 APLICAÇÃO DE ATUALIZAÇÕES:")
        print(f"Pronto para aplicar {len(all_updates):,} atualizações aos arquivos do projeto")
        apply_response = input("🤖 Aplicar atualizações automaticamente aos arquivos? (s/N): ").lower().strip()

        if apply_response in ['s', 'sim', 'y', 'yes']:
            print(f"\n🔧 Aplicando {len(all_updates):,} atualizações aos arquivos...")

            if updater.apply_updates_to_files(all_updates):
                print("✅ ATUALIZAÇÕES APLICADAS COM SUCESSO!")
                print("📁 Backups criados: src/Base_Stats.c.backup, src/Learnsets.c.backup")

                # Gera relatório final
                print("📄 Gerando relatório final...")
                report = updater.create_update_report(all_pokemon[:successful_count])
                with open("pokemon_update_report_complete.md", "w", encoding="utf-8") as f:
                    f.write(report)
                print("📄 Relatório completo gerado: pokemon_update_report_complete.md")

                print(f"\n🎉 ATUALIZAÇÃO MASSIVA COMPLETAMENTE FINALIZADA!")
                print("✅ Todos os Pokémon atualizados para Generation IX")
                print("✅ Sistema de priorização funcionando")
                print("✅ Movesets de geração única aplicados")
                print("✅ Projeto pronto para compilação")

            else:
                print("❌ Erro ao aplicar atualizações")
        else:
            print("⏸️  Atualizações não aplicadas. Dados salvos para aplicação posterior.")
            print("💡 Para aplicar depois: execute o script novamente e escolha aplicar")

    print(f"\n🎯 RESUMO FINAL:")
    print(f"📊 {successful_count:,} Pokémon processados com sucesso")
    print(f"🎮 Projeto expandido para Generation IX completa")
    print(f"⚡ Sistema de priorização automática ativo")
    print(f"🔄 Movesets de geração única implementados")
    print("=" * 70)

    print("\n🎯 PRÓXIMOS PASSOS:")
    print("1. ✅ Verificar se o projeto compila corretamente")
    print("2. 🎮 Testar alguns Pokémon no jogo")
    print("3. 🔧 Ajustar manualmente dados que precisam de refinamento:")
    print("   - EV Yields")
    print("   - Exp Yield")
    print("   - Body Color")
    print("   - Held Items")
    print("4. 💾 Fazer commit das mudanças")

    print(f"\n🎉 ATUALIZAÇÃO COMPLETA FINALIZADA!")
    print(f"🔄 {len(all_successful)} Pokémon atualizados para Generation IX!")

if __name__ == "__main__":
    main()
