#!/usr/bin/env python3
"""
Atualização Completa de Pokémon - Generation IX
Sistema para atualizar TODOS os Pokémon do projeto em lotes sequenciais
"""

from pokemon_updater import PokemonUpdater
import time

def get_all_pokemon_list():
    """Retorna lista completa de Pokémon organizados por geração"""
    
    # Generation I (1-151)
    gen1_pokemon = [
        # Starters
        (1, "bulbasaur"), (2, "ivysaur"), (3, "venusaur"),
        (4, "charmander"), (5, "charmeleon"), (6, "charizard"),
        (7, "squirtle"), (8, "wartortle"), (9, "blastoise"),
        
        # Pokémon comuns
        (10, "caterpie"), (11, "metapod"), (12, "butterfree"),
        (13, "weedle"), (14, "kakuna"), (15, "beedrill"),
        (16, "pidgey"), (17, "pidgeotto"), (18, "pidgeot"),
        (19, "rattata"), (20, "raticate"),
        (21, "spearow"), (22, "fearow"),
        (23, "ekans"), (24, "arbok"),
        (25, "pikachu"), (26, "raichu"),
        (27, "sandshrew"), (28, "sandslash"),
        (29, "nidoran_f"), (30, "nidorina"), (31, "nidoqueen"),
        (32, "nidoran_m"), (33, "nidorino"), (34, "nidoking"),
        (35, "clefairy"), (36, "clefable"),
        (37, "vulpix"), (38, "ninetales"),
        (39, "jigglypuff"), (40, "wigglytuff"),
        (41, "zubat"), (42, "golbat"),
        (43, "oddish"), (44, "gloom"), (45, "vileplume"),
        (46, "paras"), (47, "parasect"),
        (48, "venonat"), (49, "venomoth"),
        (50, "diglett"), (51, "dugtrio"),
        (52, "meowth"), (53, "persian"),
        (54, "psyduck"), (55, "golduck"),
        (56, "mankey"), (57, "primeape"),
        (58, "growlithe"), (59, "arcanine"),
        (60, "poliwag"), (61, "poliwhirl"), (62, "poliwrath"),
        (63, "abra"), (64, "kadabra"), (65, "alakazam"),
        (66, "machop"), (67, "machoke"), (68, "machamp"),
        (69, "bellsprout"), (70, "weepinbell"), (71, "victreebel"),
        (72, "tentacool"), (73, "tentacruel"),
        (74, "geodude"), (75, "graveler"), (76, "golem"),
        (77, "ponyta"), (78, "rapidash"),
        (79, "slowpoke"), (80, "slowbro"),
        (81, "magnemite"), (82, "magneton"),
        (83, "farfetchd"),
        (84, "doduo"), (85, "dodrio"),
        (86, "seel"), (87, "dewgong"),
        (88, "grimer"), (89, "muk"),
        (90, "shellder"), (91, "cloyster"),
        (92, "gastly"), (93, "haunter"), (94, "gengar"),
        (95, "onix"),
        (96, "drowzee"), (97, "hypno"),
        (98, "krabby"), (99, "kingler"),
        (100, "voltorb"), (101, "electrode"),
        (102, "exeggcute"), (103, "exeggutor"),
        (104, "cubone"), (105, "marowak"),
        (106, "hitmonlee"), (107, "hitmonchan"),
        (108, "lickitung"),
        (109, "koffing"), (110, "weezing"),
        (111, "rhyhorn"), (112, "rhydon"),
        (113, "chansey"),
        (114, "tangela"),
        (115, "kangaskhan"),
        (116, "horsea"), (117, "seadra"),
        (118, "goldeen"), (119, "seaking"),
        (120, "staryu"), (121, "starmie"),
        (122, "mr_mime"),
        (123, "scyther"),
        (124, "jynx"),
        (125, "electabuzz"),
        (126, "magmar"),
        (127, "pinsir"),
        (128, "tauros"),
        (129, "magikarp"), (130, "gyarados"),
        (131, "lapras"),
        (132, "ditto"),
        (133, "eevee"), (134, "vaporeon"), (135, "jolteon"), (136, "flareon"),
        (137, "porygon"),
        (138, "omanyte"), (139, "omastar"),
        (140, "kabuto"), (141, "kabutops"),
        (142, "aerodactyl"),
        (143, "snorlax"),
        (144, "articuno"), (145, "zapdos"), (146, "moltres"),
        (147, "dratini"), (148, "dragonair"), (149, "dragonite"),
        (150, "mewtwo"), (151, "mew")
    ]
    
    # Generation III (252-386) - Hoenn
    gen3_pokemon = [
        # Starters
        (252, "treecko"), (253, "grovyle"), (254, "sceptile"),
        (255, "torchic"), (256, "combusken"), (257, "blaziken"),
        (258, "mudkip"), (259, "marshtomp"), (260, "swampert"),
        
        # Pokémon comuns de Hoenn
        (261, "poochyena"), (262, "mightyena"),
        (263, "zigzagoon"), (264, "linoone"),
        (265, "wurmple"), (266, "silcoon"), (267, "beautifly"),
        (268, "cascoon"), (269, "dustox"),
        (270, "lotad"), (271, "lombre"), (272, "ludicolo"),
        (273, "seedot"), (274, "nuzleaf"), (275, "shiftry"),
        (276, "taillow"), (277, "swellow"),
        (278, "wingull"), (279, "pelipper"),
        (280, "ralts"), (281, "kirlia"), (282, "gardevoir"),
        (283, "surskit"), (284, "masquerain"),
        (285, "shroomish"), (286, "breloom"),
        (287, "slakoth"), (288, "vigoroth"), (289, "slaking"),
        (290, "nincada"), (291, "ninjask"), (292, "shedinja"),
        (293, "whismur"), (294, "loudred"), (295, "exploud"),
        (296, "makuhita"), (297, "hariyama"),
        (298, "azurill"),
        (299, "nosepass"),
        (300, "skitty"), (301, "delcatty"),
        (302, "sableye"),
        (303, "mawile"),
        (304, "aron"), (305, "lairon"), (306, "aggron"),
        (307, "meditite"), (308, "medicham"),
        (309, "electrike"), (310, "manectric"),
        (311, "plusle"), (312, "minun"),
        (313, "volbeat"), (314, "illumise"),
        (315, "roselia"),
        (316, "gulpin"), (317, "swalot"),
        (318, "carvanha"), (319, "sharpedo"),
        (320, "wailmer"), (321, "wailord"),
        (322, "numel"), (323, "camerupt"),
        (324, "torkoal"),
        (325, "spoink"), (326, "grumpig"),
        (327, "spinda"),
        (328, "trapinch"), (329, "vibrava"), (330, "flygon"),
        (331, "cacnea"), (332, "cacturne"),
        (333, "swablu"), (334, "altaria"),
        (335, "zangoose"), (336, "seviper"),
        (337, "lunatone"), (338, "solrock"),
        (339, "barboach"), (340, "whiscash"),
        (341, "corphish"), (342, "crawdaunt"),
        (343, "baltoy"), (344, "claydol"),
        (345, "lileep"), (346, "cradily"),
        (347, "anorith"), (348, "armaldo"),
        (349, "feebas"), (350, "milotic"),
        (351, "castform"),
        (352, "kecleon"),
        (353, "shuppet"), (354, "banette"),
        (355, "duskull"), (356, "dusclops"),
        (357, "tropius"),
        (358, "chimecho"),
        (359, "absol"),
        (360, "wynaut"),
        (361, "snorunt"), (362, "glalie"),
        (363, "spheal"), (364, "sealeo"), (365, "walrein"),
        (366, "clamperl"), (367, "huntail"), (368, "gorebyss"),
        (369, "relicanth"),
        (370, "luvdisc"),
        (371, "bagon"), (372, "shelgon"), (373, "salamence"),
        (374, "beldum"), (375, "metang"), (376, "metagross"),
        (377, "regirock"), (378, "regice"), (379, "registeel"),
        (380, "latias"), (381, "latios"),
        (382, "kyogre"), (383, "groudon"), (384, "rayquaza"),
        (385, "jirachi"), (386, "deoxys")
    ]
    
    return gen1_pokemon + gen3_pokemon

def update_pokemon_batch(updater, pokemon_batch, batch_number, total_batches):
    """Atualiza um lote de Pokémon"""
    print(f"\n🔄 LOTE {batch_number}/{total_batches} - {len(pokemon_batch)} Pokémon")
    print("=" * 60)
    
    successful_updates = []
    failed_updates = []
    pokemon_updates = []
    
    for i, (pokemon_id, pokemon_name) in enumerate(pokemon_batch, 1):
        print(f"[{i:2d}/{len(pokemon_batch)}] ", end="")
        
        try:
            if updater.update_pokemon_in_project(pokemon_id, pokemon_name):
                successful_updates.append((pokemon_id, pokemon_name))
                
                # Coleta dados para aplicação
                pokemon_data = updater.get_pokemon_data(pokemon_id)
                if pokemon_data:
                    latest_data = updater.get_latest_generation_data(pokemon_data)
                    pokemon_updates.append({
                        'pokemon_id': pokemon_id,
                        'pokemon_name': pokemon_name,
                        'base_stats_entry': updater.generate_base_stats_entry(pokemon_id, pokemon_name, latest_data),
                        'level_up_moves': updater.generate_level_up_moves(pokemon_name, latest_data['moves']['level_up']),
                        'latest_data': latest_data
                    })
            else:
                failed_updates.append((pokemon_id, pokemon_name))
        except Exception as e:
            print(f"❌ Erro crítico ao processar {pokemon_name}: {e}")
            failed_updates.append((pokemon_id, pokemon_name))
        
        # Pausa a cada 10 Pokémon para evitar rate limiting
        if i % 10 == 0:
            print(f"\n⏸️  Pausa de 2 segundos...")
            time.sleep(2)
    
    return successful_updates, failed_updates, pokemon_updates

def main():
    """Função principal para atualização completa"""
    updater = PokemonUpdater()
    
    print("🔄 ATUALIZAÇÃO COMPLETA DE POKÉMON - GENERATION IX")
    print("=" * 60)
    
    # Obtém lista completa de Pokémon
    all_pokemon = get_all_pokemon_list()
    
    print(f"📋 Total de Pokémon para atualizar: {len(all_pokemon)}")
    print(f"📊 Gerações incluídas: I (1-151) e III (252-386)")
    
    # Pergunta confirmação
    confirm = input(f"\n🤖 Prosseguir com atualização de {len(all_pokemon)} Pokémon? (s/N): ")
    if not confirm.lower().startswith('s'):
        print("❌ Operação cancelada pelo usuário")
        return
    
    # Divide em lotes de 25 Pokémon
    batch_size = 25
    batches = [all_pokemon[i:i + batch_size] for i in range(0, len(all_pokemon), batch_size)]
    
    print(f"\n📦 Dividindo em {len(batches)} lotes de até {batch_size} Pokémon cada")
    
    # Processa cada lote
    all_successful = []
    all_failed = []
    all_updates = []
    
    for batch_num, batch in enumerate(batches, 1):
        successful, failed, updates = update_pokemon_batch(updater, batch, batch_num, len(batches))
        
        all_successful.extend(successful)
        all_failed.extend(failed)
        all_updates.extend(updates)
        
        # Aplica atualizações do lote
        if updates:
            print(f"\n🔧 Aplicando {len(updates)} atualizações do lote {batch_num}...")
            if updater.apply_updates_to_files(updates):
                print(f"✅ Lote {batch_num} aplicado com sucesso!")
            else:
                print(f"❌ Erro ao aplicar lote {batch_num}")
        
        # Pausa entre lotes
        if batch_num < len(batches):
            print(f"\n⏸️  Pausa de 5 segundos antes do próximo lote...")
            time.sleep(5)
    
    # Relatório final
    print("\n" + "=" * 60)
    print("📊 RELATÓRIO FINAL DA ATUALIZAÇÃO COMPLETA")
    print("=" * 60)
    
    print(f"✅ Sucessos: {len(all_successful)}")
    print(f"❌ Falhas: {len(all_failed)}")
    print(f"📈 Taxa de sucesso: {(len(all_successful)/(len(all_successful)+len(all_failed))*100):.1f}%")
    
    if all_failed:
        print(f"\n❌ Pokémon que falharam ({len(all_failed)}):")
        for pokemon_id, pokemon_name in all_failed[:20]:  # Mostra apenas os primeiros 20
            print(f"   - {pokemon_name} (#{pokemon_id})")
        if len(all_failed) > 20:
            print(f"   ... e mais {len(all_failed) - 20} Pokémon")
    
    # Gera relatório final
    report = updater.create_update_report(all_pokemon)
    with open("complete_pokemon_update_report.md", "w", encoding="utf-8") as f:
        f.write(report)
    
    print(f"\n📄 Relatório completo salvo em: complete_pokemon_update_report.md")
    print(f"📁 Arquivos individuais salvos em: pokemon_updates/")
    
    print("\n🎯 PRÓXIMOS PASSOS:")
    print("1. ✅ Verificar se o projeto compila corretamente")
    print("2. 🎮 Testar alguns Pokémon no jogo")
    print("3. 🔧 Ajustar manualmente dados que precisam de refinamento:")
    print("   - EV Yields")
    print("   - Exp Yield")
    print("   - Body Color")
    print("   - Held Items")
    print("4. 💾 Fazer commit das mudanças")
    
    print(f"\n🎉 ATUALIZAÇÃO COMPLETA FINALIZADA!")
    print(f"🔄 {len(all_successful)} Pokémon atualizados para Generation IX!")

if __name__ == "__main__":
    main()
