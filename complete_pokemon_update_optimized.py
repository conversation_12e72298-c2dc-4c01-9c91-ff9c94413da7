#!/usr/bin/env python3
"""
Atualização Completa de Pokémon - VERSÃO OTIMIZADA
Sistema corrigido para abranger todos os Pokémon do projeto com opções flexíveis
"""

from pokemon_updater import PokemonUpdater
import time

def get_pokemon_by_generation():
    """Retorna Pokémon organizados por geração para seleção flexível"""
    
    generations = {
        "I": {
            "name": "Generation I (Kanto)",
            "range": (1, 151),
            "pokemon": [
                (1, "bulbasaur"), (2, "ivysaur"), (3, "venusaur"),
                (4, "charmander"), (5, "charmeleon"), (6, "charizard"),
                (7, "squirtle"), (8, "wartortle"), (9, "blastoise"),
                (10, "caterpie"), (11, "metapod"), (12, "butterfree"),
                (13, "weedle"), (14, "kakuna"), (15, "beedrill"),
                (16, "pidgey"), (17, "pidgeotto"), (18, "pidgeot"),
                (19, "rattata"), (20, "raticate"), (21, "spearow"), (22, "fearow"),
                (23, "ekans"), (24, "arbok"), (25, "p<PERSON>chu"), (26, "r<PERSON>hu"),
                (27, "sandshrew"), (28, "sandslash"), (29, "nidoran-f"), (30, "nidorina"),
                (31, "nidoqueen"), (32, "nidoran-m"), (33, "nidor<PERSON>"), (34, "nidoking"),
                (35, "cle<PERSON>y"), (36, "clefable"), (37, "vulpix"), (38, "ninetales"),
                (39, "jigglypuff"), (40, "wigglytuff"), (41, "zubat"), (42, "golbat"),
                (43, "oddish"), (44, "gloom"), (45, "vileplume"), (46, "paras"),
                (47, "parasect"), (48, "venonat"), (49, "venomoth"), (50, "diglett"),
                (51, "dugtrio"), (52, "meowth"), (53, "persian"), (54, "psyduck"),
                (55, "golduck"), (56, "mankey"), (57, "primeape"), (58, "growlithe"),
                (59, "arcanine"), (60, "poliwag"), (61, "poliwhirl"), (62, "poliwrath"),
                (63, "abra"), (64, "kadabra"), (65, "alakazam"), (66, "machop"),
                (67, "machoke"), (68, "machamp"), (69, "bellsprout"), (70, "weepinbell"),
                (71, "victreebel"), (72, "tentacool"), (73, "tentacruel"), (74, "geodude"),
                (75, "graveler"), (76, "golem"), (77, "ponyta"), (78, "rapidash"),
                (79, "slowpoke"), (80, "slowbro"), (81, "magnemite"), (82, "magneton"),
                (83, "farfetchd"), (84, "doduo"), (85, "dodrio"), (86, "seel"),
                (87, "dewgong"), (88, "grimer"), (89, "muk"), (90, "shellder"),
                (91, "cloyster"), (92, "gastly"), (93, "haunter"), (94, "gengar"),
                (95, "onix"), (96, "drowzee"), (97, "hypno"), (98, "krabby"),
                (99, "kingler"), (100, "voltorb"), (101, "electrode"), (102, "exeggcute"),
                (103, "exeggutor"), (104, "cubone"), (105, "marowak"), (106, "hitmonlee"),
                (107, "hitmonchan"), (108, "lickitung"), (109, "koffing"), (110, "weezing"),
                (111, "rhyhorn"), (112, "rhydon"), (113, "chansey"), (114, "tangela"),
                (115, "kangaskhan"), (116, "horsea"), (117, "seadra"), (118, "goldeen"),
                (119, "seaking"), (120, "staryu"), (121, "starmie"), (122, "mr-mime"),
                (123, "scyther"), (124, "jynx"), (125, "electabuzz"), (126, "magmar"),
                (127, "pinsir"), (128, "tauros"), (129, "magikarp"), (130, "gyarados"),
                (131, "lapras"), (132, "ditto"), (133, "eevee"), (134, "vaporeon"),
                (135, "jolteon"), (136, "flareon"), (137, "porygon"), (138, "omanyte"),
                (139, "omastar"), (140, "kabuto"), (141, "kabutops"), (142, "aerodactyl"),
                (143, "snorlax"), (144, "articuno"), (145, "zapdos"), (146, "moltres"),
                (147, "dratini"), (148, "dragonair"), (149, "dragonite"), (150, "mewtwo"),
                (151, "mew")
            ]
        },
        
        "II": {
            "name": "Generation II (Johto)",
            "range": (152, 251),
            "pokemon": [
                (152, "chikorita"), (153, "bayleef"), (154, "meganium"),
                (155, "cyndaquil"), (156, "quilava"), (157, "typhlosion"),
                (158, "totodile"), (159, "croconaw"), (160, "feraligatr"),
                (161, "sentret"), (162, "furret"), (163, "hoothoot"), (164, "noctowl"),
                (165, "ledyba"), (166, "ledian"), (167, "spinarak"), (168, "ariados"),
                (169, "crobat"), (170, "chinchou"), (171, "lanturn"), (172, "pichu"),
                (173, "cleffa"), (174, "igglybuff"), (175, "togepi"), (176, "togetic"),
                (177, "natu"), (178, "xatu"), (179, "mareep"), (180, "flaaffy"),
                (181, "ampharos"), (182, "bellossom"), (183, "marill"), (184, "azumarill"),
                (185, "sudowoodo"), (186, "politoed"), (187, "hoppip"), (188, "skiploom"),
                (189, "jumpluff"), (190, "aipom"), (191, "sunkern"), (192, "sunflora"),
                (193, "yanma"), (194, "wooper"), (195, "quagsire"), (196, "espeon"),
                (197, "umbreon"), (198, "murkrow"), (199, "slowking"), (200, "misdreavus"),
                (201, "unown"), (202, "wobbuffet"), (203, "girafarig"), (204, "pineco"),
                (205, "forretress"), (206, "dunsparce"), (207, "gligar"), (208, "steelix"),
                (209, "snubbull"), (210, "granbull"), (211, "qwilfish"), (212, "scizor"),
                (213, "shuckle"), (214, "heracross"), (215, "sneasel"), (216, "teddiursa"),
                (217, "ursaring"), (218, "slugma"), (219, "magcargo"), (220, "swinub"),
                (221, "piloswine"), (222, "corsola"), (223, "remoraid"), (224, "octillery"),
                (225, "delibird"), (226, "mantine"), (227, "skarmory"), (228, "houndour"),
                (229, "houndoom"), (230, "kingdra"), (231, "phanpy"), (232, "donphan"),
                (233, "porygon2"), (234, "stantler"), (235, "smeargle"), (236, "tyrogue"),
                (237, "hitmontop"), (238, "smoochum"), (239, "elekid"), (240, "magby"),
                (241, "miltank"), (242, "blissey"), (243, "raikou"), (244, "entei"),
                (245, "suicune"), (246, "larvitar"), (247, "pupitar"), (248, "tyranitar"),
                (249, "lugia"), (250, "ho-oh"), (251, "celebi")
            ]
        },
        
        "III": {
            "name": "Generation III (Hoenn)",
            "range": (252, 386),
            "pokemon": [(i, f"pokemon_{i}") for i in range(252, 387)]
        },
        
        "IV": {
            "name": "Generation IV (Sinnoh)",
            "range": (387, 493),
            "pokemon": [(i, f"pokemon_{i}") for i in range(387, 494)]
        },
        
        "V": {
            "name": "Generation V (Unova)",
            "range": (494, 649),
            "pokemon": [(i, f"pokemon_{i}") for i in range(494, 650)]
        },
        
        "VI": {
            "name": "Generation VI (Kalos)",
            "range": (650, 721),
            "pokemon": [(i, f"pokemon_{i}") for i in range(650, 722)]
        },
        
        "VII": {
            "name": "Generation VII (Alola)",
            "range": (722, 809),
            "pokemon": [(i, f"pokemon_{i}") for i in range(722, 810)]
        },
        
        "VIII": {
            "name": "Generation VIII (Galar)",
            "range": (810, 905),
            "pokemon": [(i, f"pokemon_{i}") for i in range(810, 906)]
        },
        
        "IX": {
            "name": "Generation IX (Paldea)",
            "range": (906, 1010),
            "pokemon": [(i, f"pokemon_{i}") for i in range(906, 1011)]
        },
        
        "FORMS": {
            "name": "Formas Alternativas",
            "range": (1011, 1440),
            "pokemon": [(i, f"pokemon_{i}") for i in range(1011, 1441)]
        }
    }
    
    return generations

def select_generations():
    """Permite ao usuário selecionar quais gerações atualizar"""
    generations = get_pokemon_by_generation()
    
    print("🎮 SELEÇÃO DE GERAÇÕES PARA ATUALIZAR")
    print("=" * 50)
    
    for key, gen_data in generations.items():
        start, end = gen_data["range"]
        count = end - start + 1
        print(f"{key:>5}: {gen_data['name']} ({count} Pokémon)")
    
    print("\n📋 Opções:")
    print("  ALL  - Todas as gerações (1440 Pokémon)")
    print("  MAIN - Apenas I-IX (1010 Pokémon)")
    print("  I,II,III - Gerações específicas")
    
    selection = input("\n🤖 Digite sua seleção: ").upper().strip()
    
    selected_pokemon = []
    
    if selection == "ALL":
        print("✅ Selecionado: TODAS as gerações")
        for gen_data in generations.values():
            selected_pokemon.extend(gen_data["pokemon"])
    
    elif selection == "MAIN":
        print("✅ Selecionado: Gerações principais (I-IX)")
        for key in ["I", "II", "III", "IV", "V", "VI", "VII", "VIII", "IX"]:
            selected_pokemon.extend(generations[key]["pokemon"])
    
    else:
        # Gerações específicas
        selected_gens = [gen.strip() for gen in selection.split(",")]
        for gen in selected_gens:
            if gen in generations:
                print(f"✅ Adicionado: {generations[gen]['name']}")
                selected_pokemon.extend(generations[gen]["pokemon"])
            else:
                print(f"⚠️  Geração '{gen}' não reconhecida")
    
    selected_pokemon.sort(key=lambda x: x[0])
    
    print(f"\n📊 Total selecionado: {len(selected_pokemon)} Pokémon")
    print(f"📋 Range: #{selected_pokemon[0][0]} até #{selected_pokemon[-1][0]}")
    
    return selected_pokemon

def update_pokemon_batch(updater, pokemon_batch, batch_num, total_batches):
    """Atualiza um lote de Pokémon"""
    print(f"\n📦 LOTE {batch_num}/{total_batches} - {len(pokemon_batch)} Pokémon")
    print("=" * 50)
    
    successful_updates = []
    failed_updates = []
    pokemon_updates = []
    
    for i, (pokemon_id, pokemon_name) in enumerate(pokemon_batch, 1):
        print(f"[{i:2d}/{len(pokemon_batch)}] ", end="")
        
        try:
            if updater.update_pokemon_in_project(pokemon_id, pokemon_name):
                successful_updates.append((pokemon_id, pokemon_name))
                
                # Coleta dados para aplicação
                pokemon_data = updater.get_pokemon_data(pokemon_id)
                if pokemon_data:
                    latest_data = updater.get_latest_generation_data(pokemon_data)
                    pokemon_updates.append({
                        'pokemon_id': pokemon_id,
                        'pokemon_name': pokemon_name,
                        'base_stats_entry': updater.generate_base_stats_entry(pokemon_id, pokemon_name, latest_data),
                        'level_up_moves': updater.generate_level_up_moves(pokemon_name, latest_data['moves']['level_up']),
                        'latest_data': latest_data
                    })
            else:
                failed_updates.append((pokemon_id, pokemon_name))
        except Exception as e:
            print(f"❌ Erro crítico ao processar {pokemon_name}: {e}")
            failed_updates.append((pokemon_id, pokemon_name))
        
        # Pausa a cada 10 Pokémon para evitar rate limiting
        if i % 10 == 0:
            print(f"\n⏸️  Pausa de 2 segundos...")
            time.sleep(2)
    
    return successful_updates, failed_updates, pokemon_updates

def main():
    """Função principal para atualização otimizada"""
    updater = PokemonUpdater()
    
    print("🔄 ATUALIZAÇÃO COMPLETA DE POKÉMON - VERSÃO OTIMIZADA")
    print("=" * 60)
    print("🎯 Sistema de priorização Generation IX com fallbacks")
    print("🔧 Movesets de geração única (sem mistura)")
    print("🗺️  Mapeamento de habilidades corrigido")
    print("⚡ Seleção flexível de gerações")
    
    # Seleção de gerações
    selected_pokemon = select_generations()
    
    if not selected_pokemon:
        print("❌ Nenhum Pokémon selecionado")
        return
    
    # Pergunta confirmação
    confirm = input(f"\n🤖 Prosseguir com atualização de {len(selected_pokemon)} Pokémon? (s/N): ")
    if not confirm.lower().startswith('s'):
        print("❌ Operação cancelada pelo usuário")
        return
    
    # Divide em lotes de 25 Pokémon
    batch_size = 25
    batches = [selected_pokemon[i:i + batch_size] for i in range(0, len(selected_pokemon), batch_size)]
    
    print(f"\n📦 Dividindo em {len(batches)} lotes de até {batch_size} Pokémon cada")
    
    # Processa cada lote
    all_successful = []
    all_failed = []
    all_updates = []
    
    for batch_num, batch in enumerate(batches, 1):
        successful, failed, updates = update_pokemon_batch(updater, batch, batch_num, len(batches))
        
        all_successful.extend(successful)
        all_failed.extend(failed)
        all_updates.extend(updates)
        
        # Aplica atualizações do lote
        if updates:
            print(f"\n🔧 Aplicando {len(updates)} atualizações do lote {batch_num}...")
            if updater.apply_updates_to_files(updates):
                print(f"✅ Lote {batch_num} aplicado com sucesso!")
            else:
                print(f"❌ Erro ao aplicar lote {batch_num}")
        
        # Pausa entre lotes
        if batch_num < len(batches):
            print(f"\n⏸️  Pausa de 5 segundos antes do próximo lote...")
            time.sleep(5)
    
    # Relatório final
    print("\n" + "=" * 60)
    print("📊 RELATÓRIO FINAL DA ATUALIZAÇÃO OTIMIZADA")
    print("=" * 60)
    
    print(f"✅ Sucessos: {len(all_successful)}")
    print(f"❌ Falhas: {len(all_failed)}")
    print(f"📈 Taxa de sucesso: {(len(all_successful)/(len(all_successful)+len(all_failed))*100):.1f}%")
    
    if all_failed:
        print(f"\n❌ Pokémon que falharam ({len(all_failed)}):")
        for pokemon_id, pokemon_name in all_failed[:10]:
            print(f"   - {pokemon_name} (#{pokemon_id})")
        if len(all_failed) > 10:
            print(f"   ... e mais {len(all_failed) - 10} Pokémon")
    
    # Gera relatório final
    report = updater.create_update_report(selected_pokemon)
    with open("pokemon_update_optimized_report.md", "w", encoding="utf-8") as f:
        f.write(report)
    
    print(f"\n📄 Relatório salvo em: pokemon_update_optimized_report.md")
    print(f"📁 Arquivos individuais em: pokemon_updates/")
    
    print("\n🎯 PRÓXIMOS PASSOS:")
    print("1. ✅ Verificar compilação do projeto")
    print("2. 🎮 Testar Pokémon no jogo")
    print("3. 💾 Fazer commit das mudanças")
    
    print(f"\n🎉 ATUALIZAÇÃO OTIMIZADA FINALIZADA!")
    print(f"🔄 {len(all_successful)} Pokémon atualizados para Generation IX!")

if __name__ == "__main__":
    main()
