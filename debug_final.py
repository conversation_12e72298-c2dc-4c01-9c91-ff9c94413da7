#!/usr/bin/env python3
"""
Script para descobrir por que a substituição não está funcionando
"""

import re
import os

def debug_substitution_issue():
    """Debugga o problema da substituição"""
    
    print("🔍 DIAGNÓSTICO FINAL: Por que a substituição não funciona?")
    print("=" * 60)
    
    # Simula exatamente o que o script faz
    pokemon_id = 253
    
    # 1. Carrega dados do arquivo Gen IX
    gen9_file = "pokemon_updates_gen9/pokemon_253_253.txt"
    with open(gen9_file, 'r', encoding='utf-8') as f:
        gen9_content = f.read()
    
    # 2. Extrai learnset Gen IX
    pattern_extract = r'static const struct LevelUpMove s.*?LevelUpLearnset\[\] = \{(.*?)\};'
    match_extract = re.search(pattern_extract, gen9_content, re.DOTALL)
    
    if match_extract:
        moves_block = match_extract.group(1)
        move_pattern = r'LEVEL_UP_MOVE\(\s*(\d+),\s*(MOVE_[\w_]+)\)'
        gen9_moves = []
        for move_match in re.finditer(move_pattern, moves_block):
            level = int(move_match.group(1))
            move = move_match.group(2)
            gen9_moves.append((level, move))
        
        print(f"✅ Gen IX moves extraídos: {len(gen9_moves)}")
        for i, (level, move) in enumerate(gen9_moves[:5]):
            print(f"   {i+1}. Level {level}: {move}")
    else:
        print("❌ Falha ao extrair moves Gen IX")
        return
    
    # 3. Carrega arquivo atual do projeto
    with open("src/Learnsets.c", 'r', encoding='utf-8') as f:
        current_content = f.read()
    
    # 4. Simula o processo de substituição
    species_map = {253: "SPECIES_GROVYLE"}
    species_name = species_map.get(pokemon_id, f"SPECIES_POKEMON_{pokemon_id}")
    learnset_name = species_name.replace("SPECIES_", "").lower().title()
    learnset_name = f"s{learnset_name}LevelUpLearnset"
    
    print(f"\n🎯 Processo de substituição:")
    print(f"   Species name: {species_name}")
    print(f"   Learnset name: {learnset_name}")
    
    # 5. Testa o padrão de busca
    pattern_search = rf'static const struct LevelUpMove {learnset_name}\[\] = \{{.*?\}};'
    print(f"   Padrão de busca: {pattern_search}")
    
    match_search = re.search(pattern_search, current_content, re.DOTALL)
    print(f"   Resultado: {'✅ ENCONTRADO' if match_search else '❌ NÃO ENCONTRADO'}")
    
    if match_search:
        old_learnset = match_search.group(0)
        print(f"   Tamanho encontrado: {len(old_learnset)} chars")
        
        # 6. Cria novo learnset
        new_learnset = f"static const struct LevelUpMove {learnset_name}[] = {{\n"
        for level, move in gen9_moves:
            # Converte underscores para formato correto
            move_corrected = move.replace('_', '')
            if not move_corrected.startswith('MOVE_'):
                move_corrected = f"MOVE_{move_corrected}"
            new_learnset += f"    LEVEL_UP_MOVE({level:2d}, {move_corrected}),\n"
        new_learnset += "    LEVEL_UP_END\n"
        new_learnset += "};"
        
        print(f"   Novo learnset criado: {len(new_learnset)} chars")
        print(f"   Primeiras 3 linhas do novo:")
        for i, line in enumerate(new_learnset.split('\n')[:3]):
            print(f"      {i+1}. {line}")
        
        # 7. Testa a substituição
        updated_content = current_content.replace(old_learnset, new_learnset)
        
        if updated_content != current_content:
            print("   ✅ SUBSTITUIÇÃO FUNCIONARIA!")
            
            # Verifica se o novo conteúdo tem os moves corretos
            new_match = re.search(pattern_search, updated_content, re.DOTALL)
            if new_match:
                new_found = new_match.group(0)
                if "MOVE_LEAFAGE" in new_found:
                    print("   ✅ MOVE_LEAFAGE encontrado no resultado!")
                else:
                    print("   ❌ MOVE_LEAFAGE NÃO encontrado no resultado!")
            
        else:
            print("   ❌ SUBSTITUIÇÃO NÃO FUNCIONOU!")
            print("   🔍 Comparando strings...")
            print(f"      Old learnset hash: {hash(old_learnset)}")
            print(f"      New learnset hash: {hash(new_learnset)}")
    
    print("\n🎯 CONCLUSÃO:")
    print("   Se a substituição funcionaria, o problema pode estar em:")
    print("   1. Conversão incorreta dos nomes dos moves")
    print("   2. Problema na escrita do arquivo")
    print("   3. Problema na lógica do loop")

if __name__ == "__main__":
    debug_substitution_issue()
