#!/usr/bin/env python3
"""
Script para diagnosticar por que os learnsets não foram aplicados
"""

import re
import os

def test_extraction():
    """Testa a extração de learnsets"""
    
    print("🔍 DIAGNÓSTICO: Por que os learnsets não foram aplicados?")
    print("=" * 60)
    
    # Testa arquivo do Grovyle
    test_file = "pokemon_updates_gen9/pokemon_253_253.txt"
    
    if not os.path.exists(test_file):
        print(f"❌ Arquivo não encontrado: {test_file}")
        return
    
    with open(test_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"📁 Testando arquivo: {test_file}")
    print(f"📊 Tamanho do arquivo: {len(content)} chars")
    
    # Mostra as linhas relevantes
    lines = content.split('\n')
    print("\n📋 CONTEÚDO DO LEARNSET:")
    for i, line in enumerate(lines[38:57], 39):
        print(f"{i:2d}: {line}")
    
    # Testa diferentes padrões
    print("\n🧪 TESTANDO PADRÕES DE REGEX:")
    
    patterns = [
        ("Padrão Original", r'static const struct LevelUpMove s\w+LevelUpLearnset\[\] = \{([^}]+)\}'),
        ("Padrão Flexível 1", r'static const struct LevelUpMove s[^[]+LevelUpLearnset\[\] = \{([^}]+)\}'),
        ("Padrão Flexível 2", r'static const struct LevelUpMove s.*?LevelUpLearnset\[\] = \{(.*?)\};'),
        ("Padrão Mais Amplo", r'LevelUpLearnset\[\] = \{(.*?)\};'),
    ]
    
    for name, pattern in patterns:
        match = re.search(pattern, content, re.DOTALL)
        print(f"  {name}: {'✅ ENCONTRADO' if match else '❌ NÃO ENCONTRADO'}")
        
        if match:
            moves_block = match.group(1)
            print(f"    📦 Bloco extraído: {len(moves_block)} chars")
            
            # Testa extração de moves
            move_patterns = [
                ("Moves Simples", r'LEVEL_UP_MOVE\(\s*(\d+),\s*(MOVE_\w+)\)'),
                ("Moves com Underscore", r'LEVEL_UP_MOVE\(\s*(\d+),\s*(MOVE_[\w_]+)\)'),
            ]
            
            for move_name, move_pattern in move_patterns:
                moves = []
                for move_match in re.finditer(move_pattern, moves_block):
                    level = int(move_match.group(1))
                    move = move_match.group(2)
                    moves.append((level, move))
                
                print(f"    🎯 {move_name}: {len(moves)} moves")
                if moves and len(moves) >= 3:
                    print(f"       Exemplos: {moves[0]}, {moves[1]}, {moves[2]}")
            break
    
    # Verifica o arquivo atual do projeto
    print("\n📂 VERIFICANDO ARQUIVO ATUAL DO PROJETO:")
    
    if os.path.exists("src/Learnsets.c"):
        with open("src/Learnsets.c", 'r', encoding='utf-8') as f:
            current_content = f.read()
        
        # Procura pelo Grovyle atual
        grovyle_pattern = r'static const struct LevelUpMove sGrovyleLevelUpLearnset\[\] = \{([^}]+)\};'
        grovyle_match = re.search(grovyle_pattern, current_content, re.DOTALL)
        
        if grovyle_match:
            print("✅ Grovyle encontrado no arquivo atual")
            current_moves = grovyle_match.group(1)
            
            # Conta moves atuais
            current_move_pattern = r'LEVEL_UP_MOVE\(\s*(\d+),\s*(MOVE_\w+)\)'
            current_moves_list = re.findall(current_move_pattern, current_moves)
            
            print(f"📊 Moves atuais no projeto: {len(current_moves_list)}")
            if current_moves_list:
                print(f"   Primeiro move: Level {current_moves_list[0][0]}, {current_moves_list[0][1]}")
                print(f"   Último move: Level {current_moves_list[-1][0]}, {current_moves_list[-1][1]}")
        else:
            print("❌ Grovyle NÃO encontrado no arquivo atual")
    
    print("\n🎯 CONCLUSÃO:")
    print("   1. Verificar se o padrão de regex está correto")
    print("   2. Verificar se os nomes dos moves estão sendo convertidos")
    print("   3. Verificar se a substituição está funcionando")

if __name__ == "__main__":
    test_extraction()
