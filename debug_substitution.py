#!/usr/bin/env python3
"""
Script para diagnosticar por que a substituição dos learnsets não funcionou
"""

import re
import os

def debug_substitution():
    """Debugga o processo de substituição"""
    
    print("🔍 DIAGNÓSTICO: Por que a SUBSTITUIÇÃO não funcionou?")
    print("=" * 60)
    
    # Carrega arquivo atual
    if not os.path.exists("src/Learnsets.c"):
        print("❌ Arquivo src/Learnsets.c não encontrado!")
        return
    
    with open("src/Learnsets.c", 'r', encoding='utf-8') as f:
        current_content = f.read()
    
    print("📂 Arquivo carregado: src/Learnsets.c")
    print(f"📊 Tamanho: {len(current_content)} chars")
    
    # Testa o padrão de busca usado pelo script
    print("\n🔍 TESTANDO PADRÃO DE BUSCA NO ARQUIVO ATUAL:")
    
    # Simula o que o script faz
    pokemon_id = 253
    species_map = {253: "SPECIES_GROVYLE"}
    species_name = species_map.get(pokemon_id, f"SPECIES_POKEMON_{pokemon_id}")
    
    # Remove "SPECIES_" e converte para formato do learnset
    learnset_name = species_name.replace("SPECIES_", "").lower().title()
    learnset_name = f"s{learnset_name}LevelUpLearnset"
    
    print(f"🎯 ID: {pokemon_id}")
    print(f"🎯 Species name: {species_name}")
    print(f"🎯 Learnset name: {learnset_name}")
    
    # Testa o padrão usado pelo script
    pattern = rf'static const struct LevelUpMove {learnset_name}\[\] = \{{[^}}]+\}};'
    print(f"🎯 Padrão de busca: {pattern}")
    
    match = re.search(pattern, current_content, re.DOTALL)
    print(f"🎯 Resultado: {'✅ ENCONTRADO' if match else '❌ NÃO ENCONTRADO'}")
    
    if match:
        old_learnset = match.group(0)
        print(f"📦 Learnset encontrado: {len(old_learnset)} chars")
        print(f"📋 Primeiras 200 chars:\n{old_learnset[:200]}...")
    else:
        print("\n🔍 TENTANDO PADRÕES ALTERNATIVOS:")
        
        # Testa padrões mais flexíveis
        alternative_patterns = [
            rf'static const struct LevelUpMove {learnset_name}\[\] = \{{.*?\}};',
            rf'static const struct LevelUpMove {learnset_name}\[\] = \{{[^}}]*\}};',
            rf'{learnset_name}\[\] = \{{.*?\}};',
            r'static const struct LevelUpMove sGrovyleLevelUpLearnset\[\] = \{.*?\};',
        ]
        
        for i, alt_pattern in enumerate(alternative_patterns, 1):
            alt_match = re.search(alt_pattern, current_content, re.DOTALL)
            print(f"  Padrão {i}: {'✅ ENCONTRADO' if alt_match else '❌ NÃO ENCONTRADO'}")
            if alt_match:
                print(f"    📦 Tamanho: {len(alt_match.group(0))} chars")
                break
    
    # Verifica se o nome está correto
    print("\n🔍 VERIFICANDO NOMES NO ARQUIVO:")
    
    grovyle_searches = [
        "sGrovyleLevelUpLearnset",
        "SPECIES_GROVYLE",
        "Grovyle",
        "GROVYLE"
    ]
    
    for search_term in grovyle_searches:
        count = current_content.count(search_term)
        print(f"  '{search_term}': {count} ocorrências")
    
    # Mostra o learnset atual do Grovyle
    print("\n📋 LEARNSET ATUAL DO GROVYLE:")
    grovyle_pattern = r'static const struct LevelUpMove sGrovyleLevelUpLearnset\[\] = \{([^}]+)\};'
    grovyle_match = re.search(grovyle_pattern, current_content, re.DOTALL)
    
    if grovyle_match:
        current_learnset = grovyle_match.group(0)
        print(f"✅ Encontrado: {len(current_learnset)} chars")
        
        # Mostra algumas linhas
        lines = current_learnset.split('\n')
        for i, line in enumerate(lines[:10]):
            print(f"  {i+1:2d}: {line}")
        if len(lines) > 10:
            print(f"  ... (+{len(lines)-10} linhas)")
    else:
        print("❌ Learnset do Grovyle não encontrado!")

if __name__ == "__main__":
    debug_substitution()
