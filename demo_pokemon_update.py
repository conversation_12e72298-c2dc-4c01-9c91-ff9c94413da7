#!/usr/bin/env python3
"""
Demonstração do Pokemon Updater
Mostra como usar o sistema para atualizar dados específicos
"""

from pokemon_updater import PokemonUpdater

def demo_single_pokemon():
    """Demonstra atualização de um único Pokémon"""
    updater = PokemonUpdater()
    
    print("🔍 DEMONSTRAÇÃO: Atualizando Vibrava")
    print("=" * 40)
    
    # Vibrava - que sabemos que foi modificado no projeto
    pokemon_data = updater.get_pokemon_data(329)  # Vibrava
    
    if pokemon_data:
        print("📊 DADOS ORIGINAIS (Generation IX):")
        latest_data = updater.get_latest_generation_data(pokemon_data)
        
        print(f"Nome: Vibrava")
        print(f"Stats: {latest_data['stats']}")
        print(f"Tipos: {latest_data['types']}")
        print(f"Habilidades: {latest_data['abilities']}")
        print(f"Capture Rate: {latest_data['capture_rate']}")
        print(f"Moves Level Up: {len(latest_data['moves']['level_up'])}")
        
        print("\n📝 CÓDIGO GERADO:")
        print("-" * 40)
        
        # Gera código
        base_stats = updater.generate_base_stats_entry(329, "vibrava", latest_data)
        print(base_stats)
        
        print("\n📋 LEVEL UP MOVES:")
        print("-" * 40)
        level_moves = updater.generate_level_up_moves("Vibrava", latest_data['moves']['level_up'][:10])  # Primeiros 10
        print(level_moves)
        
    else:
        print("❌ Erro ao obter dados do Vibrava")

def demo_comparison():
    """Demonstra comparação entre dados atuais e originais"""
    updater = PokemonUpdater()
    
    print("\n🔍 DEMONSTRAÇÃO: Comparação de Dados")
    print("=" * 40)
    
    # Pokémon para comparar
    pokemon_list = [
        (252, "treecko"),
        (329, "vibrava"),
        (1, "bulbasaur")
    ]
    
    for pokemon_id, pokemon_name in pokemon_list:
        pokemon_data = updater.get_pokemon_data(pokemon_id)
        if pokemon_data:
            latest_data = updater.get_latest_generation_data(pokemon_data)
            stats = latest_data['stats']
            
            print(f"\n📊 {pokemon_name.upper()} (#{pokemon_id}):")
            print(f"   HP: {stats['baseHP']}")
            print(f"   ATK: {stats['baseAttack']}")
            print(f"   DEF: {stats['baseDefense']}")
            print(f"   SP.ATK: {stats['baseSpAttack']}")
            print(f"   SP.DEF: {stats['baseSpDefense']}")
            print(f"   SPEED: {stats['baseSpeed']}")
            print(f"   TIPOS: {latest_data['types'][0]} / {latest_data['types'][1]}")
            print(f"   TOTAL: {sum(stats.values())}")

def demo_generate_pokemon_list():
    """Gera lista de todos os Pokémon do projeto atual"""
    print("\n🔍 DEMONSTRAÇÃO: Extraindo Pokémon do Projeto")
    print("=" * 50)
    
    try:
        with open("src/Base_Stats.c", "r", encoding="utf-8") as f:
            content = f.read()
        
        import re
        
        # Encontra todas as entradas SPECIES_
        species_pattern = r'\[SPECIES_(\w+)\]'
        matches = re.findall(species_pattern, content)
        
        print(f"📋 Encontrados {len(matches)} Pokémon no projeto:")
        print("-" * 30)
        
        # Filtra apenas Pokémon válidos (exclui NONE, EGG, etc.)
        valid_pokemon = []
        for species in matches:
            if species not in ['NONE', 'EGG', 'UNOWN_B', 'UNOWN_C']:  # Exclui entradas especiais
                valid_pokemon.append(species.lower())
        
        # Mostra primeiros 20
        for i, pokemon in enumerate(valid_pokemon[:20]):
            print(f"{i+1:3d}. {pokemon}")
        
        if len(valid_pokemon) > 20:
            print(f"... e mais {len(valid_pokemon) - 20} Pokémon")
        
        print(f"\n📊 Total de Pokémon válidos: {len(valid_pokemon)}")
        
        # Salva lista completa
        with open("pokemon_list.txt", "w", encoding="utf-8") as f:
            for pokemon in valid_pokemon:
                f.write(f"{pokemon}\n")
        
        print("✅ Lista completa salva em: pokemon_list.txt")
        
    except FileNotFoundError:
        print("❌ Arquivo src/Base_Stats.c não encontrado")
        print("   Certifique-se de estar no diretório correto do projeto")

def main():
    """Função principal da demonstração"""
    print("🎮 POKEMON UPDATER - DEMONSTRAÇÃO")
    print("=" * 50)
    
    # Demonstração 1: Pokémon único
    demo_single_pokemon()
    
    # Demonstração 2: Comparação
    demo_comparison()
    
    # Demonstração 3: Lista do projeto
    demo_generate_pokemon_list()
    
    print("\n" + "=" * 50)
    print("🎯 COMO USAR O SISTEMA COMPLETO:")
    print("1. Execute: python pokemon_updater.py")
    print("2. Revise o relatório gerado")
    print("3. Aplique as mudanças manualmente nos arquivos")
    print("4. Teste a compilação do projeto")
    print("\n💡 DICA: Comece com poucos Pokémon para testar!")

if __name__ == "__main__":
    main()
