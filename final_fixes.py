#!/usr/bin/env python3
"""
Script para aplicar as correções finais de constantes
"""

import re
import os

def apply_final_fixes():
    """Aplica todas as correções finais de constantes"""
    
    print("🔧 APLICANDO CORREÇÕES FINAIS")
    print("=" * 35)
    
    # Lê o arquivo atual
    with open("src/Base_Stats.c", "r", encoding="utf-8") as f:
        content = f.read()
    
    print("🔄 Aplicando correções de constantes...")
    
    # Correções de EGG_GROUP
    egg_group_fixes = {
        'EGG_GROUP_PLANT': 'EGG_GROUP_GRASS',
        'EGG_GROUP_GROUND': 'EGG_GROUP_FIELD',
        'EGG_GROUP_HUMANSHAPE': 'EGG_GROUP_HUMAN_LIKE',
        'EGG_GROUP_INDETERMINATE': 'EGG_GROUP_UNDISCOVERED',
        'EGG_GROUP_NO-EGGS': 'EGG_GROUP_UNDISCOVERED',
    }
    
    for wrong, correct in egg_group_fixes.items():
        content = content.replace(wrong, correct)
    
    # Correções de ABILITY
    ability_fixes = {
        'ABILITY_VITALSPIRIT': 'ABILITY_INSOMNIA',
        'ABILITY_WHITESMOKE': 'ABILITY_CLEARBODY',
        'ABILITY_SOLIDROCK': 'ABILITY_FILTER',
        'ABILITY_PUREPOWER': 'ABILITY_HUGEPOWER',
        'ABILITY_AIRLOCK': 'ABILITY_CLOUDNINE',
    }
    
    for wrong, correct in ability_fixes.items():
        content = content.replace(wrong, correct)
    
    # Correções de ITEM
    item_fixes = {
        'ITEM_STICK': 'ITEM_NONE',
    }
    
    for wrong, correct in item_fixes.items():
        content = content.replace(wrong, correct)
    
    # Corrige expYield para não exceder 255
    def fix_exp_yield(match):
        value = int(match.group(1))
        if value > 255:
            return f".expYield = 255,"
        return match.group(0)
    
    content = re.sub(r'\.expYield\s*=\s*(\d+),', fix_exp_yield, content)
    
    print("✅ Correções aplicadas:")
    print("   - EGG_GROUP names corrigidos")
    print("   - ABILITY names corrigidos")
    print("   - ITEM names corrigidos")
    print("   - expYield limitado a 255")
    
    # Salva o arquivo corrigido
    with open("src/Base_Stats.c", "w", encoding="utf-8") as f:
        f.write(content)
    
    print("💾 Arquivo Base_Stats.c corrigido!")
    
    return True

def main():
    """Função principal"""
    
    if not os.path.exists("src/Base_Stats.c"):
        print("❌ Arquivo src/Base_Stats.c não encontrado!")
        return False
    
    # Aplica correções finais
    success = apply_final_fixes()
    
    if success:
        print("\n🎯 PRÓXIMO PASSO:")
        print("Execute: python scripts/make.py")
        print("Para testar a compilação final")
        
        return True
    
    return False

if __name__ == "__main__":
    main()
