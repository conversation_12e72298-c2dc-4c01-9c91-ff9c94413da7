#!/usr/bin/env python3
"""
Script para identificar modificações nos stats de Pokémon no projeto Dynamic Pokemon Expansion.
Procura por comentários que indicam valores originais modificados.
"""

import re
import sys

def find_stat_modifications(file_path):
    """
    Encontra todas as modificações de stats marcadas com comentários.
    Retorna uma lista de modificações encontradas.
    """
    modifications = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"Erro: Arquivo {file_path} não encontrado.")
        return []
    
    current_pokemon = None
    
    for i, line in enumerate(lines, 1):
        # Detecta início de um novo Pokémon
        species_match = re.search(r'\[SPECIES_(\w+)\]', line)
        if species_match:
            current_pokemon = species_match.group(1)
            continue
        
        # Procura por stats modificados (valor seguido de comentário com valor original)
        stat_pattern = r'\.base(\w+)\s*=\s*(\d+),\s*//(\d+)'
        stat_match = re.search(stat_pattern, line)
        
        if stat_match and current_pokemon:
            stat_name = stat_match.group(1)
            new_value = int(stat_match.group(2))
            original_value = int(stat_match.group(3))
            
            modifications.append({
                'pokemon': current_pokemon,
                'stat': stat_name,
                'original': original_value,
                'new': new_value,
                'line': i,
                'change': new_value - original_value
            })
    
    return modifications

def find_type_modifications(file_path):
    """
    Encontra modificações de tipos (procura por Pokémon com tipos não-originais).
    """
    type_modifications = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"Erro: Arquivo {file_path} não encontrado.")
        return []
    
    # Alguns casos conhecidos de mudanças de tipo
    known_type_changes = {
        'ARBOK': {'original': 'Poison/Poison', 'new': 'Poison/Dark'},
        # Adicione mais conforme necessário
    }
    
    # Procura por padrões específicos que indicam mudanças de tipo
    pokemon_blocks = re.findall(r'\[SPECIES_(\w+)\]\s*=\s*\{([^}]+)\}', content, re.DOTALL)
    
    for pokemon_name, block in pokemon_blocks:
        type1_match = re.search(r'\.type1\s*=\s*TYPE_(\w+)', block)
        type2_match = re.search(r'\.type2\s*=\s*TYPE_(\w+)', block)
        
        if type1_match and type2_match:
            type1 = type1_match.group(1)
            type2 = type2_match.group(1)
            
            # Verifica se é uma mudança conhecida
            if pokemon_name in known_type_changes:
                type_modifications.append({
                    'pokemon': pokemon_name,
                    'original_types': known_type_changes[pokemon_name]['original'],
                    'new_types': f"{type1}/{type2}",
                    'modification_type': 'type_change'
                })
    
    return type_modifications

def main():
    file_path = "src/Base_Stats.c"
    
    print("=== ANÁLISE DE MODIFICAÇÕES NO DYNAMIC POKEMON EXPANSION ===\n")
    
    # Encontra modificações de stats
    stat_mods = find_stat_modifications(file_path)
    
    if stat_mods:
        print(f"🔍 ENCONTRADAS {len(stat_mods)} MODIFICAÇÕES DE STATS:\n")
        
        # Agrupa por Pokémon
        pokemon_mods = {}
        for mod in stat_mods:
            if mod['pokemon'] not in pokemon_mods:
                pokemon_mods[mod['pokemon']] = []
            pokemon_mods[mod['pokemon']].append(mod)
        
        for pokemon, mods in pokemon_mods.items():
            print(f"📊 {pokemon}:")
            for mod in mods:
                change_indicator = "↑" if mod['change'] > 0 else "↓"
                print(f"   • {mod['stat']}: {mod['original']} → {mod['new']} ({change_indicator}{abs(mod['change'])})")
            print()
    else:
        print("✅ Nenhuma modificação de stats encontrada com comentários.")
    
    # Encontra modificações de tipos
    type_mods = find_type_modifications(file_path)
    
    if type_mods:
        print(f"🎨 ENCONTRADAS {len(type_mods)} MODIFICAÇÕES DE TIPOS:\n")
        for mod in type_mods:
            print(f"📊 {mod['pokemon']}:")
            print(f"   • Tipos: {mod['original_types']} → {mod['new_types']}")
            print()
    
    # Resumo
    total_modified = len(set(mod['pokemon'] for mod in stat_mods)) + len(type_mods)
    if total_modified > 0:
        print(f"📈 RESUMO: {total_modified} Pokémon foram modificados")
        print("\n⚠️  CONCLUSÃO: O projeto NÃO mantém fidelidade total aos stats originais.")
        print("   Algumas modificações são claramente marcadas com comentários,")
        print("   mas outras podem não estar documentadas.")
    else:
        print("✅ Nenhuma modificação detectada pelos métodos automáticos.")
        print("   Isso não garante que não há modificações - apenas que não")
        print("   foram encontradas marcações óbvias.")

if __name__ == "__main__":
    main()
