#!/usr/bin/env python3
"""
Script avançado para corrigir erros específicos do projeto
"""

import re
import os

def get_project_constants():
    """Obtém as constantes válidas do projeto"""
    
    # Verifica arquivos de header para constantes válidas
    abilities = set()
    egg_groups = set()
    
    # Procura por arquivos de constantes
    header_files = [
        "include/constants/abilities.h",
        "include/constants/species.h", 
        "include/global.h",
        "src/data/pokemon/species_info.h"
    ]
    
    for file_path in header_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()
                    
                # Encontra definições de abilities
                ability_matches = re.findall(r'#define\s+(ABILITY_\w+)', content)
                abilities.update(ability_matches)
                
                # Encontra definições de egg groups
                egg_matches = re.findall(r'#define\s+(EGG_GROUP_\w+)', content)
                egg_groups.update(egg_matches)
                
            except:
                continue
    
    return abilities, egg_groups

def fix_advanced_errors():
    """Corrige erros avançados específicos do projeto"""
    
    print("🔧 CORREÇÕES AVANÇADAS PARA COMPILAÇÃO")
    print("=" * 50)
    
    # Lê o arquivo atual
    with open("src/Base_Stats.c", "r", encoding="utf-8") as f:
        content = f.read()
    
    print("🔄 Aplicando correções avançadas...")
    
    # 1. Corrige estrutura de abilities para o formato correto do projeto
    # Substitui abilities[2] pelo formato correto
    content = re.sub(r'\.abilities\[2\]\s*=\s*([^,]+),', r'.abilities = {\1, ABILITY_NONE, ABILITY_NONE},', content)
    
    # 2. Corrige formato de abilities para array
    def fix_abilities_format(match):
        full_match = match.group(0)
        
        # Extrai as abilities
        ability1_match = re.search(r'\.ability1\s*=\s*([^,]+),', full_match)
        ability2_match = re.search(r'\.ability2\s*=\s*([^,]+),', full_match)
        abilities_match = re.search(r'\.abilities\s*=\s*\{([^}]+)\}', full_match)
        
        if abilities_match:
            return full_match  # Já está no formato correto
        
        ability1 = ability1_match.group(1) if ability1_match else "ABILITY_NONE"
        ability2 = ability2_match.group(1) if ability2_match else "ABILITY_NONE"
        
        # Remove as linhas antigas
        result = full_match
        if ability1_match:
            result = result.replace(ability1_match.group(0), "")
        if ability2_match:
            result = result.replace(ability2_match.group(0), "")
        
        # Adiciona o formato correto
        abilities_line = f"        .abilities = {{{ability1}, {ability2}, ABILITY_NONE}},\n"
        
        # Insere após baseSpeed
        result = re.sub(r'(\.baseSpeed\s*=\s*[^,]+,\s*\n)', r'\1' + abilities_line, result)
        
        return result
    
    # Aplica a correção para cada entrada de Pokémon
    content = re.sub(r'\[SPECIES_\w+\]\s*=\s*\{[^}]+\}', fix_abilities_format, content, flags=re.DOTALL)
    
    # 3. Corrige nomes de habilidades específicas do projeto
    ability_mapping = {
        'ABILITY_VOLTABSORB': 'ABILITY_VOLT_ABSORB',
        'ABILITY_WATERABSORB': 'ABILITY_WATER_ABSORB',
        'ABILITY_FLASHFIRE': 'ABILITY_FLASH_FIRE',
        'ABILITY_SWIFTSWIM': 'ABILITY_SWIFT_SWIM',
        'ABILITY_CHLOROPHYLL': 'ABILITY_CHLOROPHYLL',
        'ABILITY_SOLARPOWER': 'ABILITY_SOLAR_POWER',
        'ABILITY_RAINDISH': 'ABILITY_RAIN_DISH',
        'ABILITY_SANDRUSH': 'ABILITY_SAND_RUSH',
        'ABILITY_SANDFORCE': 'ABILITY_SAND_FORCE',
        'ABILITY_LIGHTNINGROD': 'ABILITY_LIGHTNING_ROD',
        'ABILITY_WATERVEIL': 'ABILITY_WATER_VEIL',
        'ABILITY_VITALSPIRIT': 'ABILITY_VITAL_SPIRIT',
        'ABILITY_PUREPOWER': 'ABILITY_PURE_POWER',
        'ABILITY_AIRLOCK': 'ABILITY_AIR_LOCK'
    }
    
    for wrong, correct in ability_mapping.items():
        content = content.replace(wrong, correct)
    
    # 4. Corrige EGG_GROUP names
    egg_group_mapping = {
        'EGG_GROUP_PLANT': 'EGG_GROUP_GRASS',
        'EGG_GROUP_GROUND': 'EGG_GROUP_FIELD',
        'EGG_GROUP_HUMANSHAPE': 'EGG_GROUP_HUMAN_LIKE',
        'EGG_GROUP_NO_EGGS': 'EGG_GROUP_UNDISCOVERED'
    }
    
    for wrong, correct in egg_group_mapping.items():
        content = content.replace(wrong, correct)
    
    # 5. Remove campos duplicados ou inválidos
    content = re.sub(r'\.safariZoneFleeRate\s*=\s*0,\s*\n\s*\.safariZoneFleeRate\s*=\s*0,', 
                     '.safariZoneFleeRate = 0,', content)
    
    # 6. Corrige valores de expYield para uint8 (0-255)
    def fix_exp_yield(match):
        value = int(match.group(1))
        if value > 255:
            return f".expYield = 255,"
        return match.group(0)
    
    content = re.sub(r'\.expYield\s*=\s*(\d+),', fix_exp_yield, content)
    
    # 7. Adiciona campos obrigatórios que podem estar faltando
    def ensure_required_fields(match):
        entry = match.group(0)
        
        # Verifica se tem todos os campos obrigatórios
        required_fields = [
            'baseHP', 'baseAttack', 'baseDefense', 'baseSpAttack', 'baseSpDefense', 'baseSpeed',
            'type1', 'type2', 'catchRate', 'expYield', 'abilities'
        ]
        
        for field in required_fields:
            if f'.{field}' not in entry:
                if field == 'abilities':
                    # Adiciona abilities padrão
                    entry = entry.replace('},', '        .abilities = {ABILITY_NONE, ABILITY_NONE, ABILITY_NONE},\n},')
                elif field in ['type1', 'type2']:
                    # Adiciona tipos padrão
                    entry = entry.replace('},', f'        .{field} = TYPE_NORMAL,\n}}')
                elif field == 'catchRate':
                    entry = entry.replace('},', '        .catchRate = 45,\n},')
                elif field == 'expYield':
                    entry = entry.replace('},', '        .expYield = 64,\n},')
        
        return entry
    
    content = re.sub(r'\[SPECIES_\w+\]\s*=\s*\{[^}]+\}', ensure_required_fields, content, flags=re.DOTALL)
    
    print("✅ Correções avançadas aplicadas:")
    print("   - Formato de abilities corrigido")
    print("   - Nomes de abilities mapeados")
    print("   - EGG_GROUP names corrigidos")
    print("   - Campos duplicados removidos")
    print("   - Valores de expYield limitados")
    print("   - Campos obrigatórios adicionados")
    
    # Salva o arquivo corrigido
    with open("src/Base_Stats.c", "w", encoding="utf-8") as f:
        f.write(content)
    
    print("💾 Arquivo Base_Stats.c corrigido com correções avançadas!")
    
    return True

def main():
    """Função principal"""
    
    if not os.path.exists("src/Base_Stats.c"):
        print("❌ Arquivo src/Base_Stats.c não encontrado!")
        return False
    
    # Aplica correções avançadas
    success = fix_advanced_errors()
    
    if success:
        print("\n🎯 PRÓXIMO PASSO:")
        print("Execute: python scripts/make.py")
        print("Para testar a compilação")
        
        return True
    
    return False

if __name__ == "__main__":
    main()
