#!/usr/bin/env python3
"""
Script para corrigir erros de compilação no Base_Stats.c
"""

import re
import os

def fix_compilation_errors():
    """Corrige os erros de compilação no arquivo Base_Stats.c"""
    
    print("🔧 CORRIGINDO ERROS DE COMPILAÇÃO")
    print("=" * 50)
    
    # Backup do arquivo original
    if not os.path.exists("src/Base_Stats.c.backup"):
        print("📋 Criando backup do arquivo original...")
        with open("src/Base_Stats.c", "r", encoding="utf-8") as f:
            content = f.read()
        with open("src/Base_Stats.c.backup", "w", encoding="utf-8") as f:
            f.write(content)
    
    # Lê o arquivo atual
    with open("src/Base_Stats.c", "r", encoding="utf-8") as f:
        content = f.read()
    
    print("🔄 Aplicando correções...")
    
    # 1. Corrige abilityHidden para abilities[2]
    content = re.sub(r'\.abilityHidden\s*=\s*([^,]+),', r'.abilities[2] = \1,', content)
    
    # 2. Corrige nomes de EGG_GROUP incorretos
    egg_group_fixes = {
        'EGG_GROUP_PLANT': 'EGG_GROUP_GRASS',
        'EGG_GROUP_GROUND': 'EGG_GROUP_FIELD',
        'EGG_GROUP_NO-EGGS': 'EGG_GROUP_NO_EGGS',
        'EGG_GROUP_HUMANSHAPE': 'EGG_GROUP_HUMAN_LIKE'
    }
    
    for wrong, correct in egg_group_fixes.items():
        content = content.replace(wrong, correct)
    
    # 3. Corrige nomes de ABILITY incorretos
    ability_fixes = {
        'ABILITY_VITALSPIRIT': 'ABILITY_VITAL_SPIRIT',
        'ABILITY_PUREPOWER': 'ABILITY_PURE_POWER',
        'ABILITY_AIRLOCK': 'ABILITY_AIR_LOCK'
    }
    
    for wrong, correct in ability_fixes.items():
        content = content.replace(wrong, correct)
    
    # 4. Corrige valores de expYield muito altos (limita a 255)
    def fix_exp_yield(match):
        value = int(match.group(1))
        if value > 255:
            return f".expYield = 255,"
        return match.group(0)
    
    content = re.sub(r'\.expYield\s*=\s*(\d+),', fix_exp_yield, content)
    
    # 5. Remove linhas de safariZoneFleeRate duplicadas
    content = re.sub(r'\.safariZoneFleeRate\s*=\s*0,\s*\n.*\.safariZoneFleeRate\s*=\s*0,', 
                     '.safariZoneFleeRate = 0,', content)
    
    # 6. Corrige problemas com EGG_GROUP_NO-EGGS
    content = content.replace('EGG_GROUP_NO-EGGS', 'EGG_GROUP_NO_EGGS')
    content = content.replace('EGG_GROUP_NO', 'EGG_GROUP_NO_EGGS')
    content = content.replace('EGGS', '')  # Remove referências órfãs a EGGS
    
    # 7. Verifica se há habilidades não declaradas e substitui por ABILITY_NONE
    undefined_abilities = [
        'ABILITY_VITALSPIRIT', 'ABILITY_PUREPOWER', 'ABILITY_AIRLOCK',
        'ABILITY_VOLTABSORB', 'ABILITY_WATERVEIL', 'ABILITY_RAINDISH',
        'ABILITY_SOLARPOWER', 'ABILITY_SANDRUSH', 'ABILITY_SANDFORCE',
        'ABILITY_LIGHTNINGROD', 'ABILITY_FLASHFIRE', 'ABILITY_WATERABSORB',
        'ABILITY_SWIFTSWIM', 'ABILITY_CHLOROPHYLL', 'ABILITY_OVERGROW',
        'ABILITY_BLAZE', 'ABILITY_TORRENT'
    ]
    
    # Substitui habilidades não definidas por ABILITY_NONE
    for ability in undefined_abilities:
        if ability not in ['ABILITY_VITAL_SPIRIT', 'ABILITY_PURE_POWER', 'ABILITY_AIR_LOCK']:
            content = content.replace(ability, 'ABILITY_NONE')
    
    # 8. Corrige problemas com tipos duplicados
    content = re.sub(r'\.type1\s*=\s*TYPE_(\w+),\s*\.type2\s*=\s*TYPE_\1,', 
                     r'.type1 = TYPE_\1,\n        .type2 = TYPE_\1,', content)
    
    print("✅ Correções aplicadas:")
    print("   - abilityHidden → abilities[2]")
    print("   - EGG_GROUP names corrigidos")
    print("   - ABILITY names corrigidos")
    print("   - expYield limitado a 255")
    print("   - safariZoneFleeRate duplicados removidos")
    
    # Salva o arquivo corrigido
    with open("src/Base_Stats.c", "w", encoding="utf-8") as f:
        f.write(content)
    
    print("💾 Arquivo Base_Stats.c corrigido e salvo!")
    
    return True

def verify_fixes():
    """Verifica se as correções foram aplicadas corretamente"""
    
    print("\n🔍 VERIFICANDO CORREÇÕES...")
    print("=" * 30)
    
    with open("src/Base_Stats.c", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Verifica problemas restantes
    issues = []
    
    # Verifica abilityHidden
    if '.abilityHidden' in content:
        issues.append("❌ abilityHidden ainda presente")
    else:
        print("✅ abilityHidden corrigido")
    
    # Verifica EGG_GROUP_PLANT
    if 'EGG_GROUP_PLANT' in content:
        issues.append("❌ EGG_GROUP_PLANT ainda presente")
    else:
        print("✅ EGG_GROUP names corrigidos")
    
    # Verifica ABILITY_VITALSPIRIT
    if 'ABILITY_VITALSPIRIT' in content:
        issues.append("❌ ABILITY_VITALSPIRIT ainda presente")
    else:
        print("✅ ABILITY names corrigidos")
    
    # Verifica expYield > 255
    exp_yields = re.findall(r'\.expYield\s*=\s*(\d+)', content)
    high_yields = [int(y) for y in exp_yields if int(y) > 255]
    if high_yields:
        issues.append(f"❌ {len(high_yields)} expYield > 255 encontrados")
    else:
        print("✅ expYield values corrigidos")
    
    if issues:
        print("\n⚠️  PROBLEMAS RESTANTES:")
        for issue in issues:
            print(f"   {issue}")
        return False
    else:
        print("\n✅ TODAS AS CORREÇÕES APLICADAS COM SUCESSO!")
        return True

def main():
    """Função principal"""
    
    if not os.path.exists("src/Base_Stats.c"):
        print("❌ Arquivo src/Base_Stats.c não encontrado!")
        return False
    
    # Aplica correções
    success = fix_compilation_errors()
    
    if success:
        # Verifica correções
        verify_fixes()
        
        print("\n🎯 PRÓXIMO PASSO:")
        print("Execute: python scripts/make.py")
        print("Para testar a compilação")
        
        return True
    
    return False

if __name__ == "__main__":
    main()
