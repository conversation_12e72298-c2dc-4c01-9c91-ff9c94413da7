#!/usr/bin/env python3
"""
Script final para corrigir o formato exato do projeto
"""

import re
import os

def fix_final_format():
    """Corrige o formato final para corresponder exatamente ao projeto"""
    
    print("🔧 CORREÇÃO FINAL DO FORMATO")
    print("=" * 40)
    
    # Lê o arquivo atual
    with open("src/Base_Stats.c", "r", encoding="utf-8") as f:
        content = f.read()
    
    print("🔄 Aplicando correções finais...")
    
    # 1. Reverte para o formato correto: ability1, ability2, abilityHidden
    content = re.sub(r'\.abilities\s*=\s*\{([^,]+),\s*([^,]+),\s*([^}]+)\}', 
                     r'.ability1 = \1,\n        .ability2 = \2,\n        .abilityHidden = \3', content)
    
    # 2. Corrige nomes de habilidades específicas
    ability_fixes = {
        'ABILITY_VOLT_ABSORB': 'ABILITY_VOLTABSORB',
        'ABILITY_WATER_ABSORB': 'ABILITY_WATERABSORB', 
        'ABILITY_FLASH_FIRE': 'ABILITY_FLASHFIRE',
        'ABILITY_SWIFT_SWIM': 'ABILITY_SWIFTSWIM',
        'ABILITY_SOLAR_POWER': 'ABILITY_SOLARPOWER',
        'ABILITY_RAIN_DISH': 'ABILITY_RAINDISH',
        'ABILITY_SAND_RUSH': 'ABILITY_SANDRUSH',
        'ABILITY_SAND_FORCE': 'ABILITY_SANDFORCE',
        'ABILITY_LIGHTNING_ROD': 'ABILITY_LIGHTNINGROD',
        'ABILITY_WATER_VEIL': 'ABILITY_WATERVEIL',
        'ABILITY_VITAL_SPIRIT': 'ABILITY_VITALSPIRIT',
        'ABILITY_PURE_POWER': 'ABILITY_PUREPOWER',
        'ABILITY_AIR_LOCK': 'ABILITY_AIRLOCK'
    }
    
    for wrong, correct in ability_fixes.items():
        content = content.replace(wrong, correct)
    
    # 3. Corrige EGG_GROUP names para o formato do projeto
    egg_group_fixes = {
        'EGG_GROUP_GRASS': 'EGG_GROUP_PLANT',
        'EGG_GROUP_FIELD': 'EGG_GROUP_GROUND', 
        'EGG_GROUP_HUMAN_LIKE': 'EGG_GROUP_HUMANSHAPE',
        'EGG_GROUP_UNDISCOVERED': 'EGG_GROUP_NO_EGGS'
    }
    
    for wrong, correct in egg_group_fixes.items():
        content = content.replace(wrong, correct)
    
    # 4. Garante que todos os campos obrigatórios estão presentes
    def ensure_complete_entry(match):
        entry = match.group(0)
        
        # Lista de campos obrigatórios na ordem correta
        required_fields = [
            'baseHP', 'baseAttack', 'baseDefense', 'baseSpAttack', 'baseSpDefense', 'baseSpeed',
            'type1', 'type2', 'catchRate', 'expYield',
            'evYield_HP', 'evYield_Attack', 'evYield_Defense', 'evYield_SpAttack', 'evYield_SpDefense', 'evYield_Speed',
            'item1', 'item2', 'genderRatio', 'eggCycles', 'friendship', 'growthRate',
            'eggGroup1', 'eggGroup2', 'ability1', 'ability2', 'abilityHidden',
            'safariZoneFleeRate', 'bodyColor', 'noFlip'
        ]
        
        # Adiciona campos faltantes com valores padrão
        missing_fields = []
        for field in required_fields:
            if f'.{field}' not in entry:
                if field in ['item1', 'item2']:
                    missing_fields.append(f'        .{field} = ITEM_NONE,')
                elif field == 'genderRatio':
                    missing_fields.append(f'        .{field} = PERCENT_FEMALE(50),')
                elif field == 'eggCycles':
                    missing_fields.append(f'        .{field} = 20,')
                elif field == 'friendship':
                    missing_fields.append(f'        .{field} = 50,')
                elif field == 'growthRate':
                    missing_fields.append(f'        .{field} = GROWTH_MEDIUM_FAST,')
                elif field in ['eggGroup1', 'eggGroup2']:
                    missing_fields.append(f'        .{field} = EGG_GROUP_MONSTER,')
                elif field in ['ability1', 'ability2', 'abilityHidden']:
                    missing_fields.append(f'        .{field} = ABILITY_NONE,')
                elif field == 'safariZoneFleeRate':
                    missing_fields.append(f'        .{field} = 0,')
                elif field == 'bodyColor':
                    missing_fields.append(f'        .{field} = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment')
                elif field == 'noFlip':
                    missing_fields.append(f'        .{field} = FALSE,')
                elif field.startswith('evYield_'):
                    missing_fields.append(f'        .{field} = 0,')
        
        # Adiciona campos faltantes antes do fechamento
        if missing_fields:
            entry = entry.replace('},', '\n' + '\n'.join(missing_fields) + '\n    },')
        
        return entry
    
    # Aplica a correção para cada entrada
    content = re.sub(r'\[SPECIES_\w+\]\s*=\s*\{[^}]+\}', ensure_complete_entry, content, flags=re.DOTALL)
    
    # 5. Corrige valores de expYield para não exceder 255
    def fix_exp_yield(match):
        value = int(match.group(1))
        if value > 255:
            return f".expYield = 255,"
        return match.group(0)
    
    content = re.sub(r'\.expYield\s*=\s*(\d+),', fix_exp_yield, content)
    
    # 6. Remove linhas duplicadas
    content = re.sub(r'(\.safariZoneFleeRate\s*=\s*0,)\s*\n\s*\1', r'\1', content)
    
    print("✅ Correções finais aplicadas:")
    print("   - Formato ability1/ability2/abilityHidden restaurado")
    print("   - Nomes de abilities corrigidos para o projeto")
    print("   - EGG_GROUP names corrigidos para o projeto")
    print("   - Campos obrigatórios adicionados")
    print("   - expYield limitado a 255")
    print("   - Duplicatas removidas")
    
    # Salva o arquivo corrigido
    with open("src/Base_Stats.c", "w", encoding="utf-8") as f:
        f.write(content)
    
    print("💾 Arquivo Base_Stats.c corrigido com formato final!")
    
    return True

def verify_format():
    """Verifica se o formato está correto"""
    
    print("\n🔍 VERIFICANDO FORMATO FINAL...")
    print("=" * 35)
    
    with open("src/Base_Stats.c", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Verifica se tem o formato correto
    issues = []
    
    # Verifica se tem ability1, ability2, abilityHidden
    if '.ability1' not in content:
        issues.append("❌ ability1 não encontrado")
    else:
        print("✅ ability1 presente")
    
    if '.ability2' not in content:
        issues.append("❌ ability2 não encontrado")
    else:
        print("✅ ability2 presente")
    
    if '.abilityHidden' not in content:
        issues.append("❌ abilityHidden não encontrado")
    else:
        print("✅ abilityHidden presente")
    
    # Verifica se não tem formato de array abilities
    if '.abilities = {' in content:
        issues.append("❌ Formato de array abilities ainda presente")
    else:
        print("✅ Formato de array abilities removido")
    
    # Conta entradas de Pokémon
    pokemon_entries = len(re.findall(r'\[SPECIES_\w+\]', content))
    print(f"📊 {pokemon_entries} entradas de Pokémon encontradas")
    
    if issues:
        print("\n⚠️  PROBLEMAS ENCONTRADOS:")
        for issue in issues:
            print(f"   {issue}")
        return False
    else:
        print("\n✅ FORMATO CORRETO APLICADO!")
        return True

def main():
    """Função principal"""
    
    if not os.path.exists("src/Base_Stats.c"):
        print("❌ Arquivo src/Base_Stats.c não encontrado!")
        return False
    
    # Aplica correções finais
    success = fix_final_format()
    
    if success:
        # Verifica formato
        verify_format()
        
        print("\n🎯 PRÓXIMO PASSO:")
        print("Execute: python scripts/make.py")
        print("Para testar a compilação final")
        
        return True
    
    return False

if __name__ == "__main__":
    main()
