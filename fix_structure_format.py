#!/usr/bin/env python3
"""
Script para corrigir o formato exato da estrutura BaseStats
"""

import re
import os

def fix_structure_format():
    """Corrige o formato para corresponder exatamente à estrutura BaseStats"""
    
    print("🔧 CORREÇÃO FINAL DA ESTRUTURA")
    print("=" * 45)
    
    # Restaura o backup original
    if os.path.exists("src/Base_Stats.c.backup"):
        print("📋 Restaurando backup original...")
        with open("src/Base_Stats.c.backup", "r", encoding="utf-8") as f:
            content = f.read()
    else:
        print("❌ Backup não encontrado!")
        return False
    
    print("🔄 Aplicando correções estruturais...")
    
    # 1. Corrige abilityHidden para hiddenAbility
    content = content.replace('.abilityHidden', '.hiddenAbility')
    
    # 2. Corrige nomes de EGG_GROUP para os corretos do projeto
    egg_group_fixes = {
        'EGG_GROUP_PLANT': 'EGG_GROUP_GRASS',
        'EGG_GROUP_GROUND': 'EGG_GROUP_FIELD',
        'EGG_GROUP_HUMANSHAPE': 'EGG_GROUP_HUMAN_LIKE',
        'EGG_GROUP_NO_EGGS': 'EGG_GROUP_UNDISCOVERED'
    }
    
    for wrong, correct in egg_group_fixes.items():
        content = content.replace(wrong, correct)
    
    # 3. Corrige nomes de habilidades para o formato do projeto
    ability_fixes = {
        'ABILITY_SHIELDDUST': 'ABILITY_SHIELD_DUST',
        'ABILITY_SHEDSKIN': 'ABILITY_SHED_SKIN',
        'ABILITY_COMPOUNDEYES': 'ABILITY_COMPOUND_EYES',
        'ABILITY_TINTEDLENS': 'ABILITY_TINTED_LENS',
        'ABILITY_BIGPECKS': 'ABILITY_BIG_PECKS'
    }
    
    for wrong, correct in ability_fixes.items():
        content = content.replace(wrong, correct)
    
    # 4. Remove campos duplicados ou problemáticos
    content = re.sub(r'\.safariZoneFleeRate\s*=\s*0,\s*\n\s*\.safariZoneFleeRate\s*=\s*0,', 
                     '.safariZoneFleeRate = 0,', content)
    
    # 5. Corrige valores de expYield para não exceder 255
    def fix_exp_yield(match):
        value = int(match.group(1))
        if value > 255:
            return f".expYield = 255,"
        return match.group(0)
    
    content = re.sub(r'\.expYield\s*=\s*(\d+),', fix_exp_yield, content)
    
    # 6. Verifica se todas as entradas têm a estrutura correta
    def verify_entry_structure(match):
        entry = match.group(0)
        
        # Campos obrigatórios na ordem correta
        required_fields = [
            'baseHP', 'baseAttack', 'baseDefense', 'baseSpAttack', 'baseSpDefense', 'baseSpeed',
            'type1', 'type2', 'catchRate', 'expYield',
            'evYield_HP', 'evYield_Attack', 'evYield_Defense', 'evYield_SpAttack', 'evYield_SpDefense', 'evYield_Speed',
            'item1', 'item2', 'genderRatio', 'eggCycles', 'friendship', 'growthRate',
            'eggGroup1', 'eggGroup2', 'ability1', 'ability2', 'hiddenAbility',
            'safariZoneFleeRate', 'bodyColor', 'noFlip'
        ]
        
        # Verifica se todos os campos estão presentes
        missing_fields = []
        for field in required_fields:
            if f'.{field}' not in entry:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"⚠️  Campos faltando em entrada: {missing_fields[:3]}...")
        
        return entry
    
    # Aplica verificação para cada entrada
    content = re.sub(r'\[SPECIES_\w+\]\s*=\s*\{[^}]+\}', verify_entry_structure, content, flags=re.DOTALL)
    
    print("✅ Correções estruturais aplicadas:")
    print("   - abilityHidden → hiddenAbility")
    print("   - EGG_GROUP names corrigidos")
    print("   - ABILITY names corrigidos")
    print("   - expYield limitado a 255")
    print("   - Estrutura verificada")
    
    # Salva o arquivo corrigido
    with open("src/Base_Stats.c", "w", encoding="utf-8") as f:
        f.write(content)
    
    print("💾 Arquivo Base_Stats.c corrigido com estrutura correta!")
    
    return True

def verify_structure():
    """Verifica se a estrutura está correta"""
    
    print("\n🔍 VERIFICANDO ESTRUTURA FINAL...")
    print("=" * 40)
    
    with open("src/Base_Stats.c", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Verifica problemas estruturais
    issues = []
    
    # Verifica se tem hiddenAbility
    if '.hiddenAbility' not in content:
        issues.append("❌ hiddenAbility não encontrado")
    else:
        print("✅ hiddenAbility presente")
    
    # Verifica se não tem abilityHidden
    if '.abilityHidden' in content:
        issues.append("❌ abilityHidden ainda presente")
    else:
        print("✅ abilityHidden removido")
    
    # Verifica EGG_GROUP_GRASS
    if 'EGG_GROUP_GRASS' not in content:
        issues.append("❌ EGG_GROUP_GRASS não encontrado")
    else:
        print("✅ EGG_GROUP_GRASS presente")
    
    # Verifica se não tem EGG_GROUP_PLANT
    if 'EGG_GROUP_PLANT' in content:
        issues.append("❌ EGG_GROUP_PLANT ainda presente")
    else:
        print("✅ EGG_GROUP_PLANT removido")
    
    # Conta entradas de Pokémon
    pokemon_entries = len(re.findall(r'\[SPECIES_\w+\]', content))
    print(f"📊 {pokemon_entries} entradas de Pokémon encontradas")
    
    if issues:
        print("\n⚠️  PROBLEMAS ENCONTRADOS:")
        for issue in issues:
            print(f"   {issue}")
        return False
    else:
        print("\n✅ ESTRUTURA CORRETA APLICADA!")
        return True

def main():
    """Função principal"""
    
    if not os.path.exists("src/Base_Stats.c.backup"):
        print("❌ Backup não encontrado!")
        return False
    
    # Aplica correções estruturais
    success = fix_structure_format()
    
    if success:
        # Verifica estrutura
        verify_structure()
        
        print("\n🎯 PRÓXIMO PASSO:")
        print("Execute: python scripts/make.py")
        print("Para testar a compilação final")
        
        return True
    
    return False

if __name__ == "__main__":
    main()
