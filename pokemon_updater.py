#!/usr/bin/env python3
"""
Pokemon Data Updater for Dynamic Pokemon Expansion
Atualiza todos os dados dos Pokémon para Generation IX usando PokeAPI
"""

import requests
import json
import time
import re
from typing import Dict, List, Optional, Tuple

class PokemonUpdater:
    def __init__(self):
        self.base_url = "https://pokeapi.co/api/v2"
        self.session = requests.Session()
        self.cache = {}

        # Mapeamento de tipos para o formato do projeto
        self.type_mapping = {
            'normal': 'TYPE_NORMAL',
            'fighting': 'TYPE_FIGHTING',
            'flying': 'TYPE_FLYING',
            'poison': 'TYPE_POISON',
            'ground': 'TYPE_GROUND',
            'rock': 'TYPE_ROCK',
            'bug': 'TYPE_BUG',
            'ghost': 'TYPE_GHOST',
            'steel': 'TYPE_STEEL',
            'fire': 'TYPE_FIRE',
            'water': 'TYPE_WATER',
            'grass': 'TYPE_GRASS',
            'electric': 'TYPE_ELECTRIC',
            'psychic': 'TYPE_PSYCHIC',
            'ice': 'TYPE_ICE',
            'dragon': 'TYPE_DRAGON',
            'dark': 'TYPE_DARK',
            'fairy': 'TYPE_FAIRY'
        }

        # Mapeamento de habilidades (será preenchido dinamicamente)
        self.ability_mapping = {}

    def get_pokemon_data(self, pokemon_id: int) -> Optional[Dict]:
        """Obtém dados completos de um Pokémon da PokeAPI"""
        if pokemon_id in self.cache:
            return self.cache[pokemon_id]

        try:
            # Dados básicos do Pokémon
            pokemon_response = self.session.get(f"{self.base_url}/pokemon/{pokemon_id}")
            if pokemon_response.status_code != 200:
                return None

            pokemon_data = pokemon_response.json()

            # Dados da espécie
            species_response = self.session.get(f"{self.base_url}/pokemon-species/{pokemon_id}")
            if species_response.status_code != 200:
                return None

            species_data = species_response.json()

            # Combina os dados
            combined_data = {
                'pokemon': pokemon_data,
                'species': species_data
            }

            self.cache[pokemon_id] = combined_data
            time.sleep(0.1)  # Rate limiting
            return combined_data

        except Exception as e:
            print(f"Erro ao obter dados do Pokémon {pokemon_id}: {e}")
            return None

    def get_latest_generation_data(self, pokemon_data: Dict) -> Dict:
        """Extrai dados da geração mais recente (Generation IX)"""
        pokemon = pokemon_data['pokemon']
        species = pokemon_data['species']

        # Stats base e EV Yields
        stats = {}
        ev_yields = {}
        for stat in pokemon['stats']:
            stat_name = stat['stat']['name']
            base_stat = stat['base_stat']
            effort = stat['effort']  # EV Yield!

            if stat_name == 'hp':
                stats['baseHP'] = base_stat
                ev_yields['evYield_HP'] = effort
            elif stat_name == 'attack':
                stats['baseAttack'] = base_stat
                ev_yields['evYield_Attack'] = effort
            elif stat_name == 'defense':
                stats['baseDefense'] = base_stat
                ev_yields['evYield_Defense'] = effort
            elif stat_name == 'special-attack':
                stats['baseSpAttack'] = base_stat
                ev_yields['evYield_SpAttack'] = effort
            elif stat_name == 'special-defense':
                stats['baseSpDefense'] = base_stat
                ev_yields['evYield_SpDefense'] = effort
            elif stat_name == 'speed':
                stats['baseSpeed'] = base_stat
                ev_yields['evYield_Speed'] = effort

        # Tipos
        types = []
        for type_info in pokemon['types']:
            type_name = type_info['type']['name']
            types.append(self.type_mapping.get(type_name, f'TYPE_{type_name.upper()}'))

        # Preenche segundo tipo se necessário
        if len(types) == 1:
            types.append(types[0])  # Mesmo tipo para ambos os slots

        # Habilidades
        abilities = {'ability1': None, 'ability2': None, 'hiddenAbility': None}
        for ability_info in pokemon['abilities']:
            ability_name = ability_info['ability']['name']
            ability_constant = self.get_ability_constant(ability_name)

            if ability_info['is_hidden']:
                abilities['hiddenAbility'] = ability_constant
            elif ability_info['slot'] == 1:
                abilities['ability1'] = ability_constant
            elif ability_info['slot'] == 2:
                abilities['ability2'] = ability_constant

        # Se não tem segunda habilidade, usa ABILITY_NONE
        if abilities['ability2'] is None:
            abilities['ability2'] = 'ABILITY_NONE'
        if abilities['hiddenAbility'] is None:
            abilities['hiddenAbility'] = 'ABILITY_NONE'

        # Dados da espécie
        gender_rate = species['gender_rate']
        if gender_rate == -1:
            gender_ratio = 'PERCENT_FEMALE(255)'  # Genderless
        else:
            female_percent = (gender_rate / 8) * 100
            gender_ratio = f'PERCENT_FEMALE({int(female_percent)})'

        # Moveset da geração mais recente (Scarlet/Violet)
        moves = self.get_latest_moveset(pokemon)

        # Base Experience (Exp Yield) - também disponível na API!
        base_experience = pokemon.get('base_experience', 64)

        # Held Items - também disponíveis na API!
        held_items = self.get_held_items(pokemon.get('held_items', []))

        return {
            'stats': stats,
            'ev_yields': ev_yields,
            'types': types,
            'abilities': abilities,
            'capture_rate': species['capture_rate'],
            'base_experience': base_experience,
            'base_happiness': species['base_happiness'],
            'gender_ratio': gender_ratio,
            'hatch_counter': species['hatch_counter'],
            'growth_rate': self.get_growth_rate_constant(species['growth_rate']['name']),
            'egg_groups': self.get_egg_groups(species['egg_groups']),
            'held_items': held_items,
            'moves': moves
        }

    def get_ability_constant(self, ability_name: str) -> str:
        """Converte nome da habilidade para constante do projeto"""
        # Mapeamento específico para habilidades que têm nomes diferentes no projeto
        ability_mapping = {
            # Habilidades que removem hífens/espaços sem underscore
            'serene-grace': 'ABILITY_SERENEGRACE',
            'compound-eyes': 'ABILITY_COMPOUNDEYES',
            'own-tempo': 'ABILITY_OWNTEMPO',
            'suction-cups': 'ABILITY_SUCTIONCUPS',
            'shadow-tag': 'ABILITY_SHADOWTAG',
            'rough-skin': 'ABILITY_ROUGHSKIN',
            'wonder-guard': 'ABILITY_WONDERGUARD',
            'effect-spore': 'ABILITY_EFFECTSPORE',
            'clear-body': 'ABILITY_CLEARBODY',
            'natural-cure': 'ABILITY_NATURALCURE',
            'lightning-rod': 'ABILITY_LIGHTNINGROD',
            'swift-swim': 'ABILITY_SWIFTSWIM',
            'huge-power': 'ABILITY_HUGEPOWER',
            'poison-point': 'ABILITY_POISONPOINT',
            'inner-focus': 'ABILITY_INNERFOCUS',
            'magma-armor': 'ABILITY_MAGMAARMOR',
            'water-veil': 'ABILITY_WATERVEIL',
            'magnet-pull': 'ABILITY_MAGNETPULL',
            'rain-dish': 'ABILITY_RAINDISH',
            'sand-stream': 'ABILITY_SANDSTREAM',
            'thick-fat': 'ABILITY_THICKFAT',
            'early-bird': 'ABILITY_EARLYBIRD',
            'flame-body': 'ABILITY_FLAMEBODY',
            'run-away': 'ABILITY_RUNAWAY',
            'keen-eye': 'ABILITY_KEENEYE',
            'hyper-cutter': 'ABILITY_HYPERCUTTER',
            'cute-charm': 'ABILITY_CUTECHARM',
            'sticky-hold': 'ABILITY_STICKYHOLD',
            'shed-skin': 'ABILITY_SHEDSKIN',
            'marvel-scale': 'ABILITY_MARVELSCALE',
            'liquid-ooze': 'ABILITY_LIQUIDOOZE',
            'rock-head': 'ABILITY_ROCKHEAD',
            'arena-trap': 'ABILITY_ARENATRAP',
            'shell-armor': 'ABILITY_SHELLARMOR',
            'sweet-veil': 'ABILITY_SWEETVEIL',
            'skill-link': 'ABILITY_SKILLLINK',
            'motor-drive': 'ABILITY_MOTORDRIVE',
            'multi-scale': 'ABILITY_MULTISCALE',
            'super-luck': 'ABILITY_SUPERLUCK',
            'big-pecks': 'ABILITY_BIGPECKS',
            'magic-bounce': 'ABILITY_MAGICBOUNCE',
            'sheer-force': 'ABILITY_SHEERFORCE',
            'iron-fist': 'ABILITY_IRONFIST',
            'sand-force': 'ABILITY_SANDFORCE',
            'solar-power': 'ABILITY_SOLARPOWER',
            'dry-skin': 'ABILITY_DRYSKIN',
            'tinted-lens': 'ABILITY_TINTEDLENS',
            'poison-heal': 'ABILITY_POISONHEAL',
            'ice-body': 'ABILITY_ICEBODY',
            'snow-cloak': 'ABILITY_SNOWCLOAK',
            'tangled-feet': 'ABILITY_TANGLEDFEET',
            'snow-warning': 'ABILITY_SNOWWARNING',
            'quick-feet': 'ABILITY_QUICKFEET',
            'sap-sipper': 'ABILITY_SAPSIPPER',
            'magic-guard': 'ABILITY_MAGICGUARD',
            'gale-wings': 'ABILITY_GALEWINGS',
            'cursed-body': 'ABILITY_CURSEDBODY',
            'grim-neigh': 'ABILITY_GRIMNEIGH',
            'sand-rush': 'ABILITY_SANDRUSH',
            'no-guard': 'ABILITY_NOGUARD',
            'mega-launcher': 'ABILITY_MEGALAUNCHER',
            'tough-claws': 'ABILITY_TOUGHCLAWS',
            'strong-jaw': 'ABILITY_STRONGJAW',
            'victory-star': 'ABILITY_VICTORYSTAR',
            'storm-drain': 'ABILITY_STORMDRAIN',
            'dark-aura': 'ABILITY_DARKAURA',
            'fairy-aura': 'ABILITY_FAIRYAURA',
            'aura-break': 'ABILITY_AURABREAK',
            'slow-start': 'ABILITY_SLOWSTART',
            'toxic-boost': 'ABILITY_TOXICBOOST',
            'flare-boost': 'ABILITY_FLAREBOOST',
            'fur-coat': 'ABILITY_FURCOAT',
            'wonder-skin': 'ABILITY_WONDERSKIN',
            'parental-bond': 'ABILITY_PARENTALBOND',
            'mold-breaker': 'ABILITY_MOLDBREAKER',
            'zen-mode': 'ABILITY_ZENMODE',
            'battle-bond': 'ABILITY_BATTLEBOND',
            'beast-boost': 'ABILITY_BEASTBOOST',
            'emergency-exit': 'ABILITY_EMERGENCYEXIT',
            'steely-spirit': 'ABILITY_STEELYSPIRIT',
            'perishbody': 'ABILITY_PERISHBODY',
            'wandering-spirit': 'ABILITY_WANDERINGSPIRIT',
            'power-construct': 'ABILITY_POWERCONSTRUCT',
            'prism-armor': 'ABILITY_PRISMARMOR',
            'rks-system': 'ABILITY_RKS_SYSTEM',
            'shadow-shield': 'ABILITY_SHADOWSHIELD',
            'shields-down': 'ABILITY_SHIELDSDOWN',
            'slush-rush': 'ABILITY_SLUSHRUSH',
            'soul-heart': 'ABILITY_SOULHEART',
            'steel-worker': 'ABILITY_STEELWORKER',
            'water-bubble': 'ABILITY_WATERBUBBLE',
            'water-compaction': 'ABILITY_WATERCOMPACTION',
            'electric-surge': 'ABILITY_ELECTRICSURGE',
            'grassy-surge': 'ABILITY_GRASSYSURGE',
            'misty-surge': 'ABILITY_MISTYSURGE',
            'psychic-surge': 'ABILITY_PSYCHICSURGE',
            'surge-surfer': 'ABILITY_SURGESURFER',
            'grass-pelt': 'ABILITY_GRASSPELT',
            'anger-point': 'ABILITY_ANGERPOINT',
            'weak-armor': 'ABILITY_WEAKARMOR',
            'heavy-metal': 'ABILITY_HEAVYMETAL',
            'light-metal': 'ABILITY_LIGHTMETAL',
            'aroma-veil': 'ABILITY_AROMAVEIL',
            'flower-veil': 'ABILITY_FLOWERVEIL',
            'leaf-guard': 'ABILITY_LEAFGUARD',
            'flower-gift': 'ABILITY_FLOWERGIFT',
            'bad-dreams': 'ABILITY_BADDREAMS',
            'pick-pocket': 'ABILITY_PICKPOCKET',
            'portal-power': 'ABILITY_PORTALPOWER',
            'poison-touch': 'ABILITY_POISONTOUCH',
            'stance-change': 'ABILITY_STANCECHANGE',
            'primordial-sea': 'ABILITY_PRIMORDIALSEA',
            'desolate-land': 'ABILITY_DESOLATELAND',
            'delta-stream': 'ABILITY_DELTASTREAM',
            'gorilla-tactics': 'ABILITY_GORILLATACTICS',
            'long-reach': 'ABILITY_LONGREACH',
            'liquid-voice': 'ABILITY_LIQUIDVOICE',
            'innards-out': 'ABILITY_INNARDSOUT',
            'honey-gather': 'ABILITY_HONEYGATHER',
            'friend-guard': 'ABILITY_FRIENDGUARD',
            'cheek-pouch': 'ABILITY_CHEEKPOUCH',
            'curious-medicine': 'ABILITY_CURIOUSMEDICINE',
            'intrepid-sword': 'ABILITY_INTREPIDSWORD',
            'dauntless-shield': 'ABILITY_DAUNTLESSSHIELD',
            'ball-fetch': 'ABILITY_BALLFETCH',
            'cotton-down': 'ABILITY_COTTONDOWN',
            'mirror-armor': 'ABILITY_MIRRORARMOR',
            'gulp-missile': 'ABILITY_GULPMISSILE',
            'steam-engine': 'ABILITY_STEAMENGINE',
            'punk-rock': 'ABILITY_PUNKROCK',
            'sand-spit': 'ABILITY_SANDSPIT',
            'ice-scales': 'ABILITY_ICESCALES',
            'ice-face': 'ABILITY_ICEFACE',
            'power-spot': 'ABILITY_POWERSPOT',
            'screen-cleaner': 'ABILITY_SCREENCLEANER',
            'pastel-veil': 'ABILITY_PASTELVEIL',

            # Habilidades especiais/aliases
            'air-lock': 'ABILITY_CLOUDNINE',
            'vital-spirit': 'ABILITY_INSOMNIA',
            'iron-barbs': 'ABILITY_ROUGHSKIN',
            'white-smoke': 'ABILITY_CLEARBODY',
            'pure-power': 'ABILITY_HUGEPOWER',
            'wimp-out': 'ABILITY_EMERGENCYEXIT',
            'tangling-hair': 'ABILITY_GOOEY',
            'turboblaze': 'ABILITY_MOLDBREAKER',
            'teravolt': 'ABILITY_MOLDBREAKER',
            'libero': 'ABILITY_PROTEAN',
            'solid-rock': 'ABILITY_FILTER',
            'queenly-majesty': 'ABILITY_DAZZLING',
            'power-of-alchemy': 'ABILITY_RECEIVER',
            'propeller-tail': 'ABILITY_STALWART',

            # Generation IX abilities (mapped to existing ones)
            'anger-shell': 'ABILITY_ANGERSHELL',
            'armor-tail': 'ABILITY_ARMORTAIL',
            'beads-of-ruin': 'ABILITY_BEADSOFRUIN',
            'sword-of-ruin': 'ABILITY_SWORDOFRUIN',
            'tablets-of-ruin': 'ABILITY_TABLETOFRUIN',
            'vessel-of-ruin': 'ABILITY_VESSELOFRUIN',
            'cud-chew': 'ABILITY_CUDCHEW',
            'earth-eater': 'ABILITY_EARTHEATER',
            'electromorphosis': 'ABILITY_ELECTROMORPHOSIS',
            'good-as-gold': 'ABILITY_GOODASGOLD',
            'guard-dog': 'ABILITY_GUARDDOG',
            'hadron-engine': 'ABILITY_HADRONENGINE',
            'minds-eye': 'ABILITY_MINDSEYE',
            'mycelium-might': 'ABILITY_MYCELIUMMIGHT',
            'orichalcum-pulse': 'ABILITY_ORICHALCUMPULSE',
            'poison-puppeteer': 'ABILITY_POISONPUPPETEER',
            'protosynthesis': 'ABILITY_PROTOSYNTHESIS',
            'purifying-salt': 'ABILITY_PURIFYINGSALT',
            'quark-drive': 'ABILITY_QUARKDRIVE',
            'rocky-payload': 'ABILITY_ROCKYPAYLOAD',
            'seed-sower': 'ABILITY_SEEDSOWER',
            'supreme-overlord': 'ABILITY_SUPREMEOVERLORD',
            'supersweet-syrup': 'ABILITY_SUPERSWEETSYRUP',
            'thermal-exchange': 'ABILITY_THERMALEXCHANGE',
            'toxic-chain': 'ABILITY_TOXICCHAIN',
            'toxic-debris': 'ABILITY_TOXICDEBRIS',
            'well-baked-body': 'ABILITY_WELLBAKEDBODY',
            'wind-power': 'ABILITY_WINDPOWER',
            'wind-rider': 'ABILITY_WINDRIDER',
            'zero-to-hero': 'ABILITY_ZEROTOHERO',
        }

        # Normaliza o nome da habilidade
        normalized_name = ability_name.lower().replace('_', '-')

        # Verifica se existe mapeamento específico
        if normalized_name in ability_mapping:
            return ability_mapping[normalized_name]

        # Fallback: remove hífens e converte para upper case (comportamento antigo)
        constant = ability_name.replace('-', '').replace('_', '').upper()
        return f'ABILITY_{constant}'

    def get_growth_rate_constant(self, growth_rate: str) -> str:
        """Converte growth rate para constante do projeto"""
        mapping = {
            'slow': 'GROWTH_SLOW',
            'medium': 'GROWTH_MEDIUM_FAST',
            'fast': 'GROWTH_FAST',
            'medium-slow': 'GROWTH_MEDIUM_SLOW',
            'slow-then-very-fast': 'GROWTH_ERRATIC',
            'fast-then-very-slow': 'GROWTH_FLUCTUATING'
        }
        return mapping.get(growth_rate, 'GROWTH_MEDIUM_FAST')

    def get_egg_groups(self, egg_groups: List[Dict]) -> Tuple[str, str]:
        """Converte egg groups para constantes do projeto"""
        group_mapping = {
            'monster': 'EGG_GROUP_MONSTER',
            'water1': 'EGG_GROUP_WATER_1',
            'water2': 'EGG_GROUP_WATER_2',
            'water3': 'EGG_GROUP_WATER_3',
            'bug': 'EGG_GROUP_BUG',
            'flying': 'EGG_GROUP_FLYING',
            'field': 'EGG_GROUP_FIELD',
            'fairy': 'EGG_GROUP_FAIRY',
            'grass': 'EGG_GROUP_GRASS',
            'human-like': 'EGG_GROUP_HUMAN_LIKE',
            'mineral': 'EGG_GROUP_MINERAL',
            'amorphous': 'EGG_GROUP_AMORPHOUS',
            'ditto': 'EGG_GROUP_DITTO',
            'dragon': 'EGG_GROUP_DRAGON',
            'undiscovered': 'EGG_GROUP_UNDISCOVERED'
        }

        groups = []
        for group in egg_groups:
            group_name = group['name']
            groups.append(group_mapping.get(group_name, f'EGG_GROUP_{group_name.upper()}'))

        # Preenche segundo grupo se necessário
        if len(groups) == 1:
            groups.append(groups[0])
        elif len(groups) == 0:
            groups = ['EGG_GROUP_UNDISCOVERED', 'EGG_GROUP_UNDISCOVERED']

        return groups[0], groups[1]

    def get_held_items(self, held_items_data: List[Dict]) -> Tuple[str, str]:
        """Converte held items para constantes do projeto"""
        items = ['ITEM_NONE', 'ITEM_NONE']

        # Processa held items (pega os mais comuns)
        for item_info in held_items_data[:2]:  # Máximo 2 itens
            item_name = item_info['item']['name']
            item_constant = f"ITEM_{item_name.replace('-', '_').upper()}"

            # Verifica se é um item comum ou raro baseado na raridade
            version_details = item_info.get('version_details', [])
            if version_details:
                # Pega a raridade mais recente
                rarity = version_details[-1].get('rarity', 0)
                if rarity >= 50:  # Item comum
                    items[0] = item_constant
                elif rarity >= 5:  # Item raro
                    items[1] = item_constant

        return items[0], items[1]

    def get_latest_moveset(self, pokemon_data: Dict) -> Dict:
        """Extrai moveset completo de uma única geração (não mistura gerações)"""
        moves = {
            'level_up': [],
            'tm': [],
            'egg': [],
            'tutor': [],
            'generation_used': None
        }

        # Sistema de priorização de version_groups (Generation IX → VIII → VII)
        version_priority = [
            'scarlet-violet',           # Generation IX (prioridade máxima)
            'sword-shield',             # Generation VIII (fallback 1)
            'ultra-sun-ultra-moon',     # Generation VII (fallback 2)
            'sun-moon',                 # Generation VII alternativo
            'omega-ruby-alpha-sapphire', # Generation VI (fallback 3)
            'x-y'                       # Generation VI alternativo
        ]

        # Primeiro, determina qual geração usar baseada na disponibilidade
        chosen_generation = None

        # Verifica quais gerações têm dados de level-up para este Pokémon
        available_generations = set()
        for move_info in pokemon_data['moves']:
            for version_detail in move_info['version_group_details']:
                if version_detail['move_learn_method']['name'] == 'level-up':
                    version_group = version_detail['version_group']['name']
                    if version_group in version_priority:
                        available_generations.add(version_group)

        # Escolhe a geração com maior prioridade que tem dados
        for priority_version in version_priority:
            if priority_version in available_generations:
                chosen_generation = priority_version
                break

        if not chosen_generation:
            print(f"⚠️  Nenhuma geração prioritária encontrada, usando dados disponíveis")
            return moves

        moves['generation_used'] = chosen_generation
        print(f"📋 Usando moveset completo de: {chosen_generation}")

        # Agora extrai TODOS os moves APENAS da geração escolhida
        for move_info in pokemon_data['moves']:
            move_name = move_info['move']['name']

            # Procura por dados desta geração específica
            for version_detail in move_info['version_group_details']:
                version_group = version_detail['version_group']['name']

                # APENAS processa se for da geração escolhida
                if version_group == chosen_generation:
                    learn_method = version_detail['move_learn_method']['name']
                    level = version_detail['level_learned_at']

                    if learn_method == 'level-up':
                        moves['level_up'].append({
                            'level': level,
                            'move': move_name,
                            'version': chosen_generation
                        })
                    elif learn_method == 'machine':
                        moves['tm'].append(move_name)
                    elif learn_method == 'egg':
                        moves['egg'].append(move_name)
                    elif learn_method == 'tutor':
                        moves['tutor'].append(move_name)

                    break  # Para de procurar outras versões deste move

        # Ordena moves por level
        moves['level_up'].sort(key=lambda x: x['level'])

        # Remove duplicatas que podem aparecer no mesmo nível
        unique_moves = []
        seen_moves = set()
        for move in moves['level_up']:
            move_key = (move['level'], move['move'])
            if move_key not in seen_moves:
                unique_moves.append(move)
                seen_moves.add(move_key)

        moves['level_up'] = unique_moves

        return moves

    def generate_base_stats_entry(self, pokemon_id: int, pokemon_name: str, data: Dict) -> str:
        """Gera entrada para Base_Stats.c"""
        stats = data['stats']
        ev_yields = data['ev_yields']
        types = data['types']
        abilities = data['abilities']
        held_items = data['held_items']

        entry = f"""    [SPECIES_{pokemon_name.upper()}] =
    {{
        .baseHP = {stats['baseHP']},
        .baseAttack = {stats['baseAttack']},
        .baseDefense = {stats['baseDefense']},
        .baseSpAttack = {stats['baseSpAttack']},
        .baseSpDefense = {stats['baseSpDefense']},
        .baseSpeed = {stats['baseSpeed']},
        .type1 = {types[0]},
        .type2 = {types[1]},
        .catchRate = {data['capture_rate']},
        .expYield = {data['base_experience']},
        .evYield_HP = {ev_yields['evYield_HP']},
        .evYield_Attack = {ev_yields['evYield_Attack']},
        .evYield_Defense = {ev_yields['evYield_Defense']},
        .evYield_SpAttack = {ev_yields['evYield_SpAttack']},
        .evYield_SpDefense = {ev_yields['evYield_SpDefense']},
        .evYield_Speed = {ev_yields['evYield_Speed']},
        .item1 = {held_items[0]},
        .item2 = {held_items[1]},
        .genderRatio = {data['gender_ratio']},
        .eggCycles = {data['hatch_counter']},
        .friendship = {data['base_happiness']},
        .growthRate = {data['growth_rate']},
        .eggGroup1 = {data['egg_groups'][0]},
        .eggGroup2 = {data['egg_groups'][1]},
        .ability1 = {abilities['ability1']},
        .ability2 = {abilities['ability2']},
        .abilityHidden = {abilities['hiddenAbility']},
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    }},"""

        return entry

    def generate_level_up_moves(self, pokemon_name: str, moves: List[Dict]) -> str:
        """Gera tabela de level up moves"""
        if not moves:
            return f"static const struct LevelUpMove s{pokemon_name}LevelUpLearnset[] = {{\n    LEVEL_UP_END\n}};"

        entries = []
        for move in moves:
            move_constant = f"MOVE_{move['move'].replace('-', '_').upper()}"
            entries.append(f"    LEVEL_UP_MOVE({move['level']:2d}, {move_constant}),")

        entries.append("    LEVEL_UP_END")

        return f"static const struct LevelUpMove s{pokemon_name}LevelUpLearnset[] = {{\n" + "\n".join(entries) + "\n}};"

    def backup_original_file(self, filepath: str) -> str:
        """Cria backup do arquivo original"""
        import shutil
        backup_path = f"{filepath}.backup"
        shutil.copy2(filepath, backup_path)
        return backup_path

    def update_pokemon_in_project(self, pokemon_id: int, pokemon_name: str) -> bool:
        """Atualiza um Pokémon específico no projeto"""
        print(f"Atualizando {pokemon_name} (ID: {pokemon_id})...")

        # Obtém dados da PokeAPI
        pokemon_data = self.get_pokemon_data(pokemon_id)
        if not pokemon_data:
            print(f"❌ Erro ao obter dados do {pokemon_name}")
            return False

        # Extrai dados da geração mais recente
        latest_data = self.get_latest_generation_data(pokemon_data)

        # Gera código atualizado
        base_stats_entry = self.generate_base_stats_entry(pokemon_id, pokemon_name, latest_data)
        level_up_moves = self.generate_level_up_moves(pokemon_name, latest_data['moves']['level_up'])

        # Salva dados para aplicação posterior
        self.save_pokemon_update_data(pokemon_id, pokemon_name, {
            'base_stats_entry': base_stats_entry,
            'level_up_moves': level_up_moves,
            'latest_data': latest_data
        })

        print(f"✅ {pokemon_name} processado com sucesso")
        return True

    def save_pokemon_update_data(self, pokemon_id: int, pokemon_name: str, data: Dict):
        """Salva dados de atualização para aplicação posterior"""
        import os

        # Cria diretório de saída se não existir
        output_dir = "pokemon_updates"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # Salva dados individuais
        with open(f"{output_dir}/{pokemon_name}_{pokemon_id}.txt", "w", encoding="utf-8") as f:
            f.write(f"// {pokemon_name.upper()} (#{pokemon_id:03d}) - GENERATION IX UPDATE\n\n")
            f.write("// BASE STATS ENTRY:\n")
            f.write(data['base_stats_entry'])
            f.write("\n\n// LEVEL UP MOVES:\n")
            f.write(data['level_up_moves'])
            f.write("\n\n// SUMMARY:\n")
            stats = data['latest_data']['stats']
            f.write(f"// Stats Total: {sum(stats.values())}\n")
            f.write(f"// Types: {data['latest_data']['types'][0]} / {data['latest_data']['types'][1]}\n")
            f.write(f"// Abilities: {data['latest_data']['abilities']['ability1']}, {data['latest_data']['abilities']['ability2']}, {data['latest_data']['abilities']['hiddenAbility']}\n")
            f.write(f"// Level Up Moves: {len(data['latest_data']['moves']['level_up'])}\n")

    def apply_updates_to_files(self, pokemon_updates: List[Dict]) -> bool:
        """Aplica atualizações aos arquivos do projeto"""
        print("\n🔧 APLICANDO ATUALIZAÇÕES AOS ARQUIVOS...")

        try:
            # Backup dos arquivos originais
            self.backup_original_file("src/Base_Stats.c")
            self.backup_original_file("src/Learnsets.c")

            # Atualiza Base_Stats.c
            self.update_base_stats_file(pokemon_updates)

            # Atualiza Learnsets.c
            self.update_learnsets_file(pokemon_updates)

            print("✅ Arquivos atualizados com sucesso!")
            return True

        except Exception as e:
            print(f"❌ Erro ao aplicar atualizações: {e}")
            return False

    def update_base_stats_file(self, pokemon_updates: List[Dict]):
        """Atualiza o arquivo Base_Stats.c"""
        print("📝 Atualizando Base_Stats.c...")

        # Lê arquivo original
        with open("src/Base_Stats.c", "r", encoding="utf-8") as f:
            content = f.read()

        # Aplica atualizações
        for update in pokemon_updates:
            pokemon_name = update['pokemon_name']
            species_pattern = f"\\[SPECIES_{pokemon_name.upper()}\\]\\s*=\\s*{{[^}}]+}},"

            # Substitui entrada existente
            import re
            content = re.sub(species_pattern, update['base_stats_entry'], content, flags=re.DOTALL)

        # Salva arquivo atualizado
        with open("src/Base_Stats.c", "w", encoding="utf-8") as f:
            f.write(content)

        print(f"✅ Base_Stats.c atualizado com {len(pokemon_updates)} Pokémon")

    def update_learnsets_file(self, pokemon_updates: List[Dict]):
        """Atualiza o arquivo Learnsets.c"""
        print("📝 Atualizando Learnsets.c...")

        # Lê arquivo original
        with open("src/Learnsets.c", "r", encoding="utf-8") as f:
            content = f.read()

        # Aplica atualizações de movesets
        updated_count = 0
        for update in pokemon_updates:
            pokemon_name = update['pokemon_name']

            # Capitaliza o nome corretamente (primeira letra maiúscula)
            capitalized_name = pokemon_name.capitalize()

            # Padrão corrigido para encontrar o moveset existente
            # Procura por: static const struct LevelUpMove sNomeLevelUpLearnset[] = { ... };
            moveset_pattern = f"static const struct LevelUpMove s{capitalized_name}LevelUpLearnset\\[\\][^;]+;"

            # Substitui moveset existente
            import re
            old_content = content
            content = re.sub(moveset_pattern, update['level_up_moves'], content, flags=re.DOTALL)

            # Verifica se a substituição foi feita
            if content != old_content:
                print(f"   ✅ {pokemon_name} moveset atualizado")
                updated_count += 1
            else:
                print(f"   ⚠️  {pokemon_name} moveset não encontrado (padrão: s{capitalized_name}LevelUpLearnset)")

        # Salva arquivo atualizado
        with open("src/Learnsets.c", "w", encoding="utf-8") as f:
            f.write(content)

        print(f"✅ Learnsets.c atualizado com {updated_count}/{len(pokemon_updates)} Pokémon")

    def create_update_report(self, pokemon_list: List[Tuple[int, str]]) -> str:
        """Cria relatório de atualizações"""
        report = "# RELATÓRIO DE ATUALIZAÇÃO POKÉMON - GENERATION IX\n\n"
        report += f"Data: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        report += f"Total de Pokémon processados: {len(pokemon_list)}\n\n"

        report += "## Pokémon Atualizados:\n\n"

        successful = 0
        failed = 0

        for pokemon_id, pokemon_name in pokemon_list:
            pokemon_data = self.get_pokemon_data(pokemon_id)
            if pokemon_data:
                latest_data = self.get_latest_generation_data(pokemon_data)
                stats = latest_data['stats']

                generation_used = latest_data['moves'].get('generation_used', 'unknown')
                report += f"### {pokemon_name.title()} (#{pokemon_id:03d})\n"
                report += f"- **Stats**: {stats['baseHP']}/{stats['baseAttack']}/{stats['baseDefense']}/{stats['baseSpAttack']}/{stats['baseSpDefense']}/{stats['baseSpeed']}\n"
                report += f"- **Tipos**: {latest_data['types'][0]} / {latest_data['types'][1]}\n"
                report += f"- **Habilidades**: {latest_data['abilities']['ability1']}, {latest_data['abilities']['ability2']}, {latest_data['abilities']['hiddenAbility']}\n"
                report += f"- **Capture Rate**: {latest_data['capture_rate']}\n"
                report += f"- **Moves por Level**: {len(latest_data['moves']['level_up'])}\n"
                report += f"- **Geração do Moveset**: {generation_used}\n\n"
                successful += 1
            else:
                report += f"### ❌ {pokemon_name.title()} (#{pokemon_id:03d}) - FALHOU\n\n"
                failed += 1

        report += f"\n## Resumo:\n"
        report += f"- ✅ Sucessos: {successful}\n"
        report += f"- ❌ Falhas: {failed}\n"
        report += f"- 📊 Taxa de sucesso: {(successful/(successful+failed)*100):.1f}%\n"

        return report


def main():
    """Função principal para atualizar Pokémon"""
    updater = PokemonUpdater()

    print("🔄 POKEMON DATA UPDATER - GENERATION IX")
    print("=" * 50)

    # Lista de Pokémon para atualizar (sequencial por geração)
    pokemon_list = get_pokemon_list_by_generation()

    print(f"📋 Processando {len(pokemon_list)} Pokémon...")
    print()

    # Pergunta ao usuário se quer aplicar automaticamente
    apply_automatically = input("🤖 Aplicar atualizações automaticamente aos arquivos? (s/N): ").lower().startswith('s')

    # Processa cada Pokémon
    successful_updates = []
    failed_updates = []
    pokemon_updates = []

    for i, (pokemon_id, pokemon_name) in enumerate(pokemon_list, 1):
        print(f"[{i:3d}/{len(pokemon_list)}] ", end="")

        try:
            if updater.update_pokemon_in_project(pokemon_id, pokemon_name):
                successful_updates.append((pokemon_id, pokemon_name))

                # Coleta dados para aplicação
                if apply_automatically:
                    pokemon_data = updater.get_pokemon_data(pokemon_id)
                    if pokemon_data:
                        latest_data = updater.get_latest_generation_data(pokemon_data)
                        pokemon_updates.append({
                            'pokemon_id': pokemon_id,
                            'pokemon_name': pokemon_name,
                            'base_stats_entry': updater.generate_base_stats_entry(pokemon_id, pokemon_name, latest_data),
                            'level_up_moves': updater.generate_level_up_moves(pokemon_name, latest_data['moves']['level_up']),
                            'latest_data': latest_data
                        })
            else:
                failed_updates.append((pokemon_id, pokemon_name))
        except Exception as e:
            print(f"❌ Erro crítico ao processar {pokemon_name}: {e}")
            failed_updates.append((pokemon_id, pokemon_name))

        # Pausa a cada 50 Pokémon para evitar rate limiting
        if i % 50 == 0:
            print(f"\n⏸️  Pausa de 5 segundos (processados {i}/{len(pokemon_list)})...")
            time.sleep(5)

    # Aplica atualizações se solicitado
    if apply_automatically and pokemon_updates:
        print(f"\n🔧 Aplicando {len(pokemon_updates)} atualizações aos arquivos...")
        if updater.apply_updates_to_files(pokemon_updates):
            print("✅ Atualizações aplicadas com sucesso!")
        else:
            print("❌ Erro ao aplicar atualizações")

    # Gera relatório
    print("\n" + "=" * 50)
    print("📊 GERANDO RELATÓRIO...")

    report = updater.create_update_report(pokemon_list)

    # Salva relatório
    with open("pokemon_update_report.md", "w", encoding="utf-8") as f:
        f.write(report)

    print(f"✅ Relatório salvo em: pokemon_update_report.md")

    # Resumo final
    print("\n" + "=" * 50)
    print("📈 RESUMO FINAL:")
    print(f"✅ Sucessos: {len(successful_updates)}")
    print(f"❌ Falhas: {len(failed_updates)}")
    print(f"📁 Arquivos individuais salvos em: pokemon_updates/")

    if failed_updates:
        print("\n❌ Pokémon que falharam:")
        for pokemon_id, pokemon_name in failed_updates[:10]:  # Mostra apenas os primeiros 10
            print(f"   - {pokemon_name} (#{pokemon_id})")
        if len(failed_updates) > 10:
            print(f"   ... e mais {len(failed_updates) - 10} Pokémon")

    if apply_automatically:
        print("\n🎯 PRÓXIMOS PASSOS:")
        print("1. Verificar se o projeto compila corretamente")
        print("2. Testar alguns Pokémon no jogo")
        print("3. Ajustar manualmente dados que precisam de refinamento")
        print("4. Fazer commit das mudanças")
    else:
        print("\n🎯 PRÓXIMOS PASSOS:")
        print("1. Revisar o relatório gerado (pokemon_update_report.md)")
        print("2. Revisar arquivos individuais em pokemon_updates/")
        print("3. Aplicar mudanças manualmente ou executar novamente com aplicação automática")
        print("4. Testar o projeto para garantir que compila")


def get_pokemon_list_by_generation():
    """Retorna lista de Pokémon organizados por geração"""
    # Generation I (1-151)
    gen1 = [(i, f"pokemon_{i}") for i in range(1, 152)]

    # Generation II (152-251)
    gen2 = [(i, f"pokemon_{i}") for i in range(152, 252)]

    # Generation III (252-386)
    gen3 = [(i, f"pokemon_{i}") for i in range(252, 387)]

    # Para demonstração, vamos usar apenas alguns Pokémon conhecidos
    demo_list = [
        # Gen I starters
        (1, "bulbasaur"), (2, "ivysaur"), (3, "venusaur"),
        (4, "charmander"), (5, "charmeleon"), (6, "charizard"),
        (7, "squirtle"), (8, "wartortle"), (9, "blastoise"),

        # Alguns Pokémon problemáticos que sabemos que foram modificados
        (25, "pikachu"), (26, "raichu"),
        (22, "fearow"),  # Sabemos que foi modificado
        (24, "arbok"),   # Sabemos que foi modificado
        (28, "sandslash"), # Sabemos que foi modificado

        # Gen III starters
        (252, "treecko"), (253, "grovyle"), (254, "sceptile"),
        (255, "torchic"), (256, "combusken"), (257, "blaziken"),
        (258, "mudkip"), (259, "marshtomp"), (260, "swampert"),

        # Vibrava line (sabemos que foi modificado)
        (328, "trapinch"), (329, "vibrava"), (330, "flygon"),
    ]

    return demo_list


if __name__ == "__main__":
    main()
