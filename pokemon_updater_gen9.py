#!/usr/bin/env python3
"""
Script para atualizar Pokémon com dados CORRETOS da Generation IX (Scarlet/Violet)
"""

import requests
import json
import time
import os
from typing import Dict, List, Optional, Tuple

class PokemonGen9Updater:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Pokemon-ROM-Updater/1.0'
        })
        self.cache = {}

    def get_pokemon_data_gen9(self, pokemon_id: int, pokemon_name: str) -> Optional[Dict]:
        """Obtém dados do Pokémon especificamente da Generation IX"""

        print(f"🔍 Buscando dados Gen IX para #{pokemon_id} {pokemon_name}...")

        try:
            # 1. Dados básicos do Pokémon
            pokemon_url = f"https://pokeapi.co/api/v2/pokemon/{pokemon_id}"
            pokemon_response = self.session.get(pokemon_url, timeout=10)

            if pokemon_response.status_code != 200:
                print(f"❌ Erro ao buscar Pokémon #{pokemon_id}")
                return None

            pokemon_data = pokemon_response.json()

            # 2. Dados da espécie
            species_url = pokemon_data['species']['url']
            species_response = self.session.get(species_url, timeout=10)
            species_data = species_response.json()

            # 3. CRÍTICO: Buscar dados específicos da Generation IX
            gen9_data = self.get_generation_specific_data(pokemon_id, pokemon_name)

            # 4. Combinar dados
            result = {
                'id': pokemon_id,
                'name': pokemon_name,
                'base_stats': self.extract_base_stats(pokemon_data),
                'types': self.extract_types(pokemon_data),
                'abilities': self.extract_abilities(pokemon_data),
                'species_data': self.extract_species_data(species_data),
                'moves': gen9_data.get('moves', []),
                'generation': 9 if gen9_data else 8
            }

            return result

        except Exception as e:
            print(f"❌ Erro ao processar #{pokemon_id}: {e}")
            return None

    def get_generation_specific_data(self, pokemon_id: int, pokemon_name: str) -> Dict:
        """Busca dados específicos da Generation IX usando busca direta"""

        try:
            print(f"🔍 Buscando dados específicos de Scarlet/Violet...")

            # Busca moves específicos da Gen IX diretamente
            moves_data = self.get_moves_from_generation(pokemon_id, 'scarlet-violet')

            if moves_data and len(moves_data) > 0:
                print(f"✅ Dados Gen IX encontrados para #{pokemon_id}!")
                return {'moves': moves_data, 'generation': 9}
            else:
                print(f"❌ Nenhum move Gen IX encontrado para #{pokemon_id}")

        except Exception as e:
            print(f"❌ Erro ao buscar dados Gen IX: {e}")

        # Fallback: Generation VIII
        print(f"⚠️  Usando fallback Gen VIII para #{pokemon_id}")
        try:
            moves_data = self.get_moves_from_generation(pokemon_id, 'sword-shield')
            if moves_data and len(moves_data) > 0:
                return {'moves': moves_data, 'generation': 8}
        except Exception as e:
            print(f"❌ Erro no fallback Gen VIII: {e}")

        # Último recurso: dados vazios
        print(f"❌ Falha total para #{pokemon_id}")
        return {'moves': [], 'generation': 0}

    def get_moves_from_generation(self, pokemon_id: int, version_group: str) -> List[Dict]:
        """Busca moves de uma versão específica"""

        try:
            # Busca dados do Pokémon
            pokemon_url = f"https://pokeapi.co/api/v2/pokemon/{pokemon_id}"
            response = self.session.get(pokemon_url, timeout=10)

            if response.status_code != 200:
                print(f"❌ Erro HTTP {response.status_code} ao buscar Pokémon #{pokemon_id}")
                return []

            pokemon_data = response.json()
            moves = []
            found_version_groups = set()

            for move_entry in pokemon_data['moves']:
                for version_detail in move_entry['version_group_details']:
                    vg_name = version_detail['version_group']['name']
                    found_version_groups.add(vg_name)

                    # Busca EXATAMENTE pela versão especificada
                    if vg_name == version_group:
                        if version_detail['move_learn_method']['name'] == 'level-up':
                            level = version_detail['level_learned_at']
                            move_name = move_entry['move']['name']

                            moves.append({
                                'name': move_name,
                                'level': level,
                                'method': 'level-up',
                                'version_group': vg_name
                            })

            print(f"📋 Version groups encontrados: {sorted(found_version_groups)}")
            print(f"🎯 Procurando por: {version_group}")
            print(f"📊 Moves encontrados para {version_group}: {len(moves)}")

            # Ordena por nível
            moves.sort(key=lambda x: (x['level'], x['name']))
            return moves

        except Exception as e:
            print(f"❌ Erro ao buscar moves: {e}")
            return []



    def extract_base_stats(self, pokemon_data: Dict) -> Dict:
        """Extrai base stats"""
        stats = {}
        for stat in pokemon_data['stats']:
            stat_name = stat['stat']['name']
            stat_value = stat['base_stat']

            # Mapeia nomes
            stat_mapping = {
                'hp': 'baseHP',
                'attack': 'baseAttack',
                'defense': 'baseDefense',
                'special-attack': 'baseSpAttack',
                'special-defense': 'baseSpDefense',
                'speed': 'baseSpeed'
            }

            if stat_name in stat_mapping:
                stats[stat_mapping[stat_name]] = stat_value

        return stats

    def extract_types(self, pokemon_data: Dict) -> Tuple[str, str]:
        """Extrai tipos"""
        types = pokemon_data['types']
        type1 = f"TYPE_{types[0]['type']['name'].upper()}"
        type2 = f"TYPE_{types[1]['type']['name'].upper()}" if len(types) > 1 else type1
        return type1, type2

    def extract_abilities(self, pokemon_data: Dict) -> Tuple[str, str, str]:
        """Extrai habilidades"""
        abilities = pokemon_data['abilities']

        ability1 = "ABILITY_NONE"
        ability2 = "ABILITY_NONE"
        hidden_ability = "ABILITY_NONE"

        for ability in abilities:
            ability_name = f"ABILITY_{ability['ability']['name'].upper()}"

            if ability['is_hidden']:
                hidden_ability = ability_name
            elif ability['slot'] == 1:
                ability1 = ability_name
            elif ability['slot'] == 2:
                ability2 = ability_name

        return ability1, ability2, hidden_ability

    def extract_species_data(self, species_data: Dict) -> Dict:
        """Extrai dados da espécie"""
        return {
            'catch_rate': species_data.get('capture_rate', 45),
            'base_happiness': species_data.get('base_happiness', 50),
            'growth_rate': species_data.get('growth_rate', {}).get('name', 'medium-slow'),
            'egg_groups': [eg['name'] for eg in species_data.get('egg_groups', [])],
            'gender_rate': species_data.get('gender_rate', 4),
            'hatch_counter': species_data.get('hatch_counter', 20)
        }

    def format_pokemon_data(self, data: Dict) -> str:
        """Formata dados do Pokémon para o arquivo C"""

        if not data:
            return ""

        pokemon_id = data['id']
        name = data['name'].title()
        stats = data['base_stats']
        type1, type2 = data['types']
        ability1, ability2, hidden_ability = data['abilities']
        species = data['species_data']
        moves = data['moves']
        generation = data['generation']

        # Cabeçalho
        output = f"// POKEMON_{pokemon_id} (#{pokemon_id}) - GENERATION {generation} UPDATE\n\n"

        # Base Stats
        output += "// BASE STATS ENTRY:\n"
        output += f"    [SPECIES_POKEMON_{pokemon_id}] =\n"
        output += "    {\n"
        output += f"        .baseHP = {stats.get('baseHP', 50)},\n"
        output += f"        .baseAttack = {stats.get('baseAttack', 50)},\n"
        output += f"        .baseDefense = {stats.get('baseDefense', 50)},\n"
        output += f"        .baseSpAttack = {stats.get('baseSpAttack', 50)},\n"
        output += f"        .baseSpDefense = {stats.get('baseSpDefense', 50)},\n"
        output += f"        .baseSpeed = {stats.get('baseSpeed', 50)},\n"
        output += f"        .type1 = {type1},\n"
        output += f"        .type2 = {type2},\n"
        output += f"        .catchRate = {species.get('catch_rate', 45)},\n"
        output += f"        .expYield = {min(255, stats.get('baseHP', 50) + stats.get('baseAttack', 50))},\n"
        output += "        .evYield_HP = 0,\n"
        output += "        .evYield_Attack = 0,\n"
        output += "        .evYield_Defense = 0,\n"
        output += "        .evYield_SpAttack = 0,\n"
        output += "        .evYield_SpDefense = 0,\n"
        output += "        .evYield_Speed = 0,\n"
        output += "        .item1 = ITEM_NONE,\n"
        output += "        .item2 = ITEM_NONE,\n"
        output += f"        .genderRatio = PERCENT_FEMALE({species.get('gender_rate', 4) * 12.5}),\n"
        output += f"        .eggCycles = {species.get('hatch_counter', 20)},\n"
        output += f"        .friendship = {species.get('base_happiness', 50)},\n"
        output += "        .growthRate = GROWTH_MEDIUM_SLOW,\n"
        output += "        .eggGroup1 = EGG_GROUP_FIELD,\n"
        output += "        .eggGroup2 = EGG_GROUP_FIELD,\n"
        output += f"        .ability1 = {ability1},\n"
        output += f"        .ability2 = {ability2},\n"
        output += f"        .hiddenAbility = {hidden_ability},\n"
        output += "        .safariZoneFleeRate = 0,\n"
        output += "        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment\n"
        output += "        .noFlip = FALSE,\n"
        output += "    },\n\n"

        # Learnset
        output += "// LEVEL UP MOVES:\n"
        output += f"static const struct LevelUpMove s{name}LevelUpLearnset[] = {{\n"

        for move in moves:
            move_name = f"MOVE_{move['name'].upper().replace('-', '_')}"
            level = move['level']
            output += f"    LEVEL_UP_MOVE({level:2d}, {move_name}),\n"

        output += "    LEVEL_UP_END\n"
        output += "};\n\n"

        # Resumo
        total_stats = sum(stats.values())
        output += "// SUMMARY:\n"
        output += f"// Stats Total: {total_stats}\n"
        output += f"// Types: {type1} / {type2}\n"
        output += f"// Abilities: {ability1}, {ability2}, {hidden_ability}\n"
        output += f"// Level Up Moves: {len(moves)}\n"
        output += f"// Generation: {generation}\n\n"

        return output

def test_specific_pokemon():
    """Testa com Grovyle (#253) e Trapinch (#328)"""

    print("🧪 TESTE DE DADOS GENERATION IX")
    print("=" * 40)

    updater = PokemonGen9Updater()

    test_pokemon = [
        (253, "grovyle"),
        (328, "trapinch")
    ]

    for pokemon_id, pokemon_name in test_pokemon:
        print(f"\n🔍 Testando #{pokemon_id} {pokemon_name.title()}...")

        # Busca dados
        data = updater.get_pokemon_data_gen9(pokemon_id, pokemon_name)

        if data:
            # Formata dados
            formatted_data = updater.format_pokemon_data(data)

            # Salva arquivo de teste
            filename = f"test_{pokemon_name}_{pokemon_id}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(formatted_data)

            print(f"✅ Dados salvos em: {filename}")
            print(f"📊 Generation: {data['generation']}")
            print(f"📊 Moves encontrados: {len(data['moves'])}")

            # Mostra alguns moves para verificação
            if data['moves']:
                print("🎯 Primeiros moves:")
                for move in data['moves'][:5]:
                    print(f"   Nível {move['level']:2d}: {move['name']}")
        else:
            print(f"❌ Falha ao obter dados para {pokemon_name}")

        time.sleep(1)  # Rate limiting

if __name__ == "__main__":
    test_specific_pokemon()
