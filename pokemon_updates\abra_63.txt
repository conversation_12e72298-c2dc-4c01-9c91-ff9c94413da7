// ABRA (#063) - <PERSON><PERSON><PERSON>TI<PERSON> IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_ABRA] =
    {
        .baseHP = 25,
        .baseAttack = 20,
        .baseDefense = 15,
        .baseSpAttack = 105,
        .baseSpDefense = 55,
        .baseSpeed = 90,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 200,
        .expYield = 62,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_TWISTED_SPOON,
        .genderRatio = PERCENT_FEMALE(25),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_HUMANSHAPE,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_SYNCHRONIZE,
        .ability2 = ABILITY_INNERFOCUS,
        .hiddenAbility = ABILITY_MAGICGUARD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sAbraLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TELEPORT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 310
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_SYNCHRONIZE, ABILITY_INNERFOCUS, ABILITY_MAGICGUARD
// Level Up Moves: 1
