// AERODACTYL (#142) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_AERODACTYL] =
    {
        .baseHP = 80,
        .baseAttack = 105,
        .baseDefense = 65,
        .baseSpAttack = 60,
        .baseSpDefense = 75,
        .baseSpeed = 130,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 180,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 35,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_ROCKHEAD,
        .ability2 = ABILITY_PRESSURE,
        .hiddenAbility = ABILITY_UNNERVE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sAerodactylLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE( 5, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(10, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(15, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(20, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(25, MOVE_ROAR),
    LEVEL_UP_MOVE(30, MOVE_CRUNCH),
    LEVEL_UP_MOVE(35, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(40, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(45, MOVE_STONE_EDGE),
    LEVEL_UP_MOVE(50, MOVE_AGILITY),
    LEVEL_UP_MOVE(55, MOVE_HYPER_BEAM),
    LEVEL_UP_MOVE(60, MOVE_GIGA_IMPACT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 515
// Types: TYPE_ROCK / TYPE_FLYING
// Abilities: ABILITY_ROCKHEAD, ABILITY_PRESSURE, ABILITY_UNNERVE
// Level Up Moves: 14
