// AERODACTYL (#142) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_AERODACTYL] =
    {
        .baseHP = 80,
        .baseAttack = 105,
        .baseDefense = 65,
        .baseSpAttack = 60,
        .baseSpDefense = 75,
        .baseSpeed = 130,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 180,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 35,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_ROCKHEAD,
        .ability2 = ABILITY_PRESSURE,
        .abilityHidden = ABILITY_UNNERVE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove saerodactylLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE( 1, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_FANG),
    LEVEL_UP_MOVE( 1, MOVE_ICE_FANG),
    LEVEL_UP_MOVE( 1, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE( 1, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE( 9, MOVE_ROAR),
    LEVEL_UP_MOVE(17, MOVE_AGILITY),
    LEVEL_UP_MOVE(25, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(33, MOVE_CRUNCH),
    LEVEL_UP_MOVE(41, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(49, MOVE_SKY_DROP),
    LEVEL_UP_MOVE(65, MOVE_HYPER_BEAM),
    LEVEL_UP_MOVE(73, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(81, MOVE_GIGA_IMPACT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 515
// Types: TYPE_ROCK / TYPE_FLYING
// Abilities: ABILITY_ROCKHEAD, ABILITY_PRESSURE, ABILITY_UNNERVE
// Level Up Moves: 17
