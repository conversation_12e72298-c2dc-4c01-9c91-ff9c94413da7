// ALAKAZAM (#065) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_ALAKAZAM] =
    {
        .baseHP = 55,
        .baseAttack = 50,
        .baseDefense = 45,
        .baseSpAttack = 135,
        .baseSpDefense = 95,
        .baseSpeed = 120,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 50,
        .expYield = 250,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 3,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_TWISTED_SPOON,
        .genderRatio = PERCENT_FEMALE(25),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_HUMANSHAPE,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_SYNCHRONIZE,
        .ability2 = ABILITY_INNERFOCUS,
        .abilityHidden = ABILITY_MAGICGUARD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove salakazamLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_KINESIS),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_TELEPORT),
    LEVEL_UP_MOVE(18, MOVE_DISABLE),
    LEVEL_UP_MOVE(21, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(23, MOVE_MIRACLE_EYE),
    LEVEL_UP_MOVE(26, MOVE_REFLECT),
    LEVEL_UP_MOVE(28, MOVE_PSYCHO_CUT),
    LEVEL_UP_MOVE(31, MOVE_RECOVER),
    LEVEL_UP_MOVE(33, MOVE_TELEKINESIS),
    LEVEL_UP_MOVE(36, MOVE_ALLY_SWITCH),
    LEVEL_UP_MOVE(38, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(41, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(43, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(46, MOVE_TRICK),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_SYNCHRONIZE, ABILITY_INNERFOCUS, ABILITY_MAGICGUARD
// Level Up Moves: 15
