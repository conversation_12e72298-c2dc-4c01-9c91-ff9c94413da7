// ANORITH (#347) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_ANORITH] =
    {
        .baseHP = 45,
        .baseAttack = 95,
        .baseDefense = 50,
        .baseSpAttack = 40,
        .baseSpDefense = 50,
        .baseSpeed = 75,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_BUG,
        .catchRate = 45,
        .expYield = 71,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_ERRATIC,
        .eggGroup1 = EGG_GROUP_WATER_3,
        .eggGroup2 = EGG_GROUP_WATER_3,
        .ability1 = ABILITY_BATTLEARMOR,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_SWIFTSWIM,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sanorithLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 4, MOVE_MUD_SPORT),
    LEVEL_UP_MOVE( 7, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(10, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE(13, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE(17, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(21, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(25, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(29, MOVE_BRINE),
    LEVEL_UP_MOVE(34, MOVE_SLASH),
    LEVEL_UP_MOVE(39, MOVE_CRUSH_CLAW),
    LEVEL_UP_MOVE(44, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(49, MOVE_PROTECT),
    LEVEL_UP_MOVE(55, MOVE_ROCK_BLAST),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 355
// Types: TYPE_ROCK / TYPE_BUG
// Abilities: ABILITY_BATTLEARMOR, ABILITY_NONE, ABILITY_SWIFTSWIM
// Level Up Moves: 15
