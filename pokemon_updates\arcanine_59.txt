// ARCANINE (#059) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_ARCANINE] =
    {
        .baseHP = 90,
        .baseAttack = 110,
        .baseDefense = 80,
        .baseSpAttack = 100,
        .baseSpDefense = 80,
        .baseSpeed = 95,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 75,
        .expYield = 194,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_RAWST_BERRY,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(25),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_INTIMIDATE,
        .ability2 = ABILITY_FLASHFIRE,
        .hiddenAbility = ABILITY_JUSTIFIED,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sArcanineLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_EXTREME_SPEED),
    LEVEL_UP_MOVE( 1, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_ROAR),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_AGILITY),
    LEVEL_UP_MOVE( 1, MOVE_FLAME_WHEEL),
    LEVEL_UP_MOVE( 1, MOVE_REVERSAL),
    LEVEL_UP_MOVE( 1, MOVE_CRUNCH),
    LEVEL_UP_MOVE( 1, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 1, MOVE_HOWL),
    LEVEL_UP_MOVE( 1, MOVE_FLARE_BLITZ),
    LEVEL_UP_MOVE( 1, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE( 1, MOVE_RETALIATE),
    LEVEL_UP_MOVE( 1, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE( 5, MOVE_FLAMETHROWER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 555
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_INTIMIDATE, ABILITY_FLASHFIRE, ABILITY_JUSTIFIED
// Level Up Moves: 17
