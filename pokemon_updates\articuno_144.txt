// ARTICUNO (#144) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_ARTICUNO] =
    {
        .baseHP = 90,
        .baseAttack = 85,
        .baseDefense = 100,
        .baseSpAttack = 95,
        .baseSpDefense = 125,
        .baseSpeed = 85,
        .type1 = TYPE_ICE,
        .type2 = TYPE_FLYING,
        .catchRate = 3,
        .expYield = 290,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 3,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 80,
        .friendship = 35,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_PRESSURE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SNOWCLOAK,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sArticunoLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 1, MOVE_MIST),
    LEVEL_UP_MOVE( 5, MOVE_POWDER_SNOW),
    LEVEL_UP_MOVE(10, MOVE_REFLECT),
    LEVEL_UP_MOVE(15, MOVE_ICE_SHARD),
    LEVEL_UP_MOVE(20, MOVE_AGILITY),
    LEVEL_UP_MOVE(25, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(30, MOVE_TAILWIND),
    LEVEL_UP_MOVE(35, MOVE_FREEZE_DRY),
    LEVEL_UP_MOVE(40, MOVE_ROOST),
    LEVEL_UP_MOVE(45, MOVE_ICE_BEAM),
    LEVEL_UP_MOVE(50, MOVE_SNOWSCAPE),
    LEVEL_UP_MOVE(55, MOVE_HURRICANE),
    LEVEL_UP_MOVE(60, MOVE_HAZE),
    LEVEL_UP_MOVE(65, MOVE_BLIZZARD),
    LEVEL_UP_MOVE(70, MOVE_SHEER_COLD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 580
// Types: TYPE_ICE / TYPE_FLYING
// Abilities: ABILITY_PRESSURE, ABILITY_NONE, ABILITY_SNOWCLOAK
// Level Up Moves: 16
