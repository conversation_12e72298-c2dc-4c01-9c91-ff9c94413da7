// AZURILL (#298) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_AZURILL] =
    {
        .baseHP = 50,
        .baseAttack = 20,
        .baseDefense = 40,
        .baseSpAttack = 20,
        .baseSpDefense = 40,
        .baseSpeed = 20,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FAIRY,
        .catchRate = 150,
        .expYield = 38,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(75),
        .eggCycles = 10,
        .friendship = 50,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_THICKFAT,
        .ability2 = ABILITY_HUGEPOWER,
        .abilityHidden = ABILITY_SAPSIPPER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sazurillLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_SPLASH),
    LEVEL_UP_MOVE( 2, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 5, MOVE_WATER_SPORT),
    LEVEL_UP_MOVE( 7, MOVE_BUBBLE),
    LEVEL_UP_MOVE(10, MOVE_CHARM),
    LEVEL_UP_MOVE(13, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(16, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(20, MOVE_SLAM),
    LEVEL_UP_MOVE(23, MOVE_BOUNCE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 190
// Types: TYPE_NORMAL / TYPE_FAIRY
// Abilities: ABILITY_THICKFAT, ABILITY_HUGEPOWER, ABILITY_SAPSIPPER
// Level Up Moves: 10
