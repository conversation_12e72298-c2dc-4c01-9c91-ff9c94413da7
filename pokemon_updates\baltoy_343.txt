// BALTOY (#343) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_BALTOY] =
    {
        .baseHP = 40,
        .baseAttack = 40,
        .baseDefense = 55,
        .baseSpAttack = 40,
        .baseSpDefense = 70,
        .baseSpeed = 55,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 255,
        .expYield = 60,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_LIGHT_CLAY,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sbaltoyLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 4, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE( 7, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE(10, MOVE_HEAL_BLOCK),
    LEVEL_UP_MOVE(13, MOVE_ROCK_TOMB),
    LEVEL_UP_MOVE(16, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(19, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(22, MOVE_COSMIC_POWER),
    LEVEL_UP_MOVE(25, MOVE_POWER_TRICK),
    LEVEL_UP_MOVE(28, MOVE_SELF_DESTRUCT),
    LEVEL_UP_MOVE(31, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE(34, MOVE_GUARD_SPLIT),
    LEVEL_UP_MOVE(34, MOVE_POWER_SPLIT),
    LEVEL_UP_MOVE(37, MOVE_EARTH_POWER),
    LEVEL_UP_MOVE(40, MOVE_SANDSTORM),
    LEVEL_UP_MOVE(43, MOVE_IMPRISON),
    LEVEL_UP_MOVE(46, MOVE_EXPLOSION),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 300
// Types: TYPE_GROUND / TYPE_PSYCHIC
// Abilities: ABILITY_LEVITATE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 18
