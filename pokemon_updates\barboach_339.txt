// BARBOACH (#339) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_BARBOACH] =
    {
        .baseHP = 50,
        .baseAttack = 48,
        .baseDefense = 43,
        .baseSpAttack = 46,
        .baseSpDefense = 41,
        .baseSpeed = 60,
        .type1 = TYPE_WATER,
        .type2 = TYPE_GROUND,
        .catchRate = 190,
        .expYield = 58,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_2,
        .eggGroup2 = EGG_GROUP_WATER_2,
        .ability1 = ABILITY_OBLIVIOUS,
        .ability2 = ABILITY_ANTICIPATION,
        .abilityHidden = ABILITY_HYDRATION,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sbarboachLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 6, MOVE_MUD_SPORT),
    LEVEL_UP_MOVE( 6, MOVE_WATER_SPORT),
    LEVEL_UP_MOVE( 9, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(13, MOVE_MUD_BOMB),
    LEVEL_UP_MOVE(15, MOVE_AMNESIA),
    LEVEL_UP_MOVE(17, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(20, MOVE_MAGNITUDE),
    LEVEL_UP_MOVE(25, MOVE_REST),
    LEVEL_UP_MOVE(25, MOVE_SNORE),
    LEVEL_UP_MOVE(28, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(32, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(35, MOVE_MUDDY_WATER),
    LEVEL_UP_MOVE(39, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(44, MOVE_FISSURE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 288
// Types: TYPE_WATER / TYPE_GROUND
// Abilities: ABILITY_OBLIVIOUS, ABILITY_ANTICIPATION, ABILITY_HYDRATION
// Level Up Moves: 15
