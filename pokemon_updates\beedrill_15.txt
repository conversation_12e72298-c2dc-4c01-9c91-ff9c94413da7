// BEEDRILL (#015) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_BEEDRILL] =
    {
        .baseHP = 65,
        .baseAttack = 90,
        .baseDefense = 40,
        .baseSpAttack = 45,
        .baseSpDefense = 80,
        .baseSpeed = 75,
        .type1 = TYPE_BUG,
        .type2 = TYPE_POISON,
        .catchRate = 45,
        .expYield = 178,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_POISON_BARB,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_SWARM,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SNIPER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sBeedrillLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_TWINEEDLE),
    LEVEL_UP_MOVE( 1, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(14, MOVE_RAGE),
    LEVEL_UP_MOVE(17, MOVE_PURSUIT),
    LEVEL_UP_MOVE(20, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(23, MOVE_VENOSHOCK),
    LEVEL_UP_MOVE(26, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(29, MOVE_TOXIC_SPIKES),
    LEVEL_UP_MOVE(32, MOVE_PIN_MISSILE),
    LEVEL_UP_MOVE(35, MOVE_POISON_JAB),
    LEVEL_UP_MOVE(38, MOVE_AGILITY),
    LEVEL_UP_MOVE(41, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(44, MOVE_FELL_STINGER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 395
// Types: TYPE_BUG / TYPE_POISON
// Abilities: ABILITY_SWARM, ABILITY_NONE, ABILITY_SNIPER
// Level Up Moves: 13
