// BELDUM (#374) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_BELDUM] =
    {
        .baseHP = 40,
        .baseAttack = 55,
        .baseDefense = 80,
        .baseSpAttack = 35,
        .baseSpDefense = 60,
        .baseSpeed = 30,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 3,
        .expYield = 60,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_METAL_COAT,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 40,
        .friendship = 35,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_CLEARBODY,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_LIGHTMETAL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sbeldumLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAKE_DOWN),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 300
// Types: TYPE_STEEL / TYPE_PSYCHIC
// Abilities: ABILITY_CLEARBODY, ABILITY_NONE, ABILITY_LIGHTMETAL
// Level Up Moves: 2
