// BLASTOISE (#009) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_BLASTOISE] =
    {
        .baseHP = 79,
        .baseAttack = 83,
        .baseDefense = 100,
        .baseSpAttack = 85,
        .baseSpDefense = 105,
        .baseSpeed = 78,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 45,
        .expYield = 265,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 3,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_WATER_1,
        .ability1 = ABILITY_TORRENT,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_RAINDISH,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sblastoiseLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_FLASH_CANNON),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_WITHDRAW),
    LEVEL_UP_MOVE( 9, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE(12, MOVE_BITE),
    LEVEL_UP_MOVE(15, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(20, MOVE_PROTECT),
    LEVEL_UP_MOVE(25, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(30, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(35, MOVE_SHELL_SMASH),
    LEVEL_UP_MOVE(42, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(49, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(56, MOVE_WAVE_CRASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 530
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_TORRENT, ABILITY_NONE, ABILITY_RAINDISH
// Level Up Moves: 15
