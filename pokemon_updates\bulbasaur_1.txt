// BULBASAUR (#001) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_BULBASAUR] =
    {
        .baseHP = 45,
        .baseAttack = 49,
        .baseDefense = 49,
        .baseSpAttack = 65,
        .baseSpDefense = 65,
        .baseSpeed = 45,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_POISON,
        .catchRate = 45,
        .expYield = 64, // Placeholder - needs manual adjustment
        .evYield_HP = 0, // Placeholder - needs manual adjustment
        .evYield_Attack = 0, // Placeholder - needs manual adjustment
        .evYield_Defense = 0, // Placeholder - needs manual adjustment
        .evYield_SpAttack = 0, // Placeholder - needs manual adjustment
        .evYield_SpDefense = 0, // Placeholder - needs manual adjustment
        .evYield_Speed = 0, // Placeholder - needs manual adjustment
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_OVERGROW,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_CHLOROPHYLL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sbulbasaurLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 3, MOVE_GROWL),
    LEVEL_UP_MOVE( 7, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE( 9, MOVE_VINE_WHIP),
    LEVEL_UP_MOVE(13, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE(13, MOVE_SLEEP_POWDER),
    LEVEL_UP_MOVE(15, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(19, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(21, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(25, MOVE_GROWTH),
    LEVEL_UP_MOVE(27, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(31, MOVE_WORRY_SEED),
    LEVEL_UP_MOVE(33, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(37, MOVE_SEED_BOMB),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 318
// Types: TYPE_GRASS / TYPE_POISON
// Abilities: ABILITY_OVERGROW, ABILITY_NONE, ABILITY_CHLOROPHYLL
// Level Up Moves: 14
