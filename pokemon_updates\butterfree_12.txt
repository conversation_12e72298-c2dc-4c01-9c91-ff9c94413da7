// BUTTERFREE (#012) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_BUTTERFREE] =
    {
        .baseHP = 60,
        .baseAttack = 45,
        .baseDefense = 50,
        .baseSpAttack = 90,
        .baseSpDefense = 80,
        .baseSpeed = 70,
        .type1 = TYPE_BUG,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 198,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SILVER_POWDER,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_COMPOUNDEYES,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_TINTEDLENS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sbutterfreeLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_GUST),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_STRING_SHOT),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_BUG_BITE),
    LEVEL_UP_MOVE( 4, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE( 8, MOVE_CONFUSION),
    LEVEL_UP_MOVE(12, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE(12, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE(12, MOVE_SLEEP_POWDER),
    LEVEL_UP_MOVE(16, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(20, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(24, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(28, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(32, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(36, MOVE_TAILWIND),
    LEVEL_UP_MOVE(40, MOVE_RAGE_POWDER),
    LEVEL_UP_MOVE(44, MOVE_QUIVER_DANCE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 395
// Types: TYPE_BUG / TYPE_FLYING
// Abilities: ABILITY_COMPOUNDEYES, ABILITY_NONE, ABILITY_TINTEDLENS
// Level Up Moves: 18
