// CARVAN<PERSON> (#318) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_CARVANHA] =
    {
        .baseHP = 45,
        .baseAttack = 90,
        .baseDefense = 20,
        .baseSpAttack = 65,
        .baseSpDefense = 20,
        .baseSpeed = 65,
        .type1 = TYPE_WATER,
        .type2 = TYPE_DARK,
        .catchRate = 225,
        .expYield = 61,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_DEEP_SEA_TOOTH,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 35,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_2,
        .eggGroup2 = EGG_GROUP_WATER_2,
        .ability1 = ABILITY_ROUGHSKIN,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SPEEDBOOST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sCarvanhaLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_AQUA_JET),
    LEVEL_UP_MOVE( 4, MOVE_POISON_FANG),
    LEVEL_UP_MOVE( 8, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(12, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(16, MOVE_BITE),
    LEVEL_UP_MOVE(20, MOVE_ICE_FANG),
    LEVEL_UP_MOVE(24, MOVE_SCREECH),
    LEVEL_UP_MOVE(28, MOVE_SWAGGER),
    LEVEL_UP_MOVE(32, MOVE_CRUNCH),
    LEVEL_UP_MOVE(36, MOVE_AGILITY),
    LEVEL_UP_MOVE(40, MOVE_LIQUIDATION),
    LEVEL_UP_MOVE(44, MOVE_TAKE_DOWN),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 305
// Types: TYPE_WATER / TYPE_DARK
// Abilities: ABILITY_ROUGHSKIN, ABILITY_NONE, ABILITY_SPEEDBOOST
// Level Up Moves: 13
