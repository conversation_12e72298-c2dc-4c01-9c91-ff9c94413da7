// CASTFORM (#351) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_CASTFORM] =
    {
        .baseHP = 70,
        .baseAttack = 70,
        .baseDefense = 70,
        .baseSpAttack = 70,
        .baseSpDefense = 70,
        .baseSpeed = 70,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 45,
        .expYield = 147,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_MYSTIC_WATER,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 25,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_FAIRY,
        .eggGroup2 = EGG_GROUP_INDETERMINATE,
        .ability1 = ABILITY_FORECAST,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sCastformLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE(10, MOVE_EMBER),
    LEVEL_UP_MOVE(10, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(10, MOVE_POWDER_SNOW),
    LEVEL_UP_MOVE(15, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(20, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(20, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE(20, MOVE_HAIL),
    LEVEL_UP_MOVE(25, MOVE_WEATHER_BALL),
    LEVEL_UP_MOVE(35, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(35, MOVE_BLIZZARD),
    LEVEL_UP_MOVE(35, MOVE_FIRE_BLAST),
    LEVEL_UP_MOVE(45, MOVE_HURRICANE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 420
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_FORECAST, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 13
