// CHARIZARD (#006) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_CHARIZARD] =
    {
        .baseHP = 78,
        .baseAttack = 84,
        .baseDefense = 78,
        .baseSpAttack = 109,
        .baseSpDefense = 85,
        .baseSpeed = 100,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 267,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 3,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_BLAZE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_SOLARPOWER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove scharizardLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_SMOKESCREEN),
    LEVEL_UP_MOVE( 1, MOVE_HEAT_WAVE),
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(12, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(19, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE(24, MOVE_SLASH),
    LEVEL_UP_MOVE(30, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(39, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(46, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE(54, MOVE_INFERNO),
    LEVEL_UP_MOVE(62, MOVE_FLARE_BLITZ),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 534
// Types: TYPE_FIRE / TYPE_FLYING
// Abilities: ABILITY_BLAZE, ABILITY_NONE, ABILITY_SOLARPOWER
// Level Up Moves: 15
