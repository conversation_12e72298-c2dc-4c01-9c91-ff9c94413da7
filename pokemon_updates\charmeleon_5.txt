// CHARMELEON (#005) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_CHARMELEON] =
    {
        .baseHP = 58,
        .baseAttack = 64,
        .baseDefense = 58,
        .baseSpAttack = 80,
        .baseSpDefense = 65,
        .baseSpeed = 80,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 45,
        .expYield = 142,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_BLAZE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_SOLARPOWER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove scharmeleonLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE(10, MOVE_SMOKESCREEN),
    LEVEL_UP_MOVE(12, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(17, MOVE_DRAGON_RAGE),
    LEVEL_UP_MOVE(21, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(28, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE(32, MOVE_FLAME_BURST),
    LEVEL_UP_MOVE(39, MOVE_SLASH),
    LEVEL_UP_MOVE(43, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(50, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE(54, MOVE_FLARE_BLITZ),
    LEVEL_UP_MOVE(54, MOVE_INFERNO),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 405
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_BLAZE, ABILITY_NONE, ABILITY_SOLARPOWER
// Level Up Moves: 14
