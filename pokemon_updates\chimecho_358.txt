// CHIMECHO (#358) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_CHIMECHO] =
    {
        .baseHP = 75,
        .baseAttack = 50,
        .baseDefense = 80,
        .baseSpAttack = 95,
        .baseSpDefense = 90,
        .baseSpeed = 65,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 45,
        .expYield = 159,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_CLEANSE_TAG,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 25,
        .friendship = 70,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_INDETERMINATE,
        .eggGroup2 = EGG_GROUP_INDETERMINATE,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sChimechoLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WRAP),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_HEALING_WISH),
    LEVEL_UP_MOVE(13, MOVE_YAWN),
    LEVEL_UP_MOVE(16, MOVE_STORED_POWER),
    LEVEL_UP_MOVE(19, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(22, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE(27, MOVE_HEAL_BELL),
    LEVEL_UP_MOVE(32, MOVE_UPROAR),
    LEVEL_UP_MOVE(37, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(42, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(47, MOVE_HEAL_PULSE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 455
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_LEVITATE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
