// CLAMPERL (#366) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_CLAMPERL] =
    {
        .baseHP = 35,
        .baseAttack = 64,
        .baseDefense = 85,
        .baseSpAttack = 74,
        .baseSpDefense = 55,
        .baseSpeed = 32,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 255,
        .expYield = 69,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_PEARL,
        .item2 = ITEM_BLUE_SHARD,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_ERRATIC,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_WATER_1,
        .ability1 = ABILITY_SHELLARMOR,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_RATTLED,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sClamperlLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_CLAMP),
    LEVEL_UP_MOVE( 1, MOVE_WHIRLPOOL),
    LEVEL_UP_MOVE( 1, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(50, MOVE_SHELL_SMASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 345
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_SHELLARMOR, ABILITY_NONE, ABILITY_RATTLED
// Level Up Moves: 5
