// CLEFAIRY (#035) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_CLEFAIRY] =
    {
        .baseHP = 70,
        .baseAttack = 45,
        .baseDefense = 48,
        .baseSpAttack = 60,
        .baseSpDefense = 65,
        .baseSpeed = 35,
        .type1 = TYPE_FAIRY,
        .type2 = TYPE_FAIRY,
        .catchRate = 150,
        .expYield = 113,
        .evYield_HP = 2,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_LEPPA_BERRY,
        .item2 = ITEM_MOON_STONE,
        .genderRatio = PERCENT_FEMALE(75),
        .eggCycles = 10,
        .friendship = 140,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_FAIRY,
        .eggGroup2 = EGG_GROUP_FAIRY,
        .ability1 = ABILITY_CUTECHARM,
        .ability2 = ABILITY_MAGICGUARD,
        .abilityHidden = ABILITY_FRIENDGUARD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sclefairyLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_SPLASH),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_KISS),
    LEVEL_UP_MOVE( 1, MOVE_CHARM),
    LEVEL_UP_MOVE( 1, MOVE_ENCORE),
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE( 1, MOVE_DISARMING_VOICE),
    LEVEL_UP_MOVE( 1, MOVE_SPOTLIGHT),
    LEVEL_UP_MOVE( 7, MOVE_SING),
    LEVEL_UP_MOVE(10, MOVE_DOUBLE_SLAP),
    LEVEL_UP_MOVE(13, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE(16, MOVE_FOLLOW_ME),
    LEVEL_UP_MOVE(16, MOVE_LIFE_DEW),
    LEVEL_UP_MOVE(19, MOVE_BESTOW),
    LEVEL_UP_MOVE(22, MOVE_WAKE_UP_SLAP),
    LEVEL_UP_MOVE(25, MOVE_MINIMIZE),
    LEVEL_UP_MOVE(28, MOVE_STORED_POWER),
    LEVEL_UP_MOVE(31, MOVE_METRONOME),
    LEVEL_UP_MOVE(34, MOVE_COSMIC_POWER),
    LEVEL_UP_MOVE(37, MOVE_LUCKY_CHANT),
    LEVEL_UP_MOVE(40, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(43, MOVE_MOONLIGHT),
    LEVEL_UP_MOVE(46, MOVE_MOONBLAST),
    LEVEL_UP_MOVE(49, MOVE_GRAVITY),
    LEVEL_UP_MOVE(50, MOVE_METEOR_MASH),
    LEVEL_UP_MOVE(55, MOVE_HEALING_WISH),
    LEVEL_UP_MOVE(58, MOVE_AFTER_YOU),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 323
// Types: TYPE_FAIRY / TYPE_FAIRY
// Abilities: ABILITY_CUTECHARM, ABILITY_MAGICGUARD, ABILITY_FRIENDGUARD
// Level Up Moves: 28
