// CLOYSTER (#091) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_CLOYSTER] =
    {
        .baseHP = 50,
        .baseAttack = 95,
        .baseDefense = 180,
        .baseSpAttack = 85,
        .baseSpDefense = 45,
        .baseSpeed = 70,
        .type1 = TYPE_WATER,
        .type2 = TYPE_ICE,
        .catchRate = 60,
        .expYield = 184,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_PEARL,
        .item2 = ITEM_BIG_PEARL,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_3,
        .eggGroup2 = EGG_GROUP_WATER_3,
        .ability1 = ABILITY_SHELLARMOR,
        .ability2 = ABILITY_SKILLLINK,
        .abilityHidden = ABILITY_OVERCOAT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove scloysterLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_ICICLE_SPEAR),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE( 1, MOVE_AURORA_BEAM),
    LEVEL_UP_MOVE( 1, MOVE_WITHDRAW),
    LEVEL_UP_MOVE( 1, MOVE_PROTECT),
    LEVEL_UP_MOVE( 1, MOVE_WHIRLPOOL),
    LEVEL_UP_MOVE( 1, MOVE_TOXIC_SPIKES),
    LEVEL_UP_MOVE( 1, MOVE_ICE_SHARD),
    LEVEL_UP_MOVE( 1, MOVE_SHELL_SMASH),
    LEVEL_UP_MOVE( 1, MOVE_RAZOR_SHELL),
    LEVEL_UP_MOVE(13, MOVE_SPIKE_CANNON),
    LEVEL_UP_MOVE(28, MOVE_SPIKES),
    LEVEL_UP_MOVE(50, MOVE_ICICLE_CRASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 525
// Types: TYPE_WATER / TYPE_ICE
// Abilities: ABILITY_SHELLARMOR, ABILITY_SKILLLINK, ABILITY_OVERCOAT
// Level Up Moves: 17
