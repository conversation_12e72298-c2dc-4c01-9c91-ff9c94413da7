// CRADILY (#346) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_CRADILY] =
    {
        .baseHP = 86,
        .baseAttack = 81,
        .baseDefense = 97,
        .baseSpAttack = 81,
        .baseSpDefense = 107,
        .baseSpeed = 43,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_GRASS,
        .catchRate = 45,
        .expYield = 173,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 2,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_BIG_ROOT,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_ERRATIC,
        .eggGroup1 = EGG_GROUP_WATER_3,
        .eggGroup2 = EGG_GROUP_WATER_3,
        .ability1 = ABILITY_SUCTIONCUPS,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_STORMDRAIN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sCradilyLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WRAP),
    LEVEL_UP_MOVE( 1, MOVE_ACID),
    LEVEL_UP_MOVE( 1, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE(12, MOVE_INGRAIN),
    LEVEL_UP_MOVE(16, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(20, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(24, MOVE_BRINE),
    LEVEL_UP_MOVE(28, MOVE_AMNESIA),
    LEVEL_UP_MOVE(32, MOVE_GASTRO_ACID),
    LEVEL_UP_MOVE(36, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(43, MOVE_STOCKPILE),
    LEVEL_UP_MOVE(43, MOVE_SPIT_UP),
    LEVEL_UP_MOVE(43, MOVE_SWALLOW),
    LEVEL_UP_MOVE(48, MOVE_ENERGY_BALL),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 495
// Types: TYPE_ROCK / TYPE_GRASS
// Abilities: ABILITY_SUCTIONCUPS, ABILITY_NONE, ABILITY_STORMDRAIN
// Level Up Moves: 16
