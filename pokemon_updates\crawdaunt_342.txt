// CRAWDAUNT (#342) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_CRAWDAUNT] =
    {
        .baseHP = 63,
        .baseAttack = 120,
        .baseDefense = 85,
        .baseSpAttack = 90,
        .baseSpDefense = 55,
        .baseSpeed = 55,
        .type1 = TYPE_WATER,
        .type2 = TYPE_DARK,
        .catchRate = 155,
        .expYield = 164,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_FLUCTUATING,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_WATER_3,
        .ability1 = ABILITY_HYPERCUTTER,
        .ability2 = ABILITY_SHELLARMOR,
        .abilityHidden = ABILITY_ADAPTABILITY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove scrawdauntLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_SWIFT),
    LEVEL_UP_MOVE( 1, MOVE_VICE_GRIP),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_BUBBLE),
    LEVEL_UP_MOVE(14, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(17, MOVE_PROTECT),
    LEVEL_UP_MOVE(20, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE(23, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(26, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(32, MOVE_RAZOR_SHELL),
    LEVEL_UP_MOVE(36, MOVE_TAUNT),
    LEVEL_UP_MOVE(40, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(43, MOVE_CRUNCH),
    LEVEL_UP_MOVE(48, MOVE_CRABHAMMER),
    LEVEL_UP_MOVE(54, MOVE_GUILLOTINE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 468
// Types: TYPE_WATER / TYPE_DARK
// Abilities: ABILITY_HYPERCUTTER, ABILITY_SHELLARMOR, ABILITY_ADAPTABILITY
// Level Up Moves: 17
