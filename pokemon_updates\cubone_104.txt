// CUBONE (#104) - <PERSON><PERSON>RA<PERSON><PERSON> IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_CUBONE] =
    {
        .baseHP = 50,
        .baseAttack = 50,
        .baseDefense = 95,
        .baseSpAttack = 40,
        .baseSpDefense = 50,
        .baseSpeed = 35,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_GROUND,
        .catchRate = 190,
        .expYield = 64,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_THICK_CLUB,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_MONSTER,
        .ability1 = ABILITY_ROCKHEAD,
        .ability2 = ABILITY_LIGHTNINGROD,
        .hiddenAbility = ABILITY_BATTLEARMOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sCuboneLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 4, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 8, MOVE_FALSE_SWIPE),
    LEVEL_UP_MOVE(12, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(16, MOVE_RETALIATE),
    LEVEL_UP_MOVE(20, MOVE_FLING),
    LEVEL_UP_MOVE(24, MOVE_STOMPING_TANTRUM),
    LEVEL_UP_MOVE(29, MOVE_BONE_RUSH),
    LEVEL_UP_MOVE(32, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(36, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(40, MOVE_BONEMERANG),
    LEVEL_UP_MOVE(44, MOVE_THRASH),
    LEVEL_UP_MOVE(48, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 320
// Types: TYPE_GROUND / TYPE_GROUND
// Abilities: ABILITY_ROCKHEAD, ABILITY_LIGHTNINGROD, ABILITY_BATTLEARMOR
// Level Up Moves: 14
