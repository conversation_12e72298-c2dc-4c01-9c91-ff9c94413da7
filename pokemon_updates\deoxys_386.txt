// DEOXYS (#386) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_DEOXYS] =
    {
        .baseHP = 50,
        .baseAttack = 150,
        .baseDefense = 50,
        .baseSpAttack = 150,
        .baseSpDefense = 50,
        .baseSpeed = 150,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 3,
        .expYield = 270,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_PRESSURE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sDeoxysLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WRAP),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 7, MOVE_NIGHT_SHADE),
    LEVEL_UP_MOVE(13, MOVE_TELEPORT),
    LEVEL_UP_MOVE(19, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(25, MOVE_PSYSHOCK),
    LEVEL_UP_MOVE(31, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(37, MOVE_GRAVITY),
    LEVEL_UP_MOVE(43, MOVE_SKILL_SWAP),
    LEVEL_UP_MOVE(49, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(55, MOVE_COSMIC_POWER),
    LEVEL_UP_MOVE(61, MOVE_RECOVER),
    LEVEL_UP_MOVE(67, MOVE_PSYCHO_BOOST),
    LEVEL_UP_MOVE(73, MOVE_HYPER_BEAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_PRESSURE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
