// DEWGONG (#087) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_DEWGONG] =
    {
        .baseHP = 90,
        .baseAttack = 70,
        .baseDefense = 80,
        .baseSpAttack = 70,
        .baseSpDefense = 95,
        .baseSpeed = 70,
        .type1 = TYPE_WATER,
        .type2 = TYPE_ICE,
        .catchRate = 75,
        .expYield = 166,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 2,
        .evYield_Speed = 0,
        .item1 = ITEM_ASPEAR_BERRY,
        .item2 = ITEM_NEVER_MELT_ICE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_THICKFAT,
        .ability2 = ABILITY_HYDRATION,
        .hiddenAbility = ABILITY_ICEBODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sDewgongLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_SHEER_COLD),
    LEVEL_UP_MOVE( 1, MOVE_HEADBUTT),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_ICY_WIND),
    LEVEL_UP_MOVE(13, MOVE_ENCORE),
    LEVEL_UP_MOVE(17, MOVE_ICE_SHARD),
    LEVEL_UP_MOVE(21, MOVE_REST),
    LEVEL_UP_MOVE(23, MOVE_AQUA_RING),
    LEVEL_UP_MOVE(27, MOVE_AURORA_BEAM),
    LEVEL_UP_MOVE(31, MOVE_AQUA_JET),
    LEVEL_UP_MOVE(33, MOVE_BRINE),
    LEVEL_UP_MOVE(39, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(45, MOVE_DIVE),
    LEVEL_UP_MOVE(49, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(55, MOVE_ICE_BEAM),
    LEVEL_UP_MOVE(61, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(65, MOVE_SNOWSCAPE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 475
// Types: TYPE_WATER / TYPE_ICE
// Abilities: ABILITY_THICKFAT, ABILITY_HYDRATION, ABILITY_ICEBODY
// Level Up Moves: 17
