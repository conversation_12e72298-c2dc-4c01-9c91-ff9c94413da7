// DITTO (#132) - <PERSON><PERSON><PERSON><PERSON><PERSON> IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_DITTO] =
    {
        .baseHP = 48,
        .baseAttack = 48,
        .baseDefense = 48,
        .baseSpAttack = 48,
        .baseSpDefense = 48,
        .baseSpeed = 48,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 35,
        .expYield = 101,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_QUICK_POWDER,
        .item2 = ITEM_METAL_POWDER,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_DITTO,
        .eggGroup2 = EGG_GROUP_DITTO,
        .ability1 = ABILITY_LIMBER,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_IMPOSTER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sdittoLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TRANSFORM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 288
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_LIMBER, ABILITY_NONE, ABILITY_IMPOSTER
// Level Up Moves: 1
