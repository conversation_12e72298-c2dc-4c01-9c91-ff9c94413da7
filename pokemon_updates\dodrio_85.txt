// DODRIO (#085) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_DODRIO] =
    {
        .baseHP = 60,
        .baseAttack = 110,
        .baseDefense = 70,
        .baseSpAttack = 60,
        .baseSpDefense = 60,
        .baseSpeed = 110,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 165,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SHARP_BEAK,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_RUNAWAY,
        .ability2 = ABILITY_EARLYBIRD,
        .abilityHidden = ABILITY_TANGLEDFEET,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sdodrioLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_TRI_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_RAGE),
    LEVEL_UP_MOVE(12, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(15, MOVE_PURSUIT),
    LEVEL_UP_MOVE(19, MOVE_PLUCK),
    LEVEL_UP_MOVE(22, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE(26, MOVE_AGILITY),
    LEVEL_UP_MOVE(29, MOVE_UPROAR),
    LEVEL_UP_MOVE(34, MOVE_ACUPRESSURE),
    LEVEL_UP_MOVE(38, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(43, MOVE_JUMP_KICK),
    LEVEL_UP_MOVE(47, MOVE_DRILL_PECK),
    LEVEL_UP_MOVE(52, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(56, MOVE_THRASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 470
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_RUNAWAY, ABILITY_EARLYBIRD, ABILITY_TANGLEDFEET
// Level Up Moves: 17
