// DODUO (#084) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_DODUO] =
    {
        .baseHP = 35,
        .baseAttack = 85,
        .baseDefense = 45,
        .baseSpAttack = 35,
        .baseSpDefense = 35,
        .baseSpeed = 75,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 190,
        .expYield = 62,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SHARP_BEAK,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_RUNAWAY,
        .ability2 = ABILITY_EARLYBIRD,
        .hiddenAbility = ABILITY_TANGLEDFEET,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sDoduoLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 5, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 9, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(14, MOVE_PLUCK),
    LEVEL_UP_MOVE(19, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE(23, MOVE_AGILITY),
    LEVEL_UP_MOVE(27, MOVE_UPROAR),
    LEVEL_UP_MOVE(30, MOVE_ACUPRESSURE),
    LEVEL_UP_MOVE(33, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(36, MOVE_DRILL_PECK),
    LEVEL_UP_MOVE(39, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(43, MOVE_THRASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 310
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_RUNAWAY, ABILITY_EARLYBIRD, ABILITY_TANGLEDFEET
// Level Up Moves: 13
