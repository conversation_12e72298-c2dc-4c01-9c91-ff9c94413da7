// DRAGONAIR (#148) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_DRAGONAIR] =
    {
        .baseHP = 61,
        .baseAttack = 84,
        .baseDefense = 65,
        .baseSpAttack = 70,
        .baseSpDefense = 70,
        .baseSpeed = 70,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 147,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_DRAGON_FANG,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 40,
        .friendship = 35,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_SHEDSKIN,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_MARVELSCALE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sdragonairLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WRAP),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE( 1, MOVE_TWISTER),
    LEVEL_UP_MOVE(15, MOVE_DRAGON_RAGE),
    LEVEL_UP_MOVE(21, MOVE_SLAM),
    LEVEL_UP_MOVE(25, MOVE_AGILITY),
    LEVEL_UP_MOVE(33, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE(39, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(47, MOVE_DRAGON_RUSH),
    LEVEL_UP_MOVE(53, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(61, MOVE_DRAGON_DANCE),
    LEVEL_UP_MOVE(67, MOVE_OUTRAGE),
    LEVEL_UP_MOVE(75, MOVE_HYPER_BEAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 420
// Types: TYPE_DRAGON / TYPE_DRAGON
// Abilities: ABILITY_SHEDSKIN, ABILITY_NONE, ABILITY_MARVELSCALE
// Level Up Moves: 14
