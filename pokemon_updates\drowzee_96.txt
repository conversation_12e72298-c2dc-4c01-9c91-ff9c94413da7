// DROWZEE (#096) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_DROWZEE] =
    {
        .baseHP = 60,
        .baseAttack = 48,
        .baseDefense = 45,
        .baseSpAttack = 43,
        .baseSpDefense = 90,
        .baseSpeed = 42,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 190,
        .expYield = 66,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_HUMANSHAPE,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_INSOMNIA,
        .ability2 = ABILITY_FOREWARN,
        .hiddenAbility = ABILITY_INNERFOCUS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sDrowzeeLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE( 5, MOVE_DISABLE),
    LEVEL_UP_MOVE( 9, MOVE_CONFUSION),
    LEVEL_UP_MOVE(13, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(17, MOVE_POISON_GAS),
    LEVEL_UP_MOVE(21, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(25, MOVE_PSYCH_UP),
    LEVEL_UP_MOVE(29, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(33, MOVE_SWAGGER),
    LEVEL_UP_MOVE(37, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(41, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(45, MOVE_PSYSHOCK),
    LEVEL_UP_MOVE(49, MOVE_FUTURE_SIGHT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 328
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_INSOMNIA, ABILITY_FOREWARN, ABILITY_INNERFOCUS
// Level Up Moves: 14
