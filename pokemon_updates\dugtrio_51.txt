// DUG<PERSON><PERSON> (#051) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_DUGTRIO] =
    {
        .baseHP = 35,
        .baseAttack = 100,
        .baseDefense = 50,
        .baseSpAttack = 50,
        .baseSpDefense = 70,
        .baseSpeed = 120,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_GROUND,
        .catchRate = 50,
        .expYield = 149,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SOFT_SAND,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_SANDVEIL,
        .ability2 = ABILITY_ARENATRAP,
        .hiddenAbility = ABILITY_SANDFORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sDugtrioLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_SAND_TOMB),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_TRI_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(12, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE(16, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(20, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(24, MOVE_SLASH),
    LEVEL_UP_MOVE(30, MOVE_SANDSTORM),
    LEVEL_UP_MOVE(36, MOVE_DIG),
    LEVEL_UP_MOVE(42, MOVE_EARTH_POWER),
    LEVEL_UP_MOVE(48, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(54, MOVE_FISSURE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 425
// Types: TYPE_GROUND / TYPE_GROUND
// Abilities: ABILITY_SANDVEIL, ABILITY_ARENATRAP, ABILITY_SANDFORCE
// Level Up Moves: 16
