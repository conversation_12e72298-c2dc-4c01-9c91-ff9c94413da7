// DUSTOX (#269) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_DUSTOX] =
    {
        .baseHP = 60,
        .baseAttack = 50,
        .baseDefense = 70,
        .baseSpAttack = 50,
        .baseSpDefense = 90,
        .baseSpeed = 65,
        .type1 = TYPE_BUG,
        .type2 = TYPE_POISON,
        .catchRate = 45,
        .expYield = 173,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 3,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SHED_SHELL,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_SHIELDDUST,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_COMPOUNDEYES,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sDustoxLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_GUST),
    LEVEL_UP_MOVE(12, MOVE_CONFUSION),
    LEVEL_UP_MOVE(15, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE(17, MOVE_MOONLIGHT),
    LEVEL_UP_MOVE(20, MOVE_VENOSHOCK),
    LEVEL_UP_MOVE(22, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(25, MOVE_SILVER_WIND),
    LEVEL_UP_MOVE(27, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(30, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(32, MOVE_TOXIC),
    LEVEL_UP_MOVE(35, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(37, MOVE_PROTECT),
    LEVEL_UP_MOVE(40, MOVE_QUIVER_DANCE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 385
// Types: TYPE_BUG / TYPE_POISON
// Abilities: ABILITY_SHIELDDUST, ABILITY_NONE, ABILITY_COMPOUNDEYES
// Level Up Moves: 13
