// EEVEE (#133) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_EEVEE] =
    {
        .baseHP = 55,
        .baseAttack = 55,
        .baseDefense = 50,
        .baseSpAttack = 45,
        .baseSpDefense = 65,
        .baseSpeed = 55,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 45,
        .expYield = 65,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 35,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_RUNAWAY,
        .ability2 = ABILITY_ADAPTABILITY,
        .abilityHidden = ABILITY_ANTICIPATION,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove seeveeLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 1, MOVE_COVET),
    LEVEL_UP_MOVE( 5, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 9, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(13, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(17, MOVE_BITE),
    LEVEL_UP_MOVE(17, MOVE_SWIFT),
    LEVEL_UP_MOVE(20, MOVE_REFRESH),
    LEVEL_UP_MOVE(25, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(29, MOVE_CHARM),
    LEVEL_UP_MOVE(30, MOVE_COPYCAT),
    LEVEL_UP_MOVE(33, MOVE_BATON_PASS),
    LEVEL_UP_MOVE(37, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(41, MOVE_LAST_RESORT),
    LEVEL_UP_MOVE(45, MOVE_TRUMP_CARD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 325
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_RUNAWAY, ABILITY_ADAPTABILITY, ABILITY_ANTICIPATION
// Level Up Moves: 18
