// EKANS (#023) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_EKANS] =
    {
        .baseHP = 35,
        .baseAttack = 60,
        .baseDefense = 44,
        .baseSpAttack = 40,
        .baseSpDefense = 54,
        .baseSpeed = 55,
        .type1 = TYPE_POISON,
        .type2 = TYPE_POISON,
        .catchRate = 255,
        .expYield = 58,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_INTIMIDATE,
        .ability2 = ABILITY_SHEDSKIN,
        .abilityHidden = ABILITY_UNNERVE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sekansLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WRAP),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 4, MOVE_POISON_STING),
    LEVEL_UP_MOVE( 9, MOVE_BITE),
    LEVEL_UP_MOVE(12, MOVE_GLARE),
    LEVEL_UP_MOVE(17, MOVE_SCREECH),
    LEVEL_UP_MOVE(20, MOVE_ACID),
    LEVEL_UP_MOVE(25, MOVE_STOCKPILE),
    LEVEL_UP_MOVE(25, MOVE_SPIT_UP),
    LEVEL_UP_MOVE(25, MOVE_SWALLOW),
    LEVEL_UP_MOVE(28, MOVE_ACID_SPRAY),
    LEVEL_UP_MOVE(33, MOVE_MUD_BOMB),
    LEVEL_UP_MOVE(36, MOVE_GASTRO_ACID),
    LEVEL_UP_MOVE(38, MOVE_BELCH),
    LEVEL_UP_MOVE(41, MOVE_HAZE),
    LEVEL_UP_MOVE(44, MOVE_COIL),
    LEVEL_UP_MOVE(49, MOVE_GUNK_SHOT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 288
// Types: TYPE_POISON / TYPE_POISON
// Abilities: ABILITY_INTIMIDATE, ABILITY_SHEDSKIN, ABILITY_UNNERVE
// Level Up Moves: 17
