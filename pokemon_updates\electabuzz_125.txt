// ELECTABUZZ (#125) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_ELECTABUZZ] =
    {
        .baseHP = 65,
        .baseAttack = 83,
        .baseDefense = 57,
        .baseSpAttack = 95,
        .baseSpDefense = 85,
        .baseSpeed = 105,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 45,
        .expYield = 172,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_ELECTIRIZER,
        .genderRatio = PERCENT_FEMALE(25),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_HUMANSHAPE,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_STATIC,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_INSOMNIA,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sElectabuzzLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_CHARGE),
    LEVEL_UP_MOVE(12, MOVE_SWIFT),
    LEVEL_UP_MOVE(16, MOVE_SHOCK_WAVE),
    LEVEL_UP_MOVE(20, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE(24, MOVE_SCREECH),
    LEVEL_UP_MOVE(28, MOVE_THUNDER_PUNCH),
    LEVEL_UP_MOVE(34, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(40, MOVE_LOW_KICK),
    LEVEL_UP_MOVE(46, MOVE_THUNDERBOLT),
    LEVEL_UP_MOVE(52, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(58, MOVE_THUNDER),
    LEVEL_UP_MOVE(64, MOVE_GIGA_IMPACT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 490
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_STATIC, ABILITY_NONE, ABILITY_INSOMNIA
// Level Up Moves: 15
