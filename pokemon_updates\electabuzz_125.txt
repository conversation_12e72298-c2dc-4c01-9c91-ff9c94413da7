// ELECTABUZZ (#125) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_ELECTABUZZ] =
    {
        .baseHP = 65,
        .baseAttack = 83,
        .baseDefense = 57,
        .baseSpAttack = 95,
        .baseSpDefense = 85,
        .baseSpeed = 105,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 45,
        .expYield = 172,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_ELECTIRIZER,
        .genderRatio = PERCENT_FEMALE(25),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_HUMANSHAPE,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_STATIC,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_VITALSPIRIT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove selectabuzzLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_CHARGE),
    LEVEL_UP_MOVE( 8, MOVE_LOW_KICK),
    LEVEL_UP_MOVE(12, MOVE_SWIFT),
    LEVEL_UP_MOVE(15, MOVE_SHOCK_WAVE),
    LEVEL_UP_MOVE(19, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE(22, MOVE_ELECTRO_BALL),
    LEVEL_UP_MOVE(26, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(29, MOVE_THUNDER_PUNCH),
    LEVEL_UP_MOVE(36, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(42, MOVE_SCREECH),
    LEVEL_UP_MOVE(49, MOVE_THUNDERBOLT),
    LEVEL_UP_MOVE(55, MOVE_THUNDER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 490
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_STATIC, ABILITY_NONE, ABILITY_VITALSPIRIT
// Level Up Moves: 15
