// ELECTRIKE (#309) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_ELECTRIKE] =
    {
        .baseHP = 40,
        .baseAttack = 45,
        .baseDefense = 40,
        .baseSpAttack = 65,
        .baseSpDefense = 40,
        .baseSpeed = 65,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 120,
        .expYield = 59,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_STATIC,
        .ability2 = ABILITY_LIGHTNINGROD,
        .hiddenAbility = ABILITY_MINUS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sElectrikeLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE( 4, MOVE_LEER),
    LEVEL_UP_MOVE( 8, MOVE_HOWL),
    LEVEL_UP_MOVE(12, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(16, MOVE_SHOCK_WAVE),
    LEVEL_UP_MOVE(20, MOVE_BITE),
    LEVEL_UP_MOVE(24, MOVE_THUNDER_FANG),
    LEVEL_UP_MOVE(28, MOVE_ROAR),
    LEVEL_UP_MOVE(32, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(36, MOVE_CHARGE),
    LEVEL_UP_MOVE(40, MOVE_WILD_CHARGE),
    LEVEL_UP_MOVE(44, MOVE_THUNDER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 295
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_STATIC, ABILITY_LIGHTNINGROD, ABILITY_MINUS
// Level Up Moves: 13
