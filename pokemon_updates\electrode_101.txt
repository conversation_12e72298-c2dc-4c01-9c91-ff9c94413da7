// ELECTRODE (#101) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_ELECTRODE] =
    {
        .baseHP = 60,
        .baseAttack = 50,
        .baseDefense = 70,
        .baseSpAttack = 80,
        .baseSpDefense = 80,
        .baseSpeed = 150,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 60,
        .expYield = 172,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_SOUNDPROOF,
        .ability2 = ABILITY_STATIC,
        .abilityHidden = ABILITY_AFTERMATH,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove selectrodeLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_SONIC_BOOM),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 1, MOVE_CHARGE),
    LEVEL_UP_MOVE( 1, MOVE_EERIE_IMPULSE),
    LEVEL_UP_MOVE( 1, MOVE_MAGNETIC_FLUX),
    LEVEL_UP_MOVE( 9, MOVE_SPARK),
    LEVEL_UP_MOVE(11, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(13, MOVE_SCREECH),
    LEVEL_UP_MOVE(16, MOVE_CHARGE_BEAM),
    LEVEL_UP_MOVE(20, MOVE_SWIFT),
    LEVEL_UP_MOVE(22, MOVE_ELECTRO_BALL),
    LEVEL_UP_MOVE(26, MOVE_SELF_DESTRUCT),
    LEVEL_UP_MOVE(29, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(36, MOVE_MAGNET_RISE),
    LEVEL_UP_MOVE(41, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(47, MOVE_EXPLOSION),
    LEVEL_UP_MOVE(54, MOVE_GYRO_BALL),
    LEVEL_UP_MOVE(58, MOVE_MIRROR_COAT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 490
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_SOUNDPROOF, ABILITY_STATIC, ABILITY_AFTERMATH
// Level Up Moves: 19
