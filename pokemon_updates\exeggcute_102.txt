// EXEGGCUTE (#102) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_EXEGGCUTE] =
    {
        .baseHP = 60,
        .baseAttack = 40,
        .baseDefense = 80,
        .baseSpAttack = 60,
        .baseSpDefense = 45,
        .baseSpeed = 40,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 90,
        .expYield = 65,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_PSYCHIC_SEED,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_CHLOROPHYLL,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_HARVEST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sexeggcuteLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE( 1, MOVE_BARRAGE),
    LEVEL_UP_MOVE( 1, MOVE_UPROAR),
    LEVEL_UP_MOVE( 7, MOVE_REFLECT),
    LEVEL_UP_MOVE(11, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(15, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(17, MOVE_BULLET_SEED),
    LEVEL_UP_MOVE(19, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE(21, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE(23, MOVE_SLEEP_POWDER),
    LEVEL_UP_MOVE(27, MOVE_CONFUSION),
    LEVEL_UP_MOVE(33, MOVE_WORRY_SEED),
    LEVEL_UP_MOVE(37, MOVE_NATURAL_GIFT),
    LEVEL_UP_MOVE(43, MOVE_SOLAR_BEAM),
    LEVEL_UP_MOVE(47, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE(50, MOVE_BESTOW),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 325
// Types: TYPE_GRASS / TYPE_PSYCHIC
// Abilities: ABILITY_CHLOROPHYLL, ABILITY_NONE, ABILITY_HARVEST
// Level Up Moves: 17
