// EXEGGUTOR (#103) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_EXEGGUTOR] =
    {
        .baseHP = 95,
        .baseAttack = 95,
        .baseDefense = 85,
        .baseSpAttack = 125,
        .baseSpDefense = 75,
        .baseSpeed = 55,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 45,
        .expYield = 186,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_CHLOROPHYLL,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_HARVEST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sExeggutorLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_STOMP),
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE( 1, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE( 1, MOVE_SOLAR_BEAM),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE( 1, MOVE_REFLECT),
    LEVEL_UP_MOVE( 1, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE( 1, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE( 1, MOVE_UPROAR),
    LEVEL_UP_MOVE( 1, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE( 1, MOVE_BULLET_SEED),
    LEVEL_UP_MOVE( 1, MOVE_WORRY_SEED),
    LEVEL_UP_MOVE( 1, MOVE_SEED_BOMB),
    LEVEL_UP_MOVE( 1, MOVE_LEAF_STORM),
    LEVEL_UP_MOVE( 1, MOVE_WOOD_HAMMER),
    LEVEL_UP_MOVE( 1, MOVE_PSYSHOCK),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 530
// Types: TYPE_GRASS / TYPE_PSYCHIC
// Abilities: ABILITY_CHLOROPHYLL, ABILITY_NONE, ABILITY_HARVEST
// Level Up Moves: 18
