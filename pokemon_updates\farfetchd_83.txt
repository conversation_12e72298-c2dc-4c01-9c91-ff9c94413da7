// FARFETCHD (#083) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_FARFETCHD] =
    {
        .baseHP = 52,
        .baseAttack = 90,
        .baseDefense = 55,
        .baseSpAttack = 58,
        .baseSpDefense = 62,
        .baseSpeed = 60,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 132,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_STICK,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_KEENEYE,
        .ability2 = ABILITY_INNERFOCUS,
        .hiddenAbility = ABILITY_DEFIANT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sFarfetchdLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 5, MOVE_LEER),
    LEVEL_UP_MOVE(10, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE(15, MOVE_CUT),
    LEVEL_UP_MOVE(20, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(25, MOVE_AIR_CUTTER),
    LEVEL_UP_MOVE(30, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(35, MOVE_FALSE_SWIPE),
    LEVEL_UP_MOVE(40, MOVE_SLASH),
    LEVEL_UP_MOVE(45, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(50, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(55, MOVE_LEAF_BLADE),
    LEVEL_UP_MOVE(60, MOVE_AGILITY),
    LEVEL_UP_MOVE(65, MOVE_BRAVE_BIRD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 377
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_KEENEYE, ABILITY_INNERFOCUS, ABILITY_DEFIANT
// Level Up Moves: 15
