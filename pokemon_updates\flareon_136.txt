// FLAREON (#136) - <PERSON><PERSON><PERSON><PERSON><PERSON> IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_FLAREON] =
    {
        .baseHP = 65,
        .baseAttack = 130,
        .baseDefense = 60,
        .baseSpAttack = 95,
        .baseSpDefense = 110,
        .baseSpeed = 65,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 45,
        .expYield = 184,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 35,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_FLASHFIRE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_GUTS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sflareonLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE( 1, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_SWIFT),
    LEVEL_UP_MOVE( 1, MOVE_CHARM),
    LEVEL_UP_MOVE( 1, MOVE_BATON_PASS),
    LEVEL_UP_MOVE( 1, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE( 5, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 9, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(13, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(17, MOVE_BITE),
    LEVEL_UP_MOVE(20, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE(25, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE(29, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(33, MOVE_SMOG),
    LEVEL_UP_MOVE(37, MOVE_LAVA_PLUME),
    LEVEL_UP_MOVE(41, MOVE_LAST_RESORT),
    LEVEL_UP_MOVE(45, MOVE_FLARE_BLITZ),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 525
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_FLASHFIRE, ABILITY_NONE, ABILITY_GUTS
// Level Up Moves: 22
