// GASTLY (#092) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_GASTLY] =
    {
        .baseHP = 30,
        .baseAttack = 35,
        .baseDefense = 30,
        .baseSpAttack = 100,
        .baseSpDefense = 35,
        .baseSpeed = 80,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_POISON,
        .catchRate = 190,
        .expYield = 62,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_INDETERMINATE,
        .eggGroup2 = EGG_GROUP_INDETERMINATE,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sGastlyLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE( 1, MOVE_LICK),
    LEVEL_UP_MOVE( 4, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE( 8, MOVE_MEAN_LOOK),
    LEVEL_UP_MOVE(12, MOVE_PAYBACK),
    LEVEL_UP_MOVE(16, MOVE_SPITE),
    LEVEL_UP_MOVE(20, MOVE_CURSE),
    LEVEL_UP_MOVE(24, MOVE_HEX),
    LEVEL_UP_MOVE(28, MOVE_NIGHT_SHADE),
    LEVEL_UP_MOVE(32, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(36, MOVE_DARK_PULSE),
    LEVEL_UP_MOVE(40, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(44, MOVE_DESTINY_BOND),
    LEVEL_UP_MOVE(48, MOVE_DREAM_EATER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 310
// Types: TYPE_GHOST / TYPE_POISON
// Abilities: ABILITY_LEVITATE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
