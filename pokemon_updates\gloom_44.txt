// GLOOM (#044) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_GLOOM] =
    {
        .baseHP = 60,
        .baseAttack = 65,
        .baseDefense = 70,
        .baseSpAttack = 85,
        .baseSpDefense = 75,
        .baseSpeed = 40,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_POISON,
        .catchRate = 120,
        .expYield = 138,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_ABSORB_BULB,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_CHLOROPHYLL,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_STENCH,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sGloomLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ACID),
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(12, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(14, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE(16, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE(18, MOVE_SLEEP_POWDER),
    LEVEL_UP_MOVE(20, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(26, MOVE_TOXIC),
    LEVEL_UP_MOVE(32, MOVE_MOONBLAST),
    LEVEL_UP_MOVE(38, MOVE_GRASSY_TERRAIN),
    LEVEL_UP_MOVE(44, MOVE_MOONLIGHT),
    LEVEL_UP_MOVE(50, MOVE_PETAL_DANCE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 395
// Types: TYPE_GRASS / TYPE_POISON
// Abilities: ABILITY_CHLOROPHYLL, ABILITY_NONE, ABILITY_STENCH
// Level Up Moves: 14
