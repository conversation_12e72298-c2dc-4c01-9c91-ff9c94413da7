// GOLBAT (#042) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_GOLBAT] =
    {
        .baseHP = 75,
        .baseAttack = 80,
        .baseDefense = 70,
        .baseSpAttack = 65,
        .baseSpDefense = 75,
        .baseSpeed = 90,
        .type1 = TYPE_POISON,
        .type2 = TYPE_FLYING,
        .catchRate = 90,
        .expYield = 159,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_INNERFOCUS,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_INFILTRATOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sgolbatLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_SCREECH),
    LEVEL_UP_MOVE( 1, MOVE_MEAN_LOOK),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE(15, MOVE_POISON_FANG),
    LEVEL_UP_MOVE(20, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(27, MOVE_AIR_CUTTER),
    LEVEL_UP_MOVE(34, MOVE_BITE),
    LEVEL_UP_MOVE(41, MOVE_HAZE),
    LEVEL_UP_MOVE(48, MOVE_VENOSHOCK),
    LEVEL_UP_MOVE(55, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(62, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(69, MOVE_LEECH_LIFE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 455
// Types: TYPE_POISON / TYPE_FLYING
// Abilities: ABILITY_INNERFOCUS, ABILITY_NONE, ABILITY_INFILTRATOR
// Level Up Moves: 14
