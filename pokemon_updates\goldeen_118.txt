// GOLDEEN (#118) - GE<PERSON>RA<PERSON>ON IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_GOLDEEN] =
    {
        .baseHP = 45,
        .baseAttack = 67,
        .baseDefense = 60,
        .baseSpAttack = 35,
        .baseSpDefense = 50,
        .baseSpeed = 63,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 225,
        .expYield = 64,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_MYSTIC_WATER,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_2,
        .eggGroup2 = EGG_GROUP_WATER_2,
        .ability1 = ABILITY_SWIFTSWIM,
        .ability2 = ABILITY_WATERVEIL,
        .hiddenAbility = ABILITY_LIGHTNINGROD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sGoldeenLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 5, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(10, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(15, MOVE_HORN_ATTACK),
    LEVEL_UP_MOVE(20, MOVE_AGILITY),
    LEVEL_UP_MOVE(25, MOVE_AQUA_RING),
    LEVEL_UP_MOVE(30, MOVE_FLAIL),
    LEVEL_UP_MOVE(35, MOVE_WATERFALL),
    LEVEL_UP_MOVE(40, MOVE_SOAK),
    LEVEL_UP_MOVE(45, MOVE_MEGAHORN),
    LEVEL_UP_MOVE(50, MOVE_HORN_DRILL),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 320
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_SWIFTSWIM, ABILITY_WATERVEIL, ABILITY_LIGHTNINGROD
// Level Up Moves: 12
