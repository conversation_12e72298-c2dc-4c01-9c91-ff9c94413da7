// GOLDUCK (#055) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_GOLDUCK] =
    {
        .baseHP = 80,
        .baseAttack = 82,
        .baseDefense = 78,
        .baseSpAttack = 95,
        .baseSpDefense = 80,
        .baseSpeed = 85,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 75,
        .expYield = 175,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_DAMP,
        .ability2 = ABILITY_CLOUDNINE,
        .hiddenAbility = ABILITY_SWIFTSWIM,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sGolduckLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_AQUA_JET),
    LEVEL_UP_MOVE( 9, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(12, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(15, MOVE_DISABLE),
    LEVEL_UP_MOVE(18, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(21, MOVE_SCREECH),
    LEVEL_UP_MOVE(24, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(27, MOVE_SOAK),
    LEVEL_UP_MOVE(30, MOVE_PSYCH_UP),
    LEVEL_UP_MOVE(36, MOVE_AMNESIA),
    LEVEL_UP_MOVE(40, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(45, MOVE_WONDER_ROOM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_DAMP, ABILITY_CLOUDNINE, ABILITY_SWIFTSWIM
// Level Up Moves: 16
