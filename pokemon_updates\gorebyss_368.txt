// GOREBYSS (#368) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_GOREBYSS] =
    {
        .baseHP = 55,
        .baseAttack = 84,
        .baseDefense = 105,
        .baseSpAttack = 114,
        .baseSpDefense = 75,
        .baseSpeed = 52,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 60,
        .expYield = 170,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_DEEP_SEA_SCALE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_ERRATIC,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_WATER_1,
        .ability1 = ABILITY_SWIFTSWIM,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_HYDRATION,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sGorebyssLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_WHIRLPOOL),
    LEVEL_UP_MOVE( 5, MOVE_WATER_SPORT),
    LEVEL_UP_MOVE( 9, MOVE_AGILITY),
    LEVEL_UP_MOVE(11, MOVE_DRAINING_KISS),
    LEVEL_UP_MOVE(14, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(16, MOVE_AMNESIA),
    LEVEL_UP_MOVE(19, MOVE_AQUA_RING),
    LEVEL_UP_MOVE(23, MOVE_CAPTIVATE),
    LEVEL_UP_MOVE(26, MOVE_DIVE),
    LEVEL_UP_MOVE(29, MOVE_BATON_PASS),
    LEVEL_UP_MOVE(34, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(39, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(45, MOVE_COIL),
    LEVEL_UP_MOVE(50, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 485
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_SWIFTSWIM, ABILITY_NONE, ABILITY_HYDRATION
// Level Up Moves: 15
