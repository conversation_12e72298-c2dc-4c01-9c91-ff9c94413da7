// GRAVELER (#075) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_GRAVELER] =
    {
        .baseHP = 55,
        .baseAttack = 95,
        .baseDefense = 115,
        .baseSpAttack = 45,
        .baseSpDefense = 45,
        .baseSpeed = 35,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_GROUND,
        .catchRate = 120,
        .expYield = 137,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_HARD_STONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_ROCKHEAD,
        .ability2 = ABILITY_STURDY,
        .abilityHidden = ABILITY_SANDVEIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sgravelerLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SPORT),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_POLISH),
    LEVEL_UP_MOVE(10, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(12, MOVE_MAGNITUDE),
    LEVEL_UP_MOVE(16, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE(18, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE(22, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(24, MOVE_SELF_DESTRUCT),
    LEVEL_UP_MOVE(30, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(34, MOVE_ROCK_BLAST),
    LEVEL_UP_MOVE(40, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(44, MOVE_EXPLOSION),
    LEVEL_UP_MOVE(50, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(54, MOVE_STONE_EDGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 390
// Types: TYPE_ROCK / TYPE_GROUND
// Abilities: ABILITY_ROCKHEAD, ABILITY_STURDY, ABILITY_SANDVEIL
// Level Up Moves: 16
