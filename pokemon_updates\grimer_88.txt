// GRIMER (#088) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_GRIMER] =
    {
        .baseHP = 80,
        .baseAttack = 80,
        .baseDefense = 50,
        .baseSpAttack = 40,
        .baseSpDefense = 50,
        .baseSpeed = 25,
        .type1 = TYPE_POISON,
        .type2 = TYPE_POISON,
        .catchRate = 190,
        .expYield = 65,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_BLACK_SLUDGE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_INDETERMINATE,
        .eggGroup2 = EGG_GROUP_INDETERMINATE,
        .ability1 = ABILITY_STENCH,
        .ability2 = ABILITY_STICKYHOLD,
        .abilityHidden = ABILITY_POISONTOUCH,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sgrimerLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_POISON_GAS),
    LEVEL_UP_MOVE( 4, MOVE_HARDEN),
    LEVEL_UP_MOVE( 7, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE(12, MOVE_DISABLE),
    LEVEL_UP_MOVE(15, MOVE_SLUDGE),
    LEVEL_UP_MOVE(18, MOVE_MUD_SHOT),
    LEVEL_UP_MOVE(18, MOVE_MUD_BOMB),
    LEVEL_UP_MOVE(21, MOVE_MINIMIZE),
    LEVEL_UP_MOVE(26, MOVE_FLING),
    LEVEL_UP_MOVE(29, MOVE_SLUDGE_BOMB),
    LEVEL_UP_MOVE(32, MOVE_SLUDGE_WAVE),
    LEVEL_UP_MOVE(37, MOVE_SCREECH),
    LEVEL_UP_MOVE(40, MOVE_GUNK_SHOT),
    LEVEL_UP_MOVE(43, MOVE_ACID_ARMOR),
    LEVEL_UP_MOVE(46, MOVE_BELCH),
    LEVEL_UP_MOVE(48, MOVE_MEMENTO),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 325
// Types: TYPE_POISON / TYPE_POISON
// Abilities: ABILITY_STENCH, ABILITY_STICKYHOLD, ABILITY_POISONTOUCH
// Level Up Moves: 17
