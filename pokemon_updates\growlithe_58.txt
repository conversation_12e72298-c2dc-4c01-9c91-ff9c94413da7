// GROWLITHE (#058) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_GROWLITHE] =
    {
        .baseHP = 55,
        .baseAttack = 70,
        .baseDefense = 45,
        .baseSpAttack = 70,
        .baseSpDefense = 50,
        .baseSpeed = 60,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 190,
        .expYield = 70,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_RAWST_BERRY,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(25),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_INTIMIDATE,
        .ability2 = ABILITY_FLASHFIRE,
        .abilityHidden = ABILITY_JUSTIFIED,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sgrowlitheLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 4, MOVE_HOWL),
    LEVEL_UP_MOVE( 8, MOVE_BITE),
    LEVEL_UP_MOVE(12, MOVE_FLAME_WHEEL),
    LEVEL_UP_MOVE(16, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(20, MOVE_AGILITY),
    LEVEL_UP_MOVE(24, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE(28, MOVE_RETALIATE),
    LEVEL_UP_MOVE(32, MOVE_CRUNCH),
    LEVEL_UP_MOVE(36, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(40, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(44, MOVE_ROAR),
    LEVEL_UP_MOVE(48, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(52, MOVE_REVERSAL),
    LEVEL_UP_MOVE(56, MOVE_FLARE_BLITZ),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 350
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_INTIMIDATE, ABILITY_FLASHFIRE, ABILITY_JUSTIFIED
// Level Up Moves: 16
