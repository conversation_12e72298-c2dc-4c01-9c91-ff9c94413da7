// GRUMPIG (#326) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_GRUMPIG] =
    {
        .baseHP = 80,
        .baseAttack = 45,
        .baseDefense = 65,
        .baseSpAttack = 90,
        .baseSpDefense = 110,
        .baseSpeed = 80,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 60,
        .expYield = 165,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 2,
        .evYield_Speed = 0,
        .item1 = ITEM_PERSIM_BERRY,
        .item2 = ITEM_TANGA_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_THICKFAT,
        .ability2 = ABILITY_OWNTEMPO,
        .abilityHidden = ABILITY_GLUTTONY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sgrumpigLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_TEETER_DANCE),
    LEVEL_UP_MOVE( 1, MOVE_PSYBEAM),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_PSYWAVE),
    LEVEL_UP_MOVE( 1, MOVE_SPLASH),
    LEVEL_UP_MOVE( 1, MOVE_ODOR_SLEUTH),
    LEVEL_UP_MOVE( 1, MOVE_BELCH),
    LEVEL_UP_MOVE(15, MOVE_PSYCH_UP),
    LEVEL_UP_MOVE(18, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(21, MOVE_MAGIC_COAT),
    LEVEL_UP_MOVE(26, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(29, MOVE_POWER_GEM),
    LEVEL_UP_MOVE(35, MOVE_REST),
    LEVEL_UP_MOVE(35, MOVE_SNORE),
    LEVEL_UP_MOVE(42, MOVE_PSYSHOCK),
    LEVEL_UP_MOVE(46, MOVE_PAYBACK),
    LEVEL_UP_MOVE(52, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(60, MOVE_BOUNCE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 470
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_THICKFAT, ABILITY_OWNTEMPO, ABILITY_GLUTTONY
// Level Up Moves: 18
