// GULPIN (#316) - <PERSON><PERSON><PERSON><PERSON><PERSON> IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_GULPIN] =
    {
        .baseHP = 70,
        .baseAttack = 43,
        .baseDefense = 53,
        .baseSpAttack = 43,
        .baseSpDefense = 53,
        .baseSpeed = 40,
        .type1 = TYPE_POISON,
        .type2 = TYPE_POISON,
        .catchRate = 225,
        .expYield = 60,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_ORAN_BERRY,
        .item2 = ITEM_BIG_PEARL,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_FLUCTUATING,
        .eggGroup1 = EGG_GROUP_INDETERMINATE,
        .eggGroup2 = EGG_GROUP_INDETERMINATE,
        .ability1 = ABILITY_LIQUIDOOZE,
        .ability2 = ABILITY_STICKYHOLD,
        .hiddenAbility = ABILITY_GLUTTONY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sGulpinLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 5, MOVE_YAWN),
    LEVEL_UP_MOVE( 8, MOVE_POISON_GAS),
    LEVEL_UP_MOVE(10, MOVE_SLUDGE),
    LEVEL_UP_MOVE(12, MOVE_AMNESIA),
    LEVEL_UP_MOVE(17, MOVE_ACID_SPRAY),
    LEVEL_UP_MOVE(20, MOVE_ENCORE),
    LEVEL_UP_MOVE(25, MOVE_TOXIC),
    LEVEL_UP_MOVE(28, MOVE_STOCKPILE),
    LEVEL_UP_MOVE(28, MOVE_SPIT_UP),
    LEVEL_UP_MOVE(28, MOVE_SWALLOW),
    LEVEL_UP_MOVE(33, MOVE_SLUDGE_BOMB),
    LEVEL_UP_MOVE(36, MOVE_GASTRO_ACID),
    LEVEL_UP_MOVE(41, MOVE_BELCH),
    LEVEL_UP_MOVE(44, MOVE_PAIN_SPLIT),
    LEVEL_UP_MOVE(49, MOVE_GUNK_SHOT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 302
// Types: TYPE_POISON / TYPE_POISON
// Abilities: ABILITY_LIQUIDOOZE, ABILITY_STICKYHOLD, ABILITY_GLUTTONY
// Level Up Moves: 16
