// GYARADOS (#130) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_GYARADOS] =
    {
        .baseHP = 95,
        .baseAttack = 125,
        .baseDefense = 79,
        .baseSpAttack = 60,
        .baseSpDefense = 100,
        .baseSpeed = 81,
        .type1 = TYPE_WATER,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 189,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 5,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_2,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_INTIMIDATE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_MOXIE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sgyaradosLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_THRASH),
    LEVEL_UP_MOVE( 1, MOVE_SPLASH),
    LEVEL_UP_MOVE( 1, MOVE_FLAIL),
    LEVEL_UP_MOVE( 4, MOVE_WHIRLPOOL),
    LEVEL_UP_MOVE(12, MOVE_BRINE),
    LEVEL_UP_MOVE(21, MOVE_LEER),
    LEVEL_UP_MOVE(24, MOVE_TWISTER),
    LEVEL_UP_MOVE(27, MOVE_ICE_FANG),
    LEVEL_UP_MOVE(30, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(33, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(36, MOVE_DRAGON_RAGE),
    LEVEL_UP_MOVE(39, MOVE_CRUNCH),
    LEVEL_UP_MOVE(42, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(45, MOVE_DRAGON_DANCE),
    LEVEL_UP_MOVE(48, MOVE_HURRICANE),
    LEVEL_UP_MOVE(51, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(54, MOVE_HYPER_BEAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 540
// Types: TYPE_WATER / TYPE_FLYING
// Abilities: ABILITY_INTIMIDATE, ABILITY_NONE, ABILITY_MOXIE
// Level Up Moves: 19
