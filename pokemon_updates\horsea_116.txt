// HORSEA (#116) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_HORSEA] =
    {
        .baseHP = 30,
        .baseAttack = 40,
        .baseDefense = 70,
        .baseSpAttack = 70,
        .baseSpDefense = 25,
        .baseSpeed = 60,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 225,
        .expYield = 59,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_DRAGON_SCALE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_SWIFTSWIM,
        .ability2 = ABILITY_SNIPER,
        .abilityHidden = ABILITY_DAMP,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove shorseaLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BUBBLE),
    LEVEL_UP_MOVE( 5, MOVE_SMOKESCREEN),
    LEVEL_UP_MOVE( 9, MOVE_LEER),
    LEVEL_UP_MOVE(13, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(17, MOVE_TWISTER),
    LEVEL_UP_MOVE(21, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(26, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(31, MOVE_BRINE),
    LEVEL_UP_MOVE(35, MOVE_LASER_FOCUS),
    LEVEL_UP_MOVE(36, MOVE_AGILITY),
    LEVEL_UP_MOVE(41, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(46, MOVE_DRAGON_DANCE),
    LEVEL_UP_MOVE(52, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 295
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_SWIFTSWIM, ABILITY_SNIPER, ABILITY_DAMP
// Level Up Moves: 13
