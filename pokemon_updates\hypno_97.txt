// HYPNO (#097) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_HYPNO] =
    {
        .baseHP = 85,
        .baseAttack = 73,
        .baseDefense = 70,
        .baseSpAttack = 73,
        .baseSpDefense = 115,
        .baseSpeed = 67,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 75,
        .expYield = 169,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 2,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_HUMANSHAPE,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_INSOMNIA,
        .ability2 = ABILITY_FOREWARN,
        .hiddenAbility = ABILITY_INNERFOCUS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sHypnoLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_DISABLE),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE( 1, MOVE_SWITCHEROO),
    LEVEL_UP_MOVE(13, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(17, MOVE_POISON_GAS),
    LEVEL_UP_MOVE(21, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(25, MOVE_PSYCH_UP),
    LEVEL_UP_MOVE(32, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(37, MOVE_SWAGGER),
    LEVEL_UP_MOVE(42, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(47, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(51, MOVE_PSYSHOCK),
    LEVEL_UP_MOVE(56, MOVE_FUTURE_SIGHT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 483
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_INSOMNIA, ABILITY_FOREWARN, ABILITY_INNERFOCUS
// Level Up Moves: 15
