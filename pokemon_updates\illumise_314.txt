// ILLUMISE (#314) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_ILLUMISE] =
    {
        .baseHP = 65,
        .baseAttack = 47,
        .baseDefense = 75,
        .baseSpAttack = 73,
        .baseSpDefense = 85,
        .baseSpeed = 85,
        .type1 = TYPE_BUG,
        .type2 = TYPE_BUG,
        .catchRate = 150,
        .expYield = 151,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_BRIGHT_POWDER,
        .genderRatio = PERCENT_FEMALE(100),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_FLUCTUATING,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_OBLIVIOUS,
        .ability2 = ABILITY_TINTEDLENS,
        .hiddenAbility = ABILITY_PRANKSTER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sIllumiseLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_PLAY_NICE),
    LEVEL_UP_MOVE( 5, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE( 9, MOVE_CHARM),
    LEVEL_UP_MOVE(12, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(15, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE(19, MOVE_MOONLIGHT),
    LEVEL_UP_MOVE(22, MOVE_WISH),
    LEVEL_UP_MOVE(26, MOVE_ENCORE),
    LEVEL_UP_MOVE(29, MOVE_FLATTER),
    LEVEL_UP_MOVE(33, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(36, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(40, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(43, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(47, MOVE_INFESTATION),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 430
// Types: TYPE_BUG / TYPE_BUG
// Abilities: ABILITY_OBLIVIOUS, ABILITY_TINTEDLENS, ABILITY_PRANKSTER
// Level Up Moves: 15
