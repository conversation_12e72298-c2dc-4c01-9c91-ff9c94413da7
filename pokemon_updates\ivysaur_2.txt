// IVYSAUR (#002) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_IVYSAUR] =
    {
        .baseHP = 60,
        .baseAttack = 62,
        .baseDefense = 63,
        .baseSpAttack = 80,
        .baseSpDefense = 80,
        .baseSpeed = 60,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_POISON,
        .catchRate = 45,
        .expYield = 142,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_OVERGROW,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_CHLOROPHYLL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sivysaurLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE( 9, MOVE_VINE_WHIP),
    LEVEL_UP_MOVE(13, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE(13, MOVE_SLEEP_POWDER),
    LEVEL_UP_MOVE(15, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(20, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(23, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(28, MOVE_GROWTH),
    LEVEL_UP_MOVE(31, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(36, MOVE_WORRY_SEED),
    LEVEL_UP_MOVE(39, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(44, MOVE_SOLAR_BEAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 405
// Types: TYPE_GRASS / TYPE_POISON
// Abilities: ABILITY_OVERGROW, ABILITY_NONE, ABILITY_CHLOROPHYLL
// Level Up Moves: 14
