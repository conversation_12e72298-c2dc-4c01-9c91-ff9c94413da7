// JIGGLYPUFF (#039) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_JIGGLYPUFF] =
    {
        .baseHP = 115,
        .baseAttack = 45,
        .baseDefense = 20,
        .baseSpAttack = 45,
        .baseSpDefense = 25,
        .baseSpeed = 20,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FAIRY,
        .catchRate = 170,
        .expYield = 95,
        .evYield_HP = 2,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_ORAN_BERRY,
        .item2 = ITEM_MOON_STONE,
        .genderRatio = PERCENT_FEMALE(75),
        .eggCycles = 10,
        .friendship = 50,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_FAIRY,
        .eggGroup2 = EGG_GROUP_FAIRY,
        .ability1 = ABILITY_CUTECHARM,
        .ability2 = ABILITY_COMPETITIVE,
        .hiddenAbility = ABILITY_FRIENDGUARD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sJigglypuffLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_SING),
    LEVEL_UP_MOVE( 1, MOVE_DISABLE),
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_KISS),
    LEVEL_UP_MOVE( 1, MOVE_CHARM),
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE( 1, MOVE_DISARMING_VOICE),
    LEVEL_UP_MOVE( 4, MOVE_ECHOED_VOICE),
    LEVEL_UP_MOVE( 8, MOVE_COVET),
    LEVEL_UP_MOVE(12, MOVE_STOCKPILE),
    LEVEL_UP_MOVE(12, MOVE_SPIT_UP),
    LEVEL_UP_MOVE(12, MOVE_SWALLOW),
    LEVEL_UP_MOVE(16, MOVE_ROUND),
    LEVEL_UP_MOVE(20, MOVE_REST),
    LEVEL_UP_MOVE(24, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(28, MOVE_MIMIC),
    LEVEL_UP_MOVE(32, MOVE_GYRO_BALL),
    LEVEL_UP_MOVE(36, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(44, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 270
// Types: TYPE_NORMAL / TYPE_FAIRY
// Abilities: ABILITY_CUTECHARM, ABILITY_COMPETITIVE, ABILITY_FRIENDGUARD
// Level Up Moves: 20
