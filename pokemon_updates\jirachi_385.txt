// JIRACHI (#385) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_JIRACHI] =
    {
        .baseHP = 100,
        .baseAttack = 100,
        .baseDefense = 100,
        .baseSpAttack = 100,
        .baseSpDefense = 100,
        .baseSpeed = 100,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 3,
        .expYield = 300,
        .evYield_HP = 3,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_STAR_PIECE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 100,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_SERENEGRACE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sjirachiLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_WISH),
    LEVEL_UP_MOVE( 5, MOVE_REST),
    LEVEL_UP_MOVE(10, MOVE_SWIFT),
    LEVEL_UP_MOVE(15, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(20, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(21, MOVE_LIFE_DEW),
    LEVEL_UP_MOVE(25, MOVE_REFRESH),
    LEVEL_UP_MOVE(30, MOVE_LUCKY_CHANT),
    LEVEL_UP_MOVE(35, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(40, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(45, MOVE_GRAVITY),
    LEVEL_UP_MOVE(49, MOVE_METEOR_MASH),
    LEVEL_UP_MOVE(50, MOVE_HEALING_WISH),
    LEVEL_UP_MOVE(55, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(60, MOVE_COSMIC_POWER),
    LEVEL_UP_MOVE(65, MOVE_LAST_RESORT),
    LEVEL_UP_MOVE(70, MOVE_DOOM_DESIRE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_STEEL / TYPE_PSYCHIC
// Abilities: ABILITY_SERENEGRACE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 18
