// JOLTEON (#135) - <PERSON><PERSON><PERSON><PERSON><PERSON> IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_JOLTEON] =
    {
        .baseHP = 65,
        .baseAttack = 65,
        .baseDefense = 60,
        .baseSpAttack = 110,
        .baseSpDefense = 95,
        .baseSpeed = 130,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 45,
        .expYield = 184,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 35,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_VOLTABSORB,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_QUICKFEET,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sjolteonLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE( 1, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_SWIFT),
    LEVEL_UP_MOVE( 1, MOVE_CHARM),
    LEVEL_UP_MOVE( 1, MOVE_BATON_PASS),
    LEVEL_UP_MOVE( 1, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE( 5, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 9, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(13, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(17, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(20, MOVE_THUNDER_FANG),
    LEVEL_UP_MOVE(25, MOVE_PIN_MISSILE),
    LEVEL_UP_MOVE(29, MOVE_AGILITY),
    LEVEL_UP_MOVE(33, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE(37, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(41, MOVE_LAST_RESORT),
    LEVEL_UP_MOVE(45, MOVE_THUNDER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 525
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_VOLTABSORB, ABILITY_NONE, ABILITY_QUICKFEET
// Level Up Moves: 23
