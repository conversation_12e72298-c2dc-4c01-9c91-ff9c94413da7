// JYNX (#124) - GE<PERSON>RATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_JYNX] =
    {
        .baseHP = 65,
        .baseAttack = 50,
        .baseDefense = 35,
        .baseSpAttack = 115,
        .baseSpDefense = 95,
        .baseSpeed = 95,
        .type1 = TYPE_ICE,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 45,
        .expYield = 159,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_ASPEAR_BERRY,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(100),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_HUMANSHAPE,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_OBLIVIOUS,
        .ability2 = ABILITY_FOREWARN,
        .hiddenAbility = ABILITY_DRYSKIN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sJynxLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_LICK),
    LEVEL_UP_MOVE( 1, MOVE_POWDER_SNOW),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_KISS),
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE(12, MOVE_CONFUSION),
    LEVEL_UP_MOVE(16, MOVE_COVET),
    LEVEL_UP_MOVE(20, MOVE_SING),
    LEVEL_UP_MOVE(24, MOVE_FAKE_TEARS),
    LEVEL_UP_MOVE(28, MOVE_ICE_PUNCH),
    LEVEL_UP_MOVE(34, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(40, MOVE_LOVELY_KISS),
    LEVEL_UP_MOVE(46, MOVE_MEAN_LOOK),
    LEVEL_UP_MOVE(52, MOVE_PERISH_SONG),
    LEVEL_UP_MOVE(58, MOVE_BLIZZARD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 455
// Types: TYPE_ICE / TYPE_PSYCHIC
// Abilities: ABILITY_OBLIVIOUS, ABILITY_FOREWARN, ABILITY_DRYSKIN
// Level Up Moves: 15
