// KABUTOPS (#141) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_KABUTOPS] =
    {
        .baseHP = 60,
        .baseAttack = 115,
        .baseDefense = 105,
        .baseSpAttack = 65,
        .baseSpDefense = 70,
        .baseSpeed = 80,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_WATER,
        .catchRate = 45,
        .expYield = 173,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_WATER_3,
        .ability1 = ABILITY_SWIFTSWIM,
        .ability2 = ABILITY_BATTLEARMOR,
        .hiddenAbility = ABILITY_WEAKARMOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sKabutopsLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_SLASH),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_FEINT),
    LEVEL_UP_MOVE( 1, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(15, MOVE_AQUA_JET),
    LEVEL_UP_MOVE(20, MOVE_LEER),
    LEVEL_UP_MOVE(25, MOVE_MUD_SHOT),
    LEVEL_UP_MOVE(30, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(35, MOVE_BRINE),
    LEVEL_UP_MOVE(43, MOVE_PROTECT),
    LEVEL_UP_MOVE(49, MOVE_LEECH_LIFE),
    LEVEL_UP_MOVE(56, MOVE_LIQUIDATION),
    LEVEL_UP_MOVE(63, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(70, MOVE_STONE_EDGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 495
// Types: TYPE_ROCK / TYPE_WATER
// Abilities: ABILITY_SWIFTSWIM, ABILITY_BATTLEARMOR, ABILITY_WEAKARMOR
// Level Up Moves: 17
