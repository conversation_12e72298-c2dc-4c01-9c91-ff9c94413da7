// KADABRA (#064) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_KADABRA] =
    {
        .baseHP = 40,
        .baseAttack = 35,
        .baseDefense = 30,
        .baseSpAttack = 120,
        .baseSpDefense = 70,
        .baseSpeed = 105,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 100,
        .expYield = 140,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_TWISTED_SPOON,
        .genderRatio = PERCENT_FEMALE(25),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_HUMANSHAPE,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_SYNCHRONIZE,
        .ability2 = ABILITY_INNERFOCUS,
        .hiddenAbility = ABILITY_MAGICGUARD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sKadabraLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_DISABLE),
    LEVEL_UP_MOVE( 1, MOVE_TELEPORT),
    LEVEL_UP_MOVE( 1, MOVE_KINESIS),
    LEVEL_UP_MOVE( 5, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(10, MOVE_REFLECT),
    LEVEL_UP_MOVE(15, MOVE_ALLY_SWITCH),
    LEVEL_UP_MOVE(20, MOVE_PSYCHO_CUT),
    LEVEL_UP_MOVE(25, MOVE_RECOVER),
    LEVEL_UP_MOVE(30, MOVE_PSYSHOCK),
    LEVEL_UP_MOVE(35, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(40, MOVE_ROLE_PLAY),
    LEVEL_UP_MOVE(45, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(50, MOVE_CALM_MIND),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 400
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_SYNCHRONIZE, ABILITY_INNERFOCUS, ABILITY_MAGICGUARD
// Level Up Moves: 14
