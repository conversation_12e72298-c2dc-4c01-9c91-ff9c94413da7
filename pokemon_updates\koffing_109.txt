// KOFFING (#109) - <PERSON><PERSON><PERSON><PERSON><PERSON> IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_KOFFING] =
    {
        .baseHP = 40,
        .baseAttack = 65,
        .baseDefense = 95,
        .baseSpAttack = 60,
        .baseSpDefense = 45,
        .baseSpeed = 35,
        .type1 = TYPE_POISON,
        .type2 = TYPE_POISON,
        .catchRate = 190,
        .expYield = 68,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SMOKE_BALL,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_INDETERMINATE,
        .eggGroup2 = EGG_GROUP_INDETERMINATE,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NEUTRALIZINGGAS,
        .hiddenAbility = ABILITY_STENCH,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sKoffingLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_POISON_GAS),
    LEVEL_UP_MOVE( 4, MOVE_SMOG),
    LEVEL_UP_MOVE( 8, MOVE_SMOKESCREEN),
    LEVEL_UP_MOVE(12, MOVE_CLEAR_SMOG),
    LEVEL_UP_MOVE(16, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(20, MOVE_SLUDGE),
    LEVEL_UP_MOVE(24, MOVE_HAZE),
    LEVEL_UP_MOVE(28, MOVE_SELF_DESTRUCT),
    LEVEL_UP_MOVE(32, MOVE_SLUDGE_BOMB),
    LEVEL_UP_MOVE(36, MOVE_TOXIC),
    LEVEL_UP_MOVE(40, MOVE_BELCH),
    LEVEL_UP_MOVE(44, MOVE_EXPLOSION),
    LEVEL_UP_MOVE(48, MOVE_MEMENTO),
    LEVEL_UP_MOVE(52, MOVE_DESTINY_BOND),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 340
// Types: TYPE_POISON / TYPE_POISON
// Abilities: ABILITY_LEVITATE, ABILITY_NEUTRALIZINGGAS, ABILITY_STENCH
// Level Up Moves: 15
