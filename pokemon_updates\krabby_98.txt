// KRABBY (#098) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_KRABBY] =
    {
        .baseHP = 30,
        .baseAttack = 105,
        .baseDefense = 90,
        .baseSpAttack = 25,
        .baseSpDefense = 25,
        .baseSpeed = 50,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 225,
        .expYield = 65,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_3,
        .eggGroup2 = EGG_GROUP_WATER_3,
        .ability1 = ABILITY_HYPERCUTTER,
        .ability2 = ABILITY_SHELLARMOR,
        .abilityHidden = ABILITY_SHEERFORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove skrabbyLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_BUBBLE),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SPORT),
    LEVEL_UP_MOVE( 5, MOVE_VICE_GRIP),
    LEVEL_UP_MOVE( 9, MOVE_LEER),
    LEVEL_UP_MOVE(11, MOVE_HARDEN),
    LEVEL_UP_MOVE(15, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(19, MOVE_MUD_SHOT),
    LEVEL_UP_MOVE(21, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(25, MOVE_STOMP),
    LEVEL_UP_MOVE(29, MOVE_PROTECT),
    LEVEL_UP_MOVE(31, MOVE_GUILLOTINE),
    LEVEL_UP_MOVE(32, MOVE_RAZOR_SHELL),
    LEVEL_UP_MOVE(35, MOVE_SLAM),
    LEVEL_UP_MOVE(39, MOVE_BRINE),
    LEVEL_UP_MOVE(41, MOVE_CRABHAMMER),
    LEVEL_UP_MOVE(45, MOVE_FLAIL),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 325
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_HYPERCUTTER, ABILITY_SHELLARMOR, ABILITY_SHEERFORCE
// Level Up Moves: 17
