// KYOGRE (#382) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_KYOGRE] =
    {
        .baseHP = 100,
        .baseAttack = 100,
        .baseDefense = 90,
        .baseSpAttack = 150,
        .baseSpDefense = 140,
        .baseSpeed = 90,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 3,
        .expYield = 335,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 3,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_DRIZZLE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sKyogreLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE( 1, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE( 1, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE( 1, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE( 1, MOVE_ORIGIN_PULSE),
    LEVEL_UP_MOVE( 9, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(18, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(27, MOVE_MUDDY_WATER),
    LEVEL_UP_MOVE(36, MOVE_ICE_BEAM),
    LEVEL_UP_MOVE(45, MOVE_SHEER_COLD),
    LEVEL_UP_MOVE(54, MOVE_AQUA_RING),
    LEVEL_UP_MOVE(72, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(81, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(90, MOVE_WATER_SPOUT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 670
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_DRIZZLE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
