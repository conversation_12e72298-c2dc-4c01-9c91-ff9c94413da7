// LAPRAS (#131) - GE<PERSON>RATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_LAPRAS] =
    {
        .baseHP = 130,
        .baseAttack = 85,
        .baseDefense = 80,
        .baseSpAttack = 85,
        .baseSpDefense = 95,
        .baseSpeed = 60,
        .type1 = TYPE_WATER,
        .type2 = TYPE_ICE,
        .catchRate = 45,
        .expYield = 187,
        .evYield_HP = 2,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_MYSTIC_WATER,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 40,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_WATER_1,
        .ability1 = ABILITY_WATERABSORB,
        .ability2 = ABILITY_SHELLARMOR,
        .hiddenAbility = ABILITY_HYDRATION,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sLaprasLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 5, MOVE_SING),
    LEVEL_UP_MOVE(10, MOVE_MIST),
    LEVEL_UP_MOVE(15, MOVE_LIFE_DEW),
    LEVEL_UP_MOVE(20, MOVE_ICE_SHARD),
    LEVEL_UP_MOVE(25, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(30, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(35, MOVE_BRINE),
    LEVEL_UP_MOVE(40, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(45, MOVE_ICE_BEAM),
    LEVEL_UP_MOVE(50, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(55, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(60, MOVE_PERISH_SONG),
    LEVEL_UP_MOVE(65, MOVE_SHEER_COLD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 535
// Types: TYPE_WATER / TYPE_ICE
// Abilities: ABILITY_WATERABSORB, ABILITY_SHELLARMOR, ABILITY_HYDRATION
// Level Up Moves: 15
