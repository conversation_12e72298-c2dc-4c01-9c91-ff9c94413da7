// LICKITUNG (#108) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_LICKITUNG] =
    {
        .baseHP = 90,
        .baseAttack = 55,
        .baseDefense = 75,
        .baseSpAttack = 60,
        .baseSpDefense = 75,
        .baseSpeed = 30,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 45,
        .expYield = 77,
        .evYield_HP = 2,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_LAGGING_TAIL,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_MONSTER,
        .ability1 = ABILITY_OWNTEMPO,
        .ability2 = ABILITY_OBLIVIOUS,
        .hiddenAbility = ABILITY_CLOUDNINE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sLickitungLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_LICK),
    LEVEL_UP_MOVE( 6, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(12, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(18, MOVE_WRAP),
    LEVEL_UP_MOVE(24, MOVE_DISABLE),
    LEVEL_UP_MOVE(30, MOVE_STOMP),
    LEVEL_UP_MOVE(36, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(42, MOVE_SCREECH),
    LEVEL_UP_MOVE(48, MOVE_SLAM),
    LEVEL_UP_MOVE(54, MOVE_POWER_WHIP),
    LEVEL_UP_MOVE(60, MOVE_BELLY_DRUM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 385
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_OWNTEMPO, ABILITY_OBLIVIOUS, ABILITY_CLOUDNINE
// Level Up Moves: 12
