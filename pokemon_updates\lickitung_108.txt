// LICKITUNG (#108) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_LICKITUNG] =
    {
        .baseHP = 90,
        .baseAttack = 55,
        .baseDefense = 75,
        .baseSpAttack = 60,
        .baseSpDefense = 75,
        .baseSpeed = 30,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 45,
        .expYield = 77,
        .evYield_HP = 2,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_LAGGING_TAIL,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_MONSTER,
        .ability1 = ABILITY_OWNTEMPO,
        .ability2 = ABILITY_OBLIVIOUS,
        .abilityHidden = ABILITY_CLOUDNINE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove slickitungLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LICK),
    LEVEL_UP_MOVE( 5, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE( 9, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE(13, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(17, MOVE_WRAP),
    LEVEL_UP_MOVE(21, MOVE_STOMP),
    LEVEL_UP_MOVE(25, MOVE_DISABLE),
    LEVEL_UP_MOVE(29, MOVE_SLAM),
    LEVEL_UP_MOVE(33, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(37, MOVE_CHIP_AWAY),
    LEVEL_UP_MOVE(41, MOVE_ME_FIRST),
    LEVEL_UP_MOVE(45, MOVE_REFRESH),
    LEVEL_UP_MOVE(49, MOVE_SCREECH),
    LEVEL_UP_MOVE(53, MOVE_POWER_WHIP),
    LEVEL_UP_MOVE(57, MOVE_WRING_OUT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 385
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_OWNTEMPO, ABILITY_OBLIVIOUS, ABILITY_CLOUDNINE
// Level Up Moves: 15
