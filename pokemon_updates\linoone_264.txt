// LINOONE (#264) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_LINOONE] =
    {
        .baseHP = 78,
        .baseAttack = 70,
        .baseDefense = 61,
        .baseSpAttack = 50,
        .baseSpDefense = 61,
        .baseSpeed = 100,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 90,
        .expYield = 147,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_POTION,
        .item2 = ITEM_MAX_REVIVE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_PICKUP,
        .ability2 = ABILITY_GLUTTONY,
        .abilityHidden = ABILITY_QUICKFEET,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove slinooneLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_PIN_MISSILE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_SWITCHEROO),
    LEVEL_UP_MOVE( 1, MOVE_ROTOTILLER),
    LEVEL_UP_MOVE( 1, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE( 1, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(11, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(13, MOVE_ODOR_SLEUTH),
    LEVEL_UP_MOVE(15, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(17, MOVE_MUD_SPORT),
    LEVEL_UP_MOVE(19, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(24, MOVE_COVET),
    LEVEL_UP_MOVE(27, MOVE_BESTOW),
    LEVEL_UP_MOVE(28, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(32, MOVE_SLASH),
    LEVEL_UP_MOVE(35, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(38, MOVE_FLAIL),
    LEVEL_UP_MOVE(40, MOVE_REST),
    LEVEL_UP_MOVE(43, MOVE_BELLY_DRUM),
    LEVEL_UP_MOVE(48, MOVE_FLING),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 420
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_PICKUP, ABILITY_GLUTTONY, ABILITY_QUICKFEET
// Level Up Moves: 23
