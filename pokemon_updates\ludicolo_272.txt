// LUDICOLO (#272) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_LUDICOLO] =
    {
        .baseHP = 80,
        .baseAttack = 70,
        .baseDefense = 70,
        .baseSpAttack = 90,
        .baseSpDefense = 100,
        .baseSpeed = 70,
        .type1 = TYPE_WATER,
        .type2 = TYPE_GRASS,
        .catchRate = 45,
        .expYield = 240,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 3,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_MENTAL_HERB,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_SWIFTSWIM,
        .ability2 = ABILITY_RAINDISH,
        .hiddenAbility = ABILITY_OWNTEMPO,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sLudicoloLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE( 1, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE( 1, MOVE_FAKE_OUT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 480
// Types: TYPE_WATER / TYPE_GRASS
// Abilities: ABILITY_SWIFTSWIM, ABILITY_RAINDISH, ABILITY_OWNTEMPO
// Level Up Moves: 3
