// MACHOP (#066) - <PERSON><PERSON><PERSON><PERSON><PERSON> IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_MACHOP] =
    {
        .baseHP = 70,
        .baseAttack = 80,
        .baseDefense = 50,
        .baseSpAttack = 35,
        .baseSpDefense = 35,
        .baseSpeed = 35,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 180,
        .expYield = 61,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_FOCUS_BAND,
        .genderRatio = PERCENT_FEMALE(25),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_HUMANSHAPE,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_GUTS,
        .ability2 = ABILITY_NOGUARD,
        .hiddenAbility = ABILITY_STEADFAST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sMachopLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_LOW_KICK),
    LEVEL_UP_MOVE( 4, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE( 8, MOVE_REVENGE),
    LEVEL_UP_MOVE(12, MOVE_LOW_SWEEP),
    LEVEL_UP_MOVE(16, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(20, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(24, MOVE_VITAL_THROW),
    LEVEL_UP_MOVE(29, MOVE_STRENGTH),
    LEVEL_UP_MOVE(32, MOVE_DUAL_CHOP),
    LEVEL_UP_MOVE(36, MOVE_BULK_UP),
    LEVEL_UP_MOVE(40, MOVE_SEISMIC_TOSS),
    LEVEL_UP_MOVE(44, MOVE_DYNAMIC_PUNCH),
    LEVEL_UP_MOVE(48, MOVE_CROSS_CHOP),
    LEVEL_UP_MOVE(52, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 305
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_GUTS, ABILITY_NOGUARD, ABILITY_STEADFAST
// Level Up Moves: 15
