// MAGIKARP (#129) - GE<PERSON>RATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_MAGIKARP] =
    {
        .baseHP = 20,
        .baseAttack = 10,
        .baseDefense = 55,
        .baseSpAttack = 15,
        .baseSpDefense = 20,
        .baseSpeed = 80,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 255,
        .expYield = 40,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 5,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_2,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_SWIFTSWIM,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_RATTLED,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove smagikarpLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SPLASH),
    LEVEL_UP_MOVE(15, MOVE_TACKLE),
    LEVEL_UP_MOVE(30, MOVE_FLAIL),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 200
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_SWIFTSWIM, ABILITY_NONE, ABILITY_RATTLED
// Level Up Moves: 3
