// MAGMAR (#126) - GE<PERSON>RATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_MAGMAR] =
    {
        .baseHP = 65,
        .baseAttack = 95,
        .baseDefense = 57,
        .baseSpAttack = 100,
        .baseSpDefense = 85,
        .baseSpeed = 93,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 45,
        .expYield = 173,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_RAWST_BERRY,
        .item2 = ITEM_MAGMARIZER,
        .genderRatio = PERCENT_FEMALE(25),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_HUMANSHAPE,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_FLAMEBODY,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_VITALSPIRIT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove smagmarLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_SMOG),
    LEVEL_UP_MOVE( 8, MOVE_SMOKESCREEN),
    LEVEL_UP_MOVE(12, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE(15, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE(16, MOVE_FLAME_WHEEL),
    LEVEL_UP_MOVE(19, MOVE_CLEAR_SMOG),
    LEVEL_UP_MOVE(22, MOVE_FLAME_BURST),
    LEVEL_UP_MOVE(24, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(26, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(29, MOVE_FIRE_PUNCH),
    LEVEL_UP_MOVE(36, MOVE_LAVA_PLUME),
    LEVEL_UP_MOVE(42, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE(49, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(55, MOVE_FIRE_BLAST),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 495
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_FLAMEBODY, ABILITY_NONE, ABILITY_VITALSPIRIT
// Level Up Moves: 16
