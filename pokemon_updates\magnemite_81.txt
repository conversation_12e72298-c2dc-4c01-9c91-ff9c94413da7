// MAGNEMITE (#081) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_MAGNEMITE] =
    {
        .baseHP = 25,
        .baseAttack = 35,
        .baseDefense = 70,
        .baseSpAttack = 95,
        .baseSpDefense = 55,
        .baseSpeed = 45,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_STEEL,
        .catchRate = 190,
        .expYield = 65,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_METAL_COAT,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_MAGNETPULL,
        .ability2 = ABILITY_STURDY,
        .hiddenAbility = ABILITY_ANALYTIC,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sMagnemiteLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 4, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE( 8, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE(12, MOVE_ELECTRO_BALL),
    LEVEL_UP_MOVE(16, MOVE_GYRO_BALL),
    LEVEL_UP_MOVE(20, MOVE_SPARK),
    LEVEL_UP_MOVE(24, MOVE_SCREECH),
    LEVEL_UP_MOVE(28, MOVE_MAGNET_RISE),
    LEVEL_UP_MOVE(32, MOVE_FLASH_CANNON),
    LEVEL_UP_MOVE(36, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(40, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(44, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(48, MOVE_LOCK_ON),
    LEVEL_UP_MOVE(52, MOVE_ZAP_CANNON),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 325
// Types: TYPE_ELECTRIC / TYPE_STEEL
// Abilities: ABILITY_MAGNETPULL, ABILITY_STURDY, ABILITY_ANALYTIC
// Level Up Moves: 15
