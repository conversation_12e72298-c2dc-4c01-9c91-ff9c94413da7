// MAGNETON (#082) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_MAGNETON] =
    {
        .baseHP = 50,
        .baseAttack = 60,
        .baseDefense = 95,
        .baseSpAttack = 120,
        .baseSpDefense = 70,
        .baseSpeed = 70,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_STEEL,
        .catchRate = 60,
        .expYield = 163,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_MAGNET,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_MAGNETPULL,
        .ability2 = ABILITY_STURDY,
        .hiddenAbility = ABILITY_ANALYTIC,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sMagnetonLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_TRI_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE( 1, MOVE_ELECTRIC_TERRAIN),
    LEVEL_UP_MOVE(12, MOVE_ELECTRO_BALL),
    LEVEL_UP_MOVE(16, MOVE_GYRO_BALL),
    LEVEL_UP_MOVE(20, MOVE_SPARK),
    LEVEL_UP_MOVE(24, MOVE_SCREECH),
    LEVEL_UP_MOVE(28, MOVE_MAGNET_RISE),
    LEVEL_UP_MOVE(34, MOVE_FLASH_CANNON),
    LEVEL_UP_MOVE(40, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(46, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(52, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(58, MOVE_LOCK_ON),
    LEVEL_UP_MOVE(64, MOVE_ZAP_CANNON),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 465
// Types: TYPE_ELECTRIC / TYPE_STEEL
// Abilities: ABILITY_MAGNETPULL, ABILITY_STURDY, ABILITY_ANALYTIC
// Level Up Moves: 17
