// MAKUHITA (#296) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_MAKUHITA] =
    {
        .baseHP = 72,
        .baseAttack = 60,
        .baseDefense = 30,
        .baseSpAttack = 20,
        .baseSpDefense = 30,
        .baseSpeed = 25,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 180,
        .expYield = 47,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_BLACK_BELT,
        .genderRatio = PERCENT_FEMALE(25),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_FLUCTUATING,
        .eggGroup1 = EGG_GROUP_HUMANSHAPE,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_THICKFAT,
        .ability2 = ABILITY_GUTS,
        .abilityHidden = ABILITY_SHEERFORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove smakuhitaLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE( 4, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 7, MOVE_ARM_THRUST),
    LEVEL_UP_MOVE(10, MOVE_FAKE_OUT),
    LEVEL_UP_MOVE(13, MOVE_FORCE_PALM),
    LEVEL_UP_MOVE(16, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(19, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(22, MOVE_VITAL_THROW),
    LEVEL_UP_MOVE(25, MOVE_BELLY_DRUM),
    LEVEL_UP_MOVE(28, MOVE_SMELLING_SALTS),
    LEVEL_UP_MOVE(31, MOVE_SEISMIC_TOSS),
    LEVEL_UP_MOVE(34, MOVE_WAKE_UP_SLAP),
    LEVEL_UP_MOVE(37, MOVE_ENDURE),
    LEVEL_UP_MOVE(40, MOVE_CLOSE_COMBAT),
    LEVEL_UP_MOVE(43, MOVE_REVERSAL),
    LEVEL_UP_MOVE(46, MOVE_HEAVY_SLAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 237
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_THICKFAT, ABILITY_GUTS, ABILITY_SHEERFORCE
// Level Up Moves: 17
