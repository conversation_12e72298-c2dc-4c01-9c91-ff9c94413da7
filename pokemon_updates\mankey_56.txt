// MANKEY (#056) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_MANKEY] =
    {
        .baseHP = 40,
        .baseAttack = 80,
        .baseDefense = 35,
        .baseSpAttack = 35,
        .baseSpDefense = 45,
        .baseSpeed = 70,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 190,
        .expYield = 61,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_PAYAPA_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_INSOMNIA,
        .ability2 = ABILITY_ANGERPOINT,
        .hiddenAbility = ABILITY_DEFIANT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sMankeyLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE( 1, MOVE_COVET),
    LEVEL_UP_MOVE( 5, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE( 8, MOVE_LOW_KICK),
    LEVEL_UP_MOVE(12, MOVE_SEISMIC_TOSS),
    LEVEL_UP_MOVE(17, MOVE_SWAGGER),
    LEVEL_UP_MOVE(22, MOVE_CROSS_CHOP),
    LEVEL_UP_MOVE(26, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(29, MOVE_THRASH),
    LEVEL_UP_MOVE(33, MOVE_CLOSE_COMBAT),
    LEVEL_UP_MOVE(36, MOVE_SCREECH),
    LEVEL_UP_MOVE(40, MOVE_STOMPING_TANTRUM),
    LEVEL_UP_MOVE(44, MOVE_OUTRAGE),
    LEVEL_UP_MOVE(48, MOVE_FINAL_GAMBIT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 305
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_INSOMNIA, ABILITY_ANGERPOINT, ABILITY_DEFIANT
// Level Up Moves: 16
