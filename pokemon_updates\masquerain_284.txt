// MASQUERAIN (#284) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_MASQUERAIN] =
    {
        .baseHP = 70,
        .baseAttack = 60,
        .baseDefense = 62,
        .baseSpAttack = 100,
        .baseSpDefense = 82,
        .baseSpeed = 80,
        .type1 = TYPE_BUG,
        .type2 = TYPE_FLYING,
        .catchRate = 75,
        .expYield = 159,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SILVER_POWDER,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_INTIMIDATE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_UNNERVE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove smasquerainLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_BUBBLE),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE( 1, MOVE_WATER_SPORT),
    LEVEL_UP_MOVE( 1, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE( 1, MOVE_OMINOUS_WIND),
    LEVEL_UP_MOVE( 1, MOVE_QUIVER_DANCE),
    LEVEL_UP_MOVE( 1, MOVE_SOAK),
    LEVEL_UP_MOVE(17, MOVE_GUST),
    LEVEL_UP_MOVE(22, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(22, MOVE_AIR_CUTTER),
    LEVEL_UP_MOVE(26, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE(32, MOVE_SILVER_WIND),
    LEVEL_UP_MOVE(38, MOVE_AIR_SLASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 454
// Types: TYPE_BUG / TYPE_FLYING
// Abilities: ABILITY_INTIMIDATE, ABILITY_NONE, ABILITY_UNNERVE
// Level Up Moves: 16
