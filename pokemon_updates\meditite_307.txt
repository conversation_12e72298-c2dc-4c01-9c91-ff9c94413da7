// MEDITITE (#307) - GE<PERSON>RATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_MEDITITE] =
    {
        .baseHP = 30,
        .baseAttack = 40,
        .baseDefense = 55,
        .baseSpAttack = 40,
        .baseSpDefense = 55,
        .baseSpeed = 60,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 180,
        .expYield = 56,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_HUMANSHAPE,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_PUREPOWER,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_TELEPATHY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove smedititeLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BIDE),
    LEVEL_UP_MOVE( 4, MOVE_MEDITATE),
    LEVEL_UP_MOVE( 7, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 9, MOVE_DETECT),
    LEVEL_UP_MOVE(12, MOVE_ENDURE),
    LEVEL_UP_MOVE(15, MOVE_FEINT),
    LEVEL_UP_MOVE(17, MOVE_FORCE_PALM),
    LEVEL_UP_MOVE(20, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(20, MOVE_HIDDEN_POWER),
    LEVEL_UP_MOVE(23, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(25, MOVE_MIND_READER),
    LEVEL_UP_MOVE(28, MOVE_HIGH_JUMP_KICK),
    LEVEL_UP_MOVE(31, MOVE_PSYCH_UP),
    LEVEL_UP_MOVE(33, MOVE_ACUPRESSURE),
    LEVEL_UP_MOVE(36, MOVE_POWER_TRICK),
    LEVEL_UP_MOVE(39, MOVE_REVERSAL),
    LEVEL_UP_MOVE(41, MOVE_RECOVER),
    LEVEL_UP_MOVE(44, MOVE_COUNTER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 280
// Types: TYPE_FIGHTING / TYPE_PSYCHIC
// Abilities: ABILITY_PUREPOWER, ABILITY_NONE, ABILITY_TELEPATHY
// Level Up Moves: 18
