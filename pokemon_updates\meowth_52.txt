// MEOWTH (#052) - GE<PERSON>RA<PERSON>ON IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_MEOWTH] =
    {
        .baseHP = 40,
        .baseAttack = 45,
        .baseDefense = 35,
        .baseSpAttack = 40,
        .baseSpDefense = 40,
        .baseSpeed = 90,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 255,
        .expYield = 58,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_QUICK_CLAW,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_PICKUP,
        .ability2 = ABILITY_TECHNICIAN,
        .abilityHidden = ABILITY_UNNERVE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove smeowthLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 6, MOVE_BITE),
    LEVEL_UP_MOVE( 9, MOVE_FAKE_OUT),
    LEVEL_UP_MOVE(14, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(17, MOVE_SCREECH),
    LEVEL_UP_MOVE(22, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE(25, MOVE_TAUNT),
    LEVEL_UP_MOVE(30, MOVE_PAY_DAY),
    LEVEL_UP_MOVE(33, MOVE_SLASH),
    LEVEL_UP_MOVE(38, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(41, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(44, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(46, MOVE_CAPTIVATE),
    LEVEL_UP_MOVE(49, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(50, MOVE_FEINT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 290
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_PICKUP, ABILITY_TECHNICIAN, ABILITY_UNNERVE
// Level Up Moves: 16
