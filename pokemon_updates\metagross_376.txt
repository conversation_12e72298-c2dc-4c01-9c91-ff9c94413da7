// METAGROSS (#376) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_METAGROSS] =
    {
        .baseHP = 80,
        .baseAttack = 135,
        .baseDefense = 130,
        .baseSpAttack = 95,
        .baseSpDefense = 90,
        .baseSpeed = 70,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 3,
        .expYield = 300,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 3,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_METAL_COAT,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 40,
        .friendship = 35,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_CLEARBODY,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_LIGHTMETAL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove smetagrossLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE( 1, MOVE_MAGNET_RISE),
    LEVEL_UP_MOVE(23, MOVE_PURSUIT),
    LEVEL_UP_MOVE(26, MOVE_BULLET_PUNCH),
    LEVEL_UP_MOVE(29, MOVE_MIRACLE_EYE),
    LEVEL_UP_MOVE(32, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(35, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(38, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(41, MOVE_AGILITY),
    LEVEL_UP_MOVE(44, MOVE_METEOR_MASH),
    LEVEL_UP_MOVE(52, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(60, MOVE_HYPER_BEAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_STEEL / TYPE_PSYCHIC
// Abilities: ABILITY_CLEARBODY, ABILITY_NONE, ABILITY_LIGHTMETAL
// Level Up Moves: 16
