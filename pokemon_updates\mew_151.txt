// MEW (#151) - GE<PERSON>RATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_MEW] =
    {
        .baseHP = 100,
        .baseAttack = 100,
        .baseDefense = 100,
        .baseSpAttack = 100,
        .baseSpDefense = 100,
        .baseSpeed = 100,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 45,
        .expYield = 300,
        .evYield_HP = 3,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_LUM_BERRY,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 100,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_SYNCHRONIZE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove smewLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_TRANSFORM),
    LEVEL_UP_MOVE( 1, MOVE_REFLECT_TYPE),
    LEVEL_UP_MOVE(10, MOVE_MEGA_PUNCH),
    LEVEL_UP_MOVE(20, MOVE_METRONOME),
    LEVEL_UP_MOVE(30, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(40, MOVE_BARRIER),
    LEVEL_UP_MOVE(40, MOVE_LIFE_DEW),
    LEVEL_UP_MOVE(50, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(60, MOVE_AMNESIA),
    LEVEL_UP_MOVE(70, MOVE_IMPRISON),
    LEVEL_UP_MOVE(70, MOVE_ME_FIRST),
    LEVEL_UP_MOVE(80, MOVE_BATON_PASS),
    LEVEL_UP_MOVE(90, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(100, MOVE_AURA_SPHERE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_SYNCHRONIZE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
