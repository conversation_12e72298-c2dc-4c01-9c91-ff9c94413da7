// MILOTIC (#350) - GE<PERSON>RATI<PERSON> IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_MILOTIC] =
    {
        .baseHP = 95,
        .baseAttack = 60,
        .baseDefense = 79,
        .baseSpAttack = 100,
        .baseSpDefense = 125,
        .baseSpeed = 81,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 60,
        .expYield = 189,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 2,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_ERRATIC,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_MARVELSCALE,
        .ability2 = ABILITY_COMPETITIVE,
        .abilityHidden = ABILITY_CUTECHARM,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove smiloticLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_WRAP),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_SPLASH),
    LEVEL_UP_MOVE( 1, MOVE_FLAIL),
    LEVEL_UP_MOVE( 1, MOVE_REFRESH),
    LEVEL_UP_MOVE( 1, MOVE_WATER_SPORT),
    LEVEL_UP_MOVE(11, MOVE_DISARMING_VOICE),
    LEVEL_UP_MOVE(14, MOVE_TWISTER),
    LEVEL_UP_MOVE(17, MOVE_AQUA_RING),
    LEVEL_UP_MOVE(20, MOVE_LIFE_DEW),
    LEVEL_UP_MOVE(21, MOVE_CAPTIVATE),
    LEVEL_UP_MOVE(24, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE(27, MOVE_RECOVER),
    LEVEL_UP_MOVE(31, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(34, MOVE_ATTRACT),
    LEVEL_UP_MOVE(37, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(41, MOVE_COIL),
    LEVEL_UP_MOVE(44, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(47, MOVE_RAIN_DANCE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 540
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_MARVELSCALE, ABILITY_COMPETITIVE, ABILITY_CUTECHARM
// Level Up Moves: 21
