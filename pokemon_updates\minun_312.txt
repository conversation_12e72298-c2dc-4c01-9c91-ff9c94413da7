// MINUN (#312) - GE<PERSON>RATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_MINUN] =
    {
        .baseHP = 60,
        .baseAttack = 40,
        .baseDefense = 50,
        .baseSpAttack = 75,
        .baseSpDefense = 85,
        .baseSpeed = 95,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 200,
        .expYield = 142,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_CELL_BATTERY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_FAIRY,
        .eggGroup2 = EGG_GROUP_FAIRY,
        .ability1 = ABILITY_MINUS,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_VOLTABSORB,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sminunLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_PLAY_NICE),
    LEVEL_UP_MOVE( 1, MOVE_NUZZLE),
    LEVEL_UP_MOVE( 4, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 7, MOVE_SPARK),
    LEVEL_UP_MOVE(10, MOVE_ENCORE),
    LEVEL_UP_MOVE(13, MOVE_SWITCHEROO),
    LEVEL_UP_MOVE(16, MOVE_SWIFT),
    LEVEL_UP_MOVE(19, MOVE_ELECTRO_BALL),
    LEVEL_UP_MOVE(22, MOVE_COPYCAT),
    LEVEL_UP_MOVE(25, MOVE_FAKE_TEARS),
    LEVEL_UP_MOVE(28, MOVE_CHARGE),
    LEVEL_UP_MOVE(31, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(34, MOVE_BATON_PASS),
    LEVEL_UP_MOVE(37, MOVE_AGILITY),
    LEVEL_UP_MOVE(40, MOVE_TRUMP_CARD),
    LEVEL_UP_MOVE(43, MOVE_THUNDER),
    LEVEL_UP_MOVE(46, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(49, MOVE_ENTRAINMENT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 405
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_MINUS, ABILITY_NONE, ABILITY_VOLTABSORB
// Level Up Moves: 21
