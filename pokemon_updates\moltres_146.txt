// MOLTRES (#146) - GE<PERSON>RA<PERSON><PERSON> IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_MOLTRES] =
    {
        .baseHP = 90,
        .baseAttack = 100,
        .baseDefense = 90,
        .baseSpAttack = 125,
        .baseSpDefense = 85,
        .baseSpeed = 90,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FLYING,
        .catchRate = 3,
        .expYield = 290,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 3,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 80,
        .friendship = 35,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_PRESSURE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_FLAMEBODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sMoltresLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 5, MOVE_EMBER),
    LEVEL_UP_MOVE(10, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(15, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(20, MOVE_AGILITY),
    LEVEL_UP_MOVE(25, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(30, MOVE_INCINERATE),
    LEVEL_UP_MOVE(35, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(40, MOVE_ROOST),
    LEVEL_UP_MOVE(45, MOVE_HEAT_WAVE),
    LEVEL_UP_MOVE(50, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE(55, MOVE_HURRICANE),
    LEVEL_UP_MOVE(60, MOVE_ENDURE),
    LEVEL_UP_MOVE(65, MOVE_OVERHEAT),
    LEVEL_UP_MOVE(70, MOVE_SKY_ATTACK),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 580
// Types: TYPE_FIRE / TYPE_FLYING
// Abilities: ABILITY_PRESSURE, ABILITY_NONE, ABILITY_FLAMEBODY
// Level Up Moves: 16
