// MR_MIME (#122) - GE<PERSON>RATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_MR_MIME] =
    {
        .baseHP = 40,
        .baseAttack = 45,
        .baseDefense = 65,
        .baseSpAttack = 100,
        .baseSpDefense = 120,
        .baseSpeed = 90,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_FAIRY,
        .catchRate = 45,
        .expYield = 161,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 2,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_LEPPA_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_HUMANSHAPE,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_SOUNDPROOF,
        .ability2 = ABILITY_FILTER,
        .hiddenAbility = ABILITY_TECHNICIAN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sMr_mimeLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_BATON_PASS),
    LEVEL_UP_MOVE( 1, MOVE_ENCORE),
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE( 1, MOVE_POWER_SWAP),
    LEVEL_UP_MOVE( 1, MOVE_GUARD_SWAP),
    LEVEL_UP_MOVE( 1, MOVE_WIDE_GUARD),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(12, MOVE_CONFUSION),
    LEVEL_UP_MOVE(16, MOVE_ROLE_PLAY),
    LEVEL_UP_MOVE(20, MOVE_PROTECT),
    LEVEL_UP_MOVE(24, MOVE_RECYCLE),
    LEVEL_UP_MOVE(28, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(32, MOVE_MIMIC),
    LEVEL_UP_MOVE(36, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(36, MOVE_REFLECT),
    LEVEL_UP_MOVE(36, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(40, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(44, MOVE_DAZZLING_GLEAM),
    LEVEL_UP_MOVE(48, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(52, MOVE_TEETER_DANCE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 460
// Types: TYPE_PSYCHIC / TYPE_FAIRY
// Abilities: ABILITY_SOUNDPROOF, ABILITY_FILTER, ABILITY_TECHNICIAN
// Level Up Moves: 21
