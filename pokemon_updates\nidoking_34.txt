// NIDOKING (#034) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_NIDOKING] =
    {
        .baseHP = 81,
        .baseAttack = 102,
        .baseDefense = 77,
        .baseSpAttack = 85,
        .baseSpDefense = 75,
        .baseSpeed = 85,
        .type1 = TYPE_POISON,
        .type2 = TYPE_GROUND,
        .catchRate = 45,
        .expYield = 253,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_POISONPOINT,
        .ability2 = ABILITY_RIVALRY,
        .abilityHidden = ABILITY_SHEERFORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove snidokingLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE( 1, MOVE_HORN_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_POISON_STING),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE( 1, MOVE_MEGAHORN),
    LEVEL_UP_MOVE( 1, MOVE_FLATTER),
    LEVEL_UP_MOVE( 1, MOVE_TOXIC_SPIKES),
    LEVEL_UP_MOVE(23, MOVE_CHIP_AWAY),
    LEVEL_UP_MOVE(35, MOVE_THRASH),
    LEVEL_UP_MOVE(43, MOVE_EARTH_POWER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 505
// Types: TYPE_POISON / TYPE_GROUND
// Abilities: ABILITY_POISONPOINT, ABILITY_RIVALRY, ABILITY_SHEERFORCE
// Level Up Moves: 13
