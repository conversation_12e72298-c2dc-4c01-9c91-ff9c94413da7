// NIDOQUEEN (#031) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_NIDOQUEEN] =
    {
        .baseHP = 90,
        .baseAttack = 92,
        .baseDefense = 87,
        .baseSpAttack = 75,
        .baseSpDefense = 85,
        .baseSpeed = 76,
        .type1 = TYPE_POISON,
        .type2 = TYPE_GROUND,
        .catchRate = 45,
        .expYield = 253,
        .evYield_HP = 3,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(100),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_POISONPOINT,
        .ability2 = ABILITY_RIVALRY,
        .abilityHidden = ABILITY_SHEERFORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove snidoqueenLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_SUPERPOWER),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_POISON_STING),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_TOXIC),
    LEVEL_UP_MOVE( 1, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE( 1, MOVE_CRUNCH),
    LEVEL_UP_MOVE( 1, MOVE_FLATTER),
    LEVEL_UP_MOVE( 1, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 1, MOVE_TOXIC_SPIKES),
    LEVEL_UP_MOVE( 1, MOVE_EARTH_POWER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 505
// Types: TYPE_POISON / TYPE_GROUND
// Abilities: ABILITY_POISONPOINT, ABILITY_RIVALRY, ABILITY_SHEERFORCE
// Level Up Moves: 14
