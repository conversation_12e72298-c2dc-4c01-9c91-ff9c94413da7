// NIDORAN_F (#029) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_NIDORAN_F] =
    {
        .baseHP = 55,
        .baseAttack = 47,
        .baseDefense = 52,
        .baseSpAttack = 40,
        .baseSpDefense = 40,
        .baseSpeed = 41,
        .type1 = TYPE_POISON,
        .type2 = TYPE_POISON,
        .catchRate = 235,
        .expYield = 55,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(100),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_POISONPOINT,
        .ability2 = ABILITY_RIVALRY,
        .abilityHidden = ABILITY_HUSTLE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove snidoran_fLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 7, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 9, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(13, MOVE_POISON_STING),
    LEVEL_UP_MOVE(19, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(21, MOVE_BITE),
    LEVEL_UP_MOVE(25, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(31, MOVE_TOXIC_SPIKES),
    LEVEL_UP_MOVE(33, MOVE_FLATTER),
    LEVEL_UP_MOVE(37, MOVE_CRUNCH),
    LEVEL_UP_MOVE(43, MOVE_CAPTIVATE),
    LEVEL_UP_MOVE(45, MOVE_POISON_FANG),
    LEVEL_UP_MOVE(55, MOVE_EARTH_POWER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 275
// Types: TYPE_POISON / TYPE_POISON
// Abilities: ABILITY_POISONPOINT, ABILITY_RIVALRY, ABILITY_HUSTLE
// Level Up Moves: 14
