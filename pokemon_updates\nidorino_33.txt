// NIDORINO (#033) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_NIDORINO] =
    {
        .baseHP = 61,
        .baseAttack = 72,
        .baseDefense = 57,
        .baseSpAttack = 55,
        .baseSpDefense = 55,
        .baseSpeed = 65,
        .type1 = TYPE_POISON,
        .type2 = TYPE_POISON,
        .catchRate = 120,
        .expYield = 128,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_POISONPOINT,
        .ability2 = ABILITY_RIVALRY,
        .hiddenAbility = ABILITY_HUSTLE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sNidorinoLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POISON_STING),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(15, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(22, MOVE_TOXIC_SPIKES),
    LEVEL_UP_MOVE(29, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(36, MOVE_HORN_ATTACK),
    LEVEL_UP_MOVE(43, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(50, MOVE_TOXIC),
    LEVEL_UP_MOVE(57, MOVE_FLATTER),
    LEVEL_UP_MOVE(64, MOVE_POISON_JAB),
    LEVEL_UP_MOVE(71, MOVE_EARTH_POWER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 365
// Types: TYPE_POISON / TYPE_POISON
// Abilities: ABILITY_POISONPOINT, ABILITY_RIVALRY, ABILITY_HUSTLE
// Level Up Moves: 13
