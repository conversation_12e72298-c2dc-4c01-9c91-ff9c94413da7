// NINETALES (#038) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_NINETALES] =
    {
        .baseHP = 73,
        .baseAttack = 76,
        .baseDefense = 75,
        .baseSpAttack = 81,
        .baseSpDefense = 100,
        .baseSpeed = 100,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 75,
        .expYield = 177,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 1,
        .item1 = ITEM_RAWST_BERRY,
        .item2 = ITEM_CHARCOAL,
        .genderRatio = PERCENT_FEMALE(75),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_FLASHFIRE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_DROUGHT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sninetalesLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 505
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_FLASHFIRE, ABILITY_NONE, ABILITY_DROUGHT
// Level Up Moves: 3
