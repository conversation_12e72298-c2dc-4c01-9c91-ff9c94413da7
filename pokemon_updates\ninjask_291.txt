// NINJASK (#291) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_NINJASK] =
    {
        .baseHP = 61,
        .baseAttack = 90,
        .baseDefense = 45,
        .baseSpAttack = 50,
        .baseSpDefense = 50,
        .baseSpeed = 160,
        .type1 = TYPE_BUG,
        .type2 = TYPE_FLYING,
        .catchRate = 120,
        .expYield = 160,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_ERRATIC,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_SPEEDBOOST,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_INFILTRATOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sninjaskLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_SCREECH),
    LEVEL_UP_MOVE( 0, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE( 0, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_DIG),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE( 1, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(13, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(17, MOVE_AGILITY),
    LEVEL_UP_MOVE(23, MOVE_SLASH),
    LEVEL_UP_MOVE(29, MOVE_MIND_READER),
    LEVEL_UP_MOVE(35, MOVE_BATON_PASS),
    LEVEL_UP_MOVE(41, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(47, MOVE_X_SCISSOR),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 456
// Types: TYPE_BUG / TYPE_FLYING
// Abilities: ABILITY_SPEEDBOOST, ABILITY_NONE, ABILITY_INFILTRATOR
// Level Up Moves: 18
