// NOSEPASS (#299) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_NOSEPASS] =
    {
        .baseHP = 30,
        .baseAttack = 45,
        .baseDefense = 135,
        .baseSpAttack = 45,
        .baseSpDefense = 90,
        .baseSpeed = 30,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_ROCK,
        .catchRate = 255,
        .expYield = 75,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_MAGNET,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_STURDY,
        .ability2 = ABILITY_MAGNETPULL,
        .abilityHidden = ABILITY_SANDFORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove snosepassLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 4, MOVE_HARDEN),
    LEVEL_UP_MOVE( 7, MOVE_BLOCK),
    LEVEL_UP_MOVE(10, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE(13, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE(16, MOVE_REST),
    LEVEL_UP_MOVE(19, MOVE_SPARK),
    LEVEL_UP_MOVE(22, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(25, MOVE_POWER_GEM),
    LEVEL_UP_MOVE(28, MOVE_ROCK_BLAST),
    LEVEL_UP_MOVE(31, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(34, MOVE_SANDSTORM),
    LEVEL_UP_MOVE(37, MOVE_EARTH_POWER),
    LEVEL_UP_MOVE(40, MOVE_STONE_EDGE),
    LEVEL_UP_MOVE(43, MOVE_ZAP_CANNON),
    LEVEL_UP_MOVE(43, MOVE_LOCK_ON),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 375
// Types: TYPE_ROCK / TYPE_ROCK
// Abilities: ABILITY_STURDY, ABILITY_MAGNETPULL, ABILITY_SANDFORCE
// Level Up Moves: 16
