// ODDISH (#043) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_ODDISH] =
    {
        .baseHP = 45,
        .baseAttack = 50,
        .baseDefense = 55,
        .baseSpAttack = 75,
        .baseSpDefense = 65,
        .baseSpeed = 30,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_POISON,
        .catchRate = 255,
        .expYield = 64,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_ABSORB_BULB,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_CHLOROPHYLL,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_RUNAWAY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove soddishLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 4, MOVE_ACID),
    LEVEL_UP_MOVE( 8, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(12, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(14, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE(16, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE(18, MOVE_SLEEP_POWDER),
    LEVEL_UP_MOVE(20, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(24, MOVE_TOXIC),
    LEVEL_UP_MOVE(28, MOVE_MOONBLAST),
    LEVEL_UP_MOVE(32, MOVE_GRASSY_TERRAIN),
    LEVEL_UP_MOVE(36, MOVE_MOONLIGHT),
    LEVEL_UP_MOVE(40, MOVE_PETAL_DANCE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 320
// Types: TYPE_GRASS / TYPE_POISON
// Abilities: ABILITY_CHLOROPHYLL, ABILITY_NONE, ABILITY_RUNAWAY
// Level Up Moves: 14
