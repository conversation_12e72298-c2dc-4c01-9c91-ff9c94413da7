// OMASTAR (#139) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_OMASTAR] =
    {
        .baseHP = 70,
        .baseAttack = 60,
        .baseDefense = 125,
        .baseSpAttack = 115,
        .baseSpDefense = 70,
        .baseSpeed = 55,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_WATER,
        .catchRate = 45,
        .expYield = 173,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_WATER_3,
        .ability1 = ABILITY_SWIFTSWIM,
        .ability2 = ABILITY_SHELLARMOR,
        .abilityHidden = ABILITY_WEAKARMOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove somastarLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_SPIKE_CANNON),
    LEVEL_UP_MOVE( 0, MOVE_CRUNCH),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE( 1, MOVE_WITHDRAW),
    LEVEL_UP_MOVE( 1, MOVE_CONSTRICT),
    LEVEL_UP_MOVE(10, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(16, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(19, MOVE_LEER),
    LEVEL_UP_MOVE(25, MOVE_MUD_SHOT),
    LEVEL_UP_MOVE(28, MOVE_BRINE),
    LEVEL_UP_MOVE(34, MOVE_PROTECT),
    LEVEL_UP_MOVE(37, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(48, MOVE_TICKLE),
    LEVEL_UP_MOVE(56, MOVE_ROCK_BLAST),
    LEVEL_UP_MOVE(67, MOVE_SHELL_SMASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 495
// Types: TYPE_ROCK / TYPE_WATER
// Abilities: ABILITY_SWIFTSWIM, ABILITY_SHELLARMOR, ABILITY_WEAKARMOR
// Level Up Moves: 17
