// ONIX (#095) - GE<PERSON>RATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_ONIX] =
    {
        .baseHP = 35,
        .baseAttack = 45,
        .baseDefense = 160,
        .baseSpAttack = 30,
        .baseSpDefense = 45,
        .baseSpeed = 70,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_GROUND,
        .catchRate = 45,
        .expYield = 77,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_HARD_STONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_ROCKHEAD,
        .ability2 = ABILITY_STURDY,
        .hiddenAbility = ABILITY_WEAKARMOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sOnixLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BIND),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 4, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE( 8, MOVE_ROCK_POLISH),
    LEVEL_UP_MOVE(12, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(16, MOVE_CURSE),
    LEVEL_UP_MOVE(20, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(24, MOVE_SCREECH),
    LEVEL_UP_MOVE(28, MOVE_SAND_TOMB),
    LEVEL_UP_MOVE(32, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(36, MOVE_SLAM),
    LEVEL_UP_MOVE(40, MOVE_SANDSTORM),
    LEVEL_UP_MOVE(44, MOVE_DIG),
    LEVEL_UP_MOVE(48, MOVE_IRON_TAIL),
    LEVEL_UP_MOVE(52, MOVE_STONE_EDGE),
    LEVEL_UP_MOVE(56, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 385
// Types: TYPE_ROCK / TYPE_GROUND
// Abilities: ABILITY_ROCKHEAD, ABILITY_STURDY, ABILITY_WEAKARMOR
// Level Up Moves: 18
