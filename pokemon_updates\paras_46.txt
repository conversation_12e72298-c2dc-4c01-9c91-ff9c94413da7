// PARAS (#046) - GE<PERSON>RATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_PARAS] =
    {
        .baseHP = 35,
        .baseAttack = 70,
        .baseDefense = 55,
        .baseSpAttack = 45,
        .baseSpDefense = 55,
        .baseSpeed = 25,
        .type1 = TYPE_BUG,
        .type2 = TYPE_GRASS,
        .catchRate = 190,
        .expYield = 57,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_TINY_MUSHROOM,
        .item2 = ITEM_BIG_MUSHROOM,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_EFFECTSPORE,
        .ability2 = ABILITY_DRYSKIN,
        .abilityHidden = ABILITY_DAMP,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sparasLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 6, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE( 6, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE(11, MOVE_ABSORB),
    LEVEL_UP_MOVE(17, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE(22, MOVE_SPORE),
    LEVEL_UP_MOVE(27, MOVE_SLASH),
    LEVEL_UP_MOVE(33, MOVE_GROWTH),
    LEVEL_UP_MOVE(38, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(43, MOVE_AROMATHERAPY),
    LEVEL_UP_MOVE(49, MOVE_RAGE_POWDER),
    LEVEL_UP_MOVE(54, MOVE_X_SCISSOR),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 285
// Types: TYPE_BUG / TYPE_GRASS
// Abilities: ABILITY_EFFECTSPORE, ABILITY_DRYSKIN, ABILITY_DAMP
// Level Up Moves: 12
