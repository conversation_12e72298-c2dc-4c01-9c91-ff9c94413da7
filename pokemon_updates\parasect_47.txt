// PARASECT (#047) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_PARASECT] =
    {
        .baseHP = 60,
        .baseAttack = 95,
        .baseDefense = 80,
        .baseSpAttack = 60,
        .baseSpDefense = 80,
        .baseSpeed = 30,
        .type1 = TYPE_BUG,
        .type2 = TYPE_GRASS,
        .catchRate = 75,
        .expYield = 142,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_TINY_MUSHROOM,
        .item2 = ITEM_BIG_MUSHROOM,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_EFFECTSPORE,
        .ability2 = ABILITY_DRYSKIN,
        .hiddenAbility = ABILITY_DAMP,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sParasectLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE( 1, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE( 1, MOVE_CROSS_POISON),
    LEVEL_UP_MOVE(17, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE(22, MOVE_SPORE),
    LEVEL_UP_MOVE(29, MOVE_SLASH),
    LEVEL_UP_MOVE(37, MOVE_GROWTH),
    LEVEL_UP_MOVE(44, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(51, MOVE_AROMATHERAPY),
    LEVEL_UP_MOVE(59, MOVE_RAGE_POWDER),
    LEVEL_UP_MOVE(66, MOVE_X_SCISSOR),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 405
// Types: TYPE_BUG / TYPE_GRASS
// Abilities: ABILITY_EFFECTSPORE, ABILITY_DRYSKIN, ABILITY_DAMP
// Level Up Moves: 13
