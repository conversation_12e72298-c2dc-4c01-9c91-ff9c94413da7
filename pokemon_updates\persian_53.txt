// PERSIAN (#053) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_PERSIAN] =
    {
        .baseHP = 65,
        .baseAttack = 70,
        .baseDefense = 60,
        .baseSpAttack = 65,
        .baseSpDefense = 65,
        .baseSpeed = 115,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 90,
        .expYield = 154,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_QUICK_CLAW,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_LIMBER,
        .ability2 = ABILITY_TECHNICIAN,
        .abilityHidden = ABILITY_UNNERVE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spersianLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_POWER_GEM),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_FAKE_OUT),
    LEVEL_UP_MOVE( 1, MOVE_FEINT),
    LEVEL_UP_MOVE( 1, MOVE_SWITCHEROO),
    LEVEL_UP_MOVE(12, MOVE_PAY_DAY),
    LEVEL_UP_MOVE(16, MOVE_BITE),
    LEVEL_UP_MOVE(20, MOVE_TAUNT),
    LEVEL_UP_MOVE(24, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(31, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(36, MOVE_SCREECH),
    LEVEL_UP_MOVE(42, MOVE_SLASH),
    LEVEL_UP_MOVE(48, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(54, MOVE_PLAY_ROUGH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 440
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_LIMBER, ABILITY_TECHNICIAN, ABILITY_UNNERVE
// Level Up Moves: 15
