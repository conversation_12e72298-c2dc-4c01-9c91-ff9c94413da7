// PIDGEOT (#018) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_PIDGEOT] =
    {
        .baseHP = 83,
        .baseAttack = 80,
        .baseDefense = 75,
        .baseSpAttack = 70,
        .baseSpDefense = 70,
        .baseSpeed = 101,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 216,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 3,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_KEENEYE,
        .ability2 = ABILITY_TANGLEDFEET,
        .hiddenAbility = ABILITY_BIGPECKS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPidgeotLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_HURRICANE),
    LEVEL_UP_MOVE(17, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(22, MOVE_TWISTER),
    LEVEL_UP_MOVE(27, MOVE_FEATHER_DANCE),
    LEVEL_UP_MOVE(32, MOVE_AGILITY),
    LEVEL_UP_MOVE(38, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(44, MOVE_ROOST),
    LEVEL_UP_MOVE(50, MOVE_TAILWIND),
    LEVEL_UP_MOVE(56, MOVE_MIRROR_MOVE),
    LEVEL_UP_MOVE(62, MOVE_AIR_SLASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 479
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_KEENEYE, ABILITY_TANGLEDFEET, ABILITY_BIGPECKS
// Level Up Moves: 14
