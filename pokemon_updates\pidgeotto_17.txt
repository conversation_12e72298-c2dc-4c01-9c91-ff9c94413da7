// PIDGEOTTO (#017) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_PIDGEOTTO] =
    {
        .baseHP = 63,
        .baseAttack = 60,
        .baseDefense = 55,
        .baseSpAttack = 50,
        .baseSpDefense = 50,
        .baseSpeed = 71,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 120,
        .expYield = 122,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_KEENEYE,
        .ability2 = ABILITY_TANGLEDFEET,
        .abilityHidden = ABILITY_BIGPECKS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spidgeottoLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE(13, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(17, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(22, MOVE_TWISTER),
    LEVEL_UP_MOVE(27, MOVE_FEATHER_DANCE),
    LEVEL_UP_MOVE(32, MOVE_AGILITY),
    LEVEL_UP_MOVE(37, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(42, MOVE_ROOST),
    LEVEL_UP_MOVE(47, MOVE_TAILWIND),
    LEVEL_UP_MOVE(52, MOVE_MIRROR_MOVE),
    LEVEL_UP_MOVE(57, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(62, MOVE_HURRICANE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 349
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_KEENEYE, ABILITY_TANGLEDFEET, ABILITY_BIGPECKS
// Level Up Moves: 14
