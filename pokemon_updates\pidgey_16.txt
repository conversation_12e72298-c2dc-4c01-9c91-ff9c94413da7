// PIDGEY (#016) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_PIDGEY] =
    {
        .baseHP = 40,
        .baseAttack = 45,
        .baseDefense = 40,
        .baseSpAttack = 35,
        .baseSpDefense = 35,
        .baseSpeed = 56,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 255,
        .expYield = 50,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_KEENEYE,
        .ability2 = ABILITY_TANGLEDFEET,
        .abilityHidden = ABILITY_BIGPECKS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spidgeyLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 9, MOVE_GUST),
    LEVEL_UP_MOVE(13, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(17, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(21, MOVE_TWISTER),
    LEVEL_UP_MOVE(25, MOVE_FEATHER_DANCE),
    LEVEL_UP_MOVE(29, MOVE_AGILITY),
    LEVEL_UP_MOVE(33, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(37, MOVE_ROOST),
    LEVEL_UP_MOVE(41, MOVE_TAILWIND),
    LEVEL_UP_MOVE(45, MOVE_MIRROR_MOVE),
    LEVEL_UP_MOVE(49, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(53, MOVE_HURRICANE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 251
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_KEENEYE, ABILITY_TANGLEDFEET, ABILITY_BIGPECKS
// Level Up Moves: 14
