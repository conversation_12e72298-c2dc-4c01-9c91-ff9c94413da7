// PIKACHU (#025) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_PIKACHU] =
    {
        .baseHP = 35,
        .baseAttack = 55,
        .baseDefense = 40,
        .baseSpAttack = 50,
        .baseSpDefense = 50,
        .baseSpeed = 90,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 190,
        .expYield = 112,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_ORAN_BERRY,
        .item2 = ITEM_LIGHT_BALL,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 10,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_FAIRY,
        .ability1 = ABILITY_STATIC,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_LIGHTNINGROD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPikachuLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_KISS),
    LEVEL_UP_MOVE( 1, MOVE_CHARM),
    LEVEL_UP_MOVE( 1, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE( 1, MOVE_PLAY_NICE),
    LEVEL_UP_MOVE( 1, MOVE_NUZZLE),
    LEVEL_UP_MOVE( 4, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE( 8, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(12, MOVE_ELECTRO_BALL),
    LEVEL_UP_MOVE(16, MOVE_FEINT),
    LEVEL_UP_MOVE(20, MOVE_SPARK),
    LEVEL_UP_MOVE(24, MOVE_AGILITY),
    LEVEL_UP_MOVE(28, MOVE_IRON_TAIL),
    LEVEL_UP_MOVE(32, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(36, MOVE_THUNDERBOLT),
    LEVEL_UP_MOVE(40, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(44, MOVE_THUNDER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 320
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_STATIC, ABILITY_NONE, ABILITY_LIGHTNINGROD
// Level Up Moves: 20
