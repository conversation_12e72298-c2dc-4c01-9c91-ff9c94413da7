// PINSIR (#127) - <PERSON><PERSON><PERSON><PERSON><PERSON> IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_PINSIR] =
    {
        .baseHP = 65,
        .baseAttack = 125,
        .baseDefense = 100,
        .baseSpAttack = 55,
        .baseSpDefense = 70,
        .baseSpeed = 85,
        .type1 = TYPE_BUG,
        .type2 = TYPE_BUG,
        .catchRate = 45,
        .expYield = 175,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_HYPERCUTTER,
        .ability2 = ABILITY_MOLDBREAKER,
        .hiddenAbility = ABILITY_MOXIE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPinsirLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_VICE_GRIP),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 4, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE( 8, MOVE_BIND),
    LEVEL_UP_MOVE(12, MOVE_SEISMIC_TOSS),
    LEVEL_UP_MOVE(16, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(20, MOVE_STORM_THROW),
    LEVEL_UP_MOVE(24, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE(28, MOVE_VITAL_THROW),
    LEVEL_UP_MOVE(32, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(36, MOVE_STRENGTH),
    LEVEL_UP_MOVE(40, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(44, MOVE_SUBMISSION),
    LEVEL_UP_MOVE(48, MOVE_GUILLOTINE),
    LEVEL_UP_MOVE(52, MOVE_SUPERPOWER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_BUG / TYPE_BUG
// Abilities: ABILITY_HYPERCUTTER, ABILITY_MOLDBREAKER, ABILITY_MOXIE
// Level Up Moves: 15
