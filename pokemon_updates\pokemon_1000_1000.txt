// POKEMON_1000 (#1000) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1000] =
    {
        .baseHP = 87,
        .baseAttack = 60,
        .baseDefense = 95,
        .baseSpAttack = 133,
        .baseSpDefense = 91,
        .baseSpeed = 84,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_GHOST,
        .catchRate = 45,
        .expYield = 275,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 50,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_GOODASGOLD,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_1000LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 7, MOVE_NIGHT_SHADE),
    LEVEL_UP_MOVE(14, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(21, MOVE_SUBSTITUTE),
    LEVEL_UP_MOVE(28, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(35, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(42, MOVE_RECOVER),
    LEVEL_UP_MOVE(49, MOVE_POWER_GEM),
    LEVEL_UP_MOVE(56, MOVE_MAKE_IT_RAIN),
    LEVEL_UP_MOVE(63, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(70, MOVE_MEMENTO),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 550
// Types: TYPE_STEEL / TYPE_GHOST
// Abilities: ABILITY_GOODASGOLD, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 12
