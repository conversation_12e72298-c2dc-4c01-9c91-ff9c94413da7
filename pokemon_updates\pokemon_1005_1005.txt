// POKEMON_1005 (#1005) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1005] =
    {
        .baseHP = 105,
        .baseAttack = 139,
        .baseDefense = 71,
        .baseSpAttack = 55,
        .baseSpDefense = 101,
        .baseSpeed = 119,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_DARK,
        .catchRate = 10,
        .expYield = 295,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_PROTOSYNTHESIS,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_1005LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE( 7, MOVE_INCINERATE),
    LEVEL_UP_MOVE(14, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(21, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(28, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(35, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(42, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(49, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(56, MOVE_DRAGON_DANCE),
    LEVEL_UP_MOVE(63, MOVE_DRAGON_RUSH),
    LEVEL_UP_MOVE(70, MOVE_FLY),
    LEVEL_UP_MOVE(77, MOVE_THROAT_CHOP),
    LEVEL_UP_MOVE(84, MOVE_ROOST),
    LEVEL_UP_MOVE(91, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 590
// Types: TYPE_DRAGON / TYPE_DARK
// Abilities: ABILITY_PROTOSYNTHESIS, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 17
