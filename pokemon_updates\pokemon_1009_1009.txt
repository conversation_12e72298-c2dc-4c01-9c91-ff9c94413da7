// POKEMON_1009 (#1009) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1009] =
    {
        .baseHP = 99,
        .baseAttack = 83,
        .baseDefense = 91,
        .baseSpAttack = 125,
        .baseSpDefense = 83,
        .baseSpeed = 109,
        .type1 = TYPE_WATER,
        .type2 = TYPE_DRAGON,
        .catchRate = 5,
        .expYield = 295,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 3,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_PROTOSYNTHESIS,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_1009LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_LEER),
    LEVEL_UP_MOVE( 0, MOVE_ROAR),
    LEVEL_UP_MOVE( 0, MOVE_TWISTER),
    LEVEL_UP_MOVE( 0, MOVE_AQUA_JET),
    LEVEL_UP_MOVE( 7, MOVE_BITE),
    LEVEL_UP_MOVE(14, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(21, MOVE_NOBLE_ROAR),
    LEVEL_UP_MOVE(28, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(35, MOVE_BREAKING_SWIPE),
    LEVEL_UP_MOVE(42, MOVE_DRAGON_RUSH),
    LEVEL_UP_MOVE(56, MOVE_HYDRO_STEAM),
    LEVEL_UP_MOVE(63, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(70, MOVE_OUTRAGE),
    LEVEL_UP_MOVE(77, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(84, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 590
// Types: TYPE_WATER / TYPE_DRAGON
// Abilities: ABILITY_PROTOSYNTHESIS, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
