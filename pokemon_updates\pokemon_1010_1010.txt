// POKEMON_1010 (#1010) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1010] =
    {
        .baseHP = 90,
        .baseAttack = 130,
        .baseDefense = 88,
        .baseSpAttack = 70,
        .baseSpDefense = 108,
        .baseSpeed = 104,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 5,
        .expYield = 295,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_QUARKDRIVE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_1010LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_LEER),
    LEVEL_UP_MOVE( 0, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 0, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 0, MOVE_WORK_UP),
    LEVEL_UP_MOVE( 7, MOVE_MAGICAL_LEAF),
    LEVEL_UP_MOVE(14, MOVE_RETALIATE),
    LEVEL_UP_MOVE(21, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(28, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(35, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(42, MOVE_SACRED_SWORD),
    LEVEL_UP_MOVE(49, MOVE_LEAF_BLADE),
    LEVEL_UP_MOVE(56, MOVE_PSYBLADE),
    LEVEL_UP_MOVE(63, MOVE_CLOSE_COMBAT),
    LEVEL_UP_MOVE(70, MOVE_IMPRISON),
    LEVEL_UP_MOVE(77, MOVE_MEGAHORN),
    LEVEL_UP_MOVE(84, MOVE_ALLY_SWITCH),
    LEVEL_UP_MOVE(91, MOVE_SOLAR_BLADE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 590
// Types: TYPE_GRASS / TYPE_PSYCHIC
// Abilities: ABILITY_QUARKDRIVE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 17
