// POKEMON_1011 (#1011) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1011] =
    {
        .baseHP = 80,
        .baseAttack = 80,
        .baseDefense = 110,
        .baseSpAttack = 95,
        .baseSpDefense = 80,
        .baseSpeed = 40,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 170,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 20,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_SUPERSWEETSYRUP,
        .ability2 = ABILITY_GLUTTONY,
        .abilityHidden = ABILITY_STICKYHOLD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_1011LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE( 1, MOVE_WITHDRAW),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE( 1, MOVE_RECYCLE),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 4, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE( 8, MOVE_GROWTH),
    LEVEL_UP_MOVE(12, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(16, MOVE_PROTECT),
    LEVEL_UP_MOVE(20, MOVE_BULLET_SEED),
    LEVEL_UP_MOVE(28, MOVE_SYRUP_BOMB),
    LEVEL_UP_MOVE(32, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(36, MOVE_RECOVER),
    LEVEL_UP_MOVE(40, MOVE_ENERGY_BALL),
    LEVEL_UP_MOVE(44, MOVE_SUBSTITUTE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 485
// Types: TYPE_GRASS / TYPE_DRAGON
// Abilities: ABILITY_SUPERSWEETSYRUP, ABILITY_GLUTTONY, ABILITY_STICKYHOLD
// Level Up Moves: 15
