// POKEMON_1012 (#1012) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1012] =
    {
        .baseHP = 40,
        .baseAttack = 45,
        .baseDefense = 45,
        .baseSpAttack = 74,
        .baseSpDefense = 54,
        .baseSpeed = 50,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GHOST,
        .catchRate = 120,
        .expYield = 62,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_INDETERMINATE,
        .ability1 = ABILITY_HOSPITALITY,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_HEATPROOF,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_1012LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE( 1, MOVE_WITHDRAW),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 6, MOVE_ABSORB),
    LEVEL_UP_MOVE(12, MOVE_LIFE_DEW),
    LEVEL_UP_MOVE(18, MOVE_FOUL_PLAY),
    LEVEL_UP_MOVE(24, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(30, MOVE_HEX),
    LEVEL_UP_MOVE(36, MOVE_RAGE_POWDER),
    LEVEL_UP_MOVE(42, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(48, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(54, MOVE_MEMENTO),
    LEVEL_UP_MOVE(60, MOVE_LEAF_STORM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 308
// Types: TYPE_GRASS / TYPE_GHOST
// Abilities: ABILITY_HOSPITALITY, ABILITY_NONE, ABILITY_HEATPROOF
// Level Up Moves: 13
