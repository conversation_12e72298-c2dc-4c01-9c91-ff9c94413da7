// POKEMON_1015 (#1015) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1015] =
    {
        .baseHP = 88,
        .baseAttack = 75,
        .baseDefense = 66,
        .baseSpAttack = 130,
        .baseSpDefense = 90,
        .baseSpeed = 106,
        .type1 = TYPE_POISON,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 3,
        .expYield = 278,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 3,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_TOXICCHAIN,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_FRISK,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_1015LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_FAKE_OUT),
    LEVEL_UP_MOVE( 1, MOVE_FLATTER),
    LEVEL_UP_MOVE( 8, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(16, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(24, MOVE_CLEAR_SMOG),
    LEVEL_UP_MOVE(32, MOVE_POISON_JAB),
    LEVEL_UP_MOVE(40, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(48, MOVE_SLUDGE_WAVE),
    LEVEL_UP_MOVE(56, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(64, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(72, MOVE_PARTING_SHOT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 555
// Types: TYPE_POISON / TYPE_PSYCHIC
// Abilities: ABILITY_TOXICCHAIN, ABILITY_NONE, ABILITY_FRISK
// Level Up Moves: 13
