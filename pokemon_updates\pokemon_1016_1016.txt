// POKEMON_1016 (#1016) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1016] =
    {
        .baseHP = 88,
        .baseAttack = 91,
        .baseDefense = 82,
        .baseSpAttack = 70,
        .baseSpDefense = 125,
        .baseSpeed = 99,
        .type1 = TYPE_POISON,
        .type2 = TYPE_FAIRY,
        .catchRate = 3,
        .expYield = 278,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 3,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_TOXICCHAIN,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_TECHNICIAN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_1016LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_POISON_GAS),
    LEVEL_UP_MOVE( 1, MOVE_DISARMING_VOICE),
    LEVEL_UP_MOVE( 8, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(16, MOVE_ATTRACT),
    LEVEL_UP_MOVE(24, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(32, MOVE_CROSS_POISON),
    LEVEL_UP_MOVE(40, MOVE_TAIL_SLAP),
    LEVEL_UP_MOVE(48, MOVE_BEAT_UP),
    LEVEL_UP_MOVE(56, MOVE_SWAGGER),
    LEVEL_UP_MOVE(56, MOVE_FLATTER),
    LEVEL_UP_MOVE(64, MOVE_ROOST),
    LEVEL_UP_MOVE(72, MOVE_MOONBLAST),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 555
// Types: TYPE_POISON / TYPE_FAIRY
// Abilities: ABILITY_TOXICCHAIN, ABILITY_NONE, ABILITY_TECHNICIAN
// Level Up Moves: 14
