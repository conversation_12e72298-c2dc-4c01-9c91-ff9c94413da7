// POKEMON_1017 (#1017) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1017] =
    {
        .baseHP = 80,
        .baseAttack = 120,
        .baseDefense = 84,
        .baseSpAttack = 60,
        .baseSpDefense = 96,
        .baseSpeed = 110,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 5,
        .expYield = 275,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_DEFIANT,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_1017LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_VINE_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_FOLLOW_ME),
    LEVEL_UP_MOVE( 6, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(12, MOVE_GROWTH),
    LEVEL_UP_MOVE(18, MOVE_SLAM),
    LEVEL_UP_MOVE(24, MOVE_LOW_SWEEP),
    LEVEL_UP_MOVE(30, MOVE_IVY_CUDGEL),
    LEVEL_UP_MOVE(36, MOVE_THROAT_CHOP),
    LEVEL_UP_MOVE(42, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(48, MOVE_SPIKY_SHIELD),
    LEVEL_UP_MOVE(54, MOVE_POWER_WHIP),
    LEVEL_UP_MOVE(60, MOVE_SUPERPOWER),
    LEVEL_UP_MOVE(66, MOVE_WOOD_HAMMER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 550
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_DEFIANT, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
