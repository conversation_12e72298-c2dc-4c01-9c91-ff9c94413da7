// POKEMON_1018 (#1018) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1018] =
    {
        .baseHP = 90,
        .baseAttack = 105,
        .baseDefense = 130,
        .baseSpAttack = 125,
        .baseSpDefense = 65,
        .baseSpeed = 85,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_DRAGON,
        .catchRate = 10,
        .expYield = 300,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 3,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_STAMINA,
        .ability2 = ABILITY_STURDY,
        .abilityHidden = ABILITY_STALWART,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_1018LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_ELECTRO_SHOT),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE( 6, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE(12, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(18, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(24, MOVE_BREAKING_SWIPE),
    LEVEL_UP_MOVE(30, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE(36, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(42, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(48, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(54, MOVE_FLASH_CANNON),
    LEVEL_UP_MOVE(60, MOVE_METAL_BURST),
    LEVEL_UP_MOVE(66, MOVE_HYPER_BEAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_STEEL / TYPE_DRAGON
// Abilities: ABILITY_STAMINA, ABILITY_STURDY, ABILITY_STALWART
// Level Up Moves: 14
