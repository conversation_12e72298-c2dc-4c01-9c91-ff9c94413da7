// POKEMON_1019 (#1019) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1019] =
    {
        .baseHP = 106,
        .baseAttack = 80,
        .baseDefense = 110,
        .baseSpAttack = 120,
        .baseSpDefense = 80,
        .baseSpeed = 44,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_DRAGON,
        .catchRate = 10,
        .expYield = 270,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 3,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_ERRATIC,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_SUPERSWEETSYRUP,
        .ability2 = ABILITY_REGENERATOR,
        .abilityHidden = ABILITY_STICKYHOLD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_1019LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_FICKLE_BEAM),
    LEVEL_UP_MOVE( 1, MOVE_WITHDRAW),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE( 1, MOVE_RECYCLE),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 4, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE( 8, MOVE_GROWTH),
    LEVEL_UP_MOVE(12, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(16, MOVE_PROTECT),
    LEVEL_UP_MOVE(20, MOVE_BULLET_SEED),
    LEVEL_UP_MOVE(28, MOVE_SYRUP_BOMB),
    LEVEL_UP_MOVE(32, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(36, MOVE_RECOVER),
    LEVEL_UP_MOVE(40, MOVE_ENERGY_BALL),
    LEVEL_UP_MOVE(44, MOVE_SUBSTITUTE),
    LEVEL_UP_MOVE(54, MOVE_POWER_WHIP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 540
// Types: TYPE_GRASS / TYPE_DRAGON
// Abilities: ABILITY_SUPERSWEETSYRUP, ABILITY_REGENERATOR, ABILITY_STICKYHOLD
// Level Up Moves: 16
