// POKEMON_1021 (#1021) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1021] =
    {
        .baseHP = 125,
        .baseAttack = 73,
        .baseDefense = 91,
        .baseSpAttack = 137,
        .baseSpDefense = 89,
        .baseSpeed = 75,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_DRAGON,
        .catchRate = 10,
        .expYield = 295,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 3,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_PROTOSYNTHESIS,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_1021LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_STOMP),
    LEVEL_UP_MOVE( 1, MOVE_TWISTER),
    LEVEL_UP_MOVE( 1, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE( 1, MOVE_SHOCK_WAVE),
    LEVEL_UP_MOVE( 7, MOVE_CHARGE),
    LEVEL_UP_MOVE(14, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(21, MOVE_ELECTRIC_TERRAIN),
    LEVEL_UP_MOVE(28, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(35, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE(42, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(49, MOVE_THUNDERCLAP),
    LEVEL_UP_MOVE(56, MOVE_DRAGON_HAMMER),
    LEVEL_UP_MOVE(63, MOVE_RISING_VOLTAGE),
    LEVEL_UP_MOVE(70, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(77, MOVE_ZAP_CANNON),
    LEVEL_UP_MOVE(84, MOVE_BODY_PRESS),
    LEVEL_UP_MOVE(91, MOVE_THUNDER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 590
// Types: TYPE_ELECTRIC / TYPE_DRAGON
// Abilities: ABILITY_PROTOSYNTHESIS, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 17
