// POKEMON_1022 (#1022) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1022] =
    {
        .baseHP = 90,
        .baseAttack = 120,
        .baseDefense = 80,
        .baseSpAttack = 68,
        .baseSpDefense = 108,
        .baseSpeed = 124,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 10,
        .expYield = 295,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 3,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_QUARKDRIVE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_1022LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_HORN_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE( 1, MOVE_ELECTRIC_TERRAIN),
    LEVEL_UP_MOVE( 7, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(14, MOVE_SLASH),
    LEVEL_UP_MOVE(21, MOVE_AGILITY),
    LEVEL_UP_MOVE(28, MOVE_PSYCHO_CUT),
    LEVEL_UP_MOVE(35, MOVE_COUNTER),
    LEVEL_UP_MOVE(42, MOVE_ROCK_TOMB),
    LEVEL_UP_MOVE(49, MOVE_SACRED_SWORD),
    LEVEL_UP_MOVE(56, MOVE_MIGHTY_CLEAVE),
    LEVEL_UP_MOVE(63, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(70, MOVE_MEGAHORN),
    LEVEL_UP_MOVE(77, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(84, MOVE_STONE_EDGE),
    LEVEL_UP_MOVE(91, MOVE_GIGA_IMPACT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 590
// Types: TYPE_ROCK / TYPE_PSYCHIC
// Abilities: ABILITY_QUARKDRIVE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 17
