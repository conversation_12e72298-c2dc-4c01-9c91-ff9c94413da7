// POKEMON_1024 (#1024) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1024] =
    {
        .baseHP = 90,
        .baseAttack = 65,
        .baseDefense = 85,
        .baseSpAttack = 65,
        .baseSpDefense = 85,
        .baseSpeed = 60,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 255,
        .expYield = 90,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 5,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_TERASHIFT,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_1024LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WITHDRAW),
    LEVEL_UP_MOVE( 1, MOVE_TRI_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE(10, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(20, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(30, MOVE_PROTECT),
    LEVEL_UP_MOVE(40, MOVE_EARTH_POWER),
    LEVEL_UP_MOVE(50, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(60, MOVE_TERA_STARSTORM),
    LEVEL_UP_MOVE(70, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(80, MOVE_ROCK_POLISH),
    LEVEL_UP_MOVE(90, MOVE_GYRO_BALL),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 450
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_TERASHIFT, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 12
