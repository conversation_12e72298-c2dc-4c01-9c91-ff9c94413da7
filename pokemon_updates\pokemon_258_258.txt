// POKEMON_258 (#258) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_258] =
    {
        .baseHP = 50,
        .baseAttack = 70,
        .baseDefense = 50,
        .baseSpAttack = 50,
        .baseSpDefense = 50,
        .baseSpeed = 40,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 45,
        .expYield = 62,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_WATER_1,
        .ability1 = ABILITY_TORRENT,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_DAMP,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_258LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 4, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 6, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE( 9, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE(12, MOVE_FORESIGHT),
    LEVEL_UP_MOVE(15, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(17, MOVE_BIDE),
    LEVEL_UP_MOVE(20, MOVE_MUD_SPORT),
    LEVEL_UP_MOVE(25, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE(27, MOVE_AMNESIA),
    LEVEL_UP_MOVE(28, MOVE_PROTECT),
    LEVEL_UP_MOVE(33, MOVE_SCREECH),
    LEVEL_UP_MOVE(33, MOVE_WHIRLPOOL),
    LEVEL_UP_MOVE(36, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(41, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(44, MOVE_ENDEAVOR),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 310
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_TORRENT, ABILITY_NONE, ABILITY_DAMP
// Level Up Moves: 17
