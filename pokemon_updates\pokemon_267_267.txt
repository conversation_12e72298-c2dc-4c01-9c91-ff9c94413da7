// POKEMON_267 (#267) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_267] =
    {
        .baseHP = 60,
        .baseAttack = 70,
        .baseDefense = 50,
        .baseSpAttack = 100,
        .baseSpDefense = 50,
        .baseSpeed = 65,
        .type1 = TYPE_BUG,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 178,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 3,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SHED_SHELL,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_SWARM,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_RIVALRY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_267LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_GUST),
    LEVEL_UP_MOVE(12, MOVE_ABSORB),
    LEVEL_UP_MOVE(15, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE(17, MOVE_MORNING_SUN),
    LEVEL_UP_MOVE(20, MOVE_AIR_CUTTER),
    LEVEL_UP_MOVE(22, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(25, MOVE_SILVER_WIND),
    LEVEL_UP_MOVE(27, MOVE_ATTRACT),
    LEVEL_UP_MOVE(30, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(32, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(35, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(37, MOVE_RAGE),
    LEVEL_UP_MOVE(40, MOVE_QUIVER_DANCE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 395
// Types: TYPE_BUG / TYPE_FLYING
// Abilities: ABILITY_SWARM, ABILITY_NONE, ABILITY_RIVALRY
// Level Up Moves: 13
