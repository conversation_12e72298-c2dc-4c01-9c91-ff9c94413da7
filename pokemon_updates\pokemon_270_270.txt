// POKEMON_270 (#270) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_270] =
    {
        .baseHP = 40,
        .baseAttack = 30,
        .baseDefense = 30,
        .baseSpAttack = 40,
        .baseSpDefense = 50,
        .baseSpeed = 30,
        .type1 = TYPE_WATER,
        .type2 = TYPE_GRASS,
        .catchRate = 255,
        .expYield = 44,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_MENTAL_HERB,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_SWIFTSWIM,
        .ability2 = ABILITY_RAINDISH,
        .abilityHidden = ABILITY_OWNTEMPO,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_270LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 3, MOVE_GROWL),
    LEVEL_UP_MOVE( 6, MOVE_ABSORB),
    LEVEL_UP_MOVE( 9, MOVE_BUBBLE),
    LEVEL_UP_MOVE(12, MOVE_NATURAL_GIFT),
    LEVEL_UP_MOVE(15, MOVE_MIST),
    LEVEL_UP_MOVE(18, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(21, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(24, MOVE_NATURE_POWER),
    LEVEL_UP_MOVE(27, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(30, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(33, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(36, MOVE_ENERGY_BALL),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 220
// Types: TYPE_WATER / TYPE_GRASS
// Abilities: ABILITY_SWIFTSWIM, ABILITY_RAINDISH, ABILITY_OWNTEMPO
// Level Up Moves: 13
