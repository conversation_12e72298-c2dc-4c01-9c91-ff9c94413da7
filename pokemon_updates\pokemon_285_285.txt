// POKEMON_285 (#285) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_285] =
    {
        .baseHP = 60,
        .baseAttack = 40,
        .baseDefense = 60,
        .baseSpAttack = 40,
        .baseSpDefense = 60,
        .baseSpeed = 35,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 255,
        .expYield = 59,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_TINY_MUSHROOM,
        .item2 = ITEM_BIG_MUSHROOM,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_FLUCTUATING,
        .eggGroup1 = EGG_GROUP_FAIRY,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_EFFECTSPORE,
        .ability2 = ABILITY_POISONHEAL,
        .abilityHidden = ABILITY_QUICKFEET,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_285LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 5, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE( 8, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(12, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(15, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(19, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE(22, MOVE_WORRY_SEED),
    LEVEL_UP_MOVE(26, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(29, MOVE_GROWTH),
    LEVEL_UP_MOVE(33, MOVE_TOXIC),
    LEVEL_UP_MOVE(36, MOVE_SEED_BOMB),
    LEVEL_UP_MOVE(40, MOVE_SPORE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 295
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_EFFECTSPORE, ABILITY_POISONHEAL, ABILITY_QUICKFEET
// Level Up Moves: 13
