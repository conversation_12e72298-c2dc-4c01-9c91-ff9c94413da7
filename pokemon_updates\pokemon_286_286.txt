// POKEMON_286 (#286) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_286] =
    {
        .baseHP = 60,
        .baseAttack = 130,
        .baseDefense = 80,
        .baseSpAttack = 60,
        .baseSpDefense = 60,
        .baseSpeed = 70,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_FIGHTING,
        .catchRate = 90,
        .expYield = 161,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_TINY_MUSHROOM,
        .item2 = ITEM_BIG_MUSHROOM,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_FLUCTUATING,
        .eggGroup1 = EGG_GROUP_FAIRY,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_EFFECTSPORE,
        .ability2 = ABILITY_POISONHEAL,
        .abilityHidden = ABILITY_TECHNICIAN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_286LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_MACH_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 1, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE( 1, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE(12, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(15, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(19, MOVE_FEINT),
    LEVEL_UP_MOVE(22, MOVE_COUNTER),
    LEVEL_UP_MOVE(28, MOVE_FORCE_PALM),
    LEVEL_UP_MOVE(33, MOVE_MIND_READER),
    LEVEL_UP_MOVE(39, MOVE_SKY_UPPERCUT),
    LEVEL_UP_MOVE(44, MOVE_SEED_BOMB),
    LEVEL_UP_MOVE(50, MOVE_DYNAMIC_PUNCH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 460
// Types: TYPE_GRASS / TYPE_FIGHTING
// Abilities: ABILITY_EFFECTSPORE, ABILITY_POISONHEAL, ABILITY_TECHNICIAN
// Level Up Moves: 16
