// POKEMON_287 (#287) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_287] =
    {
        .baseHP = 60,
        .baseAttack = 60,
        .baseDefense = 60,
        .baseSpAttack = 35,
        .baseSpDefense = 35,
        .baseSpeed = 30,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 255,
        .expYield = 56,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_TRUANT,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_287LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_YAWN),
    LEVEL_UP_MOVE( 6, MOVE_ENCORE),
    LEVEL_UP_MOVE( 9, MOVE_SLACK_OFF),
    LEVEL_UP_MOVE(14, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(14, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE(17, MOVE_AMNESIA),
    LEVEL_UP_MOVE(22, MOVE_COVET),
    LEVEL_UP_MOVE(25, MOVE_CHIP_AWAY),
    LEVEL_UP_MOVE(25, MOVE_THROAT_CHOP),
    LEVEL_UP_MOVE(30, MOVE_COUNTER),
    LEVEL_UP_MOVE(33, MOVE_FLAIL),
    LEVEL_UP_MOVE(38, MOVE_PLAY_ROUGH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 280
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_TRUANT, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 13
