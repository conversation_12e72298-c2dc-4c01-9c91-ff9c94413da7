// POKEMON_293 (#293) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_293] =
    {
        .baseHP = 64,
        .baseAttack = 51,
        .baseDefense = 23,
        .baseSpAttack = 51,
        .baseSpDefense = 23,
        .baseSpeed = 28,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 190,
        .expYield = 48,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_CHESTO_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_SOUNDPROOF,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_RATTLED,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_293LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 4, MOVE_ECHOED_VOICE),
    LEVEL_UP_MOVE( 8, MOVE_ASTONISH),
    LEVEL_UP_MOVE(11, MOVE_HOWL),
    LEVEL_UP_MOVE(15, MOVE_SCREECH),
    LEVEL_UP_MOVE(18, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(22, MOVE_STOMP),
    LEVEL_UP_MOVE(25, MOVE_UPROAR),
    LEVEL_UP_MOVE(29, MOVE_ROAR),
    LEVEL_UP_MOVE(32, MOVE_REST),
    LEVEL_UP_MOVE(36, MOVE_SLEEP_TALK),
    LEVEL_UP_MOVE(39, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(43, MOVE_SYNCHRONOISE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 240
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_SOUNDPROOF, ABILITY_NONE, ABILITY_RATTLED
// Level Up Moves: 13
