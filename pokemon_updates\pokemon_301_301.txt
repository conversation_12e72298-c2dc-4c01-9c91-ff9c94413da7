// POKEMON_301 (#301) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_301] =
    {
        .baseHP = 70,
        .baseAttack = 65,
        .baseDefense = 65,
        .baseSpAttack = 55,
        .baseSpDefense = 55,
        .baseSpeed = 90,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 60,
        .expYield = 140,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_PECHA_BERRY,
        .item2 = ITEM_LEPPA_BERRY,
        .genderRatio = PERCENT_FEMALE(75),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_FAIRY,
        .ability1 = ABILITY_CUTECHARM,
        .ability2 = ABILITY_NORMALIZE,
        .abilityHidden = ABILITY_WONDERSKIN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_301LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DOUBLE_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_SING),
    LEVEL_UP_MOVE( 1, MOVE_ATTRACT),
    LEVEL_UP_MOVE( 1, MOVE_FAKE_OUT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 400
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_CUTECHARM, ABILITY_NORMALIZE, ABILITY_WONDERSKIN
// Level Up Moves: 4
