// POKEMON_303 (#303) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_303] =
    {
        .baseHP = 50,
        .baseAttack = 85,
        .baseDefense = 85,
        .baseSpAttack = 55,
        .baseSpDefense = 55,
        .baseSpeed = 50,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_FAIRY,
        .catchRate = 45,
        .expYield = 133,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_IRON_BALL,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_FAIRY,
        .ability1 = ABILITY_HYPERCUTTER,
        .ability2 = ABILITY_INTIMIDATE,
        .abilityHidden = ABILITY_SHEERFORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_303LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_TAUNT),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE( 1, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE( 1, MOVE_FAIRY_WIND),
    LEVEL_UP_MOVE( 5, MOVE_FAKE_TEARS),
    LEVEL_UP_MOVE( 9, MOVE_BITE),
    LEVEL_UP_MOVE(13, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(17, MOVE_VICE_GRIP),
    LEVEL_UP_MOVE(21, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE(25, MOVE_BATON_PASS),
    LEVEL_UP_MOVE(29, MOVE_CRUNCH),
    LEVEL_UP_MOVE(33, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(37, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(41, MOVE_STOCKPILE),
    LEVEL_UP_MOVE(41, MOVE_SPIT_UP),
    LEVEL_UP_MOVE(41, MOVE_SWALLOW),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 380
// Types: TYPE_STEEL / TYPE_FAIRY
// Abilities: ABILITY_HYPERCUTTER, ABILITY_INTIMIDATE, ABILITY_SHEERFORCE
// Level Up Moves: 18
