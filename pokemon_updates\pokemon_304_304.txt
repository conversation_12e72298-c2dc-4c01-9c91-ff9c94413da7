// POKEMON_304 (#304) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_304] =
    {
        .baseHP = 50,
        .baseAttack = 70,
        .baseDefense = 100,
        .baseSpAttack = 40,
        .baseSpDefense = 40,
        .baseSpeed = 30,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_ROCK,
        .catchRate = 180,
        .expYield = 66,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_HARD_STONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 35,
        .friendship = 35,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_MONSTER,
        .ability1 = ABILITY_STURDY,
        .ability2 = ABILITY_ROCKHEAD,
        .abilityHidden = ABILITY_HEAVYMETAL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_304LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 4, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 7, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(10, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(13, MOVE_ROCK_TOMB),
    LEVEL_UP_MOVE(16, MOVE_PROTECT),
    LEVEL_UP_MOVE(19, MOVE_ROAR),
    LEVEL_UP_MOVE(22, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(25, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(28, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(31, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(34, MOVE_IRON_TAIL),
    LEVEL_UP_MOVE(37, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(40, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(43, MOVE_AUTOTOMIZE),
    LEVEL_UP_MOVE(46, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(49, MOVE_METAL_BURST),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 330
// Types: TYPE_STEEL / TYPE_ROCK
// Abilities: ABILITY_STURDY, ABILITY_ROCKHEAD, ABILITY_HEAVYMETAL
// Level Up Moves: 18
