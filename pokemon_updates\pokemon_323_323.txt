// POKEMON_323 (#323) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_323] =
    {
        .baseHP = 70,
        .baseAttack = 100,
        .baseDefense = 70,
        .baseSpAttack = 105,
        .baseSpDefense = 75,
        .baseSpeed = 40,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_GROUND,
        .catchRate = 150,
        .expYield = 161,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_RAWST_BERRY,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_MAGMAARMOR,
        .ability2 = ABILITY_SOLIDROCK,
        .abilityHidden = ABILITY_ANGERPOINT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_323LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_FISSURE),
    LEVEL_UP_MOVE( 1, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE( 1, MOVE_ERUPTION),
    LEVEL_UP_MOVE(12, MOVE_MAGNITUDE),
    LEVEL_UP_MOVE(15, MOVE_FLAME_BURST),
    LEVEL_UP_MOVE(15, MOVE_INCINERATE),
    LEVEL_UP_MOVE(19, MOVE_AMNESIA),
    LEVEL_UP_MOVE(22, MOVE_LAVA_PLUME),
    LEVEL_UP_MOVE(26, MOVE_EARTH_POWER),
    LEVEL_UP_MOVE(29, MOVE_CURSE),
    LEVEL_UP_MOVE(31, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(39, MOVE_YAWN),
    LEVEL_UP_MOVE(46, MOVE_EARTHQUAKE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 460
// Types: TYPE_FIRE / TYPE_GROUND
// Abilities: ABILITY_MAGMAARMOR, ABILITY_SOLIDROCK, ABILITY_ANGERPOINT
// Level Up Moves: 17
