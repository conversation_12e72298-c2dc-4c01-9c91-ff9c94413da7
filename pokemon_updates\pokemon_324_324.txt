// POKEMON_324 (#324) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_324] =
    {
        .baseHP = 70,
        .baseAttack = 85,
        .baseDefense = 140,
        .baseSpAttack = 85,
        .baseSpDefense = 70,
        .baseSpeed = 20,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 90,
        .expYield = 165,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_CHARCOAL,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_WHITESMOKE,
        .ability2 = ABILITY_DROUGHT,
        .abilityHidden = ABILITY_SHELLARMOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_324LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 4, MOVE_SMOG),
    LEVEL_UP_MOVE( 7, MOVE_WITHDRAW),
    LEVEL_UP_MOVE(10, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE(13, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE(15, MOVE_SMOKESCREEN),
    LEVEL_UP_MOVE(18, MOVE_FLAME_WHEEL),
    LEVEL_UP_MOVE(22, MOVE_CURSE),
    LEVEL_UP_MOVE(25, MOVE_LAVA_PLUME),
    LEVEL_UP_MOVE(27, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(30, MOVE_PROTECT),
    LEVEL_UP_MOVE(34, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(38, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(40, MOVE_AMNESIA),
    LEVEL_UP_MOVE(42, MOVE_FLAIL),
    LEVEL_UP_MOVE(45, MOVE_HEAT_WAVE),
    LEVEL_UP_MOVE(47, MOVE_SHELL_SMASH),
    LEVEL_UP_MOVE(50, MOVE_INFERNO),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 470
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_WHITESMOKE, ABILITY_DROUGHT, ABILITY_SHELLARMOR
// Level Up Moves: 18
