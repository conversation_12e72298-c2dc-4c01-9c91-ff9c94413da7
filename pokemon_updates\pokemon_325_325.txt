// POKEMON_325 (#325) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_325] =
    {
        .baseHP = 60,
        .baseAttack = 25,
        .baseDefense = 35,
        .baseSpAttack = 70,
        .baseSpDefense = 80,
        .baseSpeed = 60,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 255,
        .expYield = 66,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_PERSIM_BERRY,
        .item2 = ITEM_TANGA_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_THICKFAT,
        .ability2 = ABILITY_OWNTEMPO,
        .abilityHidden = ABILITY_GLUTTONY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_325LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SPLASH),
    LEVEL_UP_MOVE( 7, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 7, MOVE_PSYWAVE),
    LEVEL_UP_MOVE(10, MOVE_GROWL),
    LEVEL_UP_MOVE(10, MOVE_ODOR_SLEUTH),
    LEVEL_UP_MOVE(14, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(15, MOVE_PSYCH_UP),
    LEVEL_UP_MOVE(18, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(21, MOVE_MAGIC_COAT),
    LEVEL_UP_MOVE(26, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(29, MOVE_REST),
    LEVEL_UP_MOVE(29, MOVE_POWER_GEM),
    LEVEL_UP_MOVE(33, MOVE_SNORE),
    LEVEL_UP_MOVE(38, MOVE_PSYSHOCK),
    LEVEL_UP_MOVE(40, MOVE_PAYBACK),
    LEVEL_UP_MOVE(44, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(50, MOVE_BOUNCE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 330
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_THICKFAT, ABILITY_OWNTEMPO, ABILITY_GLUTTONY
// Level Up Moves: 17
