// POKEMON_327 (#327) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_327] =
    {
        .baseHP = 60,
        .baseAttack = 60,
        .baseDefense = 60,
        .baseSpAttack = 60,
        .baseSpDefense = 60,
        .baseSpeed = 60,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 255,
        .expYield = 126,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_CHESTO_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_OWNTEMPO,
        .ability2 = ABILITY_TANGLEDFEET,
        .abilityHidden = ABILITY_CONTRARY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_327LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_COPYCAT),
    LEVEL_UP_MOVE(10, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE(14, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(19, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE(23, MOVE_DIZZY_PUNCH),
    LEVEL_UP_MOVE(28, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(32, MOVE_TEETER_DANCE),
    LEVEL_UP_MOVE(37, MOVE_UPROAR),
    LEVEL_UP_MOVE(41, MOVE_PSYCH_UP),
    LEVEL_UP_MOVE(46, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(50, MOVE_FLAIL),
    LEVEL_UP_MOVE(55, MOVE_THRASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 360
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_OWNTEMPO, ABILITY_TANGLEDFEET, ABILITY_CONTRARY
// Level Up Moves: 13
