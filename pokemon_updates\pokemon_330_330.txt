// POKEMON_330 (#330) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_330] =
    {
        .baseHP = 80,
        .baseAttack = 100,
        .baseDefense = 80,
        .baseSpAttack = 80,
        .baseSpDefense = 80,
        .baseSpeed = 100,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 260,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_330LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_SONIC_BOOM),
    LEVEL_UP_MOVE( 1, MOVE_FISSURE),
    LEVEL_UP_MOVE( 1, MOVE_DIG),
    LEVEL_UP_MOVE( 1, MOVE_BIDE),
    LEVEL_UP_MOVE( 1, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE( 1, MOVE_CRUNCH),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_DANCE),
    LEVEL_UP_MOVE( 1, MOVE_FEINT),
    LEVEL_UP_MOVE( 5, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 8, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(12, MOVE_SAND_TOMB),
    LEVEL_UP_MOVE(15, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(19, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(22, MOVE_SCREECH),
    LEVEL_UP_MOVE(26, MOVE_EARTH_POWER),
    LEVEL_UP_MOVE(28, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(29, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE(33, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(36, MOVE_SANDSTORM),
    LEVEL_UP_MOVE(40, MOVE_UPROAR),
    LEVEL_UP_MOVE(43, MOVE_HYPER_BEAM),
    LEVEL_UP_MOVE(47, MOVE_DRAGON_RUSH),
    LEVEL_UP_MOVE(68, MOVE_BOOMBURST),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 520
// Types: TYPE_GROUND / TYPE_DRAGON
// Abilities: ABILITY_LEVITATE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 28
