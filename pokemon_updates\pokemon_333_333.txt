// POKEMON_333 (#333) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_333] =
    {
        .baseHP = 45,
        .baseAttack = 40,
        .baseDefense = 60,
        .baseSpAttack = 40,
        .baseSpDefense = 75,
        .baseSpeed = 50,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 255,
        .expYield = 62,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_ERRATIC,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_NATURALCURE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_CLOUDNINE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_333LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 3, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 5, MOVE_SING),
    LEVEL_UP_MOVE( 7, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE( 9, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(11, MOVE_DISARMING_VOICE),
    LEVEL_UP_MOVE(14, MOVE_MIST),
    LEVEL_UP_MOVE(17, MOVE_ROUND),
    LEVEL_UP_MOVE(20, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(20, MOVE_NATURAL_GIFT),
    LEVEL_UP_MOVE(23, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(26, MOVE_REFRESH),
    LEVEL_UP_MOVE(30, MOVE_MIRROR_MOVE),
    LEVEL_UP_MOVE(34, MOVE_COTTON_GUARD),
    LEVEL_UP_MOVE(38, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(42, MOVE_PERISH_SONG),
    LEVEL_UP_MOVE(46, MOVE_MOONBLAST),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 310
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_NATURALCURE, ABILITY_NONE, ABILITY_CLOUDNINE
// Level Up Moves: 18
