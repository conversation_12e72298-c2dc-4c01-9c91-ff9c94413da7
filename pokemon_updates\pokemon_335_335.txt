// POKEMON_335 (#335) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_335] =
    {
        .baseHP = 73,
        .baseAttack = 115,
        .baseDefense = 60,
        .baseSpAttack = 60,
        .baseSpDefense = 60,
        .baseSpeed = 90,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 90,
        .expYield = 160,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_QUICK_CLAW,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_ERRATIC,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_IMMUNITY,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_TOXICBOOST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_335LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 5, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 8, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE(12, MOVE_PURSUIT),
    LEVEL_UP_MOVE(15, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(19, MOVE_SLASH),
    LEVEL_UP_MOVE(22, MOVE_REVENGE),
    LEVEL_UP_MOVE(22, MOVE_POWER_TRIP),
    LEVEL_UP_MOVE(26, MOVE_CRUSH_CLAW),
    LEVEL_UP_MOVE(29, MOVE_FALSE_SWIPE),
    LEVEL_UP_MOVE(33, MOVE_EMBARGO),
    LEVEL_UP_MOVE(33, MOVE_SWITCHEROO),
    LEVEL_UP_MOVE(36, MOVE_DETECT),
    LEVEL_UP_MOVE(40, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(43, MOVE_TAUNT),
    LEVEL_UP_MOVE(47, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(50, MOVE_CLOSE_COMBAT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 458
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_IMMUNITY, ABILITY_NONE, ABILITY_TOXICBOOST
// Level Up Moves: 18
