// POKEMON_356 (#356) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_356] =
    {
        .baseHP = 40,
        .baseAttack = 70,
        .baseDefense = 130,
        .baseSpAttack = 60,
        .baseSpDefense = 130,
        .baseSpeed = 25,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_GHOST,
        .catchRate = 90,
        .expYield = 159,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SPELL_TAG,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 25,
        .friendship = 35,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_INDETERMINATE,
        .eggGroup2 = EGG_GROUP_INDETERMINATE,
        .ability1 = ABILITY_PRESSURE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_FRISK,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_356LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_SHADOW_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_FIRE_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_ICE_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_BIND),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_DISABLE),
    LEVEL_UP_MOVE( 1, MOVE_NIGHT_SHADE),
    LEVEL_UP_MOVE( 1, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_GRAVITY),
    LEVEL_UP_MOVE(14, MOVE_FORESIGHT),
    LEVEL_UP_MOVE(17, MOVE_SHADOW_SNEAK),
    LEVEL_UP_MOVE(22, MOVE_PURSUIT),
    LEVEL_UP_MOVE(25, MOVE_WILL_O_WISP),
    LEVEL_UP_MOVE(30, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(33, MOVE_CURSE),
    LEVEL_UP_MOVE(40, MOVE_HEX),
    LEVEL_UP_MOVE(45, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(52, MOVE_MEAN_LOOK),
    LEVEL_UP_MOVE(57, MOVE_PAYBACK),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 455
// Types: TYPE_GHOST / TYPE_GHOST
// Abilities: ABILITY_PRESSURE, ABILITY_NONE, ABILITY_FRISK
// Level Up Moves: 21
