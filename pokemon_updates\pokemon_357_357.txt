// POKEMON_357 (#357) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_357] =
    {
        .baseHP = 99,
        .baseAttack = 68,
        .baseDefense = 83,
        .baseSpAttack = 72,
        .baseSpDefense = 87,
        .baseSpeed = 51,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_FLYING,
        .catchRate = 200,
        .expYield = 161,
        .evYield_HP = 2,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 25,
        .friendship = 70,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_CHLOROPHYLL,
        .ability2 = ABILITY_SOLARPOWER,
        .abilityHidden = ABILITY_HARVEST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_357LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 1, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE( 1, MOVE_LEAF_STORM),
    LEVEL_UP_MOVE( 6, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(10, MOVE_STOMP),
    LEVEL_UP_MOVE(16, MOVE_MAGICAL_LEAF),
    LEVEL_UP_MOVE(21, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(26, MOVE_LEAF_TORNADO),
    LEVEL_UP_MOVE(30, MOVE_NATURAL_GIFT),
    LEVEL_UP_MOVE(30, MOVE_WIDE_GUARD),
    LEVEL_UP_MOVE(36, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(41, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(46, MOVE_BESTOW),
    LEVEL_UP_MOVE(50, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(56, MOVE_SOLAR_BEAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 460
// Types: TYPE_GRASS / TYPE_FLYING
// Abilities: ABILITY_CHLOROPHYLL, ABILITY_SOLARPOWER, ABILITY_HARVEST
// Level Up Moves: 17
