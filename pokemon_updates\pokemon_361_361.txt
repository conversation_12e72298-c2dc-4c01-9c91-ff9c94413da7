// POKEMON_361 (#361) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_361] =
    {
        .baseHP = 50,
        .baseAttack = 50,
        .baseDefense = 50,
        .baseSpAttack = 50,
        .baseSpDefense = 50,
        .baseSpeed = 50,
        .type1 = TYPE_ICE,
        .type2 = TYPE_ICE,
        .catchRate = 190,
        .expYield = 60,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SNOWBALL,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_FAIRY,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_INNERFOCUS,
        .ability2 = ABILITY_ICEBODY,
        .abilityHidden = ABILITY_MOODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_361LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_POWDER_SNOW),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 5, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(10, MOVE_ICE_SHARD),
    LEVEL_UP_MOVE(14, MOVE_ICY_WIND),
    LEVEL_UP_MOVE(19, MOVE_BITE),
    LEVEL_UP_MOVE(23, MOVE_ICE_FANG),
    LEVEL_UP_MOVE(28, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(32, MOVE_PROTECT),
    LEVEL_UP_MOVE(37, MOVE_FROST_BREATH),
    LEVEL_UP_MOVE(41, MOVE_CRUNCH),
    LEVEL_UP_MOVE(45, MOVE_SNOWSCAPE),
    LEVEL_UP_MOVE(46, MOVE_BLIZZARD),
    LEVEL_UP_MOVE(50, MOVE_HAIL),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 300
// Types: TYPE_ICE / TYPE_ICE
// Abilities: ABILITY_INNERFOCUS, ABILITY_ICEBODY, ABILITY_MOODY
// Level Up Moves: 15
