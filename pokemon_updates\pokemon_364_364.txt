// POKEMON_364 (#364) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_364] =
    {
        .baseHP = 90,
        .baseAttack = 60,
        .baseDefense = 70,
        .baseSpAttack = 75,
        .baseSpDefense = 70,
        .baseSpeed = 45,
        .type1 = TYPE_ICE,
        .type2 = TYPE_WATER,
        .catchRate = 120,
        .expYield = 144,
        .evYield_HP = 2,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_THICKFAT,
        .ability2 = ABILITY_ICEBODY,
        .abilityHidden = ABILITY_OBLIVIOUS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_364LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_SWAGGER),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_POWDER_SNOW),
    LEVEL_UP_MOVE( 5, MOVE_ROLLOUT),
    LEVEL_UP_MOVE( 9, MOVE_ENCORE),
    LEVEL_UP_MOVE(13, MOVE_ICE_BALL),
    LEVEL_UP_MOVE(17, MOVE_BRINE),
    LEVEL_UP_MOVE(21, MOVE_AURORA_BEAM),
    LEVEL_UP_MOVE(26, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(31, MOVE_REST),
    LEVEL_UP_MOVE(31, MOVE_SNORE),
    LEVEL_UP_MOVE(38, MOVE_HAIL),
    LEVEL_UP_MOVE(45, MOVE_BLIZZARD),
    LEVEL_UP_MOVE(52, MOVE_SHEER_COLD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 410
// Types: TYPE_ICE / TYPE_WATER
// Abilities: ABILITY_THICKFAT, ABILITY_ICEBODY, ABILITY_OBLIVIOUS
// Level Up Moves: 16
