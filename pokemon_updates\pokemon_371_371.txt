// POKEMON_371 (#371) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_371] =
    {
        .baseHP = 45,
        .baseAttack = 75,
        .baseDefense = 60,
        .baseSpAttack = 40,
        .baseSpDefense = 30,
        .baseSpeed = 50,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 60,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_DRAGON_FANG,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 40,
        .friendship = 35,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_DRAGON,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_ROCKHEAD,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_SHEERFORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_371LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_RAGE),
    LEVEL_UP_MOVE( 4, MOVE_EMBER),
    LEVEL_UP_MOVE( 7, MOVE_LEER),
    LEVEL_UP_MOVE(10, MOVE_BITE),
    LEVEL_UP_MOVE(13, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(17, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(21, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(25, MOVE_CRUNCH),
    LEVEL_UP_MOVE(29, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(34, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(39, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(44, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(49, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 300
// Types: TYPE_DRAGON / TYPE_DRAGON
// Abilities: ABILITY_ROCKHEAD, ABILITY_NONE, ABILITY_SHEERFORCE
// Level Up Moves: 13
