// POKEMON_377 (#377) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_377] =
    {
        .baseHP = 80,
        .baseAttack = 100,
        .baseDefense = 200,
        .baseSpAttack = 50,
        .baseSpDefense = 100,
        .baseSpeed = 50,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_ROCK,
        .catchRate = 3,
        .expYield = 290,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 3,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 80,
        .friendship = 35,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_CLEARBODY,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_STURDY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_377LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_STOMP),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE( 1, MOVE_EXPLOSION),
    LEVEL_UP_MOVE( 1, MOVE_CHARGE_BEAM),
    LEVEL_UP_MOVE( 1, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(25, MOVE_CURSE),
    LEVEL_UP_MOVE(31, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(37, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(43, MOVE_STONE_EDGE),
    LEVEL_UP_MOVE(49, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE(55, MOVE_ZAP_CANNON),
    LEVEL_UP_MOVE(55, MOVE_LOCK_ON),
    LEVEL_UP_MOVE(61, MOVE_SUPERPOWER),
    LEVEL_UP_MOVE(67, MOVE_HYPER_BEAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 580
// Types: TYPE_ROCK / TYPE_ROCK
// Abilities: ABILITY_CLEARBODY, ABILITY_NONE, ABILITY_STURDY
// Level Up Moves: 14
