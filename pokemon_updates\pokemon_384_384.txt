// POKEMON_384 (#384) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_384] =
    {
        .baseHP = 105,
        .baseAttack = 150,
        .baseDefense = 90,
        .baseSpAttack = 150,
        .baseSpDefense = 90,
        .baseSpeed = 95,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 340,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_AIRLOCK,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_384LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TWISTER),
    LEVEL_UP_MOVE( 5, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(15, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(20, MOVE_CRUNCH),
    LEVEL_UP_MOVE(30, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(35, MOVE_REST),
    LEVEL_UP_MOVE(45, MOVE_EXTREME_SPEED),
    LEVEL_UP_MOVE(50, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(60, MOVE_DRAGON_DANCE),
    LEVEL_UP_MOVE(65, MOVE_FLY),
    LEVEL_UP_MOVE(72, MOVE_HURRICANE),
    LEVEL_UP_MOVE(75, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(80, MOVE_OUTRAGE),
    LEVEL_UP_MOVE(90, MOVE_HYPER_BEAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 680
// Types: TYPE_DRAGON / TYPE_FLYING
// Abilities: ABILITY_AIRLOCK, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
