// POKEMON_387 (#387) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_387] =
    {
        .baseHP = 55,
        .baseAttack = 68,
        .baseDefense = 64,
        .baseSpAttack = 45,
        .baseSpDefense = 55,
        .baseSpeed = 31,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 45,
        .expYield = 64,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_OVERGROW,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_SHELLARMOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_387LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_WITHDRAW),
    LEVEL_UP_MOVE( 9, MOVE_ABSORB),
    LEVEL_UP_MOVE(13, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(17, MOVE_CURSE),
    LEVEL_UP_MOVE(21, MOVE_BITE),
    LEVEL_UP_MOVE(25, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(29, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(33, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(37, MOVE_CRUNCH),
    LEVEL_UP_MOVE(41, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(45, MOVE_LEAF_STORM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 318
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_OVERGROW, ABILITY_NONE, ABILITY_SHELLARMOR
// Level Up Moves: 12
