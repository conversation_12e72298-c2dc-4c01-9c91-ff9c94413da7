// POKEMON_388 (#388) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_388] =
    {
        .baseHP = 75,
        .baseAttack = 89,
        .baseDefense = 85,
        .baseSpAttack = 55,
        .baseSpDefense = 65,
        .baseSpeed = 36,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 45,
        .expYield = 142,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_OVERGROW,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_SHELLARMOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_388LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_WITHDRAW),
    LEVEL_UP_MOVE(13, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(17, MOVE_CURSE),
    LEVEL_UP_MOVE(22, MOVE_BITE),
    LEVEL_UP_MOVE(27, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(32, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(37, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(42, MOVE_CRUNCH),
    LEVEL_UP_MOVE(47, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(52, MOVE_LEAF_STORM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 405
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_OVERGROW, ABILITY_NONE, ABILITY_SHELLARMOR
// Level Up Moves: 12
