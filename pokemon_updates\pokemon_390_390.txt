// POKEMON_390 (#390) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_390] =
    {
        .baseHP = 44,
        .baseAttack = 58,
        .baseDefense = 44,
        .baseSpAttack = 58,
        .baseSpDefense = 44,
        .baseSpeed = 61,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 45,
        .expYield = 62,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_BLAZE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_IRONFIST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_390LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 7, MOVE_EMBER),
    LEVEL_UP_MOVE( 9, MOVE_TAUNT),
    LEVEL_UP_MOVE(15, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(17, MOVE_FLAME_WHEEL),
    LEVEL_UP_MOVE(23, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(25, MOVE_TORMENT),
    LEVEL_UP_MOVE(31, MOVE_FACADE),
    LEVEL_UP_MOVE(33, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE(39, MOVE_ACROBATICS),
    LEVEL_UP_MOVE(41, MOVE_SLACK_OFF),
    LEVEL_UP_MOVE(47, MOVE_FLAMETHROWER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 309
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_BLAZE, ABILITY_NONE, ABILITY_IRONFIST
// Level Up Moves: 13
