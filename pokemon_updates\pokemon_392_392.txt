// POKEMON_392 (#392) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_392] =
    {
        .baseHP = 76,
        .baseAttack = 104,
        .baseDefense = 71,
        .baseSpAttack = 104,
        .baseSpDefense = 71,
        .baseSpeed = 108,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIGHTING,
        .catchRate = 45,
        .expYield = 240,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_BLAZE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_IRONFIST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_392LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_CLOSE_COMBAT),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_MACH_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_TAUNT),
    LEVEL_UP_MOVE( 1, MOVE_FLARE_BLITZ),
    LEVEL_UP_MOVE(16, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(19, MOVE_FLAME_WHEEL),
    LEVEL_UP_MOVE(26, MOVE_FEINT),
    LEVEL_UP_MOVE(29, MOVE_PUNISHMENT),
    LEVEL_UP_MOVE(42, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE(52, MOVE_ACROBATICS),
    LEVEL_UP_MOVE(58, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(65, MOVE_RAGING_FURY),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 534
// Types: TYPE_FIRE / TYPE_FIGHTING
// Abilities: ABILITY_BLAZE, ABILITY_NONE, ABILITY_IRONFIST
// Level Up Moves: 15
