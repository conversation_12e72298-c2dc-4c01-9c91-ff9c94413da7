// POKEMON_393 (#393) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_393] =
    {
        .baseHP = 53,
        .baseAttack = 51,
        .baseDefense = 53,
        .baseSpAttack = 61,
        .baseSpDefense = 56,
        .baseSpeed = 40,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 45,
        .expYield = 63,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_TORRENT,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_COMPETITIVE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_393LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 4, MOVE_GROWL),
    LEVEL_UP_MOVE( 8, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 8, MOVE_BUBBLE),
    LEVEL_UP_MOVE(11, MOVE_CHARM),
    LEVEL_UP_MOVE(11, MOVE_WATER_SPORT),
    LEVEL_UP_MOVE(15, MOVE_PECK),
    LEVEL_UP_MOVE(18, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(22, MOVE_BIDE),
    LEVEL_UP_MOVE(25, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(29, MOVE_BRINE),
    LEVEL_UP_MOVE(32, MOVE_WHIRLPOOL),
    LEVEL_UP_MOVE(36, MOVE_MIST),
    LEVEL_UP_MOVE(39, MOVE_DRILL_PECK),
    LEVEL_UP_MOVE(43, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 314
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_TORRENT, ABILITY_NONE, ABILITY_COMPETITIVE
// Level Up Moves: 15
