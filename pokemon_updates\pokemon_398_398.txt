// POKEMON_398 (#398) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_398] =
    {
        .baseHP = 85,
        .baseAttack = 120,
        .baseDefense = 70,
        .baseSpAttack = 50,
        .baseSpDefense = 60,
        .baseSpeed = 100,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 218,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_YACHE_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_INTIMIDATE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_RECKLESS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_398LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_CLOSE_COMBAT),
    LEVEL_UP_MOVE( 1, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(13, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(18, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(23, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(28, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(33, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(41, MOVE_AGILITY),
    LEVEL_UP_MOVE(49, MOVE_BRAVE_BIRD),
    LEVEL_UP_MOVE(57, MOVE_FINAL_GAMBIT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 485
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_INTIMIDATE, ABILITY_NONE, ABILITY_RECKLESS
// Level Up Moves: 13
