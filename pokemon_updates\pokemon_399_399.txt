// POKEMON_399 (#399) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_399] =
    {
        .baseHP = 59,
        .baseAttack = 45,
        .baseDefense = 40,
        .baseSpAttack = 35,
        .baseSpDefense = 40,
        .baseSpeed = 31,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 255,
        .expYield = 50,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_SIMPLE,
        .ability2 = ABILITY_UNAWARE,
        .abilityHidden = ABILITY_MOODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_399LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 5, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 9, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(13, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(17, MOVE_HYPER_FANG),
    LEVEL_UP_MOVE(21, MOVE_YAWN),
    LEVEL_UP_MOVE(25, MOVE_CRUNCH),
    LEVEL_UP_MOVE(29, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(33, MOVE_SUPER_FANG),
    LEVEL_UP_MOVE(37, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(41, MOVE_AMNESIA),
    LEVEL_UP_MOVE(45, MOVE_SUPERPOWER),
    LEVEL_UP_MOVE(49, MOVE_CURSE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 250
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_SIMPLE, ABILITY_UNAWARE, ABILITY_MOODY
// Level Up Moves: 14
