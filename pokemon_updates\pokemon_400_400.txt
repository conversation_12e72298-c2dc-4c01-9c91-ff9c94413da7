// POKEMON_400 (#400) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_400] =
    {
        .baseHP = 79,
        .baseAttack = 85,
        .baseDefense = 60,
        .baseSpAttack = 55,
        .baseSpDefense = 60,
        .baseSpeed = 71,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_WATER,
        .catchRate = 127,
        .expYield = 144,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_ORAN_BERRY,
        .item2 = ITEM_SITRUS_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_SIMPLE,
        .ability2 = ABILITY_UNAWARE,
        .abilityHidden = ABILITY_MOODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_400LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_AQUA_JET),
    LEVEL_UP_MOVE( 1, MOVE_ROTOTILLER),
    LEVEL_UP_MOVE( 5, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 9, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(13, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(18, MOVE_HYPER_FANG),
    LEVEL_UP_MOVE(23, MOVE_YAWN),
    LEVEL_UP_MOVE(28, MOVE_CRUNCH),
    LEVEL_UP_MOVE(33, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(38, MOVE_SUPER_FANG),
    LEVEL_UP_MOVE(43, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(48, MOVE_AMNESIA),
    LEVEL_UP_MOVE(53, MOVE_SUPERPOWER),
    LEVEL_UP_MOVE(58, MOVE_CURSE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 410
// Types: TYPE_NORMAL / TYPE_WATER
// Abilities: ABILITY_SIMPLE, ABILITY_UNAWARE, ABILITY_MOODY
// Level Up Moves: 17
