// POKEMON_401 (#401) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_401] =
    {
        .baseHP = 37,
        .baseAttack = 25,
        .baseDefense = 41,
        .baseSpAttack = 25,
        .baseSpDefense = 41,
        .baseSpeed = 25,
        .type1 = TYPE_BUG,
        .type2 = TYPE_BUG,
        .catchRate = 255,
        .expYield = 39,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_METRONOME,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_SHEDSKIN,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_RUNAWAY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_401LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_BIDE),
    LEVEL_UP_MOVE( 6, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE(16, MOVE_BUG_BITE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 194
// Types: TYPE_BUG / TYPE_BUG
// Abilities: ABILITY_SHEDSKIN, ABILITY_NONE, ABILITY_RUNAWAY
// Level Up Moves: 5
