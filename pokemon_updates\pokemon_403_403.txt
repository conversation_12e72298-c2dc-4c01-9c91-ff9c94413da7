// POKEMON_403 (#403) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_403] =
    {
        .baseHP = 45,
        .baseAttack = 65,
        .baseDefense = 34,
        .baseSpAttack = 40,
        .baseSpDefense = 34,
        .baseSpeed = 45,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 235,
        .expYield = 53,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_RIVALRY,
        .ability2 = ABILITY_INTIMIDATE,
        .abilityHidden = ABILITY_GUTS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_403LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 4, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 5, MOVE_LEER),
    LEVEL_UP_MOVE( 9, MOVE_CHARGE),
    LEVEL_UP_MOVE(11, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(13, MOVE_SPARK),
    LEVEL_UP_MOVE(17, MOVE_BITE),
    LEVEL_UP_MOVE(21, MOVE_ROAR),
    LEVEL_UP_MOVE(25, MOVE_SWAGGER),
    LEVEL_UP_MOVE(29, MOVE_THUNDER_FANG),
    LEVEL_UP_MOVE(33, MOVE_CRUNCH),
    LEVEL_UP_MOVE(37, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(41, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(45, MOVE_WILD_CHARGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 263
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_RIVALRY, ABILITY_INTIMIDATE, ABILITY_GUTS
// Level Up Moves: 14
