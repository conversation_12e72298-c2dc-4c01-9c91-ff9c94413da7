// POKEMON_404 (#404) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_404] =
    {
        .baseHP = 60,
        .baseAttack = 85,
        .baseDefense = 49,
        .baseSpAttack = 60,
        .baseSpDefense = 49,
        .baseSpeed = 60,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 120,
        .expYield = 127,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 100,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_RIVALRY,
        .ability2 = ABILITY_INTIMIDATE,
        .abilityHidden = ABILITY_GUTS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_404LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 9, MOVE_CHARGE),
    LEVEL_UP_MOVE(13, MOVE_SPARK),
    LEVEL_UP_MOVE(18, MOVE_BITE),
    LEVEL_UP_MOVE(23, MOVE_ROAR),
    LEVEL_UP_MOVE(28, MOVE_SWAGGER),
    LEVEL_UP_MOVE(33, MOVE_THUNDER_FANG),
    LEVEL_UP_MOVE(38, MOVE_CRUNCH),
    LEVEL_UP_MOVE(43, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(48, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(53, MOVE_WILD_CHARGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 363
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_RIVALRY, ABILITY_INTIMIDATE, ABILITY_GUTS
// Level Up Moves: 13
