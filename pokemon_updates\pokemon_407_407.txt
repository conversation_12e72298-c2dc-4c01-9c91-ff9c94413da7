// POKEMON_407 (#407) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_407] =
    {
        .baseHP = 60,
        .baseAttack = 70,
        .baseDefense = 65,
        .baseSpAttack = 125,
        .baseSpDefense = 105,
        .baseSpeed = 90,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_POISON,
        .catchRate = 75,
        .expYield = 258,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 3,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_POISON_BARB,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FAIRY,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_NATURALCURE,
        .ability2 = ABILITY_POISONPOINT,
        .abilityHidden = ABILITY_TECHNICIAN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_407LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POISON_STING),
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE( 1, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 1, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE( 1, MOVE_PETAL_DANCE),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE( 1, MOVE_INGRAIN),
    LEVEL_UP_MOVE( 1, MOVE_WEATHER_BALL),
    LEVEL_UP_MOVE( 1, MOVE_AROMATHERAPY),
    LEVEL_UP_MOVE( 1, MOVE_MAGICAL_LEAF),
    LEVEL_UP_MOVE( 1, MOVE_TOXIC_SPIKES),
    LEVEL_UP_MOVE( 1, MOVE_PETAL_BLIZZARD),
    LEVEL_UP_MOVE( 1, MOVE_GRASSY_TERRAIN),
    LEVEL_UP_MOVE( 1, MOVE_VENOM_DRENCH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 515
// Types: TYPE_GRASS / TYPE_POISON
// Abilities: ABILITY_NATURALCURE, ABILITY_POISONPOINT, ABILITY_TECHNICIAN
// Level Up Moves: 16
