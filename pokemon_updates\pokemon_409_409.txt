// POKEMON_409 (#409) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_409] =
    {
        .baseHP = 97,
        .baseAttack = 165,
        .baseDefense = 60,
        .baseSpAttack = 65,
        .baseSpDefense = 50,
        .baseSpeed = 58,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_ROCK,
        .catchRate = 45,
        .expYield = 173,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 30,
        .friendship = 70,
        .growthRate = GROWTH_ERRATIC,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_MONSTER,
        .ability1 = ABILITY_MOLDBREAKER,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_SHEERFORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_409LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE( 1, MOVE_HEADBUTT),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE( 1, MOVE_PURSUIT),
    LEVEL_UP_MOVE(10, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE(15, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(19, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(24, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(28, MOVE_SLAM),
    LEVEL_UP_MOVE(28, MOVE_CHIP_AWAY),
    LEVEL_UP_MOVE(36, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(43, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(51, MOVE_SCREECH),
    LEVEL_UP_MOVE(58, MOVE_HEAD_SMASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 495
// Types: TYPE_ROCK / TYPE_ROCK
// Abilities: ABILITY_MOLDBREAKER, ABILITY_NONE, ABILITY_SHEERFORCE
// Level Up Moves: 15
