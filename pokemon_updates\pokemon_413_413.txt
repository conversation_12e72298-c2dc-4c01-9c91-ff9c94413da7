// POKEMON_413 (#413) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_413] =
    {
        .baseHP = 60,
        .baseAttack = 59,
        .baseDefense = 85,
        .baseSpAttack = 79,
        .baseSpDefense = 105,
        .baseSpeed = 36,
        .type1 = TYPE_BUG,
        .type2 = TYPE_GRASS,
        .catchRate = 45,
        .expYield = 148,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 2,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SILVER_POWDER,
        .genderRatio = PERCENT_FEMALE(100),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_ANTICIPATION,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_OVERCOAT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_413LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_QUIVER_DANCE),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_PROTECT),
    LEVEL_UP_MOVE( 1, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(20, MOVE_HIDDEN_POWER),
    LEVEL_UP_MOVE(23, MOVE_CONFUSION),
    LEVEL_UP_MOVE(26, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(29, MOVE_GROWTH),
    LEVEL_UP_MOVE(32, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(35, MOVE_CAPTIVATE),
    LEVEL_UP_MOVE(38, MOVE_FLAIL),
    LEVEL_UP_MOVE(41, MOVE_ATTRACT),
    LEVEL_UP_MOVE(44, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(47, MOVE_LEAF_STORM),
    LEVEL_UP_MOVE(50, MOVE_BUG_BUZZ),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 424
// Types: TYPE_BUG / TYPE_GRASS
// Abilities: ABILITY_ANTICIPATION, ABILITY_NONE, ABILITY_OVERCOAT
// Level Up Moves: 16
