// POKEMON_414 (#414) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_414] =
    {
        .baseHP = 70,
        .baseAttack = 94,
        .baseDefense = 50,
        .baseSpAttack = 94,
        .baseSpDefense = 50,
        .baseSpeed = 66,
        .type1 = TYPE_BUG,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 148,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SILVER_POWDER,
        .genderRatio = PERCENT_FEMALE(0),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_SWARM,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_TINTEDLENS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_414LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_QUIVER_DANCE),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_PROTECT),
    LEVEL_UP_MOVE( 1, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(20, MOVE_HIDDEN_POWER),
    LEVEL_UP_MOVE(23, MOVE_CONFUSION),
    LEVEL_UP_MOVE(26, MOVE_GUST),
    LEVEL_UP_MOVE(29, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE(32, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(35, MOVE_CAMOUFLAGE),
    LEVEL_UP_MOVE(38, MOVE_SILVER_WIND),
    LEVEL_UP_MOVE(41, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(44, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(47, MOVE_LUNGE),
    LEVEL_UP_MOVE(50, MOVE_BUG_BUZZ),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 424
// Types: TYPE_BUG / TYPE_FLYING
// Abilities: ABILITY_SWARM, ABILITY_NONE, ABILITY_TINTEDLENS
// Level Up Moves: 15
