// POKEMON_415 (#415) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_415] =
    {
        .baseHP = 30,
        .baseAttack = 30,
        .baseDefense = 42,
        .baseSpAttack = 30,
        .baseSpDefense = 42,
        .baseSpeed = 70,
        .type1 = TYPE_BUG,
        .type2 = TYPE_FLYING,
        .catchRate = 120,
        .expYield = 49,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_HONEY,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_HONEYGATHER,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_HUSTLE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_415LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE( 1, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE(13, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(29, MOVE_BUG_BUZZ),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 244
// Types: TYPE_BUG / TYPE_FLYING
// Abilities: ABILITY_HONEYGATHER, ABILITY_NONE, ABILITY_HUSTLE
// Level Up Moves: 5
