// POKEMON_417 (#417) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_417] =
    {
        .baseHP = 60,
        .baseAttack = 45,
        .baseDefense = 70,
        .baseSpAttack = 45,
        .baseSpDefense = 90,
        .baseSpeed = 95,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 200,
        .expYield = 142,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 10,
        .friendship = 100,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_FAIRY,
        .ability1 = ABILITY_RUNAWAY,
        .ability2 = ABILITY_PICKUP,
        .abilityHidden = ABILITY_VOLTABSORB,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_417LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 1, MOVE_BIDE),
    LEVEL_UP_MOVE( 5, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 9, MOVE_CHARM),
    LEVEL_UP_MOVE(13, MOVE_SPARK),
    LEVEL_UP_MOVE(17, MOVE_ENDURE),
    LEVEL_UP_MOVE(19, MOVE_NUZZLE),
    LEVEL_UP_MOVE(21, MOVE_SWIFT),
    LEVEL_UP_MOVE(25, MOVE_ELECTRO_BALL),
    LEVEL_UP_MOVE(29, MOVE_SWEET_KISS),
    LEVEL_UP_MOVE(33, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE(37, MOVE_SUPER_FANG),
    LEVEL_UP_MOVE(41, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(45, MOVE_LAST_RESORT),
    LEVEL_UP_MOVE(49, MOVE_HYPER_FANG),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 405
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_RUNAWAY, ABILITY_PICKUP, ABILITY_VOLTABSORB
// Level Up Moves: 16
