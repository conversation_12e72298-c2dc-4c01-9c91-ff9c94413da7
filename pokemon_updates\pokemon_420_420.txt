// POKEMON_420 (#420) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_420] =
    {
        .baseHP = 45,
        .baseAttack = 35,
        .baseDefense = 45,
        .baseSpAttack = 62,
        .baseSpDefense = 53,
        .baseSpeed = 35,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 190,
        .expYield = 55,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_MIRACLE_SEED,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_FAIRY,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_CHLOROPHYLL,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_420LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_MORNING_SUN),
    LEVEL_UP_MOVE( 5, MOVE_LEAFAGE),
    LEVEL_UP_MOVE( 7, MOVE_GROWTH),
    LEVEL_UP_MOVE(10, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(13, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(19, MOVE_MAGICAL_LEAF),
    LEVEL_UP_MOVE(22, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE(28, MOVE_WORRY_SEED),
    LEVEL_UP_MOVE(31, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(37, MOVE_SOLAR_BEAM),
    LEVEL_UP_MOVE(40, MOVE_LUCKY_CHANT),
    LEVEL_UP_MOVE(47, MOVE_PETAL_BLIZZARD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 275
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_CHLOROPHYLL, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 13
