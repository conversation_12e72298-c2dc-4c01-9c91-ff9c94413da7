// POKEMON_428 (#428) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_428] =
    {
        .baseHP = 65,
        .baseAttack = 76,
        .baseDefense = 84,
        .baseSpAttack = 54,
        .baseSpDefense = 96,
        .baseSpeed = 105,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 60,
        .expYield = 168,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_PECHA_BERRY,
        .item2 = ITEM_CHOPLE_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 140,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_CUTECHARM,
        .ability2 = ABILITY_KLUTZ,
        .abilityHidden = ABILITY_LIMBER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_428LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_RETURN),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_SPLASH),
    LEVEL_UP_MOVE( 1, MOVE_FORESIGHT),
    LEVEL_UP_MOVE( 1, MOVE_MIRROR_COAT),
    LEVEL_UP_MOVE( 1, MOVE_MAGIC_COAT),
    LEVEL_UP_MOVE( 1, MOVE_BOUNCE),
    LEVEL_UP_MOVE( 1, MOVE_HEALING_WISH),
    LEVEL_UP_MOVE( 1, MOVE_ROTOTILLER),
    LEVEL_UP_MOVE( 6, MOVE_ENDURE),
    LEVEL_UP_MOVE(13, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(16, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(20, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(23, MOVE_JUMP_KICK),
    LEVEL_UP_MOVE(26, MOVE_BATON_PASS),
    LEVEL_UP_MOVE(32, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(33, MOVE_AGILITY),
    LEVEL_UP_MOVE(36, MOVE_DIZZY_PUNCH),
    LEVEL_UP_MOVE(43, MOVE_AFTER_YOU),
    LEVEL_UP_MOVE(44, MOVE_FLATTER),
    LEVEL_UP_MOVE(46, MOVE_CHARM),
    LEVEL_UP_MOVE(53, MOVE_ENTRAINMENT),
    LEVEL_UP_MOVE(66, MOVE_HIGH_JUMP_KICK),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 480
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_CUTECHARM, ABILITY_KLUTZ, ABILITY_LIMBER
// Level Up Moves: 24
