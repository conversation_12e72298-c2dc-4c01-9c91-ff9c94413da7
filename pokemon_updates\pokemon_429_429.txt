// POKEMON_429 (#429) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_429] =
    {
        .baseHP = 60,
        .baseAttack = 60,
        .baseDefense = 60,
        .baseSpAttack = 105,
        .baseSpDefense = 105,
        .baseSpeed = 105,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_GHOST,
        .catchRate = 45,
        .expYield = 173,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 25,
        .friendship = 35,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_INDETERMINATE,
        .eggGroup2 = EGG_GROUP_INDETERMINATE,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_429LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_PSYWAVE),
    LEVEL_UP_MOVE( 1, MOVE_SPITE),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_MAGICAL_LEAF),
    LEVEL_UP_MOVE( 1, MOVE_LUCKY_CHANT),
    LEVEL_UP_MOVE( 1, MOVE_POWER_GEM),
    LEVEL_UP_MOVE( 1, MOVE_PHANTOM_FORCE),
    LEVEL_UP_MOVE( 1, MOVE_MYSTICAL_FIRE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 495
// Types: TYPE_GHOST / TYPE_GHOST
// Abilities: ABILITY_LEVITATE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 9
