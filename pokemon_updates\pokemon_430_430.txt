// POKEMON_430 (#430) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_430] =
    {
        .baseHP = 100,
        .baseAttack = 125,
        .baseDefense = 52,
        .baseSpAttack = 105,
        .baseSpDefense = 52,
        .baseSpeed = 71,
        .type1 = TYPE_DARK,
        .type2 = TYPE_FLYING,
        .catchRate = 30,
        .expYield = 177,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_INSOMNIA,
        .ability2 = ABILITY_SUPERLUCK,
        .abilityHidden = ABILITY_MOXIE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_430LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_HAZE),
    LEVEL_UP_MOVE( 1, MOVE_PURSUIT),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(25, MOVE_SWAGGER),
    LEVEL_UP_MOVE(35, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(45, MOVE_FOUL_PLAY),
    LEVEL_UP_MOVE(65, MOVE_QUASH),
    LEVEL_UP_MOVE(65, MOVE_COMEUPPANCE),
    LEVEL_UP_MOVE(75, MOVE_DARK_PULSE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 505
// Types: TYPE_DARK / TYPE_FLYING
// Abilities: ABILITY_INSOMNIA, ABILITY_SUPERLUCK, ABILITY_MOXIE
// Level Up Moves: 12
