// POKEMON_433 (#433) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_433] =
    {
        .baseHP = 45,
        .baseAttack = 30,
        .baseDefense = 50,
        .baseSpAttack = 65,
        .baseSpDefense = 50,
        .baseSpeed = 45,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 120,
        .expYield = 57,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_CLEANSE_TAG,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 25,
        .friendship = 70,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_433LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WRAP),
    LEVEL_UP_MOVE( 4, MOVE_GROWL),
    LEVEL_UP_MOVE( 7, MOVE_ASTONISH),
    LEVEL_UP_MOVE(10, MOVE_CONFUSION),
    LEVEL_UP_MOVE(13, MOVE_YAWN),
    LEVEL_UP_MOVE(16, MOVE_LAST_RESORT),
    LEVEL_UP_MOVE(19, MOVE_ENTRAINMENT),
    LEVEL_UP_MOVE(32, MOVE_UPROAR),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 285
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_LEVITATE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 8
