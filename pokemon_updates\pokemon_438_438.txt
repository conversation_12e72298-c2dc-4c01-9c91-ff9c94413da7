// POKEMON_438 (#438) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_438] =
    {
        .baseHP = 50,
        .baseAttack = 80,
        .baseDefense = 95,
        .baseSpAttack = 10,
        .baseSpDefense = 45,
        .baseSpeed = 10,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_ROCK,
        .catchRate = 255,
        .expYield = 58,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_STURDY,
        .ability2 = ABILITY_ROCKHEAD,
        .abilityHidden = ABILITY_RATTLED,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_438LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_FAKE_TEARS),
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE( 5, MOVE_FLAIL),
    LEVEL_UP_MOVE( 8, MOVE_LOW_KICK),
    LEVEL_UP_MOVE(12, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE(15, MOVE_MIMIC),
    LEVEL_UP_MOVE(19, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE(22, MOVE_TEARFUL_LOOK),
    LEVEL_UP_MOVE(26, MOVE_ROCK_TOMB),
    LEVEL_UP_MOVE(29, MOVE_BLOCK),
    LEVEL_UP_MOVE(33, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(36, MOVE_COUNTER),
    LEVEL_UP_MOVE(40, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(43, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 290
// Types: TYPE_ROCK / TYPE_ROCK
// Abilities: ABILITY_STURDY, ABILITY_ROCKHEAD, ABILITY_RATTLED
// Level Up Moves: 14
