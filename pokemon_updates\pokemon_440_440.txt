// POKEMON_440 (#440) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_440] =
    {
        .baseHP = 100,
        .baseAttack = 5,
        .baseDefense = 5,
        .baseSpAttack = 15,
        .baseSpDefense = 65,
        .baseSpeed = 30,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 130,
        .expYield = 110,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_OVAL_STONE,
        .item2 = ITEM_LUCKY_EGG,
        .genderRatio = PERCENT_FEMALE(100),
        .eggCycles = 40,
        .friendship = 140,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_NATURALCURE,
        .ability2 = ABILITY_SERENEGRACE,
        .abilityHidden = ABILITY_FRIENDGUARD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_440LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_MINIMIZE),
    LEVEL_UP_MOVE( 1, MOVE_CHARM),
    LEVEL_UP_MOVE( 4, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 5, MOVE_COPYCAT),
    LEVEL_UP_MOVE( 9, MOVE_REFRESH),
    LEVEL_UP_MOVE(12, MOVE_SWEET_KISS),
    LEVEL_UP_MOVE(12, MOVE_DISARMING_VOICE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 220
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_NATURALCURE, ABILITY_SERENEGRACE, ABILITY_FRIENDGUARD
// Level Up Moves: 8
