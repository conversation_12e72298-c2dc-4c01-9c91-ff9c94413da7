// POKEMON_441 (#441) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_441] =
    {
        .baseHP = 76,
        .baseAttack = 65,
        .baseDefense = 45,
        .baseSpAttack = 92,
        .baseSpDefense = 42,
        .baseSpeed = 91,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 30,
        .expYield = 144,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_METRONOME,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_KEENEYE,
        .ability2 = ABILITY_TANGLEDFEET,
        .abilityHidden = ABILITY_BIGPECKS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_441LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_TAUNT),
    LEVEL_UP_MOVE( 1, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE( 1, MOVE_CHATTER),
    LEVEL_UP_MOVE( 1, MOVE_CONFIDE),
    LEVEL_UP_MOVE( 5, MOVE_GROWL),
    LEVEL_UP_MOVE( 9, MOVE_MIRROR_MOVE),
    LEVEL_UP_MOVE(13, MOVE_SING),
    LEVEL_UP_MOVE(17, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(29, MOVE_ROUND),
    LEVEL_UP_MOVE(33, MOVE_MIMIC),
    LEVEL_UP_MOVE(37, MOVE_ECHOED_VOICE),
    LEVEL_UP_MOVE(41, MOVE_ROOST),
    LEVEL_UP_MOVE(45, MOVE_UPROAR),
    LEVEL_UP_MOVE(49, MOVE_SYNCHRONOISE),
    LEVEL_UP_MOVE(50, MOVE_FEATHER_DANCE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 411
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_KEENEYE, ABILITY_TANGLEDFEET, ABILITY_BIGPECKS
// Level Up Moves: 16
