// POKEMON_442 (#442) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_442] =
    {
        .baseHP = 50,
        .baseAttack = 92,
        .baseDefense = 108,
        .baseSpAttack = 92,
        .baseSpDefense = 108,
        .baseSpeed = 35,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_DARK,
        .catchRate = 100,
        .expYield = 170,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_INDETERMINATE,
        .eggGroup2 = EGG_GROUP_INDETERMINATE,
        .ability1 = ABILITY_PRESSURE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_INFILTRATOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_442LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_NIGHT_SHADE),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE( 1, MOVE_CURSE),
    LEVEL_UP_MOVE( 1, MOVE_SPITE),
    LEVEL_UP_MOVE( 1, MOVE_PURSUIT),
    LEVEL_UP_MOVE( 1, MOVE_SHADOW_SNEAK),
    LEVEL_UP_MOVE( 7, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE(13, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE(15, MOVE_PAYBACK),
    LEVEL_UP_MOVE(19, MOVE_DREAM_EATER),
    LEVEL_UP_MOVE(25, MOVE_OMINOUS_WIND),
    LEVEL_UP_MOVE(25, MOVE_HEX),
    LEVEL_UP_MOVE(31, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(37, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(43, MOVE_MEMENTO),
    LEVEL_UP_MOVE(49, MOVE_DARK_PULSE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 485
// Types: TYPE_GHOST / TYPE_DARK
// Abilities: ABILITY_PRESSURE, ABILITY_NONE, ABILITY_INFILTRATOR
// Level Up Moves: 16
