// POKEMON_444 (#444) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_444] =
    {
        .baseHP = 68,
        .baseAttack = 90,
        .baseDefense = 65,
        .baseSpAttack = 50,
        .baseSpDefense = 55,
        .baseSpeed = 82,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_GROUND,
        .catchRate = 45,
        .expYield = 144,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_HABAN_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 40,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_SANDVEIL,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_ROUGHSKIN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_444LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_DUAL_CHOP),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_RAGE),
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(13, MOVE_SANDSTORM),
    LEVEL_UP_MOVE(15, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(19, MOVE_SAND_TOMB),
    LEVEL_UP_MOVE(27, MOVE_BITE),
    LEVEL_UP_MOVE(28, MOVE_SLASH),
    LEVEL_UP_MOVE(33, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(40, MOVE_DIG),
    LEVEL_UP_MOVE(49, MOVE_DRAGON_RUSH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 410
// Types: TYPE_DRAGON / TYPE_GROUND
// Abilities: ABILITY_SANDVEIL, ABILITY_NONE, ABILITY_ROUGHSKIN
// Level Up Moves: 13
