// POKEMON_447 (#447) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_447] =
    {
        .baseHP = 40,
        .baseAttack = 70,
        .baseDefense = 40,
        .baseSpAttack = 35,
        .baseSpDefense = 40,
        .baseSpeed = 60,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 75,
        .expYield = 57,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_STEADFAST,
        .ability2 = ABILITY_INNERFOCUS,
        .abilityHidden = ABILITY_PRANKSTER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_447LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_FORESIGHT),
    LEVEL_UP_MOVE( 1, MOVE_ENDURE),
    LEVEL_UP_MOVE( 6, MOVE_COUNTER),
    LEVEL_UP_MOVE( 8, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(11, MOVE_FEINT),
    LEVEL_UP_MOVE(15, MOVE_FORCE_PALM),
    LEVEL_UP_MOVE(19, MOVE_COPYCAT),
    LEVEL_UP_MOVE(20, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE(24, MOVE_SCREECH),
    LEVEL_UP_MOVE(29, MOVE_REVERSAL),
    LEVEL_UP_MOVE(32, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(47, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(50, MOVE_FINAL_GAMBIT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 285
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_STEADFAST, ABILITY_INNERFOCUS, ABILITY_PRANKSTER
// Level Up Moves: 14
