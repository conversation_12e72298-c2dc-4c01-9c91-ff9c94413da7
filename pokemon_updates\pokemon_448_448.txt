// POKEMON_448 (#448) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_448] =
    {
        .baseHP = 70,
        .baseAttack = 110,
        .baseDefense = 70,
        .baseSpAttack = 115,
        .baseSpDefense = 70,
        .baseSpeed = 90,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_STEEL,
        .catchRate = 45,
        .expYield = 184,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_STEADFAST,
        .ability2 = ABILITY_INNERFOCUS,
        .abilityHidden = ABILITY_JUSTIFIED,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_448LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_AURA_SPHERE),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_SCREECH),
    LEVEL_UP_MOVE( 1, MOVE_REVERSAL),
    LEVEL_UP_MOVE( 1, MOVE_FORESIGHT),
    LEVEL_UP_MOVE( 1, MOVE_DETECT),
    LEVEL_UP_MOVE( 1, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE( 1, MOVE_FORCE_PALM),
    LEVEL_UP_MOVE( 1, MOVE_VACUUM_WAVE),
    LEVEL_UP_MOVE( 1, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE( 1, MOVE_FINAL_GAMBIT),
    LEVEL_UP_MOVE( 1, MOVE_LASER_FOCUS),
    LEVEL_UP_MOVE( 1, MOVE_LIFE_DEW),
    LEVEL_UP_MOVE( 6, MOVE_COUNTER),
    LEVEL_UP_MOVE(11, MOVE_FEINT),
    LEVEL_UP_MOVE(15, MOVE_POWER_UP_PUNCH),
    LEVEL_UP_MOVE(19, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(24, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(29, MOVE_BONE_RUSH),
    LEVEL_UP_MOVE(33, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(37, MOVE_ME_FIRST),
    LEVEL_UP_MOVE(42, MOVE_WORK_UP),
    LEVEL_UP_MOVE(47, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(48, MOVE_METEOR_MASH),
    LEVEL_UP_MOVE(51, MOVE_HEAL_PULSE),
    LEVEL_UP_MOVE(55, MOVE_CLOSE_COMBAT),
    LEVEL_UP_MOVE(60, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(65, MOVE_EXTREME_SPEED),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 525
// Types: TYPE_FIGHTING / TYPE_STEEL
// Abilities: ABILITY_STEADFAST, ABILITY_INNERFOCUS, ABILITY_JUSTIFIED
// Level Up Moves: 30
