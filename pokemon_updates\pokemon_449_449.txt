// POKEMON_449 (#449) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_449] =
    {
        .baseHP = 68,
        .baseAttack = 72,
        .baseDefense = 78,
        .baseSpAttack = 38,
        .baseSpDefense = 42,
        .baseSpeed = 32,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_GROUND,
        .catchRate = 140,
        .expYield = 66,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_SANDSTREAM,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_SANDFORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_449LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 7, MOVE_BITE),
    LEVEL_UP_MOVE(13, MOVE_YAWN),
    LEVEL_UP_MOVE(19, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(19, MOVE_DIG),
    LEVEL_UP_MOVE(25, MOVE_SAND_TOMB),
    LEVEL_UP_MOVE(31, MOVE_CRUNCH),
    LEVEL_UP_MOVE(37, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(44, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(50, MOVE_FISSURE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 330
// Types: TYPE_GROUND / TYPE_GROUND
// Abilities: ABILITY_SANDSTREAM, ABILITY_NONE, ABILITY_SANDFORCE
// Level Up Moves: 11
