// POKEMON_450 (#450) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_450] =
    {
        .baseHP = 108,
        .baseAttack = 112,
        .baseDefense = 118,
        .baseSpAttack = 68,
        .baseSpDefense = 72,
        .baseSpeed = 47,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_GROUND,
        .catchRate = 60,
        .expYield = 184,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_SANDSTREAM,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_SANDFORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_450LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_YAWN),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_FANG),
    LEVEL_UP_MOVE( 1, MOVE_ICE_FANG),
    LEVEL_UP_MOVE( 1, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE(19, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(19, MOVE_DIG),
    LEVEL_UP_MOVE(25, MOVE_SAND_TOMB),
    LEVEL_UP_MOVE(31, MOVE_CRUNCH),
    LEVEL_UP_MOVE(40, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(50, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(60, MOVE_FISSURE),
    LEVEL_UP_MOVE(62, MOVE_SLACK_OFF),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 525
// Types: TYPE_GROUND / TYPE_GROUND
// Abilities: ABILITY_SANDSTREAM, ABILITY_NONE, ABILITY_SANDFORCE
// Level Up Moves: 15
