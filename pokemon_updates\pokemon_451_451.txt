// POKEMON_451 (#451) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_451] =
    {
        .baseHP = 40,
        .baseAttack = 50,
        .baseDefense = 90,
        .baseSpAttack = 30,
        .baseSpDefense = 55,
        .baseSpeed = 65,
        .type1 = TYPE_POISON,
        .type2 = TYPE_BUG,
        .catchRate = 120,
        .expYield = 66,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_POISON_BARB,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_WATER_3,
        .ability1 = ABILITY_BATTLEARMOR,
        .ability2 = ABILITY_SNIPER,
        .abilityHidden = ABILITY_KEENEYE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_451LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POISON_STING),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 5, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE( 9, MOVE_PIN_MISSILE),
    LEVEL_UP_MOVE(13, MOVE_ACUPRESSURE),
    LEVEL_UP_MOVE(16, MOVE_PURSUIT),
    LEVEL_UP_MOVE(20, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(23, MOVE_POISON_FANG),
    LEVEL_UP_MOVE(27, MOVE_VENOSHOCK),
    LEVEL_UP_MOVE(30, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(34, MOVE_TOXIC_SPIKES),
    LEVEL_UP_MOVE(38, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(41, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(45, MOVE_CRUNCH),
    LEVEL_UP_MOVE(47, MOVE_FELL_STINGER),
    LEVEL_UP_MOVE(49, MOVE_CROSS_POISON),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 330
// Types: TYPE_POISON / TYPE_BUG
// Abilities: ABILITY_BATTLEARMOR, ABILITY_SNIPER, ABILITY_KEENEYE
// Level Up Moves: 17
