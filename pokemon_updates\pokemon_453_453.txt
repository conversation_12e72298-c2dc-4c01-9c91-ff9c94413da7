// POKEMON_453 (#453) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_453] =
    {
        .baseHP = 48,
        .baseAttack = 61,
        .baseDefense = 40,
        .baseSpAttack = 61,
        .baseSpDefense = 40,
        .baseSpeed = 50,
        .type1 = TYPE_POISON,
        .type2 = TYPE_FIGHTING,
        .catchRate = 140,
        .expYield = 60,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_BLACK_SLUDGE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 10,
        .friendship = 100,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_HUMANSHAPE,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_ANTICIPATION,
        .ability2 = ABILITY_DRYSKIN,
        .abilityHidden = ABILITY_POISONTOUCH,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_453LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 3, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 8, MOVE_POISON_STING),
    LEVEL_UP_MOVE(10, MOVE_TAUNT),
    LEVEL_UP_MOVE(15, MOVE_PURSUIT),
    LEVEL_UP_MOVE(17, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE(22, MOVE_REVENGE),
    LEVEL_UP_MOVE(24, MOVE_SWAGGER),
    LEVEL_UP_MOVE(29, MOVE_MUD_BOMB),
    LEVEL_UP_MOVE(31, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(36, MOVE_VENOSHOCK),
    LEVEL_UP_MOVE(38, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(43, MOVE_POISON_JAB),
    LEVEL_UP_MOVE(45, MOVE_SLUDGE_BOMB),
    LEVEL_UP_MOVE(47, MOVE_BELCH),
    LEVEL_UP_MOVE(50, MOVE_FLATTER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 300
// Types: TYPE_POISON / TYPE_FIGHTING
// Abilities: ABILITY_ANTICIPATION, ABILITY_DRYSKIN, ABILITY_POISONTOUCH
// Level Up Moves: 16
