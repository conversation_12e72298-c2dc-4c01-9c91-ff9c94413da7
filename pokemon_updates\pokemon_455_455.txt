// POKEMON_455 (#455) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_455] =
    {
        .baseHP = 74,
        .baseAttack = 100,
        .baseDefense = 72,
        .baseSpAttack = 90,
        .baseSpDefense = 72,
        .baseSpeed = 46,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 200,
        .expYield = 159,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 25,
        .friendship = 70,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_455LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BIND),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 7, MOVE_BITE),
    LEVEL_UP_MOVE(11, MOVE_VINE_WHIP),
    LEVEL_UP_MOVE(17, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(21, MOVE_INGRAIN),
    LEVEL_UP_MOVE(27, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE(31, MOVE_LEAF_TORNADO),
    LEVEL_UP_MOVE(37, MOVE_STOCKPILE),
    LEVEL_UP_MOVE(37, MOVE_SPIT_UP),
    LEVEL_UP_MOVE(37, MOVE_SWALLOW),
    LEVEL_UP_MOVE(41, MOVE_CRUNCH),
    LEVEL_UP_MOVE(47, MOVE_WRING_OUT),
    LEVEL_UP_MOVE(50, MOVE_POWER_WHIP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 454
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_LEVITATE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
