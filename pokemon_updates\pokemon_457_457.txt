// POKEMON_457 (#457) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_457] =
    {
        .baseHP = 69,
        .baseAttack = 69,
        .baseDefense = 76,
        .baseSpAttack = 69,
        .baseSpDefense = 86,
        .baseSpeed = 91,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 75,
        .expYield = 161,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_RINDO_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_ERRATIC,
        .eggGroup1 = EGG_GROUP_WATER_2,
        .eggGroup2 = EGG_GROUP_WATER_2,
        .ability1 = ABILITY_SWIFTSWIM,
        .ability2 = ABILITY_STORMDRAIN,
        .abilityHidden = ABILITY_WATERVEIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_457LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_ATTRACT),
    LEVEL_UP_MOVE( 1, MOVE_SOAK),
    LEVEL_UP_MOVE(13, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(22, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(26, MOVE_CAPTIVATE),
    LEVEL_UP_MOVE(29, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(35, MOVE_AQUA_RING),
    LEVEL_UP_MOVE(42, MOVE_WHIRLPOOL),
    LEVEL_UP_MOVE(48, MOVE_U_TURN),
    LEVEL_UP_MOVE(53, MOVE_BOUNCE),
    LEVEL_UP_MOVE(59, MOVE_SILVER_WIND),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 460
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_SWIFTSWIM, ABILITY_STORMDRAIN, ABILITY_WATERVEIL
// Level Up Moves: 14
