// POKEMON_458 (#458) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_458] =
    {
        .baseHP = 45,
        .baseAttack = 20,
        .baseDefense = 50,
        .baseSpAttack = 60,
        .baseSpDefense = 120,
        .baseSpeed = 50,
        .type1 = TYPE_WATER,
        .type2 = TYPE_FLYING,
        .catchRate = 25,
        .expYield = 69,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_SWIFTSWIM,
        .ability2 = ABILITY_WATERABSORB,
        .abilityHidden = ABILITY_WATERVEIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_458LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_BUBBLE),
    LEVEL_UP_MOVE( 3, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE( 7, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(11, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(14, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(16, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(19, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(23, MOVE_WIDE_GUARD),
    LEVEL_UP_MOVE(27, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(32, MOVE_AGILITY),
    LEVEL_UP_MOVE(36, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(39, MOVE_AQUA_RING),
    LEVEL_UP_MOVE(46, MOVE_BOUNCE),
    LEVEL_UP_MOVE(49, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 345
// Types: TYPE_WATER / TYPE_FLYING
// Abilities: ABILITY_SWIFTSWIM, ABILITY_WATERABSORB, ABILITY_WATERVEIL
// Level Up Moves: 16
