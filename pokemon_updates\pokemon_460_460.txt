// POKEMON_460 (#460) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_460] =
    {
        .baseHP = 90,
        .baseAttack = 92,
        .baseDefense = 75,
        .baseSpAttack = 92,
        .baseSpDefense = 85,
        .baseSpeed = 60,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_ICE,
        .catchRate = 60,
        .expYield = 173,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NEVER_MELT_ICE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_SNOWWARNING,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_SOUNDPROOF,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_460LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ICE_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE( 1, MOVE_POWDER_SNOW),
    LEVEL_UP_MOVE( 1, MOVE_ICY_WIND),
    LEVEL_UP_MOVE( 1, MOVE_LEAFAGE),
    LEVEL_UP_MOVE( 1, MOVE_AURORA_VEIL),
    LEVEL_UP_MOVE(13, MOVE_GRASS_WHISTLE),
    LEVEL_UP_MOVE(17, MOVE_SWAGGER),
    LEVEL_UP_MOVE(21, MOVE_MIST),
    LEVEL_UP_MOVE(26, MOVE_ICE_SHARD),
    LEVEL_UP_MOVE(31, MOVE_INGRAIN),
    LEVEL_UP_MOVE(36, MOVE_WOOD_HAMMER),
    LEVEL_UP_MOVE(47, MOVE_BLIZZARD),
    LEVEL_UP_MOVE(58, MOVE_SHEER_COLD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 494
// Types: TYPE_GRASS / TYPE_ICE
// Abilities: ABILITY_SNOWWARNING, ABILITY_NONE, ABILITY_SOUNDPROOF
// Level Up Moves: 15
