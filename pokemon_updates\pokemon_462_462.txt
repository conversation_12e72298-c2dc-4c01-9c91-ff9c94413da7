// POKEMON_462 (#462) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_462] =
    {
        .baseHP = 70,
        .baseAttack = 70,
        .baseDefense = 115,
        .baseSpAttack = 130,
        .baseSpDefense = 90,
        .baseSpeed = 60,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_STEEL,
        .catchRate = 30,
        .expYield = 268,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 3,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_METAL_COAT,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_MAGNETPULL,
        .ability2 = ABILITY_STURDY,
        .abilityHidden = ABILITY_ANALYTIC,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_462LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE( 1, MOVE_BARRIER),
    LEVEL_UP_MOVE( 1, MOVE_TRI_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_ZAP_CANNON),
    LEVEL_UP_MOVE( 1, MOVE_MIRROR_COAT),
    LEVEL_UP_MOVE( 1, MOVE_MAGNETIC_FLUX),
    LEVEL_UP_MOVE( 1, MOVE_ELECTRIC_TERRAIN),
    LEVEL_UP_MOVE(11, MOVE_MAGNET_BOMB),
    LEVEL_UP_MOVE(13, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(17, MOVE_SONIC_BOOM),
    LEVEL_UP_MOVE(19, MOVE_SPARK),
    LEVEL_UP_MOVE(23, MOVE_MIRROR_SHOT),
    LEVEL_UP_MOVE(25, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(29, MOVE_ELECTRO_BALL),
    LEVEL_UP_MOVE(33, MOVE_FLASH_CANNON),
    LEVEL_UP_MOVE(39, MOVE_SCREECH),
    LEVEL_UP_MOVE(43, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(49, MOVE_LOCK_ON),
    LEVEL_UP_MOVE(53, MOVE_MAGNET_RISE),
    LEVEL_UP_MOVE(59, MOVE_GYRO_BALL),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 535
// Types: TYPE_ELECTRIC / TYPE_STEEL
// Abilities: ABILITY_MAGNETPULL, ABILITY_STURDY, ABILITY_ANALYTIC
// Level Up Moves: 23
