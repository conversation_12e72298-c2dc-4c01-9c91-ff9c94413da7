// POKEMON_469 (#469) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_469] =
    {
        .baseHP = 86,
        .baseAttack = 76,
        .baseDefense = 86,
        .baseSpAttack = 116,
        .baseSpDefense = 56,
        .baseSpeed = 95,
        .type1 = TYPE_BUG,
        .type2 = TYPE_FLYING,
        .catchRate = 30,
        .expYield = 180,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_WIDE_LENS,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_SPEEDBOOST,
        .ability2 = ABILITY_TINTEDLENS,
        .abilityHidden = ABILITY_FRISK,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_469LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE( 1, MOVE_FORESIGHT),
    LEVEL_UP_MOVE( 1, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE( 1, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE( 1, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE( 1, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(14, MOVE_SONIC_BOOM),
    LEVEL_UP_MOVE(17, MOVE_DETECT),
    LEVEL_UP_MOVE(22, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(27, MOVE_UPROAR),
    LEVEL_UP_MOVE(30, MOVE_PURSUIT),
    LEVEL_UP_MOVE(33, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(38, MOVE_FEINT),
    LEVEL_UP_MOVE(43, MOVE_SLASH),
    LEVEL_UP_MOVE(46, MOVE_SCREECH),
    LEVEL_UP_MOVE(49, MOVE_U_TURN),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 515
// Types: TYPE_BUG / TYPE_FLYING
// Abilities: ABILITY_SPEEDBOOST, ABILITY_TINTEDLENS, ABILITY_FRISK
// Level Up Moves: 18
