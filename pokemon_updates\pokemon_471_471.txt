// POKEMON_471 (#471) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_471] =
    {
        .baseHP = 65,
        .baseAttack = 60,
        .baseDefense = 110,
        .baseSpAttack = 130,
        .baseSpDefense = 95,
        .baseSpeed = 65,
        .type1 = TYPE_ICE,
        .type2 = TYPE_ICE,
        .catchRate = 45,
        .expYield = 184,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 35,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_SNOWCLOAK,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_ICEBODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_471LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_ICY_WIND),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE( 1, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_SWIFT),
    LEVEL_UP_MOVE( 1, MOVE_CHARM),
    LEVEL_UP_MOVE( 1, MOVE_BATON_PASS),
    LEVEL_UP_MOVE( 1, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE( 5, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 9, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(13, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(17, MOVE_BITE),
    LEVEL_UP_MOVE(20, MOVE_ICE_FANG),
    LEVEL_UP_MOVE(25, MOVE_ICE_SHARD),
    LEVEL_UP_MOVE(29, MOVE_BARRIER),
    LEVEL_UP_MOVE(33, MOVE_MIRROR_COAT),
    LEVEL_UP_MOVE(35, MOVE_SNOWSCAPE),
    LEVEL_UP_MOVE(37, MOVE_HAIL),
    LEVEL_UP_MOVE(40, MOVE_FREEZE_DRY),
    LEVEL_UP_MOVE(41, MOVE_LAST_RESORT),
    LEVEL_UP_MOVE(45, MOVE_BLIZZARD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 525
// Types: TYPE_ICE / TYPE_ICE
// Abilities: ABILITY_SNOWCLOAK, ABILITY_NONE, ABILITY_ICEBODY
// Level Up Moves: 24
