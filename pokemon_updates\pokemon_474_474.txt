// POKEMON_474 (#474) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_474] =
    {
        .baseHP = 85,
        .baseAttack = 80,
        .baseDefense = 70,
        .baseSpAttack = 135,
        .baseSpDefense = 75,
        .baseSpeed = 90,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 30,
        .expYield = 268,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 3,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_ADAPTABILITY,
        .ability2 = ABILITY_DOWNLOAD,
        .abilityHidden = ABILITY_ANALYTIC,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_474LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_CONVERSION),
    LEVEL_UP_MOVE( 1, MOVE_CONVERSION_2),
    LEVEL_UP_MOVE( 1, MOVE_ZAP_CANNON),
    LEVEL_UP_MOVE( 1, MOVE_MAGIC_COAT),
    LEVEL_UP_MOVE( 1, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE( 1, MOVE_TRICK_ROOM),
    LEVEL_UP_MOVE( 7, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(12, MOVE_AGILITY),
    LEVEL_UP_MOVE(15, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE(18, MOVE_RECOVER),
    LEVEL_UP_MOVE(23, MOVE_MAGNET_RISE),
    LEVEL_UP_MOVE(29, MOVE_SIGNAL_BEAM),
    LEVEL_UP_MOVE(34, MOVE_EMBARGO),
    LEVEL_UP_MOVE(40, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(45, MOVE_LOCK_ON),
    LEVEL_UP_MOVE(50, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(50, MOVE_TRI_ATTACK),
    LEVEL_UP_MOVE(67, MOVE_HYPER_BEAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 535
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_ADAPTABILITY, ABILITY_DOWNLOAD, ABILITY_ANALYTIC
// Level Up Moves: 20
