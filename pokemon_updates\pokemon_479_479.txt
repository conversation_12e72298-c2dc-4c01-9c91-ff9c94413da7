// POKEMON_479 (#479) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_479] =
    {
        .baseHP = 50,
        .baseAttack = 50,
        .baseDefense = 77,
        .baseSpAttack = 95,
        .baseSpDefense = 77,
        .baseSpeed = 91,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_GHOST,
        .catchRate = 45,
        .expYield = 154,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_INDETERMINATE,
        .eggGroup2 = EGG_GROUP_INDETERMINATE,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_479LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE( 1, MOVE_CHARGE),
    LEVEL_UP_MOVE( 1, MOVE_TRICK),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_DISCHARGE),
    LEVEL_UP_MOVE( 8, MOVE_UPROAR),
    LEVEL_UP_MOVE(15, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(22, MOVE_SHOCK_WAVE),
    LEVEL_UP_MOVE(29, MOVE_OMINOUS_WIND),
    LEVEL_UP_MOVE(36, MOVE_SUBSTITUTE),
    LEVEL_UP_MOVE(43, MOVE_ELECTRO_BALL),
    LEVEL_UP_MOVE(50, MOVE_HEX),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 440
// Types: TYPE_ELECTRIC / TYPE_GHOST
// Abilities: ABILITY_LEVITATE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
