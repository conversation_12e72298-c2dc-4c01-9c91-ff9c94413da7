// POKEMON_482 (#482) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_482] =
    {
        .baseHP = 75,
        .baseAttack = 125,
        .baseDefense = 70,
        .baseSpAttack = 125,
        .baseSpDefense = 70,
        .baseSpeed = 115,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 3,
        .expYield = 290,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 80,
        .friendship = 140,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_482LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_REST),
    LEVEL_UP_MOVE( 1, MOVE_NATURAL_GIFT),
    LEVEL_UP_MOVE( 1, MOVE_LAST_RESORT),
    LEVEL_UP_MOVE( 6, MOVE_IMPRISON),
    LEVEL_UP_MOVE(16, MOVE_DETECT),
    LEVEL_UP_MOVE(21, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(21, MOVE_SWIFT),
    LEVEL_UP_MOVE(31, MOVE_UPROAR),
    LEVEL_UP_MOVE(36, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(46, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(50, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE(76, MOVE_EXPLOSION),
    LEVEL_UP_MOVE(84, MOVE_MYSTICAL_POWER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 580
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_LEVITATE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
