// POKEMON_485 (#485) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_485] =
    {
        .baseHP = 91,
        .baseAttack = 90,
        .baseDefense = 106,
        .baseSpAttack = 130,
        .baseSpDefense = 106,
        .baseSpeed = 77,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_STEEL,
        .catchRate = 3,
        .expYield = 300,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 3,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 10,
        .friendship = 100,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_FLASHFIRE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_FLAMEBODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_485LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE( 1, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE( 1, MOVE_HEAT_WAVE),
    LEVEL_UP_MOVE( 1, MOVE_EARTH_POWER),
    LEVEL_UP_MOVE( 1, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE( 1, MOVE_MAGMA_STORM),
    LEVEL_UP_MOVE( 6, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE( 9, MOVE_LEER),
    LEVEL_UP_MOVE(17, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE(25, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(33, MOVE_CRUNCH),
    LEVEL_UP_MOVE(41, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(49, MOVE_LAVA_PLUME),
    LEVEL_UP_MOVE(88, MOVE_STONE_EDGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_FIRE / TYPE_STEEL
// Abilities: ABILITY_FLASHFIRE, ABILITY_NONE, ABILITY_FLAMEBODY
// Level Up Moves: 14
