// POKEMON_486 (#486) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_486] =
    {
        .baseHP = 110,
        .baseAttack = 160,
        .baseDefense = 110,
        .baseSpAttack = 80,
        .baseSpDefense = 110,
        .baseSpeed = 100,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 3,
        .expYield = 335,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_SLOWSTART,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_486LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_FIRE_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_ICE_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE( 1, MOVE_DIZZY_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_FORESIGHT),
    LEVEL_UP_MOVE( 1, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE( 1, MOVE_CRUSH_GRIP),
    LEVEL_UP_MOVE( 1, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(18, MOVE_STOMP),
    LEVEL_UP_MOVE(24, MOVE_PROTECT),
    LEVEL_UP_MOVE(25, MOVE_REVENGE),
    LEVEL_UP_MOVE(36, MOVE_MEGA_PUNCH),
    LEVEL_UP_MOVE(40, MOVE_WIDE_GUARD),
    LEVEL_UP_MOVE(42, MOVE_BODY_PRESS),
    LEVEL_UP_MOVE(50, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(65, MOVE_PAYBACK),
    LEVEL_UP_MOVE(66, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE(100, MOVE_GIGA_IMPACT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 670
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_SLOWSTART, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 20
