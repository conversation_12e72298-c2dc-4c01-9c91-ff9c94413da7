// POKEMON_488 (#488) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_488] =
    {
        .baseHP = 120,
        .baseAttack = 70,
        .baseDefense = 110,
        .baseSpAttack = 75,
        .baseSpDefense = 120,
        .baseSpeed = 85,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 3,
        .expYield = 300,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 3,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(100),
        .eggCycles = 120,
        .friendship = 100,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_488LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE( 1, MOVE_MOONLIGHT),
    LEVEL_UP_MOVE( 1, MOVE_PSYCHO_SHIFT),
    LEVEL_UP_MOVE( 1, MOVE_PSYCHO_CUT),
    LEVEL_UP_MOVE( 1, MOVE_LUNAR_DANCE),
    LEVEL_UP_MOVE(11, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(18, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(20, MOVE_MIST),
    LEVEL_UP_MOVE(29, MOVE_AURORA_BEAM),
    LEVEL_UP_MOVE(38, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(47, MOVE_SLASH),
    LEVEL_UP_MOVE(72, MOVE_LUNAR_BLESSING),
    LEVEL_UP_MOVE(93, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(99, MOVE_MOONBLAST),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 580
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_LEVITATE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
