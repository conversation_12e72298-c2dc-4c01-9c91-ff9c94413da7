// POKEMON_489 (#489) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_489] =
    {
        .baseHP = 80,
        .baseAttack = 80,
        .baseDefense = 80,
        .baseSpAttack = 80,
        .baseSpDefense = 80,
        .baseSpeed = 80,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 30,
        .expYield = 216,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 40,
        .friendship = 70,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_FAIRY,
        .ability1 = ABILITY_HYDRATION,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_489LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_BUBBLE),
    LEVEL_UP_MOVE( 1, MOVE_WATER_SPORT),
    LEVEL_UP_MOVE( 9, MOVE_CHARM),
    LEVEL_UP_MOVE(16, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(24, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(31, MOVE_ACID_ARMOR),
    LEVEL_UP_MOVE(39, MOVE_WHIRLPOOL),
    LEVEL_UP_MOVE(46, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(54, MOVE_AQUA_RING),
    LEVEL_UP_MOVE(61, MOVE_DIVE),
    LEVEL_UP_MOVE(69, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(75, MOVE_TAKE_HEART),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 480
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_HYDRATION, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 13
