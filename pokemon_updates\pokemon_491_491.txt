// POKEMON_491 (#491) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_491] =
    {
        .baseHP = 70,
        .baseAttack = 90,
        .baseDefense = 90,
        .baseSpAttack = 135,
        .baseSpDefense = 90,
        .baseSpeed = 125,
        .type1 = TYPE_DARK,
        .type2 = TYPE_DARK,
        .catchRate = 3,
        .expYield = 270,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_BADDREAMS,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_491LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DISABLE),
    LEVEL_UP_MOVE( 1, MOVE_OMINOUS_WIND),
    LEVEL_UP_MOVE(11, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(20, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE(29, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE(29, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(38, MOVE_NIGHT_SHADE),
    LEVEL_UP_MOVE(38, MOVE_NIGHTMARE),
    LEVEL_UP_MOVE(47, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(57, MOVE_HAZE),
    LEVEL_UP_MOVE(66, MOVE_DARK_VOID),
    LEVEL_UP_MOVE(75, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(84, MOVE_DREAM_EATER),
    LEVEL_UP_MOVE(93, MOVE_DARK_PULSE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_DARK / TYPE_DARK
// Abilities: ABILITY_BADDREAMS, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
