// POKEMON_495 (#495) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_495] =
    {
        .baseHP = 45,
        .baseAttack = 45,
        .baseDefense = 55,
        .baseSpAttack = 45,
        .baseSpDefense = 55,
        .baseSpeed = 63,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 45,
        .expYield = 62,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_OVERGROW,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_CONTRARY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_495LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 4, MOVE_LEER),
    LEVEL_UP_MOVE( 7, MOVE_VINE_WHIP),
    LEVEL_UP_MOVE(10, MOVE_WRAP),
    LEVEL_UP_MOVE(13, MOVE_GROWTH),
    LEVEL_UP_MOVE(16, MOVE_LEAF_TORNADO),
    LEVEL_UP_MOVE(19, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(22, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(25, MOVE_SLAM),
    LEVEL_UP_MOVE(28, MOVE_LEAF_BLADE),
    LEVEL_UP_MOVE(31, MOVE_COIL),
    LEVEL_UP_MOVE(34, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(37, MOVE_WRING_OUT),
    LEVEL_UP_MOVE(40, MOVE_GASTRO_ACID),
    LEVEL_UP_MOVE(43, MOVE_LEAF_STORM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 308
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_OVERGROW, ABILITY_NONE, ABILITY_CONTRARY
// Level Up Moves: 15
