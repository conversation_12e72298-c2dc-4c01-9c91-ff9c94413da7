// POKEMON_499 (#499) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_499] =
    {
        .baseHP = 90,
        .baseAttack = 93,
        .baseDefense = 55,
        .baseSpAttack = 70,
        .baseSpDefense = 55,
        .baseSpeed = 55,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIGHTING,
        .catchRate = 45,
        .expYield = 146,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_BLAZE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_THICKFAT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_499LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_ARM_THRUST),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_ENDURE),
    LEVEL_UP_MOVE( 1, MOVE_ODOR_SLEUTH),
    LEVEL_UP_MOVE(13, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE(15, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE(20, MOVE_SMOG),
    LEVEL_UP_MOVE(23, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(28, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(31, MOVE_HEAT_CRASH),
    LEVEL_UP_MOVE(36, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(39, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(44, MOVE_HEAD_SMASH),
    LEVEL_UP_MOVE(47, MOVE_ROAR),
    LEVEL_UP_MOVE(52, MOVE_FLARE_BLITZ),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 418
// Types: TYPE_FIRE / TYPE_FIGHTING
// Abilities: ABILITY_BLAZE, ABILITY_NONE, ABILITY_THICKFAT
// Level Up Moves: 17
