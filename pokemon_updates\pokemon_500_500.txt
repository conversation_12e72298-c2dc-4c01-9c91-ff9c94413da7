// POKEMON_500 (#500) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_500] =
    {
        .baseHP = 110,
        .baseAttack = 123,
        .baseDefense = 65,
        .baseSpAttack = 100,
        .baseSpDefense = 65,
        .baseSpeed = 65,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIGHTING,
        .catchRate = 45,
        .expYield = 238,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_BLAZE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_RECKLESS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_500LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_ARM_THRUST),
    LEVEL_UP_MOVE( 1, MOVE_ODOR_SLEUTH),
    LEVEL_UP_MOVE( 1, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE(13, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE(15, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE(20, MOVE_SMOG),
    LEVEL_UP_MOVE(23, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(28, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(31, MOVE_HEAT_CRASH),
    LEVEL_UP_MOVE(38, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(43, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(50, MOVE_HEAD_SMASH),
    LEVEL_UP_MOVE(55, MOVE_ROAR),
    LEVEL_UP_MOVE(62, MOVE_FLARE_BLITZ),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 528
// Types: TYPE_FIRE / TYPE_FIGHTING
// Abilities: ABILITY_BLAZE, ABILITY_NONE, ABILITY_RECKLESS
// Level Up Moves: 17
