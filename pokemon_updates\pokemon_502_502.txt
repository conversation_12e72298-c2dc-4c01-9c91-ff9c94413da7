// POKEMON_502 (#502) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_502] =
    {
        .baseHP = 75,
        .baseAttack = 75,
        .baseDefense = 60,
        .baseSpAttack = 83,
        .baseSpDefense = 60,
        .baseSpeed = 60,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 45,
        .expYield = 145,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_TORRENT,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_SHELLARMOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_502LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_WATER_SPORT),
    LEVEL_UP_MOVE( 1, MOVE_SOAK),
    LEVEL_UP_MOVE(13, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(18, MOVE_RAZOR_SHELL),
    LEVEL_UP_MOVE(21, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE(26, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(29, MOVE_REVENGE),
    LEVEL_UP_MOVE(34, MOVE_AQUA_JET),
    LEVEL_UP_MOVE(37, MOVE_ENCORE),
    LEVEL_UP_MOVE(42, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(45, MOVE_RETALIATE),
    LEVEL_UP_MOVE(50, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(53, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 413
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_TORRENT, ABILITY_NONE, ABILITY_SHELLARMOR
// Level Up Moves: 16
