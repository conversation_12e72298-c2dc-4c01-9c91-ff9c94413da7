// POKEMON_504 (#504) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_504] =
    {
        .baseHP = 45,
        .baseAttack = 55,
        .baseDefense = 39,
        .baseSpAttack = 35,
        .baseSpDefense = 39,
        .baseSpeed = 42,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 255,
        .expYield = 51,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_RUNAWAY,
        .ability2 = ABILITY_KEENEYE,
        .abilityHidden = ABILITY_ANALYTIC,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_504LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 3, MOVE_LEER),
    LEVEL_UP_MOVE( 6, MOVE_BITE),
    LEVEL_UP_MOVE( 8, MOVE_BIDE),
    LEVEL_UP_MOVE(11, MOVE_DETECT),
    LEVEL_UP_MOVE(13, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE(16, MOVE_CRUNCH),
    LEVEL_UP_MOVE(18, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE(21, MOVE_SUPER_FANG),
    LEVEL_UP_MOVE(23, MOVE_AFTER_YOU),
    LEVEL_UP_MOVE(26, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(28, MOVE_WORK_UP),
    LEVEL_UP_MOVE(31, MOVE_HYPER_FANG),
    LEVEL_UP_MOVE(33, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(36, MOVE_MEAN_LOOK),
    LEVEL_UP_MOVE(38, MOVE_BATON_PASS),
    LEVEL_UP_MOVE(41, MOVE_SLAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 255
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_RUNAWAY, ABILITY_KEENEYE, ABILITY_ANALYTIC
// Level Up Moves: 17
