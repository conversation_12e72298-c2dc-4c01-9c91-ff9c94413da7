// POKEMON_508 (#508) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_508] =
    {
        .baseHP = 85,
        .baseAttack = 110,
        .baseDefense = 90,
        .baseSpAttack = 45,
        .baseSpDefense = 90,
        .baseSpeed = 80,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 45,
        .expYield = 250,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_INTIMIDATE,
        .ability2 = ABILITY_SANDRUSH,
        .abilityHidden = ABILITY_SCRAPPY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_508LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_ODOR_SLEUTH),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_FANG),
    LEVEL_UP_MOVE( 1, MOVE_ICE_FANG),
    LEVEL_UP_MOVE( 1, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE(12, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(15, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(19, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(20, MOVE_WORK_UP),
    LEVEL_UP_MOVE(24, MOVE_CRUNCH),
    LEVEL_UP_MOVE(29, MOVE_ROAR),
    LEVEL_UP_MOVE(36, MOVE_RETALIATE),
    LEVEL_UP_MOVE(42, MOVE_REVERSAL),
    LEVEL_UP_MOVE(51, MOVE_LAST_RESORT),
    LEVEL_UP_MOVE(59, MOVE_GIGA_IMPACT),
    LEVEL_UP_MOVE(63, MOVE_PLAY_ROUGH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_INTIMIDATE, ABILITY_SANDRUSH, ABILITY_SCRAPPY
// Level Up Moves: 18
