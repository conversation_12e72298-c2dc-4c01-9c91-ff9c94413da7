// POKEMON_509 (#509) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_509] =
    {
        .baseHP = 41,
        .baseAttack = 50,
        .baseDefense = 37,
        .baseSpAttack = 50,
        .baseSpDefense = 37,
        .baseSpeed = 66,
        .type1 = TYPE_DARK,
        .type2 = TYPE_DARK,
        .catchRate = 255,
        .expYield = 56,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_LIMBER,
        .ability2 = ABILITY_UNBURDEN,
        .abilityHidden = ABILITY_PRANKSTER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_509LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 3, MOVE_GROWL),
    LEVEL_UP_MOVE( 6, MOVE_ASSIST),
    LEVEL_UP_MOVE(10, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE(12, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(15, MOVE_PURSUIT),
    LEVEL_UP_MOVE(19, MOVE_TORMENT),
    LEVEL_UP_MOVE(21, MOVE_FAKE_OUT),
    LEVEL_UP_MOVE(24, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(28, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(30, MOVE_SLASH),
    LEVEL_UP_MOVE(33, MOVE_CAPTIVATE),
    LEVEL_UP_MOVE(37, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(39, MOVE_SNATCH),
    LEVEL_UP_MOVE(42, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(46, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(49, MOVE_PLAY_ROUGH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 281
// Types: TYPE_DARK / TYPE_DARK
// Abilities: ABILITY_LIMBER, ABILITY_UNBURDEN, ABILITY_PRANKSTER
// Level Up Moves: 17
