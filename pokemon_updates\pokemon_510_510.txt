// POKEMON_510 (#510) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_510] =
    {
        .baseHP = 64,
        .baseAttack = 88,
        .baseDefense = 50,
        .baseSpAttack = 88,
        .baseSpDefense = 50,
        .baseSpeed = 106,
        .type1 = TYPE_DARK,
        .type2 = TYPE_DARK,
        .catchRate = 90,
        .expYield = 156,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_LIMBER,
        .ability2 = ABILITY_UNBURDEN,
        .abilityHidden = ABILITY_PRANKSTER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_510LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_ASSIST),
    LEVEL_UP_MOVE(12, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(15, MOVE_PURSUIT),
    LEVEL_UP_MOVE(19, MOVE_TORMENT),
    LEVEL_UP_MOVE(22, MOVE_FAKE_OUT),
    LEVEL_UP_MOVE(26, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(31, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(34, MOVE_SLASH),
    LEVEL_UP_MOVE(38, MOVE_TAUNT),
    LEVEL_UP_MOVE(43, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(47, MOVE_SNATCH),
    LEVEL_UP_MOVE(50, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(55, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(58, MOVE_PLAY_ROUGH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 446
// Types: TYPE_DARK / TYPE_DARK
// Abilities: ABILITY_LIMBER, ABILITY_UNBURDEN, ABILITY_PRANKSTER
// Level Up Moves: 17
