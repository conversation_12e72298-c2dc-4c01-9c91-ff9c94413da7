// POKEMON_513 (#513) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_513] =
    {
        .baseHP = 50,
        .baseAttack = 53,
        .baseDefense = 48,
        .baseSpAttack = 53,
        .baseSpDefense = 48,
        .baseSpeed = 64,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 190,
        .expYield = 63,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_ORAN_BERRY,
        .item2 = ITEM_PASSHO_BERRY,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_GLUTTONY,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_BLAZE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_513LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_PLAY_NICE),
    LEVEL_UP_MOVE( 4, MOVE_LEER),
    LEVEL_UP_MOVE( 7, MOVE_LICK),
    LEVEL_UP_MOVE(10, MOVE_INCINERATE),
    LEVEL_UP_MOVE(13, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(16, MOVE_YAWN),
    LEVEL_UP_MOVE(19, MOVE_BITE),
    LEVEL_UP_MOVE(22, MOVE_FLAME_BURST),
    LEVEL_UP_MOVE(25, MOVE_AMNESIA),
    LEVEL_UP_MOVE(28, MOVE_FLING),
    LEVEL_UP_MOVE(31, MOVE_ACROBATICS),
    LEVEL_UP_MOVE(34, MOVE_FIRE_BLAST),
    LEVEL_UP_MOVE(37, MOVE_RECYCLE),
    LEVEL_UP_MOVE(40, MOVE_NATURAL_GIFT),
    LEVEL_UP_MOVE(43, MOVE_CRUNCH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 316
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_GLUTTONY, ABILITY_NONE, ABILITY_BLAZE
// Level Up Moves: 16
