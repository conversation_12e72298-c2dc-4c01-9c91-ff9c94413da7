// POKEMON_518 (#518) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_518] =
    {
        .baseHP = 116,
        .baseAttack = 55,
        .baseDefense = 85,
        .baseSpAttack = 107,
        .baseSpDefense = 95,
        .baseSpeed = 29,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 75,
        .expYield = 170,
        .evYield_HP = 2,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 10,
        .friendship = 50,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_FOREWARN,
        .ability2 = ABILITY_SYNCHRONIZE,
        .abilityHidden = ABILITY_TELEPATHY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_518LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_PSYBEAM),
    LEVEL_UP_MOVE( 1, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_MOONLIGHT),
    LEVEL_UP_MOVE( 1, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE( 1, MOVE_YAWN),
    LEVEL_UP_MOVE( 1, MOVE_IMPRISON),
    LEVEL_UP_MOVE( 1, MOVE_LUCKY_CHANT),
    LEVEL_UP_MOVE( 1, MOVE_STORED_POWER),
    LEVEL_UP_MOVE( 1, MOVE_MOONBLAST),
    LEVEL_UP_MOVE( 1, MOVE_PSYCHIC_TERRAIN),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 487
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_FOREWARN, ABILITY_SYNCHRONIZE, ABILITY_TELEPATHY
// Level Up Moves: 11
