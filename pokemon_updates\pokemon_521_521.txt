// POKEMON_521 (#521) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_521] =
    {
        .baseHP = 80,
        .baseAttack = 115,
        .baseDefense = 80,
        .baseSpAttack = 65,
        .baseSpDefense = 55,
        .baseSpeed = 93,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 244,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_BIGPECKS,
        .ability2 = ABILITY_SUPERLUCK,
        .abilityHidden = ABILITY_RIVALRY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_521LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(15, MOVE_AIR_CUTTER),
    LEVEL_UP_MOVE(18, MOVE_ROOST),
    LEVEL_UP_MOVE(23, MOVE_DETECT),
    LEVEL_UP_MOVE(27, MOVE_TAUNT),
    LEVEL_UP_MOVE(33, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(38, MOVE_RAZOR_WIND),
    LEVEL_UP_MOVE(44, MOVE_FEATHER_DANCE),
    LEVEL_UP_MOVE(49, MOVE_SWAGGER),
    LEVEL_UP_MOVE(55, MOVE_FACADE),
    LEVEL_UP_MOVE(60, MOVE_TAILWIND),
    LEVEL_UP_MOVE(66, MOVE_SKY_ATTACK),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 488
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_BIGPECKS, ABILITY_SUPERLUCK, ABILITY_RIVALRY
// Level Up Moves: 15
