// POKEMON_523 (#523) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_523] =
    {
        .baseHP = 75,
        .baseAttack = 100,
        .baseDefense = 63,
        .baseSpAttack = 80,
        .baseSpDefense = 63,
        .baseSpeed = 116,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 75,
        .expYield = 174,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_CHERI_BERRY,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_LIGHTNINGROD,
        .ability2 = ABILITY_MOTORDRIVE,
        .abilityHidden = ABILITY_SAPSIPPER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_523LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_CHARGE),
    LEVEL_UP_MOVE( 1, MOVE_ION_DELUGE),
    LEVEL_UP_MOVE(11, MOVE_SHOCK_WAVE),
    LEVEL_UP_MOVE(18, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE(22, MOVE_PURSUIT),
    LEVEL_UP_MOVE(25, MOVE_SPARK),
    LEVEL_UP_MOVE(31, MOVE_STOMP),
    LEVEL_UP_MOVE(36, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(42, MOVE_AGILITY),
    LEVEL_UP_MOVE(47, MOVE_WILD_CHARGE),
    LEVEL_UP_MOVE(53, MOVE_THRASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 497
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_LIGHTNINGROD, ABILITY_MOTORDRIVE, ABILITY_SAPSIPPER
// Level Up Moves: 14
