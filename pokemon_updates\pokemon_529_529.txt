// POKEMON_529 (#529) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_529] =
    {
        .baseHP = 60,
        .baseAttack = 85,
        .baseDefense = 40,
        .baseSpAttack = 30,
        .baseSpDefense = 45,
        .baseSpeed = 68,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_GROUND,
        .catchRate = 120,
        .expYield = 66,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_SANDRUSH,
        .ability2 = ABILITY_SANDFORCE,
        .abilityHidden = ABILITY_MOLDBREAKER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_529LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SPORT),
    LEVEL_UP_MOVE( 5, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE( 8, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE(12, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(15, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(19, MOVE_DIG),
    LEVEL_UP_MOVE(22, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(26, MOVE_SLASH),
    LEVEL_UP_MOVE(29, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(33, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(36, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(40, MOVE_SANDSTORM),
    LEVEL_UP_MOVE(43, MOVE_DRILL_RUN),
    LEVEL_UP_MOVE(47, MOVE_FISSURE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 328
// Types: TYPE_GROUND / TYPE_GROUND
// Abilities: ABILITY_SANDRUSH, ABILITY_SANDFORCE, ABILITY_MOLDBREAKER
// Level Up Moves: 15
