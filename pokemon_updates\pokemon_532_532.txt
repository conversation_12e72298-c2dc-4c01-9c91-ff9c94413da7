// POKEMON_532 (#532) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_532] =
    {
        .baseHP = 75,
        .baseAttack = 80,
        .baseDefense = 55,
        .baseSpAttack = 25,
        .baseSpDefense = 35,
        .baseSpeed = 35,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 180,
        .expYield = 61,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(25),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_HUMANSHAPE,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_GUTS,
        .ability2 = ABILITY_SHEERFORCE,
        .abilityHidden = ABILITY_IRONFIST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_532LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 4, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE( 8, MOVE_BIDE),
    LEVEL_UP_MOVE(12, MOVE_LOW_KICK),
    LEVEL_UP_MOVE(16, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE(20, MOVE_WAKE_UP_SLAP),
    LEVEL_UP_MOVE(24, MOVE_SLAM),
    LEVEL_UP_MOVE(24, MOVE_CHIP_AWAY),
    LEVEL_UP_MOVE(28, MOVE_BULK_UP),
    LEVEL_UP_MOVE(31, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(34, MOVE_DYNAMIC_PUNCH),
    LEVEL_UP_MOVE(37, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(40, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE(43, MOVE_STONE_EDGE),
    LEVEL_UP_MOVE(46, MOVE_FOCUS_PUNCH),
    LEVEL_UP_MOVE(49, MOVE_SUPERPOWER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 305
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_GUTS, ABILITY_SHEERFORCE, ABILITY_IRONFIST
// Level Up Moves: 17
