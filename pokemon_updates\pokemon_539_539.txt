// POKEMON_539 (#539) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_539] =
    {
        .baseHP = 75,
        .baseAttack = 125,
        .baseDefense = 75,
        .baseSpAttack = 30,
        .baseSpDefense = 75,
        .baseSpeed = 85,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 45,
        .expYield = 163,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_BLACK_BELT,
        .genderRatio = PERCENT_FEMALE(0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_HUMANSHAPE,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_STURDY,
        .ability2 = ABILITY_INNERFOCUS,
        .abilityHidden = ABILITY_MOLDBREAKER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_539LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE( 1, MOVE_BIDE),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE( 5, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE( 9, MOVE_LOW_SWEEP),
    LEVEL_UP_MOVE(13, MOVE_COUNTER),
    LEVEL_UP_MOVE(17, MOVE_KARATE_CHOP),
    LEVEL_UP_MOVE(21, MOVE_BRICK_BREAK),
    LEVEL_UP_MOVE(25, MOVE_BULK_UP),
    LEVEL_UP_MOVE(29, MOVE_RETALIATE),
    LEVEL_UP_MOVE(33, MOVE_ENDURE),
    LEVEL_UP_MOVE(37, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(41, MOVE_CLOSE_COMBAT),
    LEVEL_UP_MOVE(45, MOVE_REVERSAL),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 465
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_STURDY, ABILITY_INNERFOCUS, ABILITY_MOLDBREAKER
// Level Up Moves: 15
