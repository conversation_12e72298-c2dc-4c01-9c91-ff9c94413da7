// POKEMON_541 (#541) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_541] =
    {
        .baseHP = 55,
        .baseAttack = 63,
        .baseDefense = 90,
        .baseSpAttack = 50,
        .baseSpDefense = 80,
        .baseSpeed = 42,
        .type1 = TYPE_BUG,
        .type2 = TYPE_GRASS,
        .catchRate = 120,
        .expYield = 133,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_MENTAL_HERB,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_LEAFGUARD,
        .ability2 = ABILITY_CHLOROPHYLL,
        .abilityHidden = ABILITY_OVERCOAT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_541LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_PROTECT),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE( 1, MOVE_STRING_SHOT),
    LEVEL_UP_MOVE( 1, MOVE_GRASS_WHISTLE),
    LEVEL_UP_MOVE( 1, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(22, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE(29, MOVE_ENDURE),
    LEVEL_UP_MOVE(31, MOVE_STICKY_WEB),
    LEVEL_UP_MOVE(36, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(43, MOVE_FLAIL),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 380
// Types: TYPE_BUG / TYPE_GRASS
// Abilities: ABILITY_LEAFGUARD, ABILITY_CHLOROPHYLL, ABILITY_OVERCOAT
// Level Up Moves: 11
