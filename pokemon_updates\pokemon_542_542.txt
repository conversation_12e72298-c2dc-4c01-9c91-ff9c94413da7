// POKEMON_542 (#542) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_542] =
    {
        .baseHP = 75,
        .baseAttack = 103,
        .baseDefense = 80,
        .baseSpAttack = 70,
        .baseSpDefense = 80,
        .baseSpeed = 92,
        .type1 = TYPE_BUG,
        .type2 = TYPE_GRASS,
        .catchRate = 45,
        .expYield = 225,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_MENTAL_HERB,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_SWARM,
        .ability2 = ABILITY_CHLOROPHYLL,
        .abilityHidden = ABILITY_OVERCOAT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_542LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_SLASH),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE( 1, MOVE_STRING_SHOT),
    LEVEL_UP_MOVE( 1, MOVE_FALSE_SWIPE),
    LEVEL_UP_MOVE( 1, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(22, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE(29, MOVE_FELL_STINGER),
    LEVEL_UP_MOVE(32, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(36, MOVE_LEAF_BLADE),
    LEVEL_UP_MOVE(39, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(43, MOVE_ENTRAINMENT),
    LEVEL_UP_MOVE(46, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(50, MOVE_LEAF_STORM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_BUG / TYPE_GRASS
// Abilities: ABILITY_SWARM, ABILITY_CHLOROPHYLL, ABILITY_OVERCOAT
// Level Up Moves: 14
