// POKEMON_543 (#543) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_543] =
    {
        .baseHP = 30,
        .baseAttack = 45,
        .baseDefense = 59,
        .baseSpAttack = 30,
        .baseSpDefense = 39,
        .baseSpeed = 57,
        .type1 = TYPE_BUG,
        .type2 = TYPE_POISON,
        .catchRate = 255,
        .expYield = 52,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_PECHA_BERRY,
        .item2 = ITEM_POISON_BARB,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_POISONPOINT,
        .ability2 = ABILITY_SWARM,
        .abilityHidden = ABILITY_SPEEDBOOST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_543LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_ROLLOUT),
    LEVEL_UP_MOVE( 5, MOVE_POISON_STING),
    LEVEL_UP_MOVE( 8, MOVE_SCREECH),
    LEVEL_UP_MOVE(12, MOVE_PURSUIT),
    LEVEL_UP_MOVE(15, MOVE_PROTECT),
    LEVEL_UP_MOVE(19, MOVE_POISON_TAIL),
    LEVEL_UP_MOVE(22, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(26, MOVE_VENOSHOCK),
    LEVEL_UP_MOVE(29, MOVE_AGILITY),
    LEVEL_UP_MOVE(33, MOVE_STEAMROLLER),
    LEVEL_UP_MOVE(36, MOVE_TOXIC),
    LEVEL_UP_MOVE(38, MOVE_VENOM_DRENCH),
    LEVEL_UP_MOVE(40, MOVE_ROCK_CLIMB),
    LEVEL_UP_MOVE(43, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 260
// Types: TYPE_BUG / TYPE_POISON
// Abilities: ABILITY_POISONPOINT, ABILITY_SWARM, ABILITY_SPEEDBOOST
// Level Up Moves: 15
