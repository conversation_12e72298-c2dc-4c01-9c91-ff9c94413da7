// POKEMON_546 (#546) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_546] =
    {
        .baseHP = 40,
        .baseAttack = 27,
        .baseDefense = 60,
        .baseSpAttack = 37,
        .baseSpDefense = 50,
        .baseSpeed = 66,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_FAIRY,
        .catchRate = 190,
        .expYield = 56,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_ABSORB_BULB,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_FAIRY,
        .ability1 = ABILITY_PRANKSTER,
        .ability2 = ABILITY_INFILTRATOR,
        .abilityHidden = ABILITY_CHLOROPHYLL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_546LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_FAIRY_WIND),
    LEVEL_UP_MOVE( 4, MOVE_GROWTH),
    LEVEL_UP_MOVE( 8, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(10, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE(13, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(17, MOVE_COTTON_SPORE),
    LEVEL_UP_MOVE(19, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(22, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE(26, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(28, MOVE_CHARM),
    LEVEL_UP_MOVE(31, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(35, MOVE_ENERGY_BALL),
    LEVEL_UP_MOVE(37, MOVE_COTTON_GUARD),
    LEVEL_UP_MOVE(40, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE(44, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(46, MOVE_SOLAR_BEAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 280
// Types: TYPE_GRASS / TYPE_FAIRY
// Abilities: ABILITY_PRANKSTER, ABILITY_INFILTRATOR, ABILITY_CHLOROPHYLL
// Level Up Moves: 17
