// POKEMON_547 (#547) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_547] =
    {
        .baseHP = 60,
        .baseAttack = 67,
        .baseDefense = 85,
        .baseSpAttack = 77,
        .baseSpDefense = 75,
        .baseSpeed = 116,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_FAIRY,
        .catchRate = 75,
        .expYield = 168,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_ABSORB_BULB,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_FAIRY,
        .ability1 = ABILITY_PRANKSTER,
        .ability2 = ABILITY_INFILTRATOR,
        .abilityHidden = ABILITY_CHLOROPHYLL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_547LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE( 1, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 1, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE( 1, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE( 1, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE( 1, MOVE_COTTON_SPORE),
    LEVEL_UP_MOVE( 1, MOVE_CHARM),
    LEVEL_UP_MOVE( 1, MOVE_MEMENTO),
    LEVEL_UP_MOVE( 1, MOVE_COTTON_GUARD),
    LEVEL_UP_MOVE( 1, MOVE_FAIRY_WIND),
    LEVEL_UP_MOVE(10, MOVE_GUST),
    LEVEL_UP_MOVE(28, MOVE_TAILWIND),
    LEVEL_UP_MOVE(46, MOVE_HURRICANE),
    LEVEL_UP_MOVE(50, MOVE_MOONBLAST),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 480
// Types: TYPE_GRASS / TYPE_FAIRY
// Abilities: ABILITY_PRANKSTER, ABILITY_INFILTRATOR, ABILITY_CHLOROPHYLL
// Level Up Moves: 16
