// POKEMON_548 (#548) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_548] =
    {
        .baseHP = 45,
        .baseAttack = 35,
        .baseDefense = 50,
        .baseSpAttack = 70,
        .baseSpDefense = 50,
        .baseSpeed = 30,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 190,
        .expYield = 56,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_ABSORB_BULB,
        .genderRatio = PERCENT_FEMALE(100),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_CHLOROPHYLL,
        .ability2 = ABILITY_OWNTEMPO,
        .abilityHidden = ABILITY_LEAFGUARD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_548LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 4, MOVE_GROWTH),
    LEVEL_UP_MOVE( 8, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(10, MOVE_SLEEP_POWDER),
    LEVEL_UP_MOVE(13, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(17, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(19, MOVE_MAGICAL_LEAF),
    LEVEL_UP_MOVE(22, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE(26, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(28, MOVE_AROMATHERAPY),
    LEVEL_UP_MOVE(31, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(35, MOVE_ENERGY_BALL),
    LEVEL_UP_MOVE(37, MOVE_ENTRAINMENT),
    LEVEL_UP_MOVE(40, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE(44, MOVE_AFTER_YOU),
    LEVEL_UP_MOVE(46, MOVE_LEAF_STORM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 280
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_CHLOROPHYLL, ABILITY_OWNTEMPO, ABILITY_LEAFGUARD
// Level Up Moves: 16
