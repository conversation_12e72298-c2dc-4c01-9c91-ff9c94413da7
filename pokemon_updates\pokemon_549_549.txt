// POKEMON_549 (#549) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_549] =
    {
        .baseHP = 70,
        .baseAttack = 60,
        .baseDefense = 75,
        .baseSpAttack = 110,
        .baseSpDefense = 75,
        .baseSpeed = 90,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 75,
        .expYield = 168,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_ABSORB_BULB,
        .genderRatio = PERCENT_FEMALE(100),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_CHLOROPHYLL,
        .ability2 = ABILITY_OWNTEMPO,
        .abilityHidden = ABILITY_LEAFGUARD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_549LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE( 1, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 1, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE( 1, MOVE_SLEEP_POWDER),
    LEVEL_UP_MOVE( 1, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE( 1, MOVE_AROMATHERAPY),
    LEVEL_UP_MOVE( 1, MOVE_MAGICAL_LEAF),
    LEVEL_UP_MOVE( 1, MOVE_LEAF_STORM),
    LEVEL_UP_MOVE( 1, MOVE_ENTRAINMENT),
    LEVEL_UP_MOVE(10, MOVE_TEETER_DANCE),
    LEVEL_UP_MOVE(28, MOVE_QUIVER_DANCE),
    LEVEL_UP_MOVE(46, MOVE_PETAL_DANCE),
    LEVEL_UP_MOVE(50, MOVE_PETAL_BLIZZARD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 480
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_CHLOROPHYLL, ABILITY_OWNTEMPO, ABILITY_LEAFGUARD
// Level Up Moves: 15
