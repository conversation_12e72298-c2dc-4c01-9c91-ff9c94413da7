// POKEMON_554 (#554) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_554] =
    {
        .baseHP = 70,
        .baseAttack = 90,
        .baseDefense = 45,
        .baseSpAttack = 15,
        .baseSpDefense = 45,
        .baseSpeed = 50,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 120,
        .expYield = 63,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_RAWST_BERRY,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_HUSTLE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_INNERFOCUS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_554LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 3, MOVE_ROLLOUT),
    LEVEL_UP_MOVE( 6, MOVE_INCINERATE),
    LEVEL_UP_MOVE( 8, MOVE_BITE),
    LEVEL_UP_MOVE( 9, MOVE_RAGE),
    LEVEL_UP_MOVE(11, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE(14, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(17, MOVE_UPROAR),
    LEVEL_UP_MOVE(19, MOVE_FACADE),
    LEVEL_UP_MOVE(22, MOVE_FIRE_PUNCH),
    LEVEL_UP_MOVE(25, MOVE_WORK_UP),
    LEVEL_UP_MOVE(27, MOVE_THRASH),
    LEVEL_UP_MOVE(30, MOVE_BELLY_DRUM),
    LEVEL_UP_MOVE(33, MOVE_FLARE_BLITZ),
    LEVEL_UP_MOVE(35, MOVE_TAUNT),
    LEVEL_UP_MOVE(39, MOVE_SUPERPOWER),
    LEVEL_UP_MOVE(42, MOVE_OVERHEAT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 315
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_HUSTLE, ABILITY_NONE, ABILITY_INNERFOCUS
// Level Up Moves: 18
