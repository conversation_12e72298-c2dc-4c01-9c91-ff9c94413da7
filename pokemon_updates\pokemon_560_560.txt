// POKEMON_560 (#560) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_560] =
    {
        .baseHP = 65,
        .baseAttack = 90,
        .baseDefense = 115,
        .baseSpAttack = 45,
        .baseSpDefense = 115,
        .baseSpeed = 58,
        .type1 = TYPE_DARK,
        .type2 = TYPE_FIGHTING,
        .catchRate = 90,
        .expYield = 171,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SHED_SHELL,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_SHEDSKIN,
        .ability2 = ABILITY_MOXIE,
        .abilityHidden = ABILITY_INTIMIDATE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_560LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_HEADBUTT),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE(12, MOVE_SWAGGER),
    LEVEL_UP_MOVE(16, MOVE_LOW_KICK),
    LEVEL_UP_MOVE(20, MOVE_PAYBACK),
    LEVEL_UP_MOVE(23, MOVE_BRICK_BREAK),
    LEVEL_UP_MOVE(24, MOVE_BEAT_UP),
    LEVEL_UP_MOVE(27, MOVE_CHIP_AWAY),
    LEVEL_UP_MOVE(31, MOVE_HIGH_JUMP_KICK),
    LEVEL_UP_MOVE(34, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(38, MOVE_CRUNCH),
    LEVEL_UP_MOVE(45, MOVE_FACADE),
    LEVEL_UP_MOVE(51, MOVE_ROCK_CLIMB),
    LEVEL_UP_MOVE(58, MOVE_FOCUS_PUNCH),
    LEVEL_UP_MOVE(65, MOVE_HEAD_SMASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 488
// Types: TYPE_DARK / TYPE_FIGHTING
// Abilities: ABILITY_SHEDSKIN, ABILITY_MOXIE, ABILITY_INTIMIDATE
// Level Up Moves: 17
