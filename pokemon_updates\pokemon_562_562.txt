// POKEMON_562 (#562) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_562] =
    {
        .baseHP = 38,
        .baseAttack = 30,
        .baseDefense = 85,
        .baseSpAttack = 55,
        .baseSpDefense = 65,
        .baseSpeed = 30,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_GHOST,
        .catchRate = 190,
        .expYield = 61,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SPELL_TAG,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_INDETERMINATE,
        .ability1 = ABILITY_MUMMY,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_562LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_PROTECT),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 5, MOVE_DISABLE),
    LEVEL_UP_MOVE( 9, MOVE_HAZE),
    LEVEL_UP_MOVE(13, MOVE_NIGHT_SHADE),
    LEVEL_UP_MOVE(17, MOVE_HEX),
    LEVEL_UP_MOVE(21, MOVE_WILL_O_WISP),
    LEVEL_UP_MOVE(25, MOVE_OMINOUS_WIND),
    LEVEL_UP_MOVE(29, MOVE_CURSE),
    LEVEL_UP_MOVE(33, MOVE_GUARD_SPLIT),
    LEVEL_UP_MOVE(33, MOVE_POWER_SPLIT),
    LEVEL_UP_MOVE(37, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(41, MOVE_GRUDGE),
    LEVEL_UP_MOVE(45, MOVE_MEAN_LOOK),
    LEVEL_UP_MOVE(49, MOVE_DESTINY_BOND),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 303
// Types: TYPE_GHOST / TYPE_GHOST
// Abilities: ABILITY_MUMMY, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
