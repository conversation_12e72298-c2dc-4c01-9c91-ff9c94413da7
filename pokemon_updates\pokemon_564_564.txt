// POKEMON_564 (#564) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_564] =
    {
        .baseHP = 54,
        .baseAttack = 78,
        .baseDefense = 103,
        .baseSpAttack = 53,
        .baseSpDefense = 45,
        .baseSpeed = 22,
        .type1 = TYPE_WATER,
        .type2 = TYPE_ROCK,
        .catchRate = 45,
        .expYield = 71,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_WATER_3,
        .ability1 = ABILITY_SOLIDROCK,
        .ability2 = ABILITY_STURDY,
        .abilityHidden = ABILITY_SWIFTSWIM,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_564LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_WITHDRAW),
    LEVEL_UP_MOVE( 1, MOVE_BIDE),
    LEVEL_UP_MOVE( 5, MOVE_ROLLOUT),
    LEVEL_UP_MOVE( 8, MOVE_BITE),
    LEVEL_UP_MOVE(11, MOVE_PROTECT),
    LEVEL_UP_MOVE(15, MOVE_AQUA_JET),
    LEVEL_UP_MOVE(18, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(21, MOVE_CRUNCH),
    LEVEL_UP_MOVE(25, MOVE_WIDE_GUARD),
    LEVEL_UP_MOVE(28, MOVE_BRINE),
    LEVEL_UP_MOVE(31, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE(35, MOVE_CURSE),
    LEVEL_UP_MOVE(38, MOVE_SHELL_SMASH),
    LEVEL_UP_MOVE(41, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(45, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(48, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(50, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 355
// Types: TYPE_WATER / TYPE_ROCK
// Abilities: ABILITY_SOLIDROCK, ABILITY_STURDY, ABILITY_SWIFTSWIM
// Level Up Moves: 18
