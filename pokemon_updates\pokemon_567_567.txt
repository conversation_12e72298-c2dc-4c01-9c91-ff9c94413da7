// POKEMON_567 (#567) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_567] =
    {
        .baseHP = 75,
        .baseAttack = 140,
        .baseDefense = 65,
        .baseSpAttack = 112,
        .baseSpDefense = 65,
        .baseSpeed = 110,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 177,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_WATER_3,
        .ability1 = ABILITY_DEFEATIST,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_567LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 8, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(11, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(15, MOVE_PLUCK),
    LEVEL_UP_MOVE(18, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(21, MOVE_AGILITY),
    LEVEL_UP_MOVE(25, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(28, MOVE_ACROBATICS),
    LEVEL_UP_MOVE(31, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(35, MOVE_CRUNCH),
    LEVEL_UP_MOVE(40, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(45, MOVE_U_TURN),
    LEVEL_UP_MOVE(51, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(56, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(61, MOVE_THRASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 567
// Types: TYPE_ROCK / TYPE_FLYING
// Abilities: ABILITY_DEFEATIST, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 18
