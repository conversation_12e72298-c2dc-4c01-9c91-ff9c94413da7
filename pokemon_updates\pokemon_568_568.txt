// POKEMON_568 (#568) - GE<PERSON>RATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_568] =
    {
        .baseHP = 50,
        .baseAttack = 50,
        .baseDefense = 62,
        .baseSpAttack = 40,
        .baseSpDefense = 62,
        .baseSpeed = 65,
        .type1 = TYPE_POISON,
        .type2 = TYPE_POISON,
        .catchRate = 190,
        .expYield = 66,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SILK_SCARF,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_STENCH,
        .ability2 = ABILITY_STICKYHOLD,
        .abilityHidden = ABILITY_AFTERMATH,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_568LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_POISON_GAS),
    LEVEL_UP_MOVE( 3, MOVE_RECYCLE),
    LEVEL_UP_MOVE( 7, MOVE_TOXIC_SPIKES),
    LEVEL_UP_MOVE(12, MOVE_ACID_SPRAY),
    LEVEL_UP_MOVE(14, MOVE_DOUBLE_SLAP),
    LEVEL_UP_MOVE(18, MOVE_SLUDGE),
    LEVEL_UP_MOVE(23, MOVE_STOCKPILE),
    LEVEL_UP_MOVE(23, MOVE_SWALLOW),
    LEVEL_UP_MOVE(25, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(29, MOVE_SLUDGE_BOMB),
    LEVEL_UP_MOVE(34, MOVE_CLEAR_SMOG),
    LEVEL_UP_MOVE(36, MOVE_TOXIC),
    LEVEL_UP_MOVE(40, MOVE_AMNESIA),
    LEVEL_UP_MOVE(42, MOVE_BELCH),
    LEVEL_UP_MOVE(45, MOVE_GUNK_SHOT),
    LEVEL_UP_MOVE(47, MOVE_EXPLOSION),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 329
// Types: TYPE_POISON / TYPE_POISON
// Abilities: ABILITY_STENCH, ABILITY_STICKYHOLD, ABILITY_AFTERMATH
// Level Up Moves: 17
