// POKEMON_570 (#570) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_570] =
    {
        .baseHP = 40,
        .baseAttack = 65,
        .baseDefense = 40,
        .baseSpAttack = 80,
        .baseSpDefense = 40,
        .baseSpeed = 65,
        .type1 = TYPE_DARK,
        .type2 = TYPE_DARK,
        .catchRate = 75,
        .expYield = 66,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_ILLUSION,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_570LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 5, MOVE_PURSUIT),
    LEVEL_UP_MOVE( 8, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE( 9, MOVE_FAKE_TEARS),
    LEVEL_UP_MOVE(13, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(17, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE(21, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(25, MOVE_TAUNT),
    LEVEL_UP_MOVE(29, MOVE_FOUL_PLAY),
    LEVEL_UP_MOVE(33, MOVE_TORMENT),
    LEVEL_UP_MOVE(37, MOVE_AGILITY),
    LEVEL_UP_MOVE(41, MOVE_EMBARGO),
    LEVEL_UP_MOVE(45, MOVE_PUNISHMENT),
    LEVEL_UP_MOVE(49, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(53, MOVE_IMPRISON),
    LEVEL_UP_MOVE(57, MOVE_NIGHT_DAZE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 330
// Types: TYPE_DARK / TYPE_DARK
// Abilities: ABILITY_ILLUSION, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 17
