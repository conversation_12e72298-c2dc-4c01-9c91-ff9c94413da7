// POKEMON_572 (#572) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_572] =
    {
        .baseHP = 55,
        .baseAttack = 50,
        .baseDefense = 40,
        .baseSpAttack = 40,
        .baseSpDefense = 40,
        .baseSpeed = 75,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 255,
        .expYield = 60,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_CHESTO_BERRY,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(75),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_CUTECHARM,
        .ability2 = ABILITY_TECHNICIAN,
        .abilityHidden = ABILITY_SKILLLINK,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_572LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 3, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE( 7, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 9, MOVE_TICKLE),
    LEVEL_UP_MOVE(13, MOVE_DOUBLE_SLAP),
    LEVEL_UP_MOVE(15, MOVE_ENCORE),
    LEVEL_UP_MOVE(19, MOVE_SWIFT),
    LEVEL_UP_MOVE(21, MOVE_SING),
    LEVEL_UP_MOVE(25, MOVE_TAIL_SLAP),
    LEVEL_UP_MOVE(27, MOVE_CHARM),
    LEVEL_UP_MOVE(31, MOVE_WAKE_UP_SLAP),
    LEVEL_UP_MOVE(33, MOVE_ECHOED_VOICE),
    LEVEL_UP_MOVE(37, MOVE_SLAM),
    LEVEL_UP_MOVE(39, MOVE_CAPTIVATE),
    LEVEL_UP_MOVE(43, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(45, MOVE_LAST_RESORT),
    LEVEL_UP_MOVE(49, MOVE_AFTER_YOU),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 300
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_CUTECHARM, ABILITY_TECHNICIAN, ABILITY_SKILLLINK
// Level Up Moves: 17
