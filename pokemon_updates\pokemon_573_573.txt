// POKEMON_573 (#573) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_573] =
    {
        .baseHP = 75,
        .baseAttack = 95,
        .baseDefense = 60,
        .baseSpAttack = 65,
        .baseSpDefense = 60,
        .baseSpeed = 115,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 60,
        .expYield = 165,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_CHESTO_BERRY,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(75),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_CUTECHARM,
        .ability2 = ABILITY_TECHNICIAN,
        .abilityHidden = ABILITY_SKILLLINK,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_573LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_SLAM),
    LEVEL_UP_MOVE( 1, MOVE_SING),
    LEVEL_UP_MOVE( 1, MOVE_SWIFT),
    LEVEL_UP_MOVE( 1, MOVE_CHARM),
    LEVEL_UP_MOVE( 1, MOVE_ENCORE),
    LEVEL_UP_MOVE( 1, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 1, MOVE_TICKLE),
    LEVEL_UP_MOVE( 1, MOVE_BULLET_SEED),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_BLAST),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 470
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_CUTECHARM, ABILITY_TECHNICIAN, ABILITY_SKILLLINK
// Level Up Moves: 12
