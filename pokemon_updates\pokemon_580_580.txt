// POKEMON_580 (#580) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_580] =
    {
        .baseHP = 62,
        .baseAttack = 44,
        .baseDefense = 50,
        .baseSpAttack = 44,
        .baseSpDefense = 50,
        .baseSpeed = 55,
        .type1 = TYPE_WATER,
        .type2 = TYPE_FLYING,
        .catchRate = 190,
        .expYield = 61,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_KEENEYE,
        .ability2 = ABILITY_BIGPECKS,
        .abilityHidden = ABILITY_HYDRATION,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_580LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 3, MOVE_WATER_SPORT),
    LEVEL_UP_MOVE( 6, MOVE_DEFOG),
    LEVEL_UP_MOVE( 9, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(13, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(15, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(19, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(21, MOVE_FEATHER_DANCE),
    LEVEL_UP_MOVE(24, MOVE_AQUA_RING),
    LEVEL_UP_MOVE(27, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(30, MOVE_ROOST),
    LEVEL_UP_MOVE(34, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(37, MOVE_TAILWIND),
    LEVEL_UP_MOVE(41, MOVE_BRAVE_BIRD),
    LEVEL_UP_MOVE(46, MOVE_HURRICANE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 305
// Types: TYPE_WATER / TYPE_FLYING
// Abilities: ABILITY_KEENEYE, ABILITY_BIGPECKS, ABILITY_HYDRATION
// Level Up Moves: 15
