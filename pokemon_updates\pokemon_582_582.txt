// POKEMON_582 (#582) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_582] =
    {
        .baseHP = 36,
        .baseAttack = 50,
        .baseDefense = 50,
        .baseSpAttack = 65,
        .baseSpDefense = 60,
        .baseSpeed = 44,
        .type1 = TYPE_ICE,
        .type2 = TYPE_ICE,
        .catchRate = 255,
        .expYield = 61,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NEVER_MELT_ICE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_ICEBODY,
        .ability2 = ABILITY_SNOWCLOAK,
        .abilityHidden = ABILITY_WEAKARMOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_582LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ICICLE_SPEAR),
    LEVEL_UP_MOVE( 4, MOVE_HARDEN),
    LEVEL_UP_MOVE( 7, MOVE_ASTONISH),
    LEVEL_UP_MOVE(10, MOVE_UPROAR),
    LEVEL_UP_MOVE(13, MOVE_ICY_WIND),
    LEVEL_UP_MOVE(16, MOVE_MIST),
    LEVEL_UP_MOVE(19, MOVE_AVALANCHE),
    LEVEL_UP_MOVE(22, MOVE_TAUNT),
    LEVEL_UP_MOVE(26, MOVE_MIRROR_SHOT),
    LEVEL_UP_MOVE(31, MOVE_ACID_ARMOR),
    LEVEL_UP_MOVE(35, MOVE_ICE_BEAM),
    LEVEL_UP_MOVE(40, MOVE_HAIL),
    LEVEL_UP_MOVE(44, MOVE_MIRROR_COAT),
    LEVEL_UP_MOVE(49, MOVE_BLIZZARD),
    LEVEL_UP_MOVE(53, MOVE_SHEER_COLD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 305
// Types: TYPE_ICE / TYPE_ICE
// Abilities: ABILITY_ICEBODY, ABILITY_SNOWCLOAK, ABILITY_WEAKARMOR
// Level Up Moves: 15
