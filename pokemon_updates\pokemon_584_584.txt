// POKEMON_584 (#584) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_584] =
    {
        .baseHP = 71,
        .baseAttack = 95,
        .baseDefense = 85,
        .baseSpAttack = 110,
        .baseSpDefense = 95,
        .baseSpeed = 79,
        .type1 = TYPE_ICE,
        .type2 = TYPE_ICE,
        .catchRate = 45,
        .expYield = 268,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 3,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NEVER_MELT_ICE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_ICEBODY,
        .ability2 = ABILITY_SNOWWARNING,
        .abilityHidden = ABILITY_WEAKARMOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_584LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_UPROAR),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_WEATHER_BALL),
    LEVEL_UP_MOVE( 1, MOVE_SHEER_COLD),
    LEVEL_UP_MOVE( 1, MOVE_ICICLE_SPEAR),
    LEVEL_UP_MOVE( 1, MOVE_ICICLE_CRASH),
    LEVEL_UP_MOVE( 1, MOVE_FREEZE_DRY),
    LEVEL_UP_MOVE(13, MOVE_ICY_WIND),
    LEVEL_UP_MOVE(16, MOVE_MIST),
    LEVEL_UP_MOVE(19, MOVE_AVALANCHE),
    LEVEL_UP_MOVE(22, MOVE_TAUNT),
    LEVEL_UP_MOVE(26, MOVE_MIRROR_SHOT),
    LEVEL_UP_MOVE(31, MOVE_ACID_ARMOR),
    LEVEL_UP_MOVE(36, MOVE_ICE_BEAM),
    LEVEL_UP_MOVE(42, MOVE_HAIL),
    LEVEL_UP_MOVE(50, MOVE_MIRROR_COAT),
    LEVEL_UP_MOVE(59, MOVE_BLIZZARD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 535
// Types: TYPE_ICE / TYPE_ICE
// Abilities: ABILITY_ICEBODY, ABILITY_SNOWWARNING, ABILITY_WEAKARMOR
// Level Up Moves: 18
