// POKEMON_587 (#587) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_587] =
    {
        .baseHP = 55,
        .baseAttack = 75,
        .baseDefense = 60,
        .baseSpAttack = 75,
        .baseSpDefense = 60,
        .baseSpeed = 103,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_FLYING,
        .catchRate = 200,
        .expYield = 150,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_CHERI_BERRY,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_STATIC,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_MOTORDRIVE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_587LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 4, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 7, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE(10, MOVE_CHARGE),
    LEVEL_UP_MOVE(13, MOVE_SPARK),
    LEVEL_UP_MOVE(15, MOVE_NUZZLE),
    LEVEL_UP_MOVE(16, MOVE_PURSUIT),
    LEVEL_UP_MOVE(19, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(22, MOVE_SHOCK_WAVE),
    LEVEL_UP_MOVE(26, MOVE_ELECTRO_BALL),
    LEVEL_UP_MOVE(30, MOVE_ACROBATICS),
    LEVEL_UP_MOVE(34, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(38, MOVE_ENCORE),
    LEVEL_UP_MOVE(42, MOVE_VOLT_SWITCH),
    LEVEL_UP_MOVE(46, MOVE_AGILITY),
    LEVEL_UP_MOVE(50, MOVE_DISCHARGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 428
// Types: TYPE_ELECTRIC / TYPE_FLYING
// Abilities: ABILITY_STATIC, ABILITY_NONE, ABILITY_MOTORDRIVE
// Level Up Moves: 16
