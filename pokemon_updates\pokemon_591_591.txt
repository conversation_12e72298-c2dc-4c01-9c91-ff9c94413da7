// POKEMON_591 (#591) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_591] =
    {
        .baseHP = 114,
        .baseAttack = 85,
        .baseDefense = 70,
        .baseSpAttack = 85,
        .baseSpDefense = 80,
        .baseSpeed = 30,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_POISON,
        .catchRate = 75,
        .expYield = 162,
        .evYield_HP = 2,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_TINY_MUSHROOM,
        .item2 = ITEM_BIG_MUSHROOM,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_EFFECTSPORE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_REGENERATOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_591LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 1, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE( 1, MOVE_BIDE),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE(15, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(18, MOVE_INGRAIN),
    LEVEL_UP_MOVE(20, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE(24, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(28, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(32, MOVE_TOXIC),
    LEVEL_UP_MOVE(35, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(43, MOVE_CLEAR_SMOG),
    LEVEL_UP_MOVE(49, MOVE_SOLAR_BEAM),
    LEVEL_UP_MOVE(54, MOVE_RAGE_POWDER),
    LEVEL_UP_MOVE(62, MOVE_SPORE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 464
// Types: TYPE_GRASS / TYPE_POISON
// Abilities: ABILITY_EFFECTSPORE, ABILITY_NONE, ABILITY_REGENERATOR
// Level Up Moves: 16
