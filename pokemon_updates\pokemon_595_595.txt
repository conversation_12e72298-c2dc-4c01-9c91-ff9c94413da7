// POKEMON_595 (#595) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_595] =
    {
        .baseHP = 50,
        .baseAttack = 47,
        .baseDefense = 50,
        .baseSpAttack = 57,
        .baseSpDefense = 50,
        .baseSpeed = 65,
        .type1 = TYPE_BUG,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 190,
        .expYield = 64,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_COMPOUNDEYES,
        .ability2 = ABILITY_UNNERVE,
        .abilityHidden = ABILITY_SWARM,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_595LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_STRING_SHOT),
    LEVEL_UP_MOVE( 1, MOVE_SPIDER_WEB),
    LEVEL_UP_MOVE( 4, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE( 7, MOVE_SCREECH),
    LEVEL_UP_MOVE(12, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE(15, MOVE_ELECTROWEB),
    LEVEL_UP_MOVE(18, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(23, MOVE_GASTRO_ACID),
    LEVEL_UP_MOVE(26, MOVE_SLASH),
    LEVEL_UP_MOVE(29, MOVE_ELECTRO_BALL),
    LEVEL_UP_MOVE(34, MOVE_SIGNAL_BEAM),
    LEVEL_UP_MOVE(37, MOVE_AGILITY),
    LEVEL_UP_MOVE(40, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(45, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(48, MOVE_BUG_BUZZ),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 319
// Types: TYPE_BUG / TYPE_ELECTRIC
// Abilities: ABILITY_COMPOUNDEYES, ABILITY_UNNERVE, ABILITY_SWARM
// Level Up Moves: 16
