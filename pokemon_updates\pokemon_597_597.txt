// POKEMON_597 (#597) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_597] =
    {
        .baseHP = 44,
        .baseAttack = 50,
        .baseDefense = 91,
        .baseSpAttack = 24,
        .baseSpDefense = 86,
        .baseSpeed = 10,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_STEEL,
        .catchRate = 255,
        .expYield = 61,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_STICKY_BARB,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_IRONBARBS,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_597LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 6, MOVE_ROLLOUT),
    LEVEL_UP_MOVE( 9, MOVE_CURSE),
    LEVEL_UP_MOVE(14, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(18, MOVE_PIN_MISSILE),
    LEVEL_UP_MOVE(21, MOVE_GYRO_BALL),
    LEVEL_UP_MOVE(26, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(30, MOVE_MIRROR_SHOT),
    LEVEL_UP_MOVE(35, MOVE_INGRAIN),
    LEVEL_UP_MOVE(38, MOVE_SELF_DESTRUCT),
    LEVEL_UP_MOVE(43, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(47, MOVE_PAYBACK),
    LEVEL_UP_MOVE(52, MOVE_FLASH_CANNON),
    LEVEL_UP_MOVE(55, MOVE_EXPLOSION),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 305
// Types: TYPE_GRASS / TYPE_STEEL
// Abilities: ABILITY_IRONBARBS, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
