// POKEMON_600 (#600) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_600] =
    {
        .baseHP = 60,
        .baseAttack = 80,
        .baseDefense = 95,
        .baseSpAttack = 70,
        .baseSpDefense = 85,
        .baseSpeed = 50,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_STEEL,
        .catchRate = 60,
        .expYield = 154,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_PLUS,
        .ability2 = ABILITY_MINUS,
        .abilityHidden = ABILITY_CLEARBODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_600LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_VICE_GRIP),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 1, MOVE_CHARGE),
    LEVEL_UP_MOVE( 1, MOVE_GEAR_GRIND),
    LEVEL_UP_MOVE(21, MOVE_BIND),
    LEVEL_UP_MOVE(26, MOVE_CHARGE_BEAM),
    LEVEL_UP_MOVE(31, MOVE_AUTOTOMIZE),
    LEVEL_UP_MOVE(36, MOVE_MIRROR_SHOT),
    LEVEL_UP_MOVE(40, MOVE_SCREECH),
    LEVEL_UP_MOVE(44, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(48, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(52, MOVE_SHIFT_GEAR),
    LEVEL_UP_MOVE(56, MOVE_LOCK_ON),
    LEVEL_UP_MOVE(60, MOVE_ZAP_CANNON),
    LEVEL_UP_MOVE(64, MOVE_HYPER_BEAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 440
// Types: TYPE_STEEL / TYPE_STEEL
// Abilities: ABILITY_PLUS, ABILITY_MINUS, ABILITY_CLEARBODY
// Level Up Moves: 15
