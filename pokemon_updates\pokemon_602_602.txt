// POKEMON_602 (#602) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_602] =
    {
        .baseHP = 35,
        .baseAttack = 55,
        .baseDefense = 40,
        .baseSpAttack = 45,
        .baseSpDefense = 40,
        .baseSpeed = 60,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 190,
        .expYield = 55,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_INDETERMINATE,
        .eggGroup2 = EGG_GROUP_INDETERMINATE,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_602LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE( 1, MOVE_SPARK),
    LEVEL_UP_MOVE( 1, MOVE_CHARGE_BEAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 275
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_LEVITATE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 4
