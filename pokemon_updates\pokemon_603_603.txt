// POKEMON_603 (#603) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_603] =
    {
        .baseHP = 65,
        .baseAttack = 85,
        .baseDefense = 70,
        .baseSpAttack = 75,
        .baseSpDefense = 70,
        .baseSpeed = 40,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 60,
        .expYield = 142,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_INDETERMINATE,
        .eggGroup2 = EGG_GROUP_INDETERMINATE,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_603LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_CRUNCH),
    LEVEL_UP_MOVE( 1, MOVE_HEADBUTT),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE( 1, MOVE_SPARK),
    LEVEL_UP_MOVE( 1, MOVE_CHARGE_BEAM),
    LEVEL_UP_MOVE( 9, MOVE_BIND),
    LEVEL_UP_MOVE(19, MOVE_ACID),
    LEVEL_UP_MOVE(29, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(44, MOVE_THUNDERBOLT),
    LEVEL_UP_MOVE(49, MOVE_ACID_SPRAY),
    LEVEL_UP_MOVE(54, MOVE_COIL),
    LEVEL_UP_MOVE(59, MOVE_WILD_CHARGE),
    LEVEL_UP_MOVE(64, MOVE_GASTRO_ACID),
    LEVEL_UP_MOVE(69, MOVE_ZAP_CANNON),
    LEVEL_UP_MOVE(74, MOVE_THRASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 405
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_LEVITATE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
