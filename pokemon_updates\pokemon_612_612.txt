// POKEMON_612 (#612) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_612] =
    {
        .baseHP = 76,
        .baseAttack = 147,
        .baseDefense = 90,
        .baseSpAttack = 60,
        .baseSpDefense = 70,
        .baseSpeed = 97,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 270,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 40,
        .friendship = 35,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_RIVALRY,
        .ability2 = ABILITY_MOLDBREAKER,
        .abilityHidden = ABILITY_UNNERVE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_612LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_RAGE),
    LEVEL_UP_MOVE( 1, MOVE_OUTRAGE),
    LEVEL_UP_MOVE( 1, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(13, MOVE_DUAL_CHOP),
    LEVEL_UP_MOVE(16, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(20, MOVE_SLASH),
    LEVEL_UP_MOVE(24, MOVE_FALSE_SWIPE),
    LEVEL_UP_MOVE(24, MOVE_CRUNCH),
    LEVEL_UP_MOVE(28, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(32, MOVE_DRAGON_DANCE),
    LEVEL_UP_MOVE(36, MOVE_TAUNT),
    LEVEL_UP_MOVE(42, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(50, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(58, MOVE_GUILLOTINE),
    LEVEL_UP_MOVE(74, MOVE_GIGA_IMPACT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 540
// Types: TYPE_DRAGON / TYPE_DRAGON
// Abilities: ABILITY_RIVALRY, ABILITY_MOLDBREAKER, ABILITY_UNNERVE
// Level Up Moves: 18
