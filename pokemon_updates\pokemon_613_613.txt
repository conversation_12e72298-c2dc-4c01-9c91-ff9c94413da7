// POKEMON_613 (#613) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_613] =
    {
        .baseHP = 55,
        .baseAttack = 70,
        .baseDefense = 40,
        .baseSpAttack = 60,
        .baseSpDefense = 40,
        .baseSpeed = 40,
        .type1 = TYPE_ICE,
        .type2 = TYPE_ICE,
        .catchRate = 120,
        .expYield = 61,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_ASPEAR_BERRY,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_SNOWCLOAK,
        .ability2 = ABILITY_SLUSHRUSH,
        .abilityHidden = ABILITY_RATTLED,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_613LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 5, MOVE_POWDER_SNOW),
    LEVEL_UP_MOVE( 9, MOVE_BIDE),
    LEVEL_UP_MOVE(13, MOVE_ICY_WIND),
    LEVEL_UP_MOVE(15, MOVE_PLAY_NICE),
    LEVEL_UP_MOVE(17, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(21, MOVE_BRINE),
    LEVEL_UP_MOVE(25, MOVE_ENDURE),
    LEVEL_UP_MOVE(29, MOVE_CHARM),
    LEVEL_UP_MOVE(30, MOVE_SNOWSCAPE),
    LEVEL_UP_MOVE(33, MOVE_SLASH),
    LEVEL_UP_MOVE(36, MOVE_FLAIL),
    LEVEL_UP_MOVE(41, MOVE_REST),
    LEVEL_UP_MOVE(45, MOVE_BLIZZARD),
    LEVEL_UP_MOVE(49, MOVE_HAIL),
    LEVEL_UP_MOVE(53, MOVE_THRASH),
    LEVEL_UP_MOVE(57, MOVE_SHEER_COLD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 305
// Types: TYPE_ICE / TYPE_ICE
// Abilities: ABILITY_SNOWCLOAK, ABILITY_SLUSHRUSH, ABILITY_RATTLED
// Level Up Moves: 17
