// POKEMON_621 (#621) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_621] =
    {
        .baseHP = 77,
        .baseAttack = 120,
        .baseDefense = 90,
        .baseSpAttack = 60,
        .baseSpDefense = 90,
        .baseSpeed = 48,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 170,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_DRAGON_FANG,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_DRAGON,
        .eggGroup2 = EGG_GROUP_MONSTER,
        .ability1 = ABILITY_ROUGHSKIN,
        .ability2 = ABILITY_SHEERFORCE,
        .abilityHidden = ABILITY_MOLDBREAKER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_621LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 5, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE( 9, MOVE_BITE),
    LEVEL_UP_MOVE(13, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(18, MOVE_DRAGON_RAGE),
    LEVEL_UP_MOVE(21, MOVE_SLASH),
    LEVEL_UP_MOVE(25, MOVE_CRUNCH),
    LEVEL_UP_MOVE(27, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(31, MOVE_CHIP_AWAY),
    LEVEL_UP_MOVE(35, MOVE_REVENGE),
    LEVEL_UP_MOVE(40, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(45, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE(49, MOVE_ROCK_CLIMB),
    LEVEL_UP_MOVE(55, MOVE_SUPERPOWER),
    LEVEL_UP_MOVE(62, MOVE_OUTRAGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 485
// Types: TYPE_DRAGON / TYPE_DRAGON
// Abilities: ABILITY_ROUGHSKIN, ABILITY_SHEERFORCE, ABILITY_MOLDBREAKER
// Level Up Moves: 16
