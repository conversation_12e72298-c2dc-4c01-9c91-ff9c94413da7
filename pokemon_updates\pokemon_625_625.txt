// POKEMON_625 (#625) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_625] =
    {
        .baseHP = 65,
        .baseAttack = 125,
        .baseDefense = 100,
        .baseSpAttack = 60,
        .baseSpDefense = 70,
        .baseSpeed = 70,
        .type1 = TYPE_DARK,
        .type2 = TYPE_STEEL,
        .catchRate = 45,
        .expYield = 172,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_HUMANSHAPE,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_DEFIANT,
        .ability2 = ABILITY_INNERFOCUS,
        .abilityHidden = ABILITY_PRESSURE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_625LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_GUILLOTINE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 1, MOVE_TORMENT),
    LEVEL_UP_MOVE( 1, MOVE_METAL_BURST),
    LEVEL_UP_MOVE( 1, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(17, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE(22, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(25, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(30, MOVE_SLASH),
    LEVEL_UP_MOVE(33, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(38, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(41, MOVE_EMBARGO),
    LEVEL_UP_MOVE(46, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(49, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(63, MOVE_SWORDS_DANCE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 490
// Types: TYPE_DARK / TYPE_STEEL
// Abilities: ABILITY_DEFIANT, ABILITY_INNERFOCUS, ABILITY_PRESSURE
// Level Up Moves: 17
