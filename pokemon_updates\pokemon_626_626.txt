// POKEMON_626 (#626) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_626] =
    {
        .baseHP = 95,
        .baseAttack = 110,
        .baseDefense = 95,
        .baseSpAttack = 40,
        .baseSpDefense = 95,
        .baseSpeed = 55,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 45,
        .expYield = 172,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_RECKLESS,
        .ability2 = ABILITY_SAPSIPPER,
        .abilityHidden = ABILITY_SOUNDPROOF,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_626LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_PURSUIT),
    LEVEL_UP_MOVE( 6, MOVE_RAGE),
    LEVEL_UP_MOVE(11, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(16, MOVE_HORN_ATTACK),
    LEVEL_UP_MOVE(21, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(26, MOVE_REVENGE),
    LEVEL_UP_MOVE(31, MOVE_HEAD_CHARGE),
    LEVEL_UP_MOVE(35, MOVE_THROAT_CHOP),
    LEVEL_UP_MOVE(36, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(41, MOVE_MEGAHORN),
    LEVEL_UP_MOVE(46, MOVE_REVERSAL),
    LEVEL_UP_MOVE(50, MOVE_THRASH),
    LEVEL_UP_MOVE(56, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(61, MOVE_GIGA_IMPACT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 490
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_RECKLESS, ABILITY_SAPSIPPER, ABILITY_SOUNDPROOF
// Level Up Moves: 16
