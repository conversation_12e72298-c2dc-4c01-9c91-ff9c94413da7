// POKEMON_627 (#627) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_627] =
    {
        .baseHP = 70,
        .baseAttack = 83,
        .baseDefense = 50,
        .baseSpAttack = 37,
        .baseSpDefense = 50,
        .baseSpeed = 60,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 190,
        .expYield = 70,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_KEENEYE,
        .ability2 = ABILITY_SHEERFORCE,
        .abilityHidden = ABILITY_HUSTLE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_627LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 5, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(10, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(14, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(19, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(23, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(28, MOVE_SLASH),
    LEVEL_UP_MOVE(32, MOVE_DEFOG),
    LEVEL_UP_MOVE(37, MOVE_TAILWIND),
    LEVEL_UP_MOVE(41, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(46, MOVE_CRUSH_CLAW),
    LEVEL_UP_MOVE(50, MOVE_SKY_DROP),
    LEVEL_UP_MOVE(55, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(59, MOVE_BRAVE_BIRD),
    LEVEL_UP_MOVE(64, MOVE_THRASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 350
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_KEENEYE, ABILITY_SHEERFORCE, ABILITY_HUSTLE
// Level Up Moves: 16
