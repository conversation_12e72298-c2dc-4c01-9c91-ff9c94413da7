// POKEMON_629 (#629) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_629] =
    {
        .baseHP = 70,
        .baseAttack = 55,
        .baseDefense = 75,
        .baseSpAttack = 45,
        .baseSpDefense = 65,
        .baseSpeed = 60,
        .type1 = TYPE_DARK,
        .type2 = TYPE_FLYING,
        .catchRate = 190,
        .expYield = 74,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(100),
        .eggCycles = 20,
        .friendship = 35,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_BIGPECKS,
        .ability2 = ABILITY_OVERCOAT,
        .abilityHidden = ABILITY_WEAKARMOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_629LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 5, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(10, MOVE_PLUCK),
    LEVEL_UP_MOVE(14, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(19, MOVE_FLATTER),
    LEVEL_UP_MOVE(23, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE(28, MOVE_PUNISHMENT),
    LEVEL_UP_MOVE(30, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(32, MOVE_DEFOG),
    LEVEL_UP_MOVE(37, MOVE_TAILWIND),
    LEVEL_UP_MOVE(41, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(46, MOVE_DARK_PULSE),
    LEVEL_UP_MOVE(50, MOVE_EMBARGO),
    LEVEL_UP_MOVE(55, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(59, MOVE_BRAVE_BIRD),
    LEVEL_UP_MOVE(64, MOVE_MIRROR_MOVE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 370
// Types: TYPE_DARK / TYPE_FLYING
// Abilities: ABILITY_BIGPECKS, ABILITY_OVERCOAT, ABILITY_WEAKARMOR
// Level Up Moves: 17
