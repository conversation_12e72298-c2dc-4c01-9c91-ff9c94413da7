// POKEMON_630 (#630) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_630] =
    {
        .baseHP = 110,
        .baseAttack = 65,
        .baseDefense = 105,
        .baseSpAttack = 55,
        .baseSpDefense = 95,
        .baseSpeed = 80,
        .type1 = TYPE_DARK,
        .type2 = TYPE_FLYING,
        .catchRate = 60,
        .expYield = 179,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(100),
        .eggCycles = 20,
        .friendship = 35,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_BIGPECKS,
        .ability2 = ABILITY_OVERCOAT,
        .abilityHidden = ABILITY_WEAKARMOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_630LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_BONE_RUSH),
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 1, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE( 1, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_MIRROR_MOVE),
    LEVEL_UP_MOVE( 1, MOVE_PLUCK),
    LEVEL_UP_MOVE( 1, MOVE_BRAVE_BIRD),
    LEVEL_UP_MOVE(14, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(19, MOVE_FLATTER),
    LEVEL_UP_MOVE(23, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE(28, MOVE_PUNISHMENT),
    LEVEL_UP_MOVE(32, MOVE_DEFOG),
    LEVEL_UP_MOVE(37, MOVE_TAILWIND),
    LEVEL_UP_MOVE(41, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(46, MOVE_DARK_PULSE),
    LEVEL_UP_MOVE(50, MOVE_EMBARGO),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 510
// Types: TYPE_DARK / TYPE_FLYING
// Abilities: ABILITY_BIGPECKS, ABILITY_OVERCOAT, ABILITY_WEAKARMOR
// Level Up Moves: 17
