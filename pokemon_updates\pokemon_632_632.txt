// POKEMON_632 (#632) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_632] =
    {
        .baseHP = 58,
        .baseAttack = 109,
        .baseDefense = 112,
        .baseSpAttack = 48,
        .baseSpDefense = 48,
        .baseSpeed = 109,
        .type1 = TYPE_BUG,
        .type2 = TYPE_STEEL,
        .catchRate = 90,
        .expYield = 169,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_SWARM,
        .ability2 = ABILITY_HUSTLE,
        .abilityHidden = ABILITY_TRUANT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_632LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_VICE_GRIP),
    LEVEL_UP_MOVE( 1, MOVE_GUILLOTINE),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 1, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE( 6, MOVE_AGILITY),
    LEVEL_UP_MOVE(11, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(12, MOVE_BEAT_UP),
    LEVEL_UP_MOVE(16, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(21, MOVE_CRUNCH),
    LEVEL_UP_MOVE(26, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(31, MOVE_DIG),
    LEVEL_UP_MOVE(36, MOVE_ENTRAINMENT),
    LEVEL_UP_MOVE(41, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(46, MOVE_IRON_DEFENSE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 484
// Types: TYPE_BUG / TYPE_STEEL
// Abilities: ABILITY_SWARM, ABILITY_HUSTLE, ABILITY_TRUANT
// Level Up Moves: 16
