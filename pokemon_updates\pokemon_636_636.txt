// POKEMON_636 (#636) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_636] =
    {
        .baseHP = 55,
        .baseAttack = 85,
        .baseDefense = 55,
        .baseSpAttack = 50,
        .baseSpDefense = 55,
        .baseSpeed = 60,
        .type1 = TYPE_BUG,
        .type2 = TYPE_FIRE,
        .catchRate = 45,
        .expYield = 72,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 40,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_FLAMEBODY,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_SWARM,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_636LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_STRING_SHOT),
    LEVEL_UP_MOVE(10, MOVE_ABSORB),
    LEVEL_UP_MOVE(12, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE(20, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(30, MOVE_SCREECH),
    LEVEL_UP_MOVE(30, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE(40, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(50, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(60, MOVE_FLAME_WHEEL),
    LEVEL_UP_MOVE(70, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(80, MOVE_AMNESIA),
    LEVEL_UP_MOVE(90, MOVE_THRASH),
    LEVEL_UP_MOVE(100, MOVE_FLARE_BLITZ),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 360
// Types: TYPE_BUG / TYPE_FIRE
// Abilities: ABILITY_FLAMEBODY, ABILITY_NONE, ABILITY_SWARM
// Level Up Moves: 14
