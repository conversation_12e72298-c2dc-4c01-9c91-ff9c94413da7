// POKEMON_638 (#638) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_638] =
    {
        .baseHP = 91,
        .baseAttack = 90,
        .baseDefense = 129,
        .baseSpAttack = 90,
        .baseSpDefense = 72,
        .baseSpeed = 108,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_FIGHTING,
        .catchRate = 3,
        .expYield = 290,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 3,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 80,
        .friendship = 35,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_JUSTIFIED,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_638LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE( 1, MOVE_METAL_BURST),
    LEVEL_UP_MOVE( 1, MOVE_CLOSE_COMBAT),
    LEVEL_UP_MOVE( 7, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(13, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(19, MOVE_RETALIATE),
    LEVEL_UP_MOVE(25, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(31, MOVE_SACRED_SWORD),
    LEVEL_UP_MOVE(37, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(42, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(49, MOVE_WORK_UP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 580
// Types: TYPE_STEEL / TYPE_FIGHTING
// Abilities: ABILITY_JUSTIFIED, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
