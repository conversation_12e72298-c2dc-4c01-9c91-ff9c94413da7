// POKEMON_643 (#643) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_643] =
    {
        .baseHP = 100,
        .baseAttack = 120,
        .baseDefense = 100,
        .baseSpAttack = 150,
        .baseSpDefense = 120,
        .baseSpeed = 90,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_FIRE,
        .catchRate = 3,
        .expYield = 340,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 3,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_TURBOBLAZE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_643LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_RAGE),
    LEVEL_UP_MOVE( 1, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE( 8, MOVE_IMPRISON),
    LEVEL_UP_MOVE(15, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(22, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(29, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(36, MOVE_SLASH),
    LEVEL_UP_MOVE(43, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE(50, MOVE_FUSION_FLARE),
    LEVEL_UP_MOVE(54, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(64, MOVE_NOBLE_ROAR),
    LEVEL_UP_MOVE(71, MOVE_CRUNCH),
    LEVEL_UP_MOVE(78, MOVE_FIRE_BLAST),
    LEVEL_UP_MOVE(85, MOVE_OUTRAGE),
    LEVEL_UP_MOVE(92, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(100, MOVE_BLUE_FLARE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 680
// Types: TYPE_DRAGON / TYPE_FIRE
// Abilities: ABILITY_TURBOBLAZE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
