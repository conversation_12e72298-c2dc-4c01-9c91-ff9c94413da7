// POKEMON_648 (#648) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_648] =
    {
        .baseHP = 100,
        .baseAttack = 77,
        .baseDefense = 77,
        .baseSpAttack = 128,
        .baseSpDefense = 128,
        .baseSpeed = 90,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 3,
        .expYield = 270,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 1,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_STAR_PIECE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 100,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_SERENEGRACE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_648LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SING),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_ROUND),
    LEVEL_UP_MOVE(21, MOVE_TEETER_DANCE),
    LEVEL_UP_MOVE(26, MOVE_ACROBATICS),
    LEVEL_UP_MOVE(31, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(36, MOVE_ECHOED_VOICE),
    LEVEL_UP_MOVE(43, MOVE_U_TURN),
    LEVEL_UP_MOVE(50, MOVE_WAKE_UP_SLAP),
    LEVEL_UP_MOVE(57, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(64, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(71, MOVE_ROLE_PLAY),
    LEVEL_UP_MOVE(78, MOVE_CLOSE_COMBAT),
    LEVEL_UP_MOVE(85, MOVE_PERISH_SONG),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_NORMAL / TYPE_PSYCHIC
// Abilities: ABILITY_SERENEGRACE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
