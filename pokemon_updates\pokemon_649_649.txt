// POKEMON_649 (#649) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_649] =
    {
        .baseHP = 71,
        .baseAttack = 120,
        .baseDefense = 95,
        .baseSpAttack = 120,
        .baseSpDefense = 95,
        .baseSpeed = 99,
        .type1 = TYPE_BUG,
        .type2 = TYPE_STEEL,
        .catchRate = 3,
        .expYield = 300,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_DOWNLOAD,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_649LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_SCREECH),
    LEVEL_UP_MOVE( 1, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE( 1, MOVE_MAGNET_RISE),
    LEVEL_UP_MOVE( 1, MOVE_TECHNO_BLAST),
    LEVEL_UP_MOVE( 1, MOVE_FELL_STINGER),
    LEVEL_UP_MOVE( 7, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE(11, MOVE_LOCK_ON),
    LEVEL_UP_MOVE(18, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE(22, MOVE_MAGNET_BOMB),
    LEVEL_UP_MOVE(29, MOVE_SLASH),
    LEVEL_UP_MOVE(33, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(40, MOVE_SIGNAL_BEAM),
    LEVEL_UP_MOVE(44, MOVE_TRI_ATTACK),
    LEVEL_UP_MOVE(51, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(55, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(62, MOVE_SIMPLE_BEAM),
    LEVEL_UP_MOVE(66, MOVE_ZAP_CANNON),
    LEVEL_UP_MOVE(73, MOVE_HYPER_BEAM),
    LEVEL_UP_MOVE(77, MOVE_SELF_DESTRUCT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_BUG / TYPE_STEEL
// Abilities: ABILITY_DOWNLOAD, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 20
