// POKEMON_650 (#650) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_650] =
    {
        .baseHP = 56,
        .baseAttack = 61,
        .baseDefense = 65,
        .baseSpAttack = 48,
        .baseSpDefense = 45,
        .baseSpeed = 38,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 45,
        .expYield = 63,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_OVERGROW,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_BULLETPROOF,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_650LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_VINE_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 8, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(11, MOVE_BITE),
    LEVEL_UP_MOVE(15, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(18, MOVE_PIN_MISSILE),
    LEVEL_UP_MOVE(27, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(32, MOVE_SEED_BOMB),
    LEVEL_UP_MOVE(35, MOVE_MUD_SHOT),
    LEVEL_UP_MOVE(39, MOVE_BULK_UP),
    LEVEL_UP_MOVE(42, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(45, MOVE_PAIN_SPLIT),
    LEVEL_UP_MOVE(48, MOVE_WOOD_HAMMER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 313
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_OVERGROW, ABILITY_NONE, ABILITY_BULLETPROOF
// Level Up Moves: 13
