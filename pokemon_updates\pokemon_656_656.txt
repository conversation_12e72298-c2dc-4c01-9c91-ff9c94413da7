// POKEMON_656 (#656) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_656] =
    {
        .baseHP = 41,
        .baseAttack = 56,
        .baseDefense = 40,
        .baseSpAttack = 62,
        .baseSpDefense = 44,
        .baseSpeed = 71,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 45,
        .expYield = 63,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_WATER_1,
        .ability1 = ABILITY_TORRENT,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_PROTEAN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_656LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 5, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 5, MOVE_BUBBLE),
    LEVEL_UP_MOVE( 8, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(10, MOVE_LICK),
    LEVEL_UP_MOVE(14, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(18, MOVE_SMOKESCREEN),
    LEVEL_UP_MOVE(21, MOVE_ROUND),
    LEVEL_UP_MOVE(25, MOVE_FLING),
    LEVEL_UP_MOVE(29, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE(35, MOVE_SUBSTITUTE),
    LEVEL_UP_MOVE(39, MOVE_BOUNCE),
    LEVEL_UP_MOVE(43, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(48, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 314
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_TORRENT, ABILITY_NONE, ABILITY_PROTEAN
// Level Up Moves: 15
