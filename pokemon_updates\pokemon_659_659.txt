// POKEMON_659 (#659) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_659] =
    {
        .baseHP = 38,
        .baseAttack = 36,
        .baseDefense = 38,
        .baseSpAttack = 32,
        .baseSpDefense = 36,
        .baseSpeed = 57,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 255,
        .expYield = 47,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_PICKUP,
        .ability2 = ABILITY_CHEEKPOUCH,
        .abilityHidden = ABILITY_HUGEPOWER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_659LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_AGILITY),
    LEVEL_UP_MOVE( 6, MOVE_LASER_FOCUS),
    LEVEL_UP_MOVE( 7, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(10, MOVE_DOUBLE_SLAP),
    LEVEL_UP_MOVE(13, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE(15, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(18, MOVE_MUD_SHOT),
    LEVEL_UP_MOVE(20, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(25, MOVE_ODOR_SLEUTH),
    LEVEL_UP_MOVE(29, MOVE_FLAIL),
    LEVEL_UP_MOVE(33, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(33, MOVE_DIG),
    LEVEL_UP_MOVE(38, MOVE_BOUNCE),
    LEVEL_UP_MOVE(42, MOVE_SUPER_FANG),
    LEVEL_UP_MOVE(47, MOVE_FACADE),
    LEVEL_UP_MOVE(49, MOVE_EARTHQUAKE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 237
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_PICKUP, ABILITY_CHEEKPOUCH, ABILITY_HUGEPOWER
// Level Up Moves: 18
