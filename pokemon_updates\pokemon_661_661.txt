// POKEMON_661 (#661) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_661] =
    {
        .baseHP = 45,
        .baseAttack = 50,
        .baseDefense = 43,
        .baseSpAttack = 40,
        .baseSpDefense = 38,
        .baseSpeed = 62,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 255,
        .expYield = 56,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_BIGPECKS,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_GALEWINGS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_661LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 6, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(10, MOVE_EMBER),
    LEVEL_UP_MOVE(10, MOVE_PECK),
    LEVEL_UP_MOVE(13, MOVE_AGILITY),
    LEVEL_UP_MOVE(16, MOVE_FLAIL),
    LEVEL_UP_MOVE(21, MOVE_ROOST),
    LEVEL_UP_MOVE(25, MOVE_RAZOR_WIND),
    LEVEL_UP_MOVE(29, MOVE_NATURAL_GIFT),
    LEVEL_UP_MOVE(34, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE(39, MOVE_ACROBATICS),
    LEVEL_UP_MOVE(41, MOVE_ME_FIRST),
    LEVEL_UP_MOVE(45, MOVE_TAILWIND),
    LEVEL_UP_MOVE(48, MOVE_STEEL_WING),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 278
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_BIGPECKS, ABILITY_NONE, ABILITY_GALEWINGS
// Level Up Moves: 15
