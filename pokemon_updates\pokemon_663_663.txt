// POKEMON_663 (#663) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_663] =
    {
        .baseHP = 78,
        .baseAttack = 81,
        .baseDefense = 71,
        .baseSpAttack = 74,
        .baseSpDefense = 69,
        .baseSpeed = 126,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 175,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 3,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_FLAMEBODY,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_GALEWINGS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_663LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_FEINT),
    LEVEL_UP_MOVE( 1, MOVE_FLARE_BLITZ),
    LEVEL_UP_MOVE( 1, MOVE_BRAVE_BIRD),
    LEVEL_UP_MOVE(13, MOVE_AGILITY),
    LEVEL_UP_MOVE(16, MOVE_FLAIL),
    LEVEL_UP_MOVE(25, MOVE_ROOST),
    LEVEL_UP_MOVE(27, MOVE_RAZOR_WIND),
    LEVEL_UP_MOVE(31, MOVE_NATURAL_GIFT),
    LEVEL_UP_MOVE(39, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE(44, MOVE_ACROBATICS),
    LEVEL_UP_MOVE(49, MOVE_ME_FIRST),
    LEVEL_UP_MOVE(55, MOVE_TAILWIND),
    LEVEL_UP_MOVE(60, MOVE_STEEL_WING),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 499
// Types: TYPE_FIRE / TYPE_FLYING
// Abilities: ABILITY_FLAMEBODY, ABILITY_NONE, ABILITY_GALEWINGS
// Level Up Moves: 18
