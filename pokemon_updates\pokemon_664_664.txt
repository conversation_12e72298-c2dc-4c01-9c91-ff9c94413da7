// POKEMON_664 (#664) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_664] =
    {
        .baseHP = 38,
        .baseAttack = 35,
        .baseDefense = 40,
        .baseSpAttack = 27,
        .baseSpDefense = 25,
        .baseSpeed = 35,
        .type1 = TYPE_BUG,
        .type2 = TYPE_BUG,
        .catchRate = 255,
        .expYield = 40,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_SHIELDDUST,
        .ability2 = ABILITY_COMPOUNDEYES,
        .abilityHidden = ABILITY_FRIENDGUARD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_664LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_STRING_SHOT),
    LEVEL_UP_MOVE( 6, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE(15, MOVE_BUG_BITE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 200
// Types: TYPE_BUG / TYPE_BUG
// Abilities: ABILITY_SHIELDDUST, ABILITY_COMPOUNDEYES, ABILITY_FRIENDGUARD
// Level Up Moves: 4
