// POKEMON_666 (#666) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_666] =
    {
        .baseHP = 80,
        .baseAttack = 52,
        .baseDefense = 50,
        .baseSpAttack = 90,
        .baseSpDefense = 50,
        .baseSpeed = 89,
        .type1 = TYPE_BUG,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 185,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_SHIELDDUST,
        .ability2 = ABILITY_COMPOUNDEYES,
        .abilityHidden = ABILITY_FRIENDGUARD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_666LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_GUST),
    LEVEL_UP_MOVE( 1, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE( 1, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE( 1, MOVE_SLEEP_POWDER),
    LEVEL_UP_MOVE( 1, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE( 1, MOVE_POWDER),
    LEVEL_UP_MOVE(12, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(17, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(21, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(25, MOVE_DRAINING_KISS),
    LEVEL_UP_MOVE(31, MOVE_AROMATHERAPY),
    LEVEL_UP_MOVE(35, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(41, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(45, MOVE_QUIVER_DANCE),
    LEVEL_UP_MOVE(50, MOVE_HURRICANE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 411
// Types: TYPE_BUG / TYPE_FLYING
// Abilities: ABILITY_SHIELDDUST, ABILITY_COMPOUNDEYES, ABILITY_FRIENDGUARD
// Level Up Moves: 15
