// POKEMON_670 (#670) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_670] =
    {
        .baseHP = 54,
        .baseAttack = 45,
        .baseDefense = 47,
        .baseSpAttack = 75,
        .baseSpDefense = 98,
        .baseSpeed = 52,
        .type1 = TYPE_FAIRY,
        .type2 = TYPE_FAIRY,
        .catchRate = 120,
        .expYield = 130,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 2,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(100),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_FAIRY,
        .eggGroup2 = EGG_GROUP_FAIRY,
        .ability1 = ABILITY_FLOWERVEIL,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_SYMBIOSIS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_670LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_VINE_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_FAIRY_WIND),
    LEVEL_UP_MOVE(10, MOVE_LUCKY_CHANT),
    LEVEL_UP_MOVE(15, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(20, MOVE_WISH),
    LEVEL_UP_MOVE(25, MOVE_MAGICAL_LEAF),
    LEVEL_UP_MOVE(27, MOVE_GRASSY_TERRAIN),
    LEVEL_UP_MOVE(33, MOVE_PETAL_BLIZZARD),
    LEVEL_UP_MOVE(38, MOVE_AROMATHERAPY),
    LEVEL_UP_MOVE(43, MOVE_MISTY_TERRAIN),
    LEVEL_UP_MOVE(46, MOVE_MOONBLAST),
    LEVEL_UP_MOVE(51, MOVE_PETAL_DANCE),
    LEVEL_UP_MOVE(58, MOVE_SOLAR_BEAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 371
// Types: TYPE_FAIRY / TYPE_FAIRY
// Abilities: ABILITY_FLOWERVEIL, ABILITY_NONE, ABILITY_SYMBIOSIS
// Level Up Moves: 14
