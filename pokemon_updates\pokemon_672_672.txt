// POKEMON_672 (#672) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_672] =
    {
        .baseHP = 66,
        .baseAttack = 65,
        .baseDefense = 48,
        .baseSpAttack = 62,
        .baseSpDefense = 57,
        .baseSpeed = 52,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 200,
        .expYield = 70,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_SAPSIPPER,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_GRASSPELT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_672LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 7, MOVE_VINE_WHIP),
    LEVEL_UP_MOVE( 9, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE(12, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(13, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(16, MOVE_WORRY_SEED),
    LEVEL_UP_MOVE(20, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(22, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(26, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(30, MOVE_SEED_BOMB),
    LEVEL_UP_MOVE(34, MOVE_BULK_UP),
    LEVEL_UP_MOVE(38, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(42, MOVE_HORN_LEECH),
    LEVEL_UP_MOVE(45, MOVE_LEAF_BLADE),
    LEVEL_UP_MOVE(50, MOVE_MILK_DRINK),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 350
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_SAPSIPPER, ABILITY_NONE, ABILITY_GRASSPELT
// Level Up Moves: 16
