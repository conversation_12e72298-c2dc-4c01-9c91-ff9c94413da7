// POKEMON_674 (#674) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_674] =
    {
        .baseHP = 67,
        .baseAttack = 82,
        .baseDefense = 62,
        .baseSpAttack = 46,
        .baseSpDefense = 48,
        .baseSpeed = 43,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 220,
        .expYield = 70,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_MENTAL_HERB,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_IRONFIST,
        .ability2 = ABILITY_MOLDBREAKER,
        .abilityHidden = ABILITY_SCRAPPY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_674LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 7, MOVE_ARM_THRUST),
    LEVEL_UP_MOVE( 8, MOVE_TAUNT),
    LEVEL_UP_MOVE(10, MOVE_WORK_UP),
    LEVEL_UP_MOVE(12, MOVE_KARATE_CHOP),
    LEVEL_UP_MOVE(15, MOVE_COMET_PUNCH),
    LEVEL_UP_MOVE(20, MOVE_SLASH),
    LEVEL_UP_MOVE(25, MOVE_CIRCLE_THROW),
    LEVEL_UP_MOVE(27, MOVE_VITAL_THROW),
    LEVEL_UP_MOVE(33, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(39, MOVE_CRUNCH),
    LEVEL_UP_MOVE(42, MOVE_ENTRAINMENT),
    LEVEL_UP_MOVE(45, MOVE_PARTING_SHOT),
    LEVEL_UP_MOVE(48, MOVE_SKY_UPPERCUT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 348
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_IRONFIST, ABILITY_MOLDBREAKER, ABILITY_SCRAPPY
// Level Up Moves: 15
