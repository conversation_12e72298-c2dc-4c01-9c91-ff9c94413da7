// POKEMON_676 (#676) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_676] =
    {
        .baseHP = 75,
        .baseAttack = 80,
        .baseDefense = 60,
        .baseSpAttack = 65,
        .baseSpDefense = 90,
        .baseSpeed = 102,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 160,
        .expYield = 165,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_FURCOAT,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_676LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 5, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 9, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(12, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(15, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE(22, MOVE_BITE),
    LEVEL_UP_MOVE(27, MOVE_ODOR_SLEUTH),
    LEVEL_UP_MOVE(33, MOVE_RETALIATE),
    LEVEL_UP_MOVE(35, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(38, MOVE_CHARM),
    LEVEL_UP_MOVE(42, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(48, MOVE_COTTON_GUARD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 472
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_FURCOAT, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 13
