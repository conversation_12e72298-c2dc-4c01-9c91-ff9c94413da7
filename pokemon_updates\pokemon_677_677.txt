// POKEMON_677 (#677) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_677] =
    {
        .baseHP = 62,
        .baseAttack = 48,
        .baseDefense = 54,
        .baseSpAttack = 63,
        .baseSpDefense = 60,
        .baseSpeed = 68,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 190,
        .expYield = 71,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_KEENEYE,
        .ability2 = ABILITY_INFILTRATOR,
        .abilityHidden = ABILITY_OWNTEMPO,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_677LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 5, MOVE_COVET),
    LEVEL_UP_MOVE( 9, MOVE_CONFUSION),
    LEVEL_UP_MOVE(13, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(17, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(19, MOVE_FAKE_OUT),
    LEVEL_UP_MOVE(22, MOVE_DISARMING_VOICE),
    LEVEL_UP_MOVE(25, MOVE_PSYSHOCK),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 355
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_KEENEYE, ABILITY_INFILTRATOR, ABILITY_OWNTEMPO
// Level Up Moves: 9
