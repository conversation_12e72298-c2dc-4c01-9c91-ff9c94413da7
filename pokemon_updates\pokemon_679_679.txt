// POKEMON_679 (#679) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_679] =
    {
        .baseHP = 45,
        .baseAttack = 80,
        .baseDefense = 100,
        .baseSpAttack = 35,
        .baseSpDefense = 37,
        .baseSpeed = 28,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_GHOST,
        .catchRate = 180,
        .expYield = 65,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_NOGUARD,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_679LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 8, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(13, MOVE_PURSUIT),
    LEVEL_UP_MOVE(18, MOVE_AUTOTOMIZE),
    LEVEL_UP_MOVE(20, MOVE_SHADOW_SNEAK),
    LEVEL_UP_MOVE(22, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(26, MOVE_RETALIATE),
    LEVEL_UP_MOVE(29, MOVE_SLASH),
    LEVEL_UP_MOVE(32, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(35, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(39, MOVE_POWER_TRICK),
    LEVEL_UP_MOVE(42, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(47, MOVE_SACRED_SWORD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 325
// Types: TYPE_STEEL / TYPE_GHOST
// Abilities: ABILITY_NOGUARD, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
