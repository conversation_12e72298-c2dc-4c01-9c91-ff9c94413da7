// POKEMON_680 (#680) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_680] =
    {
        .baseHP = 59,
        .baseAttack = 110,
        .baseDefense = 150,
        .baseSpAttack = 45,
        .baseSpDefense = 49,
        .baseSpeed = 35,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_GHOST,
        .catchRate = 90,
        .expYield = 157,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_NOGUARD,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_680LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 8, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(13, MOVE_PURSUIT),
    LEVEL_UP_MOVE(18, MOVE_AUTOTOMIZE),
    LEVEL_UP_MOVE(20, MOVE_SHADOW_SNEAK),
    LEVEL_UP_MOVE(22, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(26, MOVE_RETALIATE),
    LEVEL_UP_MOVE(29, MOVE_SLASH),
    LEVEL_UP_MOVE(32, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(36, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(41, MOVE_POWER_TRICK),
    LEVEL_UP_MOVE(45, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(51, MOVE_SACRED_SWORD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 448
// Types: TYPE_STEEL / TYPE_GHOST
// Abilities: ABILITY_NOGUARD, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
