// POKEMON_681 (#681) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_681] =
    {
        .baseHP = 60,
        .baseAttack = 50,
        .baseDefense = 140,
        .baseSpAttack = 50,
        .baseSpDefense = 140,
        .baseSpeed = 60,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_GHOST,
        .catchRate = 45,
        .expYield = 250,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_STANCECHANGE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_681LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_SLASH),
    LEVEL_UP_MOVE( 1, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 1, MOVE_PURSUIT),
    LEVEL_UP_MOVE( 1, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE( 1, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE( 1, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE( 1, MOVE_POWER_TRICK),
    LEVEL_UP_MOVE( 1, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE( 1, MOVE_SHADOW_SNEAK),
    LEVEL_UP_MOVE( 1, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE( 1, MOVE_HEAD_SMASH),
    LEVEL_UP_MOVE( 1, MOVE_AUTOTOMIZE),
    LEVEL_UP_MOVE( 1, MOVE_RETALIATE),
    LEVEL_UP_MOVE( 1, MOVE_SACRED_SWORD),
    LEVEL_UP_MOVE( 1, MOVE_KINGS_SHIELD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_STEEL / TYPE_GHOST
// Abilities: ABILITY_STANCECHANGE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 17
