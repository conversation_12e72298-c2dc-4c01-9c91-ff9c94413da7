// POKEMON_684 (#684) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_684] =
    {
        .baseHP = 62,
        .baseAttack = 48,
        .baseDefense = 66,
        .baseSpAttack = 59,
        .baseSpDefense = 57,
        .baseSpeed = 49,
        .type1 = TYPE_FAIRY,
        .type2 = TYPE_FAIRY,
        .catchRate = 200,
        .expYield = 68,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_FAIRY,
        .eggGroup2 = EGG_GROUP_FAIRY,
        .ability1 = ABILITY_SWEETVEIL,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_UNBURDEN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_684LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE( 5, MOVE_FAIRY_WIND),
    LEVEL_UP_MOVE( 8, MOVE_PLAY_NICE),
    LEVEL_UP_MOVE(10, MOVE_FAKE_TEARS),
    LEVEL_UP_MOVE(13, MOVE_ROUND),
    LEVEL_UP_MOVE(17, MOVE_COTTON_SPORE),
    LEVEL_UP_MOVE(21, MOVE_STRING_SHOT),
    LEVEL_UP_MOVE(21, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(26, MOVE_AROMATHERAPY),
    LEVEL_UP_MOVE(31, MOVE_DRAINING_KISS),
    LEVEL_UP_MOVE(36, MOVE_ENERGY_BALL),
    LEVEL_UP_MOVE(41, MOVE_COTTON_GUARD),
    LEVEL_UP_MOVE(45, MOVE_WISH),
    LEVEL_UP_MOVE(49, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(58, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(67, MOVE_SAFEGUARD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 341
// Types: TYPE_FAIRY / TYPE_FAIRY
// Abilities: ABILITY_SWEETVEIL, ABILITY_NONE, ABILITY_UNBURDEN
// Level Up Moves: 17
