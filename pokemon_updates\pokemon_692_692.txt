// POKEMON_692 (#692) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_692] =
    {
        .baseHP = 50,
        .baseAttack = 53,
        .baseDefense = 62,
        .baseSpAttack = 58,
        .baseSpDefense = 63,
        .baseSpeed = 44,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 225,
        .expYield = 66,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_WATER_3,
        .ability1 = ABILITY_MEGALAUNCHER,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_692LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_SPLASH),
    LEVEL_UP_MOVE( 7, MOVE_WATER_SPORT),
    LEVEL_UP_MOVE( 9, MOVE_VICE_GRIP),
    LEVEL_UP_MOVE(12, MOVE_BUBBLE),
    LEVEL_UP_MOVE(16, MOVE_FLAIL),
    LEVEL_UP_MOVE(20, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(25, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(25, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(30, MOVE_CRABHAMMER),
    LEVEL_UP_MOVE(34, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(39, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE(40, MOVE_AURA_SPHERE),
    LEVEL_UP_MOVE(43, MOVE_AQUA_JET),
    LEVEL_UP_MOVE(48, MOVE_MUDDY_WATER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 330
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_MEGALAUNCHER, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
