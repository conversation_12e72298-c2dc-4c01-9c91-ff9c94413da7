// POKEMON_695 (#695) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_695] =
    {
        .baseHP = 62,
        .baseAttack = 55,
        .baseDefense = 52,
        .baseSpAttack = 109,
        .baseSpDefense = 94,
        .baseSpeed = 109,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_NORMAL,
        .catchRate = 75,
        .expYield = 168,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_DRYSKIN,
        .ability2 = ABILITY_SANDVEIL,
        .abilityHidden = ABILITY_SOLARPOWER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_695LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_RAZOR_WIND),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_CHARGE),
    LEVEL_UP_MOVE( 1, MOVE_DISCHARGE),
    LEVEL_UP_MOVE( 1, MOVE_PARABOLIC_CHARGE),
    LEVEL_UP_MOVE( 1, MOVE_ELECTRIFY),
    LEVEL_UP_MOVE( 1, MOVE_EERIE_IMPULSE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 481
// Types: TYPE_ELECTRIC / TYPE_NORMAL
// Abilities: ABILITY_DRYSKIN, ABILITY_SANDVEIL, ABILITY_SOLARPOWER
// Level Up Moves: 12
