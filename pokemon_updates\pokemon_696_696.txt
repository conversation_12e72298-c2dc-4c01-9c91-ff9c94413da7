// POKEMON_696 (#696) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_696] =
    {
        .baseHP = 58,
        .baseAttack = 89,
        .baseDefense = 77,
        .baseSpAttack = 45,
        .baseSpDefense = 45,
        .baseSpeed = 48,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 72,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_STRONGJAW,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_STURDY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_696LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 6, MOVE_ROAR),
    LEVEL_UP_MOVE(10, MOVE_STOMP),
    LEVEL_UP_MOVE(12, MOVE_BIDE),
    LEVEL_UP_MOVE(15, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(17, MOVE_BITE),
    LEVEL_UP_MOVE(20, MOVE_CHARM),
    LEVEL_UP_MOVE(26, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(30, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE(34, MOVE_CRUNCH),
    LEVEL_UP_MOVE(37, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(40, MOVE_THRASH),
    LEVEL_UP_MOVE(44, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(49, MOVE_HORN_DRILL),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 362
// Types: TYPE_ROCK / TYPE_DRAGON
// Abilities: ABILITY_STRONGJAW, ABILITY_NONE, ABILITY_STURDY
// Level Up Moves: 15
