// POKEMON_697 (#697) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_697] =
    {
        .baseHP = 82,
        .baseAttack = 121,
        .baseDefense = 119,
        .baseSpAttack = 69,
        .baseSpDefense = 59,
        .baseSpeed = 71,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 182,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_STRONGJAW,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_ROCKHEAD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_697LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE( 1, MOVE_STOMP),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_ROAR),
    LEVEL_UP_MOVE( 1, MOVE_HEAD_SMASH),
    LEVEL_UP_MOVE(12, MOVE_BIDE),
    LEVEL_UP_MOVE(15, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(17, MOVE_BITE),
    LEVEL_UP_MOVE(20, MOVE_CHARM),
    LEVEL_UP_MOVE(26, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(30, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE(34, MOVE_CRUNCH),
    LEVEL_UP_MOVE(37, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(42, MOVE_THRASH),
    LEVEL_UP_MOVE(47, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(53, MOVE_HORN_DRILL),
    LEVEL_UP_MOVE(68, MOVE_GIGA_IMPACT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 521
// Types: TYPE_ROCK / TYPE_DRAGON
// Abilities: ABILITY_STRONGJAW, ABILITY_NONE, ABILITY_ROCKHEAD
// Level Up Moves: 18
