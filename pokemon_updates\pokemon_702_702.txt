// POKEMON_702 (#702) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_702] =
    {
        .baseHP = 67,
        .baseAttack = 58,
        .baseDefense = 57,
        .baseSpAttack = 81,
        .baseSpDefense = 67,
        .baseSpeed = 101,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_FAIRY,
        .catchRate = 180,
        .expYield = 151,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_FAIRY,
        .ability1 = ABILITY_CHEEKPOUCH,
        .ability2 = ABILITY_PICKUP,
        .abilityHidden = ABILITY_PLUS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_702LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 7, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE(11, MOVE_CHARGE),
    LEVEL_UP_MOVE(14, MOVE_CHARM),
    LEVEL_UP_MOVE(17, MOVE_PARABOLIC_CHARGE),
    LEVEL_UP_MOVE(20, MOVE_NUZZLE),
    LEVEL_UP_MOVE(23, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE(26, MOVE_VOLT_SWITCH),
    LEVEL_UP_MOVE(30, MOVE_REST),
    LEVEL_UP_MOVE(31, MOVE_SNORE),
    LEVEL_UP_MOVE(34, MOVE_CHARGE_BEAM),
    LEVEL_UP_MOVE(39, MOVE_ENTRAINMENT),
    LEVEL_UP_MOVE(42, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(45, MOVE_THUNDER),
    LEVEL_UP_MOVE(50, MOVE_DISCHARGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 431
// Types: TYPE_ELECTRIC / TYPE_FAIRY
// Abilities: ABILITY_CHEEKPOUCH, ABILITY_PICKUP, ABILITY_PLUS
// Level Up Moves: 16
