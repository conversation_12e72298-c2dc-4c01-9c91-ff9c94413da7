// POKEMON_705 (#705) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_705] =
    {
        .baseHP = 68,
        .baseAttack = 75,
        .baseDefense = 53,
        .baseSpAttack = 83,
        .baseSpDefense = 113,
        .baseSpeed = 60,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 158,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 2,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SHED_SHELL,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 40,
        .friendship = 35,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_DRAGON,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_SAPSIPPER,
        .ability2 = ABILITY_HYDRATION,
        .abilityHidden = ABILITY_GOOEY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_705LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_ACID_SPRAY),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_BUBBLE),
    LEVEL_UP_MOVE( 1, MOVE_ACID_ARMOR),
    LEVEL_UP_MOVE( 9, MOVE_PROTECT),
    LEVEL_UP_MOVE(13, MOVE_BIDE),
    LEVEL_UP_MOVE(18, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(25, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(28, MOVE_FLAIL),
    LEVEL_UP_MOVE(32, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(38, MOVE_MUDDY_WATER),
    LEVEL_UP_MOVE(43, MOVE_CURSE),
    LEVEL_UP_MOVE(47, MOVE_DRAGON_PULSE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 452
// Types: TYPE_DRAGON / TYPE_DRAGON
// Abilities: ABILITY_SAPSIPPER, ABILITY_HYDRATION, ABILITY_GOOEY
// Level Up Moves: 15
