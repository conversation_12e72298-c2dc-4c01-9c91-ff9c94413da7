// POKEMON_714 (#714) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_714] =
    {
        .baseHP = 40,
        .baseAttack = 30,
        .baseDefense = 35,
        .baseSpAttack = 45,
        .baseSpDefense = 40,
        .baseSpeed = 55,
        .type1 = TYPE_FLYING,
        .type2 = TYPE_DRAGON,
        .catchRate = 190,
        .expYield = 49,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_FRISK,
        .ability2 = ABILITY_INFILTRATOR,
        .abilityHidden = ABILITY_TELEPATHY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_714LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE( 1, MOVE_SCREECH),
    LEVEL_UP_MOVE( 5, MOVE_ABSORB),
    LEVEL_UP_MOVE(11, MOVE_GUST),
    LEVEL_UP_MOVE(13, MOVE_BITE),
    LEVEL_UP_MOVE(16, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(18, MOVE_AGILITY),
    LEVEL_UP_MOVE(23, MOVE_AIR_CUTTER),
    LEVEL_UP_MOVE(27, MOVE_ROOST),
    LEVEL_UP_MOVE(31, MOVE_RAZOR_WIND),
    LEVEL_UP_MOVE(35, MOVE_TAILWIND),
    LEVEL_UP_MOVE(40, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(43, MOVE_SUPER_FANG),
    LEVEL_UP_MOVE(48, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(58, MOVE_HURRICANE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 245
// Types: TYPE_FLYING / TYPE_DRAGON
// Abilities: ABILITY_FRISK, ABILITY_INFILTRATOR, ABILITY_TELEPATHY
// Level Up Moves: 16
