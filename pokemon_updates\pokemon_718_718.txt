// POKEMON_718 (#718) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_718] =
    {
        .baseHP = 108,
        .baseAttack = 100,
        .baseDefense = 121,
        .baseSpAttack = 81,
        .baseSpDefense = 95,
        .baseSpeed = 95,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_GROUND,
        .catchRate = 3,
        .expYield = 300,
        .evYield_HP = 3,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_AURABREAK,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_718LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_GLARE),
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE( 1, MOVE_BULLDOZE),
    LEVEL_UP_MOVE( 5, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(10, MOVE_DIG),
    LEVEL_UP_MOVE(18, MOVE_BIND),
    LEVEL_UP_MOVE(26, MOVE_LANDS_WRATH),
    LEVEL_UP_MOVE(35, MOVE_SANDSTORM),
    LEVEL_UP_MOVE(44, MOVE_HAZE),
    LEVEL_UP_MOVE(51, MOVE_CRUNCH),
    LEVEL_UP_MOVE(55, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(59, MOVE_CAMOUFLAGE),
    LEVEL_UP_MOVE(63, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(72, MOVE_COIL),
    LEVEL_UP_MOVE(80, MOVE_OUTRAGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_DRAGON / TYPE_GROUND
// Abilities: ABILITY_AURABREAK, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
