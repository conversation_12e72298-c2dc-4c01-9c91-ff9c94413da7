// POKEMON_719 (#719) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_719] =
    {
        .baseHP = 50,
        .baseAttack = 100,
        .baseDefense = 150,
        .baseSpAttack = 100,
        .baseSpDefense = 150,
        .baseSpeed = 50,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_FAIRY,
        .catchRate = 3,
        .expYield = 300,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 2,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_CLEARBODY,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_719LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 5, MOVE_SHARPEN),
    LEVEL_UP_MOVE( 8, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE(12, MOVE_REFLECT),
    LEVEL_UP_MOVE(18, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(21, MOVE_GUARD_SPLIT),
    LEVEL_UP_MOVE(27, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(31, MOVE_FLAIL),
    LEVEL_UP_MOVE(35, MOVE_SKILL_SWAP),
    LEVEL_UP_MOVE(40, MOVE_POWER_GEM),
    LEVEL_UP_MOVE(46, MOVE_TRICK_ROOM),
    LEVEL_UP_MOVE(49, MOVE_STONE_EDGE),
    LEVEL_UP_MOVE(50, MOVE_MOONBLAST),
    LEVEL_UP_MOVE(50, MOVE_DIAMOND_STORM),
    LEVEL_UP_MOVE(60, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(70, MOVE_SAFEGUARD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_ROCK / TYPE_FAIRY
// Abilities: ABILITY_CLEARBODY, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 18
