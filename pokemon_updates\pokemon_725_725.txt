// POKEMON_725 (#725) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_725] =
    {
        .baseHP = 45,
        .baseAttack = 65,
        .baseDefense = 40,
        .baseSpAttack = 60,
        .baseSpDefense = 40,
        .baseSpeed = 70,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 45,
        .expYield = 64,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_BLAZE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_INTIMIDATE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_725LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 4, MOVE_GROWL),
    LEVEL_UP_MOVE( 8, MOVE_LICK),
    LEVEL_UP_MOVE(11, MOVE_LEER),
    LEVEL_UP_MOVE(14, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE(16, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(18, MOVE_ROAR),
    LEVEL_UP_MOVE(22, MOVE_BITE),
    LEVEL_UP_MOVE(25, MOVE_SWAGGER),
    LEVEL_UP_MOVE(29, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(32, MOVE_THRASH),
    LEVEL_UP_MOVE(36, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(39, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(43, MOVE_FLARE_BLITZ),
    LEVEL_UP_MOVE(46, MOVE_OUTRAGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 320
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_BLAZE, ABILITY_NONE, ABILITY_INTIMIDATE
// Level Up Moves: 16
