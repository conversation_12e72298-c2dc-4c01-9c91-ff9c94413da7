// POKEMON_726 (#726) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_726] =
    {
        .baseHP = 65,
        .baseAttack = 85,
        .baseDefense = 50,
        .baseSpAttack = 80,
        .baseSpDefense = 50,
        .baseSpeed = 90,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 45,
        .expYield = 147,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_BLAZE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_INTIMIDATE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_726LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_LICK),
    LEVEL_UP_MOVE(11, MOVE_LEER),
    LEVEL_UP_MOVE(14, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE(16, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(19, MOVE_ROAR),
    LEVEL_UP_MOVE(24, MOVE_BITE),
    LEVEL_UP_MOVE(28, MOVE_SWAGGER),
    LEVEL_UP_MOVE(33, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(37, MOVE_THRASH),
    LEVEL_UP_MOVE(42, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(46, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(51, MOVE_FLARE_BLITZ),
    LEVEL_UP_MOVE(55, MOVE_OUTRAGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 420
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_BLAZE, ABILITY_NONE, ABILITY_INTIMIDATE
// Level Up Moves: 16
