// POKEMON_732 (#732) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_732] =
    {
        .baseHP = 55,
        .baseAttack = 85,
        .baseDefense = 50,
        .baseSpAttack = 40,
        .baseSpDefense = 50,
        .baseSpeed = 75,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 120,
        .expYield = 124,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SITRUS_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_KEENEYE,
        .ability2 = ABILITY_SKILLLINK,
        .abilityHidden = ABILITY_PICKUP,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_732LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_BLAST),
    LEVEL_UP_MOVE( 1, MOVE_ECHOED_VOICE),
    LEVEL_UP_MOVE(13, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(16, MOVE_PLUCK),
    LEVEL_UP_MOVE(21, MOVE_ROOST),
    LEVEL_UP_MOVE(24, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(29, MOVE_SCREECH),
    LEVEL_UP_MOVE(32, MOVE_DRILL_PECK),
    LEVEL_UP_MOVE(37, MOVE_BULLET_SEED),
    LEVEL_UP_MOVE(40, MOVE_FEATHER_DANCE),
    LEVEL_UP_MOVE(45, MOVE_HYPER_VOICE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 355
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_KEENEYE, ABILITY_SKILLLINK, ABILITY_PICKUP
// Level Up Moves: 14
