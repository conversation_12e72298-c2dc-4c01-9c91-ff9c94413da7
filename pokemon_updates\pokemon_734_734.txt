// POKEMON_734 (#734) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_734] =
    {
        .baseHP = 48,
        .baseAttack = 70,
        .baseDefense = 30,
        .baseSpAttack = 30,
        .baseSpDefense = 30,
        .baseSpeed = 45,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 255,
        .expYield = 51,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_PECHA_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_STAKEOUT,
        .ability2 = ABILITY_STRONGJAW,
        .abilityHidden = ABILITY_ADAPTABILITY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_734LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 3, MOVE_LEER),
    LEVEL_UP_MOVE( 7, MOVE_PURSUIT),
    LEVEL_UP_MOVE(10, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE(13, MOVE_ODOR_SLEUTH),
    LEVEL_UP_MOVE(16, MOVE_BIDE),
    LEVEL_UP_MOVE(19, MOVE_BITE),
    LEVEL_UP_MOVE(22, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE(25, MOVE_SUPER_FANG),
    LEVEL_UP_MOVE(28, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(31, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(34, MOVE_CRUNCH),
    LEVEL_UP_MOVE(37, MOVE_HYPER_FANG),
    LEVEL_UP_MOVE(40, MOVE_YAWN),
    LEVEL_UP_MOVE(43, MOVE_THRASH),
    LEVEL_UP_MOVE(46, MOVE_REST),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 253
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_STAKEOUT, ABILITY_STRONGJAW, ABILITY_ADAPTABILITY
// Level Up Moves: 16
