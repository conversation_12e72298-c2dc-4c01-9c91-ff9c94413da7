// POKEMON_735 (#735) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_735] =
    {
        .baseHP = 88,
        .baseAttack = 110,
        .baseDefense = 60,
        .baseSpAttack = 55,
        .baseSpDefense = 60,
        .baseSpeed = 45,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 127,
        .expYield = 146,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_PECHA_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_STAKEOUT,
        .ability2 = ABILITY_STRONGJAW,
        .abilityHidden = ABILITY_ADAPTABILITY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_735LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_PURSUIT),
    LEVEL_UP_MOVE(13, MOVE_ODOR_SLEUTH),
    LEVEL_UP_MOVE(16, MOVE_BIDE),
    LEVEL_UP_MOVE(19, MOVE_BITE),
    LEVEL_UP_MOVE(23, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE(27, MOVE_SUPER_FANG),
    LEVEL_UP_MOVE(31, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(35, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(39, MOVE_CRUNCH),
    LEVEL_UP_MOVE(43, MOVE_HYPER_FANG),
    LEVEL_UP_MOVE(47, MOVE_YAWN),
    LEVEL_UP_MOVE(51, MOVE_THRASH),
    LEVEL_UP_MOVE(55, MOVE_REST),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 418
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_STAKEOUT, ABILITY_STRONGJAW, ABILITY_ADAPTABILITY
// Level Up Moves: 16
