// POKEMON_736 (#736) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_736] =
    {
        .baseHP = 47,
        .baseAttack = 62,
        .baseDefense = 45,
        .baseSpAttack = 55,
        .baseSpDefense = 45,
        .baseSpeed = 46,
        .type1 = TYPE_BUG,
        .type2 = TYPE_BUG,
        .catchRate = 255,
        .expYield = 60,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_SWARM,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_736LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_VICE_GRIP),
    LEVEL_UP_MOVE( 4, MOVE_STRING_SHOT),
    LEVEL_UP_MOVE( 7, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE(10, MOVE_BITE),
    LEVEL_UP_MOVE(13, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(16, MOVE_SPARK),
    LEVEL_UP_MOVE(19, MOVE_ACROBATICS),
    LEVEL_UP_MOVE(22, MOVE_CRUNCH),
    LEVEL_UP_MOVE(25, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(25, MOVE_STICKY_WEB),
    LEVEL_UP_MOVE(28, MOVE_DIG),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 300
// Types: TYPE_BUG / TYPE_BUG
// Abilities: ABILITY_SWARM, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 11
