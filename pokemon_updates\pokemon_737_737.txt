// POKEMON_737 (#737) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_737] =
    {
        .baseHP = 57,
        .baseAttack = 82,
        .baseDefense = 95,
        .baseSpAttack = 55,
        .baseSpDefense = 75,
        .baseSpeed = 36,
        .type1 = TYPE_BUG,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 120,
        .expYield = 140,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_CELL_BATTERY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_BATTERY,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_737LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_CHARGE),
    LEVEL_UP_MOVE( 1, MOVE_VICE_GRIP),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_STRING_SHOT),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE(13, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(16, MOVE_SPARK),
    LEVEL_UP_MOVE(19, MOVE_ACROBATICS),
    LEVEL_UP_MOVE(25, MOVE_CRUNCH),
    LEVEL_UP_MOVE(29, MOVE_STICKY_WEB),
    LEVEL_UP_MOVE(31, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(37, MOVE_DIG),
    LEVEL_UP_MOVE(43, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(49, MOVE_IRON_DEFENSE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 400
// Types: TYPE_BUG / TYPE_ELECTRIC
// Abilities: ABILITY_BATTERY, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
