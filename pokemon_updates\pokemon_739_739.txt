// POKEMON_739 (#739) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_739] =
    {
        .baseHP = 47,
        .baseAttack = 82,
        .baseDefense = 57,
        .baseSpAttack = 42,
        .baseSpDefense = 47,
        .baseSpeed = 63,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 225,
        .expYield = 68,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_ASPEAR_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_3,
        .eggGroup2 = EGG_GROUP_WATER_3,
        .ability1 = ABILITY_HYPERCUTTER,
        .ability2 = ABILITY_IRONFIST,
        .abilityHidden = ABILITY_ANGERPOINT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_739LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BUBBLE),
    LEVEL_UP_MOVE( 5, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE( 9, MOVE_LEER),
    LEVEL_UP_MOVE(13, MOVE_PURSUIT),
    LEVEL_UP_MOVE(17, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(22, MOVE_POWER_UP_PUNCH),
    LEVEL_UP_MOVE(25, MOVE_SLAM),
    LEVEL_UP_MOVE(25, MOVE_DIZZY_PUNCH),
    LEVEL_UP_MOVE(29, MOVE_PAYBACK),
    LEVEL_UP_MOVE(33, MOVE_REVERSAL),
    LEVEL_UP_MOVE(37, MOVE_CRABHAMMER),
    LEVEL_UP_MOVE(42, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(45, MOVE_DYNAMIC_PUNCH),
    LEVEL_UP_MOVE(49, MOVE_CLOSE_COMBAT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 338
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_HYPERCUTTER, ABILITY_IRONFIST, ABILITY_ANGERPOINT
// Level Up Moves: 14
