// POKEMON_740 (#740) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_740] =
    {
        .baseHP = 97,
        .baseAttack = 132,
        .baseDefense = 77,
        .baseSpAttack = 62,
        .baseSpDefense = 67,
        .baseSpeed = 43,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_ICE,
        .catchRate = 60,
        .expYield = 167,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_CHERI_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_3,
        .eggGroup2 = EGG_GROUP_WATER_3,
        .ability1 = ABILITY_HYPERCUTTER,
        .ability2 = ABILITY_IRONFIST,
        .abilityHidden = ABILITY_ANGERPOINT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_740LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_ICE_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_BUBBLE),
    LEVEL_UP_MOVE( 1, MOVE_PURSUIT),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE(17, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(22, MOVE_POWER_UP_PUNCH),
    LEVEL_UP_MOVE(25, MOVE_SLAM),
    LEVEL_UP_MOVE(25, MOVE_DIZZY_PUNCH),
    LEVEL_UP_MOVE(29, MOVE_AVALANCHE),
    LEVEL_UP_MOVE(33, MOVE_REVERSAL),
    LEVEL_UP_MOVE(37, MOVE_ICE_HAMMER),
    LEVEL_UP_MOVE(42, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(45, MOVE_DYNAMIC_PUNCH),
    LEVEL_UP_MOVE(49, MOVE_CLOSE_COMBAT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 478
// Types: TYPE_FIGHTING / TYPE_ICE
// Abilities: ABILITY_HYPERCUTTER, ABILITY_IRONFIST, ABILITY_ANGERPOINT
// Level Up Moves: 15
