// POKEMON_743 (#743) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_743] =
    {
        .baseHP = 60,
        .baseAttack = 55,
        .baseDefense = 60,
        .baseSpAttack = 95,
        .baseSpDefense = 70,
        .baseSpeed = 124,
        .type1 = TYPE_BUG,
        .type2 = TYPE_FAIRY,
        .catchRate = 75,
        .expYield = 162,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_HONEY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_FAIRY,
        .ability1 = ABILITY_HONEYGATHER,
        .ability2 = ABILITY_SHIELDDUST,
        .abilityHidden = ABILITY_SWEETVEIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_743LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_POLLEN_PUFF),
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE( 1, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE( 1, MOVE_FAIRY_WIND),
    LEVEL_UP_MOVE(13, MOVE_SILVER_WIND),
    LEVEL_UP_MOVE(16, MOVE_DRAINING_KISS),
    LEVEL_UP_MOVE(21, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(28, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(35, MOVE_DAZZLING_GLEAM),
    LEVEL_UP_MOVE(40, MOVE_SWITCHEROO),
    LEVEL_UP_MOVE(42, MOVE_AROMATHERAPY),
    LEVEL_UP_MOVE(49, MOVE_QUIVER_DANCE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 464
// Types: TYPE_BUG / TYPE_FAIRY
// Abilities: ABILITY_HONEYGATHER, ABILITY_SHIELDDUST, ABILITY_SWEETVEIL
// Level Up Moves: 13
