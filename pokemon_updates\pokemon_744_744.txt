// POKEMON_744 (#744) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_744] =
    {
        .baseHP = 45,
        .baseAttack = 65,
        .baseDefense = 40,
        .baseSpAttack = 30,
        .baseSpDefense = 40,
        .baseSpeed = 60,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_ROCK,
        .catchRate = 190,
        .expYield = 56,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_KEENEYE,
        .ability2 = ABILITY_VITALSPIRIT,
        .abilityHidden = ABILITY_STEADFAST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_744LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 4, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 7, MOVE_BITE),
    LEVEL_UP_MOVE(12, MOVE_HOWL),
    LEVEL_UP_MOVE(15, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE(18, MOVE_ODOR_SLEUTH),
    LEVEL_UP_MOVE(23, MOVE_ROCK_TOMB),
    LEVEL_UP_MOVE(26, MOVE_ROAR),
    LEVEL_UP_MOVE(29, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(34, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(37, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(40, MOVE_CRUNCH),
    LEVEL_UP_MOVE(45, MOVE_ROCK_CLIMB),
    LEVEL_UP_MOVE(48, MOVE_STONE_EDGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 280
// Types: TYPE_ROCK / TYPE_ROCK
// Abilities: ABILITY_KEENEYE, ABILITY_VITALSPIRIT, ABILITY_STEADFAST
// Level Up Moves: 15
