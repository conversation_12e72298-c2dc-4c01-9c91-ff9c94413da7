// POKEMON_746 (#746) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_746] =
    {
        .baseHP = 45,
        .baseAttack = 20,
        .baseDefense = 20,
        .baseSpAttack = 25,
        .baseSpDefense = 25,
        .baseSpeed = 40,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 60,
        .expYield = 61,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_WATER_2,
        .eggGroup2 = EGG_GROUP_WATER_2,
        .ability1 = ABILITY_SCHOOLING,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_746LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 6, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 9, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE(14, MOVE_BRINE),
    LEVEL_UP_MOVE(17, MOVE_AQUA_RING),
    LEVEL_UP_MOVE(22, MOVE_TEARFUL_LOOK),
    LEVEL_UP_MOVE(25, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(28, MOVE_UPROAR),
    LEVEL_UP_MOVE(30, MOVE_DIVE),
    LEVEL_UP_MOVE(33, MOVE_BEAT_UP),
    LEVEL_UP_MOVE(38, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(41, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(46, MOVE_SOAK),
    LEVEL_UP_MOVE(49, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(54, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 175
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_SCHOOLING, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
