// POKEMON_750 (#750) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_750] =
    {
        .baseHP = 100,
        .baseAttack = 125,
        .baseDefense = 100,
        .baseSpAttack = 55,
        .baseSpDefense = 85,
        .baseSpeed = 35,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_GROUND,
        .catchRate = 60,
        .expYield = 175,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_LIGHT_CLAY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_OWNTEMPO,
        .ability2 = ABILITY_STAMINA,
        .abilityHidden = ABILITY_INNERFOCUS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_750LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SPORT),
    LEVEL_UP_MOVE( 1, MOVE_BULLDOZE),
    LEVEL_UP_MOVE( 1, MOVE_ROTOTILLER),
    LEVEL_UP_MOVE(15, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(17, MOVE_STOMP),
    LEVEL_UP_MOVE(20, MOVE_STRENGTH),
    LEVEL_UP_MOVE(22, MOVE_BIDE),
    LEVEL_UP_MOVE(24, MOVE_HIGH_HORSEPOWER),
    LEVEL_UP_MOVE(29, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(34, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(42, MOVE_COUNTER),
    LEVEL_UP_MOVE(47, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(55, MOVE_MEGA_KICK),
    LEVEL_UP_MOVE(60, MOVE_SUPERPOWER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_GROUND / TYPE_GROUND
// Abilities: ABILITY_OWNTEMPO, ABILITY_STAMINA, ABILITY_INNERFOCUS
// Level Up Moves: 16
