// POKEMON_751 (#751) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_751] =
    {
        .baseHP = 38,
        .baseAttack = 40,
        .baseDefense = 52,
        .baseSpAttack = 40,
        .baseSpDefense = 72,
        .baseSpeed = 27,
        .type1 = TYPE_WATER,
        .type2 = TYPE_BUG,
        .catchRate = 200,
        .expYield = 54,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_MYSTIC_WATER,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_WATERBUBBLE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_WATERABSORB,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_751LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_BUBBLE),
    LEVEL_UP_MOVE( 1, MOVE_WATER_SPORT),
    LEVEL_UP_MOVE( 5, MOVE_INFESTATION),
    LEVEL_UP_MOVE( 8, MOVE_SPIDER_WEB),
    LEVEL_UP_MOVE(13, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(16, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(20, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(21, MOVE_BITE),
    LEVEL_UP_MOVE(24, MOVE_AQUA_RING),
    LEVEL_UP_MOVE(28, MOVE_SOAK),
    LEVEL_UP_MOVE(29, MOVE_LEECH_LIFE),
    LEVEL_UP_MOVE(32, MOVE_CRUNCH),
    LEVEL_UP_MOVE(37, MOVE_LUNGE),
    LEVEL_UP_MOVE(40, MOVE_MIRROR_COAT),
    LEVEL_UP_MOVE(45, MOVE_LIQUIDATION),
    LEVEL_UP_MOVE(48, MOVE_ENTRAINMENT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 269
// Types: TYPE_WATER / TYPE_BUG
// Abilities: ABILITY_WATERBUBBLE, ABILITY_NONE, ABILITY_WATERABSORB
// Level Up Moves: 17
