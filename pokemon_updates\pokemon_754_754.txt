// POKEMON_754 (#754) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_754] =
    {
        .baseHP = 70,
        .baseAttack = 105,
        .baseDefense = 90,
        .baseSpAttack = 80,
        .baseSpDefense = 90,
        .baseSpeed = 45,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 75,
        .expYield = 168,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_MIRACLE_SEED,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_LEAFGUARD,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_CONTRARY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_754LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_PETAL_BLIZZARD),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 1, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE( 1, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 1, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE( 1, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE( 1, MOVE_LEAFAGE),
    LEVEL_UP_MOVE(19, MOVE_INGRAIN),
    LEVEL_UP_MOVE(23, MOVE_LEAF_BLADE),
    LEVEL_UP_MOVE(28, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(32, MOVE_SLASH),
    LEVEL_UP_MOVE(40, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(47, MOVE_SOLAR_BLADE),
    LEVEL_UP_MOVE(55, MOVE_SUNNY_DAY),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 480
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_LEAFGUARD, ABILITY_NONE, ABILITY_CONTRARY
// Level Up Moves: 14
