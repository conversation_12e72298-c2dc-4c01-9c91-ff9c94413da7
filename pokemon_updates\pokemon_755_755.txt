// POKEMON_755 (#755) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_755] =
    {
        .baseHP = 40,
        .baseAttack = 35,
        .baseDefense = 55,
        .baseSpAttack = 65,
        .baseSpDefense = 75,
        .baseSpeed = 15,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_FAIRY,
        .catchRate = 190,
        .expYield = 57,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_TINY_MUSHROOM,
        .item2 = ITEM_BIG_MUSHROOM,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_ILLUMINATE,
        .ability2 = ABILITY_EFFECTSPORE,
        .abilityHidden = ABILITY_RAINDISH,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_755LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 4, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 8, MOVE_FLASH),
    LEVEL_UP_MOVE(11, MOVE_MOONLIGHT),
    LEVEL_UP_MOVE(15, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(18, MOVE_SLEEP_POWDER),
    LEVEL_UP_MOVE(22, MOVE_INGRAIN),
    LEVEL_UP_MOVE(25, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(29, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(32, MOVE_STRENGTH_SAP),
    LEVEL_UP_MOVE(36, MOVE_SPORE),
    LEVEL_UP_MOVE(39, MOVE_MOONBLAST),
    LEVEL_UP_MOVE(43, MOVE_DREAM_EATER),
    LEVEL_UP_MOVE(46, MOVE_SPOTLIGHT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 285
// Types: TYPE_GRASS / TYPE_FAIRY
// Abilities: ABILITY_ILLUMINATE, ABILITY_EFFECTSPORE, ABILITY_RAINDISH
// Level Up Moves: 14
