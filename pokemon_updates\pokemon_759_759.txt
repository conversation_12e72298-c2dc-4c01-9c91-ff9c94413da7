// POKEMON_759 (#759) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_759] =
    {
        .baseHP = 70,
        .baseAttack = 75,
        .baseDefense = 50,
        .baseSpAttack = 45,
        .baseSpDefense = 50,
        .baseSpeed = 50,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FIGHTING,
        .catchRate = 140,
        .expYield = 68,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_FLUFFY,
        .ability2 = ABILITY_KLUTZ,
        .abilityHidden = ABILITY_CUTECHARM,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_759LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 5, MOVE_BIDE),
    LEVEL_UP_MOVE(10, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(14, MOVE_BRUTAL_SWING),
    LEVEL_UP_MOVE(19, MOVE_FLAIL),
    LEVEL_UP_MOVE(20, MOVE_STRENGTH),
    LEVEL_UP_MOVE(23, MOVE_PAYBACK),
    LEVEL_UP_MOVE(28, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(32, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE(37, MOVE_THRASH),
    LEVEL_UP_MOVE(41, MOVE_PAIN_SPLIT),
    LEVEL_UP_MOVE(46, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(50, MOVE_SUPERPOWER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 340
// Types: TYPE_NORMAL / TYPE_FIGHTING
// Abilities: ABILITY_FLUFFY, ABILITY_KLUTZ, ABILITY_CUTECHARM
// Level Up Moves: 14
