// POKEMON_760 (#760) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_760] =
    {
        .baseHP = 120,
        .baseAttack = 125,
        .baseDefense = 80,
        .baseSpAttack = 55,
        .baseSpDefense = 60,
        .baseSpeed = 60,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FIGHTING,
        .catchRate = 70,
        .expYield = 175,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_FLUFFY,
        .ability2 = ABILITY_KLUTZ,
        .abilityHidden = ABILITY_UNNERVE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_760LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_BIND),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 5, MOVE_BIDE),
    LEVEL_UP_MOVE(10, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(14, MOVE_BRUTAL_SWING),
    LEVEL_UP_MOVE(16, MOVE_ENDURE),
    LEVEL_UP_MOVE(19, MOVE_FLAIL),
    LEVEL_UP_MOVE(20, MOVE_STRENGTH),
    LEVEL_UP_MOVE(23, MOVE_PAYBACK),
    LEVEL_UP_MOVE(30, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(36, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE(43, MOVE_THRASH),
    LEVEL_UP_MOVE(49, MOVE_PAIN_SPLIT),
    LEVEL_UP_MOVE(56, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(62, MOVE_SUPERPOWER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_NORMAL / TYPE_FIGHTING
// Abilities: ABILITY_FLUFFY, ABILITY_KLUTZ, ABILITY_UNNERVE
// Level Up Moves: 16
