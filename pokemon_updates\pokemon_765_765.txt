// POKEMON_765 (#765) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_765] =
    {
        .baseHP = 90,
        .baseAttack = 60,
        .baseDefense = 80,
        .baseSpAttack = 90,
        .baseSpDefense = 110,
        .baseSpeed = 60,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 45,
        .expYield = 172,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 2,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_INNERFOCUS,
        .ability2 = ABILITY_TELEPATHY,
        .abilityHidden = ABILITY_SYMBIOSIS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_765LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 4, MOVE_AFTER_YOU),
    LEVEL_UP_MOVE( 8, MOVE_TAUNT),
    LEVEL_UP_MOVE(11, MOVE_QUASH),
    LEVEL_UP_MOVE(15, MOVE_STORED_POWER),
    LEVEL_UP_MOVE(18, MOVE_PSYCH_UP),
    LEVEL_UP_MOVE(22, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE(25, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(29, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(32, MOVE_INSTRUCT),
    LEVEL_UP_MOVE(36, MOVE_FOUL_PLAY),
    LEVEL_UP_MOVE(39, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(43, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(46, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(50, MOVE_TRICK_ROOM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 490
// Types: TYPE_NORMAL / TYPE_PSYCHIC
// Abilities: ABILITY_INNERFOCUS, ABILITY_TELEPATHY, ABILITY_SYMBIOSIS
// Level Up Moves: 15
