// POKEMON_766 (#766) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_766] =
    {
        .baseHP = 100,
        .baseAttack = 120,
        .baseDefense = 90,
        .baseSpAttack = 40,
        .baseSpDefense = 60,
        .baseSpeed = 80,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 45,
        .expYield = 172,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_RECEIVER,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_DEFIANT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_766LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 4, MOVE_LEER),
    LEVEL_UP_MOVE( 8, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE(11, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(15, MOVE_BEAT_UP),
    LEVEL_UP_MOVE(18, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(22, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(25, MOVE_BESTOW),
    LEVEL_UP_MOVE(29, MOVE_THRASH),
    LEVEL_UP_MOVE(32, MOVE_BULK_UP),
    LEVEL_UP_MOVE(36, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(39, MOVE_FLING),
    LEVEL_UP_MOVE(43, MOVE_CLOSE_COMBAT),
    LEVEL_UP_MOVE(46, MOVE_REVERSAL),
    LEVEL_UP_MOVE(50, MOVE_GIGA_IMPACT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 490
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_RECEIVER, ABILITY_NONE, ABILITY_DEFIANT
// Level Up Moves: 15
