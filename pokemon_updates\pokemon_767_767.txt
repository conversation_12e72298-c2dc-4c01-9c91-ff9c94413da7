// POKEMON_767 (#767) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_767] =
    {
        .baseHP = 25,
        .baseAttack = 35,
        .baseDefense = 40,
        .baseSpAttack = 20,
        .baseSpDefense = 30,
        .baseSpeed = 80,
        .type1 = TYPE_BUG,
        .type2 = TYPE_WATER,
        .catchRate = 90,
        .expYield = 46,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_WATER_3,
        .ability1 = ABILITY_WIMPOUT,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_767LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_STRUGGLE_BUG),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 230
// Types: TYPE_BUG / TYPE_WATER
// Abilities: ABILITY_WIMPOUT, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 3
