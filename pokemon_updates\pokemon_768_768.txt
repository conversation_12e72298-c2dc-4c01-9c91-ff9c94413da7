// POKEMON_768 (#768) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_768] =
    {
        .baseHP = 75,
        .baseAttack = 125,
        .baseDefense = 140,
        .baseSpAttack = 60,
        .baseSpDefense = 90,
        .baseSpeed = 40,
        .type1 = TYPE_BUG,
        .type2 = TYPE_WATER,
        .catchRate = 45,
        .expYield = 186,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_WATER_3,
        .ability1 = ABILITY_EMERGENCYEXIT,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_768LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_FIRST_IMPRESSION),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE( 1, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE(10, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(12, MOVE_MUD_SHOT),
    LEVEL_UP_MOVE(13, MOVE_SPITE),
    LEVEL_UP_MOVE(16, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(21, MOVE_SLASH),
    LEVEL_UP_MOVE(26, MOVE_RAZOR_SHELL),
    LEVEL_UP_MOVE(31, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(36, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(41, MOVE_PIN_MISSILE),
    LEVEL_UP_MOVE(48, MOVE_LIQUIDATION),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 530
// Types: TYPE_BUG / TYPE_WATER
// Abilities: ABILITY_EMERGENCYEXIT, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
