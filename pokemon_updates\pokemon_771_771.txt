// POKEMON_771 (#771) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_771] =
    {
        .baseHP = 55,
        .baseAttack = 60,
        .baseDefense = 130,
        .baseSpAttack = 30,
        .baseSpDefense = 130,
        .baseSpeed = 5,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 60,
        .expYield = 144,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 2,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_WATER_1,
        .ability1 = ABILITY_INNARDSOUT,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_UNAWARE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_771LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_BIDE),
    LEVEL_UP_MOVE( 1, MOVE_BATON_PASS),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SPORT),
    LEVEL_UP_MOVE( 1, MOVE_WATER_SPORT),
    LEVEL_UP_MOVE( 5, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 9, MOVE_TAUNT),
    LEVEL_UP_MOVE(13, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(17, MOVE_COUNTER),
    LEVEL_UP_MOVE(21, MOVE_PURIFY),
    LEVEL_UP_MOVE(25, MOVE_CURSE),
    LEVEL_UP_MOVE(29, MOVE_GASTRO_ACID),
    LEVEL_UP_MOVE(33, MOVE_PAIN_SPLIT),
    LEVEL_UP_MOVE(37, MOVE_RECOVER),
    LEVEL_UP_MOVE(41, MOVE_SOAK),
    LEVEL_UP_MOVE(45, MOVE_TOXIC),
    LEVEL_UP_MOVE(49, MOVE_MEMENTO),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 410
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_INNARDSOUT, ABILITY_NONE, ABILITY_UNAWARE
// Level Up Moves: 17
