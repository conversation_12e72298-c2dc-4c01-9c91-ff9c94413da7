// POKEMON_772 (#772) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_772] =
    {
        .baseHP = 95,
        .baseAttack = 95,
        .baseDefense = 95,
        .baseSpAttack = 95,
        .baseSpDefense = 95,
        .baseSpeed = 59,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 3,
        .expYield = 107,
        .evYield_HP = 2,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_BATTLEARMOR,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_772LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_RAGE),
    LEVEL_UP_MOVE(10, MOVE_PURSUIT),
    LEVEL_UP_MOVE(15, MOVE_IMPRISON),
    LEVEL_UP_MOVE(20, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(25, MOVE_CRUSH_CLAW),
    LEVEL_UP_MOVE(30, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(35, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(40, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(45, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(50, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(55, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE(60, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(65, MOVE_PUNISHMENT),
    LEVEL_UP_MOVE(70, MOVE_RAZOR_WIND),
    LEVEL_UP_MOVE(75, MOVE_TRI_ATTACK),
    LEVEL_UP_MOVE(80, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(85, MOVE_HEAL_BLOCK),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 534
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_BATTLEARMOR, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 18
