// POKEMON_773 (#773) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_773] =
    {
        .baseHP = 95,
        .baseAttack = 95,
        .baseDefense = 95,
        .baseSpAttack = 95,
        .baseSpDefense = 95,
        .baseSpeed = 95,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 3,
        .expYield = 285,
        .evYield_HP = 3,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_RKSSYSTEM,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_773LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_MULTI_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_IMPRISON),
    LEVEL_UP_MOVE( 1, MOVE_POISON_FANG),
    LEVEL_UP_MOVE( 1, MOVE_HEAL_BLOCK),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_FANG),
    LEVEL_UP_MOVE( 1, MOVE_ICE_FANG),
    LEVEL_UP_MOVE( 1, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE( 1, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE( 5, MOVE_RAGE),
    LEVEL_UP_MOVE(10, MOVE_PURSUIT),
    LEVEL_UP_MOVE(15, MOVE_BITE),
    LEVEL_UP_MOVE(20, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(25, MOVE_CRUSH_CLAW),
    LEVEL_UP_MOVE(30, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(35, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(40, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(45, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(50, MOVE_CRUNCH),
    LEVEL_UP_MOVE(55, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE(60, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(65, MOVE_PUNISHMENT),
    LEVEL_UP_MOVE(70, MOVE_RAZOR_WIND),
    LEVEL_UP_MOVE(75, MOVE_TRI_ATTACK),
    LEVEL_UP_MOVE(80, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(85, MOVE_PARTING_SHOT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_RKSSYSTEM, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 26
