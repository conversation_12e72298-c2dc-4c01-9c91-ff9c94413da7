// POKEMON_776 (#776) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_776] =
    {
        .baseHP = 60,
        .baseAttack = 78,
        .baseDefense = 135,
        .baseSpAttack = 91,
        .baseSpDefense = 85,
        .baseSpeed = 36,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_DRAGON,
        .catchRate = 70,
        .expYield = 170,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_CHARCOAL,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_SHELLARMOR,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_776LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 5, MOVE_SMOG),
    LEVEL_UP_MOVE( 9, MOVE_PROTECT),
    LEVEL_UP_MOVE(13, MOVE_INCINERATE),
    LEVEL_UP_MOVE(17, MOVE_FLAIL),
    LEVEL_UP_MOVE(21, MOVE_ENDURE),
    LEVEL_UP_MOVE(25, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(29, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(33, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(37, MOVE_SHELL_SMASH),
    LEVEL_UP_MOVE(41, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(45, MOVE_SHELL_TRAP),
    LEVEL_UP_MOVE(49, MOVE_OVERHEAT),
    LEVEL_UP_MOVE(53, MOVE_EXPLOSION),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 485
// Types: TYPE_FIRE / TYPE_DRAGON
// Abilities: ABILITY_SHELLARMOR, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
