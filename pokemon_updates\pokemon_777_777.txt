// POKEMON_777 (#777) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_777] =
    {
        .baseHP = 65,
        .baseAttack = 98,
        .baseDefense = 63,
        .baseSpAttack = 40,
        .baseSpDefense = 73,
        .baseSpeed = 96,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_STEEL,
        .catchRate = 180,
        .expYield = 152,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_ELECTRIC_SEED,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 10,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_FAIRY,
        .ability1 = ABILITY_IRONBARBS,
        .ability2 = ABILITY_LIGHTNINGROD,
        .abilityHidden = ABILITY_STURDY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_777LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 5, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 9, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(13, MOVE_CHARGE),
    LEVEL_UP_MOVE(17, MOVE_SPARK),
    LEVEL_UP_MOVE(21, MOVE_NUZZLE),
    LEVEL_UP_MOVE(25, MOVE_MAGNET_RISE),
    LEVEL_UP_MOVE(29, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(33, MOVE_ZING_ZAP),
    LEVEL_UP_MOVE(37, MOVE_ELECTRIC_TERRAIN),
    LEVEL_UP_MOVE(41, MOVE_WILD_CHARGE),
    LEVEL_UP_MOVE(45, MOVE_PIN_MISSILE),
    LEVEL_UP_MOVE(49, MOVE_SPIKY_SHIELD),
    LEVEL_UP_MOVE(53, MOVE_FELL_STINGER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 435
// Types: TYPE_ELECTRIC / TYPE_STEEL
// Abilities: ABILITY_IRONBARBS, ABILITY_LIGHTNINGROD, ABILITY_STURDY
// Level Up Moves: 15
