// POKEMON_778 (#778) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_778] =
    {
        .baseHP = 55,
        .baseAttack = 90,
        .baseDefense = 80,
        .baseSpAttack = 50,
        .baseSpDefense = 105,
        .baseSpeed = 96,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_FAIRY,
        .catchRate = 45,
        .expYield = 167,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 2,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_CHESTO_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_INDETERMINATE,
        .eggGroup2 = EGG_GROUP_INDETERMINATE,
        .ability1 = ABILITY_DISGUISE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_778LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_SPLASH),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE( 1, MOVE_WOOD_HAMMER),
    LEVEL_UP_MOVE( 5, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(10, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(14, MOVE_SHADOW_SNEAK),
    LEVEL_UP_MOVE(19, MOVE_MIMIC),
    LEVEL_UP_MOVE(23, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE(28, MOVE_CHARM),
    LEVEL_UP_MOVE(32, MOVE_SLASH),
    LEVEL_UP_MOVE(37, MOVE_SHADOW_CLAW),
    LEVEL_UP_MOVE(41, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(46, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(50, MOVE_PAIN_SPLIT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 476
// Types: TYPE_GHOST / TYPE_FAIRY
// Abilities: ABILITY_DISGUISE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
