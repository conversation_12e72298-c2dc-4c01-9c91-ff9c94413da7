// POKEMON_780 (#780) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_780] =
    {
        .baseHP = 78,
        .baseAttack = 60,
        .baseDefense = 85,
        .baseSpAttack = 135,
        .baseSpDefense = 91,
        .baseSpeed = 36,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_DRAGON,
        .catchRate = 70,
        .expYield = 170,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_PERSIM_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_BERSERK,
        .ability2 = ABILITY_SAPSIPPER,
        .abilityHidden = ABILITY_CLOUDNINE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_780LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ECHOED_VOICE),
    LEVEL_UP_MOVE( 1, MOVE_PLAY_NICE),
    LEVEL_UP_MOVE( 5, MOVE_TWISTER),
    LEVEL_UP_MOVE( 9, MOVE_PROTECT),
    LEVEL_UP_MOVE(13, MOVE_GLARE),
    LEVEL_UP_MOVE(17, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(21, MOVE_DRAGON_RAGE),
    LEVEL_UP_MOVE(25, MOVE_NATURAL_GIFT),
    LEVEL_UP_MOVE(29, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(33, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(37, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE(41, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(45, MOVE_FLY),
    LEVEL_UP_MOVE(49, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(53, MOVE_OUTRAGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 485
// Types: TYPE_NORMAL / TYPE_DRAGON
// Abilities: ABILITY_BERSERK, ABILITY_SAPSIPPER, ABILITY_CLOUDNINE
// Level Up Moves: 15
