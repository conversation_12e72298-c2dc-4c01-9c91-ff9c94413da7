// POKEMON_781 (#781) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_781] =
    {
        .baseHP = 70,
        .baseAttack = 131,
        .baseDefense = 100,
        .baseSpAttack = 86,
        .baseSpDefense = 90,
        .baseSpeed = 40,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_GRASS,
        .catchRate = 25,
        .expYield = 181,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_STEELWORKER,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_781LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 1, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_SWITCHEROO),
    LEVEL_UP_MOVE( 5, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE( 9, MOVE_WRAP),
    LEVEL_UP_MOVE(14, MOVE_GYRO_BALL),
    LEVEL_UP_MOVE(18, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(23, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(27, MOVE_WHIRLPOOL),
    LEVEL_UP_MOVE(32, MOVE_ANCHOR_SHOT),
    LEVEL_UP_MOVE(36, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(41, MOVE_ENERGY_BALL),
    LEVEL_UP_MOVE(45, MOVE_SLAM),
    LEVEL_UP_MOVE(50, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(54, MOVE_PHANTOM_FORCE),
    LEVEL_UP_MOVE(59, MOVE_POWER_WHIP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 517
// Types: TYPE_GHOST / TYPE_GRASS
// Abilities: ABILITY_STEELWORKER, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 18
