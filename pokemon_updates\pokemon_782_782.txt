// POKEMON_782 (#782) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_782] =
    {
        .baseHP = 45,
        .baseAttack = 55,
        .baseDefense = 65,
        .baseSpAttack = 45,
        .baseSpDefense = 45,
        .baseSpeed = 45,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 60,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_RAZOR_CLAW,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 40,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_DRAGON,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_BULLETPROOF,
        .ability2 = ABILITY_SOUNDPROOF,
        .abilityHidden = ABILITY_OVERCOAT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_782LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_LEER),
    LEVEL_UP_MOVE( 9, MOVE_BIDE),
    LEVEL_UP_MOVE(13, MOVE_PROTECT),
    LEVEL_UP_MOVE(17, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE(21, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(25, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(29, MOVE_WORK_UP),
    LEVEL_UP_MOVE(33, MOVE_SCREECH),
    LEVEL_UP_MOVE(37, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(41, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(45, MOVE_NOBLE_ROAR),
    LEVEL_UP_MOVE(49, MOVE_DRAGON_DANCE),
    LEVEL_UP_MOVE(53, MOVE_OUTRAGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 300
// Types: TYPE_DRAGON / TYPE_DRAGON
// Abilities: ABILITY_BULLETPROOF, ABILITY_SOUNDPROOF, ABILITY_OVERCOAT
// Level Up Moves: 14
