// POKEMON_783 (#783) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_783] =
    {
        .baseHP = 55,
        .baseAttack = 75,
        .baseDefense = 90,
        .baseSpAttack = 65,
        .baseSpDefense = 70,
        .baseSpeed = 65,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_FIGHTING,
        .catchRate = 45,
        .expYield = 147,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_RAZOR_CLAW,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 40,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_DRAGON,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_BULLETPROOF,
        .ability2 = ABILITY_SOUNDPROOF,
        .abilityHidden = ABILITY_OVERCOAT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_783LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_SKY_UPPERCUT),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_BIDE),
    LEVEL_UP_MOVE( 1, MOVE_PROTECT),
    LEVEL_UP_MOVE( 1, MOVE_AUTOTOMIZE),
    LEVEL_UP_MOVE(17, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE(21, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(25, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(29, MOVE_WORK_UP),
    LEVEL_UP_MOVE(33, MOVE_SCREECH),
    LEVEL_UP_MOVE(38, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(43, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(48, MOVE_NOBLE_ROAR),
    LEVEL_UP_MOVE(53, MOVE_DRAGON_DANCE),
    LEVEL_UP_MOVE(58, MOVE_OUTRAGE),
    LEVEL_UP_MOVE(63, MOVE_CLOSE_COMBAT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 420
// Types: TYPE_DRAGON / TYPE_FIGHTING
// Abilities: ABILITY_BULLETPROOF, ABILITY_SOUNDPROOF, ABILITY_OVERCOAT
// Level Up Moves: 17
