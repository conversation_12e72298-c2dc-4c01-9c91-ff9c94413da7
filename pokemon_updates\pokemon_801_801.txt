// POKEMON_801 (#801) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_801] =
    {
        .baseHP = 80,
        .baseAttack = 95,
        .baseDefense = 115,
        .baseSpAttack = 130,
        .baseSpDefense = 115,
        .baseSpeed = 65,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_FAIRY,
        .catchRate = 3,
        .expYield = 300,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 3,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_SOULHEART,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_801LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SONIC_BOOM),
    LEVEL_UP_MOVE( 1, MOVE_PSYBEAM),
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 1, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE( 1, MOVE_SHIFT_GEAR),
    LEVEL_UP_MOVE( 1, MOVE_CRAFTY_SHIELD),
    LEVEL_UP_MOVE( 1, MOVE_GEAR_UP),
    LEVEL_UP_MOVE( 9, MOVE_LUCKY_CHANT),
    LEVEL_UP_MOVE(12, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(17, MOVE_AURORA_BEAM),
    LEVEL_UP_MOVE(24, MOVE_MAGNETIC_FLUX),
    LEVEL_UP_MOVE(25, MOVE_MIRROR_SHOT),
    LEVEL_UP_MOVE(33, MOVE_MIND_READER),
    LEVEL_UP_MOVE(41, MOVE_FLASH_CANNON),
    LEVEL_UP_MOVE(42, MOVE_LOCK_ON),
    LEVEL_UP_MOVE(49, MOVE_FLEUR_CANNON),
    LEVEL_UP_MOVE(57, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(65, MOVE_PAIN_SPLIT),
    LEVEL_UP_MOVE(73, MOVE_SYNCHRONOISE),
    LEVEL_UP_MOVE(81, MOVE_AURA_SPHERE),
    LEVEL_UP_MOVE(84, MOVE_ZAP_CANNON),
    LEVEL_UP_MOVE(89, MOVE_HEART_SWAP),
    LEVEL_UP_MOVE(97, MOVE_TRUMP_CARD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_STEEL / TYPE_FAIRY
// Abilities: ABILITY_SOULHEART, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 24
