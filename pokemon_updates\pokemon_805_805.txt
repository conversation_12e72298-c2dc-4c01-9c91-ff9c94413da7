// POKEMON_805 (#805) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_805] =
    {
        .baseHP = 61,
        .baseAttack = 131,
        .baseDefense = 211,
        .baseSpAttack = 53,
        .baseSpDefense = 101,
        .baseSpeed = 13,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_STEEL,
        .catchRate = 30,
        .expYield = 285,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 3,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_BEASTBOOST,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_805LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_PROTECT),
    LEVEL_UP_MOVE( 5, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(11, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(15, MOVE_STOMP),
    LEVEL_UP_MOVE(17, MOVE_BIDE),
    LEVEL_UP_MOVE(19, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(23, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE(31, MOVE_AUTOTOMIZE),
    LEVEL_UP_MOVE(37, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(43, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(47, MOVE_ROCK_BLAST),
    LEVEL_UP_MOVE(53, MOVE_WIDE_GUARD),
    LEVEL_UP_MOVE(61, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_ROCK / TYPE_STEEL
// Abilities: ABILITY_BEASTBOOST, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
