// POKEMON_807 (#807) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_807] =
    {
        .baseHP = 88,
        .baseAttack = 112,
        .baseDefense = 75,
        .baseSpAttack = 102,
        .baseSpDefense = 80,
        .baseSpeed = 143,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 3,
        .expYield = 300,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 3,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_VOLTABSORB,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_807LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_SPARK),
    LEVEL_UP_MOVE( 1, MOVE_POWER_UP_PUNCH),
    LEVEL_UP_MOVE( 5, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE( 8, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(12, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(15, MOVE_VOLT_SWITCH),
    LEVEL_UP_MOVE(19, MOVE_SNARL),
    LEVEL_UP_MOVE(22, MOVE_FAKE_OUT),
    LEVEL_UP_MOVE(26, MOVE_CHARGE),
    LEVEL_UP_MOVE(29, MOVE_THUNDER_PUNCH),
    LEVEL_UP_MOVE(33, MOVE_SLASH),
    LEVEL_UP_MOVE(36, MOVE_WILD_CHARGE),
    LEVEL_UP_MOVE(40, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(43, MOVE_PLASMA_FISTS),
    LEVEL_UP_MOVE(47, MOVE_CLOSE_COMBAT),
    LEVEL_UP_MOVE(50, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(80, MOVE_AGILITY),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_VOLTABSORB, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 18
