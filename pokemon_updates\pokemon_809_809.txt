// POKEMON_809 (#809) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_809] =
    {
        .baseHP = 135,
        .baseAttack = 143,
        .baseDefense = 143,
        .baseSpAttack = 80,
        .baseSpDefense = 65,
        .baseSpeed = 34,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_STEEL,
        .catchRate = 3,
        .expYield = 300,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_IRONFIST,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_809LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_THUNDER_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_HEADBUTT),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE(24, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE(32, MOVE_ACID_ARMOR),
    LEVEL_UP_MOVE(40, MOVE_FLASH_CANNON),
    LEVEL_UP_MOVE(48, MOVE_MEGA_PUNCH),
    LEVEL_UP_MOVE(56, MOVE_PROTECT),
    LEVEL_UP_MOVE(64, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(72, MOVE_DYNAMIC_PUNCH),
    LEVEL_UP_MOVE(80, MOVE_SUPERPOWER),
    LEVEL_UP_MOVE(88, MOVE_DOUBLE_IRON_BASH),
    LEVEL_UP_MOVE(96, MOVE_HYPER_BEAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_STEEL / TYPE_STEEL
// Abilities: ABILITY_IRONFIST, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
