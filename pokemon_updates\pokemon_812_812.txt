// POKEMON_812 (#812) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_812] =
    {
        .baseHP = 100,
        .baseAttack = 125,
        .baseDefense = 90,
        .baseSpAttack = 60,
        .baseSpDefense = 70,
        .baseSpeed = 85,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 45,
        .expYield = 265,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_OVERGROW,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_GRASSYSURGE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_812LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_DRUM_BEATING),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_TAUNT),
    LEVEL_UP_MOVE( 1, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE( 1, MOVE_NOBLE_ROAR),
    LEVEL_UP_MOVE( 1, MOVE_GRASSY_TERRAIN),
    LEVEL_UP_MOVE( 1, MOVE_BRANCH_POKE),
    LEVEL_UP_MOVE(12, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(19, MOVE_SCREECH),
    LEVEL_UP_MOVE(24, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(30, MOVE_SLAM),
    LEVEL_UP_MOVE(38, MOVE_UPROAR),
    LEVEL_UP_MOVE(46, MOVE_WOOD_HAMMER),
    LEVEL_UP_MOVE(54, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(62, MOVE_BOOMBURST),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 530
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_OVERGROW, ABILITY_NONE, ABILITY_GRASSYSURGE
// Level Up Moves: 16
