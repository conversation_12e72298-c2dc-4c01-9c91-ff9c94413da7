// POKEMON_819 (#819) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_819] =
    {
        .baseHP = 70,
        .baseAttack = 55,
        .baseDefense = 55,
        .baseSpAttack = 35,
        .baseSpDefense = 35,
        .baseSpeed = 25,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 255,
        .expYield = 55,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_CHEEKPOUCH,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_GLUTTONY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_819LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 5, MOVE_BITE),
    LEVEL_UP_MOVE(10, MOVE_STUFF_CHEEKS),
    LEVEL_UP_MOVE(15, MOVE_STOCKPILE),
    LEVEL_UP_MOVE(15, MOVE_SPIT_UP),
    LEVEL_UP_MOVE(15, MOVE_SWALLOW),
    LEVEL_UP_MOVE(20, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(25, MOVE_REST),
    LEVEL_UP_MOVE(30, MOVE_COUNTER),
    LEVEL_UP_MOVE(35, MOVE_BULLET_SEED),
    LEVEL_UP_MOVE(40, MOVE_SUPER_FANG),
    LEVEL_UP_MOVE(45, MOVE_BELCH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 275
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_CHEEKPOUCH, ABILITY_NONE, ABILITY_GLUTTONY
// Level Up Moves: 13
