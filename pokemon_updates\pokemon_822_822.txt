// POKEMON_822 (#822) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_822] =
    {
        .baseHP = 68,
        .baseAttack = 67,
        .baseDefense = 55,
        .baseSpAttack = 43,
        .baseSpDefense = 55,
        .baseSpeed = 77,
        .type1 = TYPE_FLYING,
        .type2 = TYPE_FLYING,
        .catchRate = 120,
        .expYield = 128,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_KEENEYE,
        .ability2 = ABILITY_UNNERVE,
        .abilityHidden = ABILITY_BIGPECKS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_822LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE( 1, MOVE_POWER_TRIP),
    LEVEL_UP_MOVE(12, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(16, MOVE_PLUCK),
    LEVEL_UP_MOVE(22, MOVE_TAUNT),
    LEVEL_UP_MOVE(28, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(34, MOVE_DRILL_PECK),
    LEVEL_UP_MOVE(40, MOVE_SWAGGER),
    LEVEL_UP_MOVE(46, MOVE_BRAVE_BIRD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 365
// Types: TYPE_FLYING / TYPE_FLYING
// Abilities: ABILITY_KEENEYE, ABILITY_UNNERVE, ABILITY_BIGPECKS
// Level Up Moves: 11
