// POKEMON_823 (#823) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_823] =
    {
        .baseHP = 98,
        .baseAttack = 87,
        .baseDefense = 105,
        .baseSpAttack = 53,
        .baseSpDefense = 85,
        .baseSpeed = 67,
        .type1 = TYPE_FLYING,
        .type2 = TYPE_STEEL,
        .catchRate = 45,
        .expYield = 248,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 3,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_PRESSURE,
        .ability2 = ABILITY_UNNERVE,
        .abilityHidden = ABILITY_MIRRORARMOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_823LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_STEEL_WING),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE( 1, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE( 1, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE( 1, MOVE_POWER_TRIP),
    LEVEL_UP_MOVE(12, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(16, MOVE_PLUCK),
    LEVEL_UP_MOVE(22, MOVE_TAUNT),
    LEVEL_UP_MOVE(28, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(34, MOVE_DRILL_PECK),
    LEVEL_UP_MOVE(42, MOVE_SWAGGER),
    LEVEL_UP_MOVE(50, MOVE_BRAVE_BIRD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 495
// Types: TYPE_FLYING / TYPE_STEEL
// Abilities: ABILITY_PRESSURE, ABILITY_UNNERVE, ABILITY_MIRRORARMOR
// Level Up Moves: 14
