// POKEMON_825 (#825) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_825] =
    {
        .baseHP = 50,
        .baseAttack = 35,
        .baseDefense = 80,
        .baseSpAttack = 50,
        .baseSpDefense = 90,
        .baseSpeed = 30,
        .type1 = TYPE_BUG,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 120,
        .expYield = 117,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 2,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_SWARM,
        .ability2 = ABILITY_COMPOUNDEYES,
        .abilityHidden = ABILITY_TELEPATHY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_825LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 0, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE( 0, MOVE_REFLECT),
    LEVEL_UP_MOVE( 1, MOVE_STRUGGLE_BUG),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 335
// Types: TYPE_BUG / TYPE_PSYCHIC
// Abilities: ABILITY_SWARM, ABILITY_COMPOUNDEYES, ABILITY_TELEPATHY
// Level Up Moves: 4
