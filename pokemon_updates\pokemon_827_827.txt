// POKEMON_827 (#827) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_827] =
    {
        .baseHP = 40,
        .baseAttack = 28,
        .baseDefense = 28,
        .baseSpAttack = 47,
        .baseSpDefense = 52,
        .baseSpeed = 50,
        .type1 = TYPE_DARK,
        .type2 = TYPE_DARK,
        .catchRate = 255,
        .expYield = 49,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_RUNAWAY,
        .ability2 = ABILITY_UNBURDEN,
        .abilityHidden = ABILITY_STAKEOUT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_827LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 4, MOVE_BEAT_UP),
    LEVEL_UP_MOVE( 8, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(12, MOVE_SNARL),
    LEVEL_UP_MOVE(16, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(20, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(24, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(28, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(32, MOVE_TAIL_SLAP),
    LEVEL_UP_MOVE(36, MOVE_FOUL_PLAY),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 245
// Types: TYPE_DARK / TYPE_DARK
// Abilities: ABILITY_RUNAWAY, ABILITY_UNBURDEN, ABILITY_STAKEOUT
// Level Up Moves: 11
