// POKEMON_828 (#828) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_828] =
    {
        .baseHP = 70,
        .baseAttack = 58,
        .baseDefense = 58,
        .baseSpAttack = 87,
        .baseSpDefense = 92,
        .baseSpeed = 90,
        .type1 = TYPE_DARK,
        .type2 = TYPE_DARK,
        .catchRate = 127,
        .expYield = 159,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 2,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_RUNAWAY,
        .ability2 = ABILITY_UNBURDEN,
        .abilityHidden = ABILITY_STAKEOUT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_828LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_THIEF),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_BEAT_UP),
    LEVEL_UP_MOVE( 1, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(12, MOVE_SNARL),
    LEVEL_UP_MOVE(16, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(22, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(28, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(34, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(40, MOVE_TAIL_SLAP),
    LEVEL_UP_MOVE(46, MOVE_FOUL_PLAY),
    LEVEL_UP_MOVE(52, MOVE_PARTING_SHOT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 455
// Types: TYPE_DARK / TYPE_DARK
// Abilities: ABILITY_RUNAWAY, ABILITY_UNBURDEN, ABILITY_STAKEOUT
// Level Up Moves: 13
