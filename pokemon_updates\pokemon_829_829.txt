// POKEMON_829 (#829) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_829] =
    {
        .baseHP = 40,
        .baseAttack = 40,
        .baseDefense = 60,
        .baseSpAttack = 40,
        .baseSpDefense = 60,
        .baseSpeed = 10,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 190,
        .expYield = 50,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_COTTONDOWN,
        .ability2 = ABILITY_REGENERATOR,
        .abilityHidden = ABILITY_EFFECTSPORE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_829LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SING),
    LEVEL_UP_MOVE( 1, MOVE_LEAFAGE),
    LEVEL_UP_MOVE( 4, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE( 8, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(12, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(16, MOVE_ROUND),
    LEVEL_UP_MOVE(21, MOVE_LEAF_TORNADO),
    LEVEL_UP_MOVE(24, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(28, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(32, MOVE_AROMATHERAPY),
    LEVEL_UP_MOVE(36, MOVE_LEAF_STORM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 250
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_COTTONDOWN, ABILITY_REGENERATOR, ABILITY_EFFECTSPORE
// Level Up Moves: 11
