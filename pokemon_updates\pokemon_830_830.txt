// POKEMON_830 (#830) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_830] =
    {
        .baseHP = 60,
        .baseAttack = 50,
        .baseDefense = 90,
        .baseSpAttack = 80,
        .baseSpDefense = 120,
        .baseSpeed = 60,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 75,
        .expYield = 161,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 2,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_COTTONDOWN,
        .ability2 = ABILITY_REGENERATOR,
        .abilityHidden = ABILITY_EFFECTSPORE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_830LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_COTTON_SPORE),
    LEVEL_UP_MOVE( 1, MOVE_SING),
    LEVEL_UP_MOVE( 1, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE( 1, MOVE_LEAFAGE),
    LEVEL_UP_MOVE(12, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(16, MOVE_ROUND),
    LEVEL_UP_MOVE(23, MOVE_LEAF_TORNADO),
    LEVEL_UP_MOVE(28, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(34, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(40, MOVE_AROMATHERAPY),
    LEVEL_UP_MOVE(46, MOVE_LEAF_STORM),
    LEVEL_UP_MOVE(52, MOVE_COTTON_GUARD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 460
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_COTTONDOWN, ABILITY_REGENERATOR, ABILITY_EFFECTSPORE
// Level Up Moves: 13
