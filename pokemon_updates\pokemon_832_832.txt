// POKEMON_832 (#832) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_832] =
    {
        .baseHP = 72,
        .baseAttack = 80,
        .baseDefense = 100,
        .baseSpAttack = 60,
        .baseSpDefense = 90,
        .baseSpeed = 88,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 127,
        .expYield = 172,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_FLUFFY,
        .ability2 = ABILITY_STEADFAST,
        .abilityHidden = ABILITY_BULLETPROOF,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_832LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE(12, MOVE_GUARD_SPLIT),
    LEVEL_UP_MOVE(16, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(21, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(27, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(32, MOVE_GUARD_SWAP),
    LEVEL_UP_MOVE(38, MOVE_REVERSAL),
    LEVEL_UP_MOVE(44, MOVE_COTTON_GUARD),
    LEVEL_UP_MOVE(50, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(56, MOVE_LAST_RESORT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 490
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_FLUFFY, ABILITY_STEADFAST, ABILITY_BULLETPROOF
// Level Up Moves: 13
