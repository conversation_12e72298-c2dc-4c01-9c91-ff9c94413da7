// POKEMON_833 (#833) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_833] =
    {
        .baseHP = 50,
        .baseAttack = 64,
        .baseDefense = 50,
        .baseSpAttack = 38,
        .baseSpDefense = 38,
        .baseSpeed = 44,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 255,
        .expYield = 57,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_WATER_1,
        .ability1 = ABILITY_STRONGJAW,
        .ability2 = ABILITY_SHELLARMOR,
        .abilityHidden = ABILITY_SWIFTSWIM,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_833LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 7, MOVE_BITE),
    LEVEL_UP_MOVE(14, MOVE_PROTECT),
    LEVEL_UP_MOVE(21, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(28, MOVE_COUNTER),
    LEVEL_UP_MOVE(35, MOVE_JAW_LOCK),
    LEVEL_UP_MOVE(42, MOVE_LIQUIDATION),
    LEVEL_UP_MOVE(49, MOVE_BODY_SLAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 284
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_STRONGJAW, ABILITY_SHELLARMOR, ABILITY_SWIFTSWIM
// Level Up Moves: 9
