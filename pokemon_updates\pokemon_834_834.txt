// POKEMON_834 (#834) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_834] =
    {
        .baseHP = 90,
        .baseAttack = 115,
        .baseDefense = 90,
        .baseSpAttack = 48,
        .baseSpDefense = 68,
        .baseSpeed = 74,
        .type1 = TYPE_WATER,
        .type2 = TYPE_ROCK,
        .catchRate = 75,
        .expYield = 170,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_WATER_1,
        .ability1 = ABILITY_STRONGJAW,
        .ability2 = ABILITY_SHELLARMOR,
        .abilityHidden = ABILITY_SWIFTSWIM,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_834LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_ROCK_TOMB),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_PROTECT),
    LEVEL_UP_MOVE( 1, MOVE_CRUNCH),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_POLISH),
    LEVEL_UP_MOVE( 1, MOVE_RAZOR_SHELL),
    LEVEL_UP_MOVE(21, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(30, MOVE_COUNTER),
    LEVEL_UP_MOVE(39, MOVE_JAW_LOCK),
    LEVEL_UP_MOVE(48, MOVE_LIQUIDATION),
    LEVEL_UP_MOVE(57, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(66, MOVE_HEAD_SMASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 485
// Types: TYPE_WATER / TYPE_ROCK
// Abilities: ABILITY_STRONGJAW, ABILITY_SHELLARMOR, ABILITY_SWIFTSWIM
// Level Up Moves: 14
