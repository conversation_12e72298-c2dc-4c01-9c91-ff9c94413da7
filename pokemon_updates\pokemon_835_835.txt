// POKEMON_835 (#835) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_835] =
    {
        .baseHP = 59,
        .baseAttack = 45,
        .baseDefense = 50,
        .baseSpAttack = 40,
        .baseSpDefense = 50,
        .baseSpeed = 26,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 255,
        .expYield = 54,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_BALLFETCH,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_RATTLED,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_835LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 5, MOVE_NUZZLE),
    LEVEL_UP_MOVE(10, MOVE_BITE),
    LEVEL_UP_MOVE(15, MOVE_ROAR),
    LEVEL_UP_MOVE(20, MOVE_SPARK),
    LEVEL_UP_MOVE(26, MOVE_CHARM),
    LEVEL_UP_MOVE(30, MOVE_CRUNCH),
    LEVEL_UP_MOVE(35, MOVE_CHARGE),
    LEVEL_UP_MOVE(40, MOVE_WILD_CHARGE),
    LEVEL_UP_MOVE(45, MOVE_PLAY_ROUGH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 270
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_BALLFETCH, ABILITY_NONE, ABILITY_RATTLED
// Level Up Moves: 11
