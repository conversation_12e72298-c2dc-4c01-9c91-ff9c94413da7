// POKEMON_838 (#838) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_838] =
    {
        .baseHP = 80,
        .baseAttack = 60,
        .baseDefense = 90,
        .baseSpAttack = 60,
        .baseSpDefense = 70,
        .baseSpeed = 50,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_FIRE,
        .catchRate = 120,
        .expYield = 144,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_STEAMENGINE,
        .ability2 = ABILITY_FLAMEBODY,
        .abilityHidden = ABILITY_FLASHFIRE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_838LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_SMOKESCREEN),
    LEVEL_UP_MOVE( 1, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE( 1, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE(15, MOVE_ROCK_POLISH),
    LEVEL_UP_MOVE(20, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(27, MOVE_INCINERATE),
    LEVEL_UP_MOVE(35, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(41, MOVE_HEAT_CRASH),
    LEVEL_UP_MOVE(48, MOVE_ROCK_BLAST),
    LEVEL_UP_MOVE(55, MOVE_BURN_UP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 410
// Types: TYPE_ROCK / TYPE_FIRE
// Abilities: ABILITY_STEAMENGINE, ABILITY_FLAMEBODY, ABILITY_FLASHFIRE
// Level Up Moves: 12
