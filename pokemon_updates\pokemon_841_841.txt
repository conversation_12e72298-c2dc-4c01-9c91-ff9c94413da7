// POKEMON_841 (#841) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_841] =
    {
        .baseHP = 70,
        .baseAttack = 110,
        .baseDefense = 80,
        .baseSpAttack = 95,
        .baseSpDefense = 60,
        .baseSpeed = 70,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 170,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_ERRATIC,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_RIPEN,
        .ability2 = ABILITY_GLUTTONY,
        .abilityHidden = ABILITY_HUSTLE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_841LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 1, MOVE_WITHDRAW),
    LEVEL_UP_MOVE( 1, MOVE_TWISTER),
    LEVEL_UP_MOVE( 1, MOVE_RECYCLE),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 4, MOVE_ACID_SPRAY),
    LEVEL_UP_MOVE( 8, MOVE_ACROBATICS),
    LEVEL_UP_MOVE(12, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(16, MOVE_PROTECT),
    LEVEL_UP_MOVE(20, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(24, MOVE_DRAGON_DANCE),
    LEVEL_UP_MOVE(28, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(32, MOVE_GRAV_APPLE),
    LEVEL_UP_MOVE(36, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(40, MOVE_FLY),
    LEVEL_UP_MOVE(44, MOVE_DRAGON_RUSH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 485
// Types: TYPE_GRASS / TYPE_DRAGON
// Abilities: ABILITY_RIPEN, ABILITY_GLUTTONY, ABILITY_HUSTLE
// Level Up Moves: 17
