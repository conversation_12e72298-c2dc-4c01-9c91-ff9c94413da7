// POKEMON_845 (#845) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_845] =
    {
        .baseHP = 70,
        .baseAttack = 85,
        .baseDefense = 55,
        .baseSpAttack = 85,
        .baseSpDefense = 95,
        .baseSpeed = 85,
        .type1 = TYPE_FLYING,
        .type2 = TYPE_WATER,
        .catchRate = 45,
        .expYield = 166,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 2,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_GULPMISSILE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_845LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_STOCKPILE),
    LEVEL_UP_MOVE( 1, MOVE_SPIT_UP),
    LEVEL_UP_MOVE( 1, MOVE_SWALLOW),
    LEVEL_UP_MOVE( 1, MOVE_BELCH),
    LEVEL_UP_MOVE( 7, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(14, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(21, MOVE_PLUCK),
    LEVEL_UP_MOVE(28, MOVE_DIVE),
    LEVEL_UP_MOVE(35, MOVE_DRILL_PECK),
    LEVEL_UP_MOVE(42, MOVE_AMNESIA),
    LEVEL_UP_MOVE(49, MOVE_THRASH),
    LEVEL_UP_MOVE(56, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 475
// Types: TYPE_FLYING / TYPE_WATER
// Abilities: ABILITY_GULPMISSILE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 13
