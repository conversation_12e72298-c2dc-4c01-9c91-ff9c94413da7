// POKEMON_846 (#846) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_846] =
    {
        .baseHP = 41,
        .baseAttack = 63,
        .baseDefense = 40,
        .baseSpAttack = 40,
        .baseSpDefense = 30,
        .baseSpeed = 66,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 255,
        .expYield = 56,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_2,
        .eggGroup2 = EGG_GROUP_WATER_2,
        .ability1 = ABILITY_SWIFTSWIM,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_PROPELLERTAIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_846LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_AQUA_JET),
    LEVEL_UP_MOVE( 6, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(12, MOVE_BITE),
    LEVEL_UP_MOVE(18, MOVE_AGILITY),
    LEVEL_UP_MOVE(24, MOVE_DIVE),
    LEVEL_UP_MOVE(30, MOVE_LASER_FOCUS),
    LEVEL_UP_MOVE(36, MOVE_CRUNCH),
    LEVEL_UP_MOVE(42, MOVE_LIQUIDATION),
    LEVEL_UP_MOVE(48, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 280
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_SWIFTSWIM, ABILITY_NONE, ABILITY_PROPELLERTAIL
// Level Up Moves: 10
