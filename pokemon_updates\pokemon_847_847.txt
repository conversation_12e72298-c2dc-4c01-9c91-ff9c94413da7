// POKEMON_847 (#847) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_847] =
    {
        .baseHP = 61,
        .baseAttack = 123,
        .baseDefense = 60,
        .baseSpAttack = 60,
        .baseSpDefense = 50,
        .baseSpeed = 136,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 60,
        .expYield = 172,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_2,
        .eggGroup2 = EGG_GROUP_WATER_2,
        .ability1 = ABILITY_SWIFTSWIM,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_PROPELLERTAIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_847LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_AQUA_JET),
    LEVEL_UP_MOVE( 1, MOVE_THROAT_CHOP),
    LEVEL_UP_MOVE(18, MOVE_AGILITY),
    LEVEL_UP_MOVE(24, MOVE_DIVE),
    LEVEL_UP_MOVE(32, MOVE_LASER_FOCUS),
    LEVEL_UP_MOVE(40, MOVE_CRUNCH),
    LEVEL_UP_MOVE(48, MOVE_LIQUIDATION),
    LEVEL_UP_MOVE(56, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 490
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_SWIFTSWIM, ABILITY_NONE, ABILITY_PROPELLERTAIL
// Level Up Moves: 11
