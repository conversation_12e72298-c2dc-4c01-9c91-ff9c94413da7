// POKEMON_848 (#848) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_848] =
    {
        .baseHP = 40,
        .baseAttack = 38,
        .baseDefense = 35,
        .baseSpAttack = 54,
        .baseSpDefense = 35,
        .baseSpeed = 40,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_POISON,
        .catchRate = 75,
        .expYield = 48,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_RATTLED,
        .ability2 = ABILITY_STATIC,
        .abilityHidden = ABILITY_KLUTZ,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_848LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_ACID),
    LEVEL_UP_MOVE( 1, MOVE_FLAIL),
    LEVEL_UP_MOVE( 1, MOVE_BELCH),
    LEVEL_UP_MOVE( 1, MOVE_NUZZLE),
    LEVEL_UP_MOVE( 1, MOVE_TEARFUL_LOOK),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 242
// Types: TYPE_ELECTRIC / TYPE_POISON
// Abilities: ABILITY_RATTLED, ABILITY_STATIC, ABILITY_KLUTZ
// Level Up Moves: 6
