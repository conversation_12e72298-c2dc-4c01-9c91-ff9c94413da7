// POKEMON_850 (#850) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_850] =
    {
        .baseHP = 50,
        .baseAttack = 65,
        .baseDefense = 45,
        .baseSpAttack = 50,
        .baseSpDefense = 50,
        .baseSpeed = 45,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_BUG,
        .catchRate = 190,
        .expYield = 61,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_FLASHFIRE,
        .ability2 = ABILITY_WHITESMOKE,
        .abilityHidden = ABILITY_FLAMEBODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_850LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_SMOKESCREEN),
    LEVEL_UP_MOVE( 5, MOVE_WRAP),
    LEVEL_UP_MOVE(10, MOVE_BITE),
    LEVEL_UP_MOVE(15, MOVE_FLAME_WHEEL),
    LEVEL_UP_MOVE(20, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(25, MOVE_COIL),
    LEVEL_UP_MOVE(30, MOVE_SLAM),
    LEVEL_UP_MOVE(35, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE(40, MOVE_CRUNCH),
    LEVEL_UP_MOVE(45, MOVE_FIRE_LASH),
    LEVEL_UP_MOVE(50, MOVE_LUNGE),
    LEVEL_UP_MOVE(55, MOVE_BURN_UP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 305
// Types: TYPE_FIRE / TYPE_BUG
// Abilities: ABILITY_FLASHFIRE, ABILITY_WHITESMOKE, ABILITY_FLAMEBODY
// Level Up Moves: 13
