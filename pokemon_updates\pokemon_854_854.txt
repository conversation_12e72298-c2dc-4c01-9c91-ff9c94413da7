// POKEMON_854 (#854) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_854] =
    {
        .baseHP = 40,
        .baseAttack = 45,
        .baseDefense = 45,
        .baseSpAttack = 74,
        .baseSpDefense = 54,
        .baseSpeed = 50,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_GHOST,
        .catchRate = 120,
        .expYield = 62,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_INDETERMINATE,
        .ability1 = ABILITY_WEAKARMOR,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_CURSEDBODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_854LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WITHDRAW),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 6, MOVE_AROMATIC_MIST),
    LEVEL_UP_MOVE(12, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(18, MOVE_PROTECT),
    LEVEL_UP_MOVE(24, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(30, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(30, MOVE_AROMATHERAPY),
    LEVEL_UP_MOVE(36, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(42, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(48, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(54, MOVE_MEMENTO),
    LEVEL_UP_MOVE(60, MOVE_SHELL_SMASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 308
// Types: TYPE_GHOST / TYPE_GHOST
// Abilities: ABILITY_WEAKARMOR, ABILITY_NONE, ABILITY_CURSEDBODY
// Level Up Moves: 13
