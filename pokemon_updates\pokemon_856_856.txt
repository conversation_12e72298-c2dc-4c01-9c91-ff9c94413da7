// POKEMON_856 (#856) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_856] =
    {
        .baseHP = 42,
        .baseAttack = 30,
        .baseDefense = 45,
        .baseSpAttack = 56,
        .baseSpDefense = 53,
        .baseSpeed = 39,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 235,
        .expYield = 53,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(100),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_FAIRY,
        .eggGroup2 = EGG_GROUP_FAIRY,
        .ability1 = ABILITY_HEALER,
        .ability2 = ABILITY_ANTICIPATION,
        .abilityHidden = ABILITY_MAGICBOUNCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_856LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_PLAY_NICE),
    LEVEL_UP_MOVE( 5, MOVE_LIFE_DEW),
    LEVEL_UP_MOVE(10, MOVE_DISARMING_VOICE),
    LEVEL_UP_MOVE(15, MOVE_AROMATHERAPY),
    LEVEL_UP_MOVE(20, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(25, MOVE_HEAL_PULSE),
    LEVEL_UP_MOVE(30, MOVE_DAZZLING_GLEAM),
    LEVEL_UP_MOVE(35, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(40, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(45, MOVE_HEALING_WISH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 265
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_HEALER, ABILITY_ANTICIPATION, ABILITY_MAGICBOUNCE
// Level Up Moves: 11
