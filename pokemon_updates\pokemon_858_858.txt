// POKEMON_858 (#858) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_858] =
    {
        .baseHP = 57,
        .baseAttack = 90,
        .baseDefense = 95,
        .baseSpAttack = 136,
        .baseSpDefense = 103,
        .baseSpeed = 29,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_FAIRY,
        .catchRate = 45,
        .expYield = 255,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 3,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(100),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_FAIRY,
        .eggGroup2 = EGG_GROUP_FAIRY,
        .ability1 = ABILITY_HEALER,
        .ability2 = ABILITY_ANTICIPATION,
        .abilityHidden = ABILITY_MAGICBOUNCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_858LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_PSYCHO_CUT),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_DISARMING_VOICE),
    LEVEL_UP_MOVE( 1, MOVE_PLAY_NICE),
    LEVEL_UP_MOVE( 1, MOVE_BRUTAL_SWING),
    LEVEL_UP_MOVE( 1, MOVE_LIFE_DEW),
    LEVEL_UP_MOVE(15, MOVE_AROMATHERAPY),
    LEVEL_UP_MOVE(15, MOVE_AROMATIC_MIST),
    LEVEL_UP_MOVE(20, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(25, MOVE_HEAL_PULSE),
    LEVEL_UP_MOVE(30, MOVE_DAZZLING_GLEAM),
    LEVEL_UP_MOVE(37, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(46, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(55, MOVE_HEALING_WISH),
    LEVEL_UP_MOVE(64, MOVE_MAGIC_POWDER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 510
// Types: TYPE_PSYCHIC / TYPE_FAIRY
// Abilities: ABILITY_HEALER, ABILITY_ANTICIPATION, ABILITY_MAGICBOUNCE
// Level Up Moves: 15
