// POKEMON_863 (#863) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_863] =
    {
        .baseHP = 70,
        .baseAttack = 110,
        .baseDefense = 100,
        .baseSpAttack = 50,
        .baseSpDefense = 60,
        .baseSpeed = 50,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_STEEL,
        .catchRate = 90,
        .expYield = 154,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_BATTLEARMOR,
        .ability2 = ABILITY_TOUGHCLAWS,
        .abilityHidden = ABILITY_STEELYSPIRIT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_863LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_FAKE_OUT),
    LEVEL_UP_MOVE( 1, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE( 1, MOVE_METAL_BURST),
    LEVEL_UP_MOVE( 1, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(12, MOVE_PAY_DAY),
    LEVEL_UP_MOVE(16, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(20, MOVE_TAUNT),
    LEVEL_UP_MOVE(24, MOVE_SWAGGER),
    LEVEL_UP_MOVE(31, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(36, MOVE_SCREECH),
    LEVEL_UP_MOVE(42, MOVE_SLASH),
    LEVEL_UP_MOVE(48, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(54, MOVE_THRASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 440
// Types: TYPE_STEEL / TYPE_STEEL
// Abilities: ABILITY_BATTLEARMOR, ABILITY_TOUGHCLAWS, ABILITY_STEELYSPIRIT
// Level Up Moves: 16
