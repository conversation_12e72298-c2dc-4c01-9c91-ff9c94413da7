// POKEMON_864 (#864) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_864] =
    {
        .baseHP = 60,
        .baseAttack = 95,
        .baseDefense = 50,
        .baseSpAttack = 145,
        .baseSpDefense = 130,
        .baseSpeed = 30,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_GHOST,
        .catchRate = 30,
        .expYield = 179,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(75),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_WATER_3,
        .ability1 = ABILITY_WEAKARMOR,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_PERISHBODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_864LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_DISABLE),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_PERISH_SONG),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE(15, MOVE_SPITE),
    LEVEL_UP_MOVE(20, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(25, MOVE_HEX),
    LEVEL_UP_MOVE(30, MOVE_CURSE),
    LEVEL_UP_MOVE(35, MOVE_STRENGTH_SAP),
    LEVEL_UP_MOVE(40, MOVE_POWER_GEM),
    LEVEL_UP_MOVE(45, MOVE_NIGHT_SHADE),
    LEVEL_UP_MOVE(50, MOVE_GRUDGE),
    LEVEL_UP_MOVE(55, MOVE_MIRROR_COAT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 510
// Types: TYPE_GHOST / TYPE_GHOST
// Abilities: ABILITY_WEAKARMOR, ABILITY_NONE, ABILITY_PERISHBODY
// Level Up Moves: 14
