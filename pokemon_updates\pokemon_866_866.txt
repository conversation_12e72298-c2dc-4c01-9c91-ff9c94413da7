// POKEMON_866 (#866) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_866] =
    {
        .baseHP = 80,
        .baseAttack = 85,
        .baseDefense = 75,
        .baseSpAttack = 110,
        .baseSpDefense = 100,
        .baseSpeed = 70,
        .type1 = TYPE_ICE,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 45,
        .expYield = 182,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 3,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_HUMANSHAPE,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_TANGLEDFEET,
        .ability2 = ABILITY_SCREENCLEANER,
        .abilityHidden = ABILITY_ICEBODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_866LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_MIMIC),
    LEVEL_UP_MOVE( 1, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE( 1, MOVE_REFLECT),
    LEVEL_UP_MOVE( 1, MOVE_PROTECT),
    LEVEL_UP_MOVE( 1, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE( 1, MOVE_BATON_PASS),
    LEVEL_UP_MOVE( 1, MOVE_ENCORE),
    LEVEL_UP_MOVE( 1, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE( 1, MOVE_ROLE_PLAY),
    LEVEL_UP_MOVE( 1, MOVE_RECYCLE),
    LEVEL_UP_MOVE( 1, MOVE_SLACK_OFF),
    LEVEL_UP_MOVE( 1, MOVE_FAKE_TEARS),
    LEVEL_UP_MOVE( 1, MOVE_BLOCK),
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE( 1, MOVE_ICE_SHARD),
    LEVEL_UP_MOVE( 1, MOVE_AFTER_YOU),
    LEVEL_UP_MOVE( 1, MOVE_MISTY_TERRAIN),
    LEVEL_UP_MOVE( 1, MOVE_DAZZLING_GLEAM),
    LEVEL_UP_MOVE(12, MOVE_CONFUSION),
    LEVEL_UP_MOVE(16, MOVE_ALLY_SWITCH),
    LEVEL_UP_MOVE(20, MOVE_ICY_WIND),
    LEVEL_UP_MOVE(24, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(28, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(32, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE(36, MOVE_MIRROR_COAT),
    LEVEL_UP_MOVE(40, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(44, MOVE_FREEZE_DRY),
    LEVEL_UP_MOVE(48, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(52, MOVE_TEETER_DANCE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 520
// Types: TYPE_ICE / TYPE_PSYCHIC
// Abilities: ABILITY_TANGLEDFEET, ABILITY_SCREENCLEANER, ABILITY_ICEBODY
// Level Up Moves: 30
