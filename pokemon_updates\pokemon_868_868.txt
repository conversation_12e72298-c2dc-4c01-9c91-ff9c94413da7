// POKEMON_868 (#868) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_868] =
    {
        .baseHP = 45,
        .baseAttack = 40,
        .baseDefense = 40,
        .baseSpAttack = 50,
        .baseSpDefense = 61,
        .baseSpeed = 34,
        .type1 = TYPE_FAIRY,
        .type2 = TYPE_FAIRY,
        .catchRate = 200,
        .expYield = 54,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(100),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_FAIRY,
        .eggGroup2 = EGG_GROUP_INDETERMINATE,
        .ability1 = ABILITY_SWEETVEIL,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_AROMAVEIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_868LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_AROMATIC_MIST),
    LEVEL_UP_MOVE( 5, MOVE_SWEET_KISS),
    LEVEL_UP_MOVE(10, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(15, MOVE_DRAINING_KISS),
    LEVEL_UP_MOVE(20, MOVE_AROMATHERAPY),
    LEVEL_UP_MOVE(25, MOVE_ATTRACT),
    LEVEL_UP_MOVE(30, MOVE_ACID_ARMOR),
    LEVEL_UP_MOVE(35, MOVE_DAZZLING_GLEAM),
    LEVEL_UP_MOVE(40, MOVE_RECOVER),
    LEVEL_UP_MOVE(45, MOVE_MISTY_TERRAIN),
    LEVEL_UP_MOVE(50, MOVE_ENTRAINMENT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 270
// Types: TYPE_FAIRY / TYPE_FAIRY
// Abilities: ABILITY_SWEETVEIL, ABILITY_NONE, ABILITY_AROMAVEIL
// Level Up Moves: 12
