// POKEMON_869 (#869) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_869] =
    {
        .baseHP = 65,
        .baseAttack = 60,
        .baseDefense = 75,
        .baseSpAttack = 110,
        .baseSpDefense = 121,
        .baseSpeed = 64,
        .type1 = TYPE_FAIRY,
        .type2 = TYPE_FAIRY,
        .catchRate = 100,
        .expYield = 173,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 2,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(100),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_FAIRY,
        .eggGroup2 = EGG_GROUP_INDETERMINATE,
        .ability1 = ABILITY_SWEETVEIL,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_AROMAVEIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_869LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_DECORATE),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_KISS),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE( 1, MOVE_AROMATIC_MIST),
    LEVEL_UP_MOVE(15, MOVE_DRAINING_KISS),
    LEVEL_UP_MOVE(20, MOVE_AROMATHERAPY),
    LEVEL_UP_MOVE(25, MOVE_ATTRACT),
    LEVEL_UP_MOVE(30, MOVE_ACID_ARMOR),
    LEVEL_UP_MOVE(35, MOVE_DAZZLING_GLEAM),
    LEVEL_UP_MOVE(40, MOVE_RECOVER),
    LEVEL_UP_MOVE(45, MOVE_MISTY_TERRAIN),
    LEVEL_UP_MOVE(50, MOVE_ENTRAINMENT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 495
// Types: TYPE_FAIRY / TYPE_FAIRY
// Abilities: ABILITY_SWEETVEIL, ABILITY_NONE, ABILITY_AROMAVEIL
// Level Up Moves: 13
