// POKEMON_870 (#870) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_870] =
    {
        .baseHP = 65,
        .baseAttack = 100,
        .baseDefense = 100,
        .baseSpAttack = 70,
        .baseSpDefense = 60,
        .baseSpeed = 75,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 45,
        .expYield = 165,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_FAIRY,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_BATTLEARMOR,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_DEFIANT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_870LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_PROTECT),
    LEVEL_UP_MOVE( 5, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE(10, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(15, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(20, MOVE_BULK_UP),
    LEVEL_UP_MOVE(25, MOVE_ENDURE),
    LEVEL_UP_MOVE(30, MOVE_REVERSAL),
    LEVEL_UP_MOVE(35, MOVE_FIRST_IMPRESSION),
    LEVEL_UP_MOVE(40, MOVE_NO_RETREAT),
    LEVEL_UP_MOVE(45, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(50, MOVE_CLOSE_COMBAT),
    LEVEL_UP_MOVE(55, MOVE_MEGAHORN),
    LEVEL_UP_MOVE(60, MOVE_COUNTER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 470
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_BATTLEARMOR, ABILITY_NONE, ABILITY_DEFIANT
// Level Up Moves: 14
