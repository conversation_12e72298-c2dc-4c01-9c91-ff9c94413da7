// POKEMON_871 (#871) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_871] =
    {
        .baseHP = 48,
        .baseAttack = 101,
        .baseDefense = 95,
        .baseSpAttack = 91,
        .baseSpDefense = 85,
        .baseSpeed = 15,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 75,
        .expYield = 152,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_INDETERMINATE,
        .ability1 = ABILITY_LIGHTNINGROD,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_ELECTRICSURGE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_871LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 5, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(10, MOVE_CHARGE),
    LEVEL_UP_MOVE(15, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(20, MOVE_SPARK),
    LEVEL_UP_MOVE(25, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(30, MOVE_RECOVER),
    LEVEL_UP_MOVE(35, MOVE_CURSE),
    LEVEL_UP_MOVE(40, MOVE_ELECTRIC_TERRAIN),
    LEVEL_UP_MOVE(45, MOVE_POISON_JAB),
    LEVEL_UP_MOVE(50, MOVE_ZING_ZAP),
    LEVEL_UP_MOVE(55, MOVE_ACUPRESSURE),
    LEVEL_UP_MOVE(60, MOVE_DISCHARGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 435
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_LIGHTNINGROD, ABILITY_NONE, ABILITY_ELECTRICSURGE
// Level Up Moves: 14
