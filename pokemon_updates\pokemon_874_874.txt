// POKEMON_874 (#874) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_874] =
    {
        .baseHP = 100,
        .baseAttack = 125,
        .baseDefense = 135,
        .baseSpAttack = 20,
        .baseSpDefense = 20,
        .baseSpeed = 70,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_ROCK,
        .catchRate = 60,
        .expYield = 165,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_POWERSPOT,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_874LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE( 1, MOVE_BLOCK),
    LEVEL_UP_MOVE( 6, MOVE_ROCK_POLISH),
    LEVEL_UP_MOVE(12, MOVE_ROCK_TOMB),
    LEVEL_UP_MOVE(18, MOVE_GRAVITY),
    LEVEL_UP_MOVE(24, MOVE_STOMP),
    LEVEL_UP_MOVE(30, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(36, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(42, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(48, MOVE_WIDE_GUARD),
    LEVEL_UP_MOVE(54, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(60, MOVE_STONE_EDGE),
    LEVEL_UP_MOVE(66, MOVE_MEGA_KICK),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 470
// Types: TYPE_ROCK / TYPE_ROCK
// Abilities: ABILITY_POWERSPOT, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 13
