// POKEMON_875 (#875) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_875] =
    {
        .baseHP = 75,
        .baseAttack = 80,
        .baseDefense = 110,
        .baseSpAttack = 65,
        .baseSpDefense = 90,
        .baseSpeed = 50,
        .type1 = TYPE_ICE,
        .type2 = TYPE_ICE,
        .catchRate = 60,
        .expYield = 165,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_ICEFACE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_875LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_POWDER_SNOW),
    LEVEL_UP_MOVE( 6, MOVE_MIST),
    LEVEL_UP_MOVE(12, MOVE_WEATHER_BALL),
    LEVEL_UP_MOVE(18, MOVE_ICY_WIND),
    LEVEL_UP_MOVE(24, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(30, MOVE_AMNESIA),
    LEVEL_UP_MOVE(36, MOVE_FREEZE_DRY),
    LEVEL_UP_MOVE(42, MOVE_HAIL),
    LEVEL_UP_MOVE(42, MOVE_SNOWSCAPE),
    LEVEL_UP_MOVE(48, MOVE_AURORA_VEIL),
    LEVEL_UP_MOVE(54, MOVE_SURF),
    LEVEL_UP_MOVE(60, MOVE_BLIZZARD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 470
// Types: TYPE_ICE / TYPE_ICE
// Abilities: ABILITY_ICEFACE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 13
