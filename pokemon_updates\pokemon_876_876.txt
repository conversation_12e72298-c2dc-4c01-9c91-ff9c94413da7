// POKEMON_876 (#876) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_876] =
    {
        .baseHP = 60,
        .baseAttack = 65,
        .baseDefense = 55,
        .baseSpAttack = 105,
        .baseSpDefense = 95,
        .baseSpeed = 95,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_NORMAL,
        .catchRate = 30,
        .expYield = 166,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 40,
        .friendship = 140,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_FAIRY,
        .eggGroup2 = EGG_GROUP_FAIRY,
        .ability1 = ABILITY_INNERFOCUS,
        .ability2 = ABILITY_SYNCHRONIZE,
        .abilityHidden = ABILITY_PSYCHICSURGE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_876LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_STORED_POWER),
    LEVEL_UP_MOVE( 1, MOVE_PLAY_NICE),
    LEVEL_UP_MOVE( 5, MOVE_ENCORE),
    LEVEL_UP_MOVE(10, MOVE_DISARMING_VOICE),
    LEVEL_UP_MOVE(15, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(20, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(25, MOVE_AFTER_YOU),
    LEVEL_UP_MOVE(30, MOVE_AROMATHERAPY),
    LEVEL_UP_MOVE(30, MOVE_HEALING_WISH),
    LEVEL_UP_MOVE(35, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(40, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(45, MOVE_POWER_SPLIT),
    LEVEL_UP_MOVE(50, MOVE_PSYCHIC_TERRAIN),
    LEVEL_UP_MOVE(55, MOVE_LAST_RESORT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 475
// Types: TYPE_PSYCHIC / TYPE_NORMAL
// Abilities: ABILITY_INNERFOCUS, ABILITY_SYNCHRONIZE, ABILITY_PSYCHICSURGE
// Level Up Moves: 14
