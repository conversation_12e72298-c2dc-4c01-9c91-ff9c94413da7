// POKEMON_878 (#878) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_878] =
    {
        .baseHP = 72,
        .baseAttack = 80,
        .baseDefense = 49,
        .baseSpAttack = 40,
        .baseSpDefense = 49,
        .baseSpeed = 40,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_STEEL,
        .catchRate = 190,
        .expYield = 66,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_SHEERFORCE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_HEAVYMETAL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_878LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 5, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(10, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE(15, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(20, MOVE_STOMP),
    LEVEL_UP_MOVE(25, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(30, MOVE_DIG),
    LEVEL_UP_MOVE(35, MOVE_STRENGTH),
    LEVEL_UP_MOVE(40, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(45, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(50, MOVE_HIGH_HORSEPOWER),
    LEVEL_UP_MOVE(55, MOVE_SUPERPOWER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 330
// Types: TYPE_STEEL / TYPE_STEEL
// Abilities: ABILITY_SHEERFORCE, ABILITY_NONE, ABILITY_HEAVYMETAL
// Level Up Moves: 13
