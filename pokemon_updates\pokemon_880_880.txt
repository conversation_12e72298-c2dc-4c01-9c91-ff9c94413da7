// POKEMON_880 (#880) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_880] =
    {
        .baseHP = 90,
        .baseAttack = 100,
        .baseDefense = 90,
        .baseSpAttack = 80,
        .baseSpDefense = 70,
        .baseSpeed = 75,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 177,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 35,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_VOLTABSORB,
        .ability2 = ABILITY_HUSTLE,
        .abilityHidden = ABILITY_SANDRUSH,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_880LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 7, MOVE_CHARGE),
    LEVEL_UP_MOVE(14, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(21, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(28, MOVE_PLUCK),
    LEVEL_UP_MOVE(35, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE(42, MOVE_STOMP),
    LEVEL_UP_MOVE(49, MOVE_SLAM),
    LEVEL_UP_MOVE(56, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(63, MOVE_BOLT_BEAK),
    LEVEL_UP_MOVE(70, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(77, MOVE_DRAGON_RUSH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 505
// Types: TYPE_ELECTRIC / TYPE_DRAGON
// Abilities: ABILITY_VOLTABSORB, ABILITY_HUSTLE, ABILITY_SANDRUSH
// Level Up Moves: 13
