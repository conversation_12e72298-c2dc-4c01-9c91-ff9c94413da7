// POKEMON_883 (#883) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_883] =
    {
        .baseHP = 90,
        .baseAttack = 90,
        .baseDefense = 100,
        .baseSpAttack = 80,
        .baseSpDefense = 90,
        .baseSpeed = 55,
        .type1 = TYPE_WATER,
        .type2 = TYPE_ICE,
        .catchRate = 45,
        .expYield = 177,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 35,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_WATERABSORB,
        .ability2 = ABILITY_ICEBODY,
        .abilityHidden = ABILITY_SLUSHRUSH,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_883LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_POWDER_SNOW),
    LEVEL_UP_MOVE( 7, MOVE_PROTECT),
    LEVEL_UP_MOVE(14, MOVE_ICY_WIND),
    LEVEL_UP_MOVE(21, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(28, MOVE_BITE),
    LEVEL_UP_MOVE(35, MOVE_AURORA_VEIL),
    LEVEL_UP_MOVE(42, MOVE_FREEZE_DRY),
    LEVEL_UP_MOVE(49, MOVE_SUPER_FANG),
    LEVEL_UP_MOVE(56, MOVE_CRUNCH),
    LEVEL_UP_MOVE(63, MOVE_FISHIOUS_REND),
    LEVEL_UP_MOVE(70, MOVE_ICICLE_CRASH),
    LEVEL_UP_MOVE(77, MOVE_BLIZZARD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 505
// Types: TYPE_WATER / TYPE_ICE
// Abilities: ABILITY_WATERABSORB, ABILITY_ICEBODY, ABILITY_SLUSHRUSH
// Level Up Moves: 13
