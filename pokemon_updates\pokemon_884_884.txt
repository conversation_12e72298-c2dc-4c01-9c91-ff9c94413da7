// POKEMON_884 (#884) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_884] =
    {
        .baseHP = 70,
        .baseAttack = 95,
        .baseDefense = 115,
        .baseSpAttack = 120,
        .baseSpDefense = 50,
        .baseSpeed = 85,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 187,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_LIGHTMETAL,
        .ability2 = ABILITY_HEAVYMETAL,
        .abilityHidden = ABILITY_STALWART,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_884LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE( 6, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE(12, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(18, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(24, MOVE_BREAKING_SWIPE),
    LEVEL_UP_MOVE(30, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE(36, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(42, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(42, MOVE_LASER_FOCUS),
    LEVEL_UP_MOVE(48, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(54, MOVE_FLASH_CANNON),
    LEVEL_UP_MOVE(60, MOVE_METAL_BURST),
    LEVEL_UP_MOVE(66, MOVE_HYPER_BEAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 535
// Types: TYPE_STEEL / TYPE_DRAGON
// Abilities: ABILITY_LIGHTMETAL, ABILITY_HEAVYMETAL, ABILITY_STALWART
// Level Up Moves: 14
