// POKEMON_885 (#885) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_885] =
    {
        .baseHP = 28,
        .baseAttack = 60,
        .baseDefense = 30,
        .baseSpAttack = 40,
        .baseSpDefense = 30,
        .baseSpeed = 82,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_GHOST,
        .catchRate = 45,
        .expYield = 54,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 40,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_INDETERMINATE,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_CLEARBODY,
        .ability2 = ABILITY_INFILTRATOR,
        .abilityHidden = ABILITY_CURSEDBODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_885LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_INFESTATION),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 270
// Types: TYPE_DRAGON / TYPE_GHOST
// Abilities: ABILITY_CLEARBODY, ABILITY_INFILTRATOR, ABILITY_CURSEDBODY
// Level Up Moves: 4
