// POKEMON_886 (#886) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_886] =
    {
        .baseHP = 68,
        .baseAttack = 80,
        .baseDefense = 50,
        .baseSpAttack = 60,
        .baseSpDefense = 50,
        .baseSpeed = 102,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_GHOST,
        .catchRate = 45,
        .expYield = 144,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 40,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_INDETERMINATE,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_CLEARBODY,
        .ability2 = ABILITY_INFILTRATOR,
        .abilityHidden = ABILITY_CURSEDBODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_886LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_INFESTATION),
    LEVEL_UP_MOVE( 6, MOVE_LOCK_ON),
    LEVEL_UP_MOVE(12, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(18, MOVE_HEX),
    LEVEL_UP_MOVE(24, MOVE_AGILITY),
    LEVEL_UP_MOVE(30, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE(36, MOVE_U_TURN),
    LEVEL_UP_MOVE(42, MOVE_DRAGON_DANCE),
    LEVEL_UP_MOVE(48, MOVE_PHANTOM_FORCE),
    LEVEL_UP_MOVE(54, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(61, MOVE_DRAGON_RUSH),
    LEVEL_UP_MOVE(66, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(72, MOVE_LAST_RESORT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 410
// Types: TYPE_DRAGON / TYPE_GHOST
// Abilities: ABILITY_CLEARBODY, ABILITY_INFILTRATOR, ABILITY_CURSEDBODY
// Level Up Moves: 17
