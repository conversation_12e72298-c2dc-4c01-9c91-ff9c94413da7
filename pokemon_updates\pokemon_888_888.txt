// POKEMON_888 (#888) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_888] =
    {
        .baseHP = 92,
        .baseAttack = 120,
        .baseDefense = 115,
        .baseSpAttack = 80,
        .baseSpDefense = 115,
        .baseSpeed = 138,
        .type1 = TYPE_FAIRY,
        .type2 = TYPE_FAIRY,
        .catchRate = 10,
        .expYield = 335,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 3,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_INTREPIDSWORD,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_888LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE( 1, MOVE_HOWL),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE( 1, MOVE_SACRED_SWORD),
    LEVEL_UP_MOVE(11, MOVE_SLASH),
    LEVEL_UP_MOVE(22, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(33, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(44, MOVE_NOBLE_ROAR),
    LEVEL_UP_MOVE(44, MOVE_LASER_FOCUS),
    LEVEL_UP_MOVE(55, MOVE_CRUNCH),
    LEVEL_UP_MOVE(66, MOVE_MOONBLAST),
    LEVEL_UP_MOVE(77, MOVE_CLOSE_COMBAT),
    LEVEL_UP_MOVE(88, MOVE_GIGA_IMPACT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 660
// Types: TYPE_FAIRY / TYPE_FAIRY
// Abilities: ABILITY_INTREPIDSWORD, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
