// POKEMON_889 (#889) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_889] =
    {
        .baseHP = 92,
        .baseAttack = 120,
        .baseDefense = 115,
        .baseSpAttack = 80,
        .baseSpDefense = 115,
        .baseSpeed = 138,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 10,
        .expYield = 335,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 3,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_DAUNTLESSSHIELD,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_889LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE( 1, MOVE_HOWL),
    LEVEL_UP_MOVE( 1, MOVE_METAL_BURST),
    LEVEL_UP_MOVE( 1, MOVE_WIDE_GUARD),
    LEVEL_UP_MOVE(11, MOVE_SLASH),
    LEVEL_UP_MOVE(22, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(33, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(44, MOVE_LASER_FOCUS),
    LEVEL_UP_MOVE(55, MOVE_CRUNCH),
    LEVEL_UP_MOVE(66, MOVE_MOONBLAST),
    LEVEL_UP_MOVE(77, MOVE_CLOSE_COMBAT),
    LEVEL_UP_MOVE(88, MOVE_GIGA_IMPACT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 660
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_DAUNTLESSSHIELD, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
