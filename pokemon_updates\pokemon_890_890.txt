// POKEMON_890 (#890) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_890] =
    {
        .baseHP = 140,
        .baseAttack = 85,
        .baseDefense = 95,
        .baseSpAttack = 145,
        .baseSpDefense = 95,
        .baseSpeed = 130,
        .type1 = TYPE_POISON,
        .type2 = TYPE_DRAGON,
        .catchRate = 255,
        .expYield = 345,
        .evYield_HP = 3,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_PRESSURE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_890LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_AGILITY),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE( 1, MOVE_POISON_TAIL),
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE( 8, MOVE_TOXIC),
    LEVEL_UP_MOVE(16, MOVE_VENOSHOCK),
    LEVEL_UP_MOVE(24, MOVE_DRAGON_DANCE),
    LEVEL_UP_MOVE(32, MOVE_CROSS_POISON),
    LEVEL_UP_MOVE(40, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(48, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(56, MOVE_DYNAMAX_CANNON),
    LEVEL_UP_MOVE(64, MOVE_COSMIC_POWER),
    LEVEL_UP_MOVE(72, MOVE_RECOVER),
    LEVEL_UP_MOVE(80, MOVE_HYPER_BEAM),
    LEVEL_UP_MOVE(88, MOVE_OUTRAGE),
    LEVEL_UP_MOVE(88, MOVE_ETERNABEAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 690
// Types: TYPE_POISON / TYPE_DRAGON
// Abilities: ABILITY_PRESSURE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
