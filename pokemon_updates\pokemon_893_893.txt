// POKEMON_893 (#893) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_893] =
    {
        .baseHP = 105,
        .baseAttack = 120,
        .baseDefense = 105,
        .baseSpAttack = 70,
        .baseSpDefense = 95,
        .baseSpeed = 105,
        .type1 = TYPE_DARK,
        .type2 = TYPE_GRASS,
        .catchRate = 3,
        .expYield = 300,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_LEAFGUARD,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_893LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_BIND),
    LEVEL_UP_MOVE( 6, MOVE_LEER),
    LEVEL_UP_MOVE(12, MOVE_VINE_WHIP),
    LEVEL_UP_MOVE(18, MOVE_GROWTH),
    LEVEL_UP_MOVE(24, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(30, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(36, MOVE_GRASS_KNOT),
    LEVEL_UP_MOVE(42, MOVE_BITE),
    LEVEL_UP_MOVE(48, MOVE_U_TURN),
    LEVEL_UP_MOVE(54, MOVE_SWAGGER),
    LEVEL_UP_MOVE(60, MOVE_ENERGY_BALL),
    LEVEL_UP_MOVE(66, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(72, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE(78, MOVE_THRASH),
    LEVEL_UP_MOVE(84, MOVE_POWER_WHIP),
    LEVEL_UP_MOVE(90, MOVE_JUNGLE_HEALING),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_DARK / TYPE_GRASS
// Abilities: ABILITY_LEAFGUARD, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 17
