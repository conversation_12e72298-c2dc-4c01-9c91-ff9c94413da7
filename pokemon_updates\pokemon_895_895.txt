// POKEMON_895 (#895) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_895] =
    {
        .baseHP = 200,
        .baseAttack = 100,
        .baseDefense = 50,
        .baseSpAttack = 100,
        .baseSpDefense = 50,
        .baseSpeed = 80,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_DRAGON,
        .catchRate = 3,
        .expYield = 290,
        .evYield_HP = 3,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 120,
        .friendship = 35,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_DRAGONSMAW,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_895LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_VICE_GRIP),
    LEVEL_UP_MOVE( 1, MOVE_TWISTER),
    LEVEL_UP_MOVE( 6, MOVE_BITE),
    LEVEL_UP_MOVE(12, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(18, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(24, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(30, MOVE_CRUNCH),
    LEVEL_UP_MOVE(36, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(42, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE(48, MOVE_DRAGON_DANCE),
    LEVEL_UP_MOVE(54, MOVE_THRASH),
    LEVEL_UP_MOVE(60, MOVE_LASER_FOCUS),
    LEVEL_UP_MOVE(66, MOVE_DRAGON_ENERGY),
    LEVEL_UP_MOVE(72, MOVE_HYPER_BEAM),
    LEVEL_UP_MOVE(78, MOVE_EXPLOSION),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 580
// Types: TYPE_DRAGON / TYPE_DRAGON
// Abilities: ABILITY_DRAGONSMAW, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
