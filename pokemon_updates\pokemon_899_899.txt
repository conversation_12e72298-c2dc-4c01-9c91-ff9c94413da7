// POKEMON_899 (#899) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_899] =
    {
        .baseHP = 103,
        .baseAttack = 105,
        .baseDefense = 72,
        .baseSpAttack = 105,
        .baseSpDefense = 75,
        .baseSpeed = 65,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 135,
        .expYield = 263,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_INTIMIDATE,
        .ability2 = ABILITY_FRISK,
        .abilityHidden = ABILITY_SAPSIPPER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_899LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_PSYSHIELD_BASH),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 3, MOVE_LEER),
    LEVEL_UP_MOVE( 7, MOVE_ASTONISH),
    LEVEL_UP_MOVE(10, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE(13, MOVE_STOMP),
    LEVEL_UP_MOVE(16, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE(21, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(23, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(27, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(32, MOVE_ROLE_PLAY),
    LEVEL_UP_MOVE(37, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(49, MOVE_IMPRISON),
    LEVEL_UP_MOVE(55, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(62, MOVE_MEGAHORN),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 525
// Types: TYPE_NORMAL / TYPE_PSYCHIC
// Abilities: ABILITY_INTIMIDATE, ABILITY_FRISK, ABILITY_SAPSIPPER
// Level Up Moves: 15
