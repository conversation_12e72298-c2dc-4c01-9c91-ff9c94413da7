// POKEMON_901 (#901) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_901] =
    {
        .baseHP = 130,
        .baseAttack = 140,
        .baseDefense = 105,
        .baseSpAttack = 45,
        .baseSpDefense = 80,
        .baseSpeed = 50,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_NORMAL,
        .catchRate = 75,
        .expYield = 275,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_GUTS,
        .ability2 = ABILITY_BULLETPROOF,
        .abilityHidden = ABILITY_UNNERVE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_901LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_HEADLONG_RUSH),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_LICK),
    LEVEL_UP_MOVE( 1, MOVE_FAKE_TEARS),
    LEVEL_UP_MOVE( 1, MOVE_COVET),
    LEVEL_UP_MOVE( 8, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(13, MOVE_PAYBACK),
    LEVEL_UP_MOVE(17, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(22, MOVE_SLASH),
    LEVEL_UP_MOVE(25, MOVE_PLAY_NICE),
    LEVEL_UP_MOVE(29, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(35, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(41, MOVE_REST),
    LEVEL_UP_MOVE(41, MOVE_SNORE),
    LEVEL_UP_MOVE(48, MOVE_HIGH_HORSEPOWER),
    LEVEL_UP_MOVE(56, MOVE_THRASH),
    LEVEL_UP_MOVE(64, MOVE_HAMMER_ARM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 550
// Types: TYPE_GROUND / TYPE_NORMAL
// Abilities: ABILITY_GUTS, ABILITY_BULLETPROOF, ABILITY_UNNERVE
// Level Up Moves: 18
