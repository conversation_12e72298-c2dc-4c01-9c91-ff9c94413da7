// POKEMON_902 (#902) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_902] =
    {
        .baseHP = 120,
        .baseAttack = 112,
        .baseDefense = 65,
        .baseSpAttack = 80,
        .baseSpDefense = 75,
        .baseSpeed = 78,
        .type1 = TYPE_WATER,
        .type2 = TYPE_GHOST,
        .catchRate = 135,
        .expYield = 265,
        .evYield_HP = 2,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_2,
        .eggGroup2 = EGG_GROUP_WATER_2,
        .ability1 = ABILITY_SWIFTSWIM,
        .ability2 = ABILITY_ADAPTABILITY,
        .abilityHidden = ABILITY_MOLDBREAKER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_902LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE( 1, MOVE_PHANTOM_FORCE),
    LEVEL_UP_MOVE( 4, MOVE_TACKLE),
    LEVEL_UP_MOVE( 8, MOVE_FLAIL),
    LEVEL_UP_MOVE(12, MOVE_AQUA_JET),
    LEVEL_UP_MOVE(16, MOVE_BITE),
    LEVEL_UP_MOVE(20, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(24, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(28, MOVE_SOAK),
    LEVEL_UP_MOVE(32, MOVE_CRUNCH),
    LEVEL_UP_MOVE(36, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(40, MOVE_UPROAR),
    LEVEL_UP_MOVE(44, MOVE_WAVE_CRASH),
    LEVEL_UP_MOVE(48, MOVE_THRASH),
    LEVEL_UP_MOVE(52, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(56, MOVE_HEAD_SMASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 530
// Types: TYPE_WATER / TYPE_GHOST
// Abilities: ABILITY_SWIFTSWIM, ABILITY_ADAPTABILITY, ABILITY_MOLDBREAKER
// Level Up Moves: 18
