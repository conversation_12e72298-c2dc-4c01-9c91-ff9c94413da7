// POKEMON_903 (#903) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_903] =
    {
        .baseHP = 80,
        .baseAttack = 130,
        .baseDefense = 60,
        .baseSpAttack = 40,
        .baseSpDefense = 80,
        .baseSpeed = 120,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_POISON,
        .catchRate = 135,
        .expYield = 102,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_PRESSURE,
        .ability2 = ABILITY_UNBURDEN,
        .abilityHidden = ABILITY_POISONTOUCH,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_903LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_DIRE_CLAW),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE( 1, MOVE_FLING),
    LEVEL_UP_MOVE( 6, MOVE_TAUNT),
    LEVEL_UP_MOVE(12, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(18, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(24, MOVE_POISON_JAB),
    LEVEL_UP_MOVE(30, MOVE_BRICK_BREAK),
    LEVEL_UP_MOVE(36, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(42, MOVE_SLASH),
    LEVEL_UP_MOVE(48, MOVE_AGILITY),
    LEVEL_UP_MOVE(54, MOVE_SCREECH),
    LEVEL_UP_MOVE(60, MOVE_CLOSE_COMBAT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 510
// Types: TYPE_FIGHTING / TYPE_POISON
// Abilities: ABILITY_PRESSURE, ABILITY_UNBURDEN, ABILITY_POISONTOUCH
// Level Up Moves: 15
