// POKEMON_917 (#917) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_917] =
    {
        .baseHP = 35,
        .baseAttack = 41,
        .baseDefense = 45,
        .baseSpAttack = 29,
        .baseSpDefense = 40,
        .baseSpeed = 20,
        .type1 = TYPE_BUG,
        .type2 = TYPE_BUG,
        .catchRate = 255,
        .expYield = 42,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_ERRATIC,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_INSOMNIA,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_STAKEOUT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_917LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_STRING_SHOT),
    LEVEL_UP_MOVE( 5, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE( 8, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(11, MOVE_FEINT),
    LEVEL_UP_MOVE(14, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(18, MOVE_BLOCK),
    LEVEL_UP_MOVE(22, MOVE_COUNTER),
    LEVEL_UP_MOVE(25, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(29, MOVE_STICKY_WEB),
    LEVEL_UP_MOVE(33, MOVE_GASTRO_ACID),
    LEVEL_UP_MOVE(36, MOVE_CIRCLE_THROW),
    LEVEL_UP_MOVE(40, MOVE_THROAT_CHOP),
    LEVEL_UP_MOVE(44, MOVE_SKITTER_SMACK),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 210
// Types: TYPE_BUG / TYPE_BUG
// Abilities: ABILITY_INSOMNIA, ABILITY_NONE, ABILITY_STAKEOUT
// Level Up Moves: 14
