// POKEMON_919 (#919) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_919] =
    {
        .baseHP = 33,
        .baseAttack = 46,
        .baseDefense = 40,
        .baseSpAttack = 21,
        .baseSpDefense = 25,
        .baseSpeed = 45,
        .type1 = TYPE_BUG,
        .type2 = TYPE_BUG,
        .catchRate = 190,
        .expYield = 42,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 20,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_SWARM,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_TINTEDLENS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_919LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 4, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE( 6, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 9, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(11, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(14, MOVE_SCREECH),
    LEVEL_UP_MOVE(18, MOVE_ENDURE),
    LEVEL_UP_MOVE(22, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(26, MOVE_FEINT),
    LEVEL_UP_MOVE(30, MOVE_AGILITY),
    LEVEL_UP_MOVE(38, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(41, MOVE_FIRST_IMPRESSION),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 210
// Types: TYPE_BUG / TYPE_BUG
// Abilities: ABILITY_SWARM, ABILITY_NONE, ABILITY_TINTEDLENS
// Level Up Moves: 13
