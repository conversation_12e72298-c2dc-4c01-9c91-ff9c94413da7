// POKEMON_920 (#920) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_920] =
    {
        .baseHP = 71,
        .baseAttack = 102,
        .baseDefense = 78,
        .baseSpAttack = 52,
        .baseSpDefense = 55,
        .baseSpeed = 92,
        .type1 = TYPE_BUG,
        .type2 = TYPE_DARK,
        .catchRate = 30,
        .expYield = 158,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_SWARM,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_TINTEDLENS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_920LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_LUNGE),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_LOW_KICK),
    LEVEL_UP_MOVE( 1, MOVE_DETECT),
    LEVEL_UP_MOVE( 4, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE( 6, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 9, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(11, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(14, MOVE_SCREECH),
    LEVEL_UP_MOVE(18, MOVE_ENDURE),
    LEVEL_UP_MOVE(22, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(28, MOVE_FEINT),
    LEVEL_UP_MOVE(32, MOVE_AGILITY),
    LEVEL_UP_MOVE(36, MOVE_THROAT_CHOP),
    LEVEL_UP_MOVE(40, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(44, MOVE_FIRST_IMPRESSION),
    LEVEL_UP_MOVE(48, MOVE_BOUNCE),
    LEVEL_UP_MOVE(53, MOVE_AXE_KICK),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 450
// Types: TYPE_BUG / TYPE_DARK
// Abilities: ABILITY_SWARM, ABILITY_NONE, ABILITY_TINTEDLENS
// Level Up Moves: 19
