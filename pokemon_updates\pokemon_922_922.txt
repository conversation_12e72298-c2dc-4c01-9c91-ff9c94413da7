// POKEMON_922 (#922) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_922] =
    {
        .baseHP = 60,
        .baseAttack = 75,
        .baseDefense = 40,
        .baseSpAttack = 50,
        .baseSpDefense = 40,
        .baseSpeed = 85,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_FIGHTING,
        .catchRate = 80,
        .expYield = 123,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_VOLTABSORB,
        .ability2 = ABILITY_NATURALCURE,
        .abilityHidden = ABILITY_IRONFIST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_922LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_ARM_THRUST),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 3, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 6, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 8, MOVE_CHARGE),
    LEVEL_UP_MOVE(12, MOVE_NUZZLE),
    LEVEL_UP_MOVE(15, MOVE_DIG),
    LEVEL_UP_MOVE(19, MOVE_BITE),
    LEVEL_UP_MOVE(23, MOVE_SPARK),
    LEVEL_UP_MOVE(27, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE(32, MOVE_SLAM),
    LEVEL_UP_MOVE(38, MOVE_ENTRAINMENT),
    LEVEL_UP_MOVE(42, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(46, MOVE_AGILITY),
    LEVEL_UP_MOVE(52, MOVE_WILD_CHARGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 350
// Types: TYPE_ELECTRIC / TYPE_FIGHTING
// Abilities: ABILITY_VOLTABSORB, ABILITY_NATURALCURE, ABILITY_IRONFIST
// Level Up Moves: 16
