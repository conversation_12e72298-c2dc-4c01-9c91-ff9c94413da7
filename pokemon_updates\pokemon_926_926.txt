// POKEMON_926 (#926) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_926] =
    {
        .baseHP = 37,
        .baseAttack = 55,
        .baseDefense = 70,
        .baseSpAttack = 30,
        .baseSpDefense = 55,
        .baseSpeed = 65,
        .type1 = TYPE_FAIRY,
        .type2 = TYPE_FAIRY,
        .catchRate = 190,
        .expYield = 62,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_OWNTEMPO,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_KLUTZ,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_926LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 3, MOVE_LICK),
    LEVEL_UP_MOVE( 6, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 8, MOVE_COVET),
    LEVEL_UP_MOVE(11, MOVE_BITE),
    LEVEL_UP_MOVE(15, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(18, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(22, MOVE_WORK_UP),
    LEVEL_UP_MOVE(26, MOVE_BATON_PASS),
    LEVEL_UP_MOVE(30, MOVE_ROAR),
    LEVEL_UP_MOVE(33, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(36, MOVE_CHARM),
    LEVEL_UP_MOVE(40, MOVE_CRUNCH),
    LEVEL_UP_MOVE(45, MOVE_LAST_RESORT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 312
// Types: TYPE_FAIRY / TYPE_FAIRY
// Abilities: ABILITY_OWNTEMPO, ABILITY_NONE, ABILITY_KLUTZ
// Level Up Moves: 15
