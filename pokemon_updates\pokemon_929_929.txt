// POKEMON_929 (#929) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_929] =
    {
        .baseHP = 52,
        .baseAttack = 53,
        .baseDefense = 60,
        .baseSpAttack = 78,
        .baseSpDefense = 78,
        .baseSpeed = 33,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_NORMAL,
        .catchRate = 120,
        .expYield = 124,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_EARLYBIRD,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_HARVEST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_929LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE( 5, MOVE_ABSORB),
    LEVEL_UP_MOVE( 7, MOVE_GROWTH),
    LEVEL_UP_MOVE(10, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(13, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(16, MOVE_FLAIL),
    LEVEL_UP_MOVE(20, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(23, MOVE_GRASSY_TERRAIN),
    LEVEL_UP_MOVE(29, MOVE_SEED_BOMB),
    LEVEL_UP_MOVE(34, MOVE_ENERGY_BALL),
    LEVEL_UP_MOVE(37, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(42, MOVE_TERRAIN_PULSE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 354
// Types: TYPE_GRASS / TYPE_NORMAL
// Abilities: ABILITY_EARLYBIRD, ABILITY_NONE, ABILITY_HARVEST
// Level Up Moves: 13
