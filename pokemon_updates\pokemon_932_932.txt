// POKEMON_932 (#932) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_932] =
    {
        .baseHP = 55,
        .baseAttack = 55,
        .baseDefense = 75,
        .baseSpAttack = 35,
        .baseSpDefense = 35,
        .baseSpeed = 25,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_ROCK,
        .catchRate = 255,
        .expYield = 56,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_PURIFYINGSALT,
        .ability2 = ABILITY_STURDY,
        .abilityHidden = ABILITY_CLEARBODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_932LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 5, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE( 7, MOVE_MUD_SHOT),
    LEVEL_UP_MOVE(10, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE(13, MOVE_ROCK_POLISH),
    LEVEL_UP_MOVE(16, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(20, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(25, MOVE_RECOVER),
    LEVEL_UP_MOVE(30, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(33, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(35, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(40, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(45, MOVE_STONE_EDGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 280
// Types: TYPE_ROCK / TYPE_ROCK
// Abilities: ABILITY_PURIFYINGSALT, ABILITY_STURDY, ABILITY_CLEARBODY
// Level Up Moves: 14
