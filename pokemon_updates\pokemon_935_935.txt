// POKEMON_935 (#935) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_935] =
    {
        .baseHP = 40,
        .baseAttack = 50,
        .baseDefense = 40,
        .baseSpAttack = 50,
        .baseSpDefense = 40,
        .baseSpeed = 35,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 90,
        .expYield = 51,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 35,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_HUMANSHAPE,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_FLASHFIRE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_FLAMEBODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_935LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 8, MOVE_CLEAR_SMOG),
    LEVEL_UP_MOVE(12, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE(16, MOVE_WILL_O_WISP),
    LEVEL_UP_MOVE(20, MOVE_NIGHT_SHADE),
    LEVEL_UP_MOVE(24, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE(28, MOVE_INCINERATE),
    LEVEL_UP_MOVE(32, MOVE_LAVA_PLUME),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 255
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_FLASHFIRE, ABILITY_NONE, ABILITY_FLAMEBODY
// Level Up Moves: 10
