// POKEMON_939 (#939) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_939] =
    {
        .baseHP = 109,
        .baseAttack = 64,
        .baseDefense = 91,
        .baseSpAttack = 103,
        .baseSpDefense = 83,
        .baseSpeed = 45,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 50,
        .expYield = 173,
        .evYield_HP = 2,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_WATER_1,
        .ability1 = ABILITY_ELECTROMORPHOSIS,
        .ability2 = ABILITY_STATIC,
        .abilityHidden = ABILITY_DAMP,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_939LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_SLACK_OFF),
    LEVEL_UP_MOVE( 7, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE(11, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(17, MOVE_CHARGE),
    LEVEL_UP_MOVE(21, MOVE_SPARK),
    LEVEL_UP_MOVE(24, MOVE_MUD_SHOT),
    LEVEL_UP_MOVE(25, MOVE_FLAIL),
    LEVEL_UP_MOVE(32, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(36, MOVE_WEATHER_BALL),
    LEVEL_UP_MOVE(40, MOVE_ELECTRIC_TERRAIN),
    LEVEL_UP_MOVE(45, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(50, MOVE_ZAP_CANNON),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 495
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_ELECTROMORPHOSIS, ABILITY_STATIC, ABILITY_DAMP
// Level Up Moves: 14
