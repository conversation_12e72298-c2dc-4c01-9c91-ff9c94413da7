// POKEMON_940 (#940) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_940] =
    {
        .baseHP = 40,
        .baseAttack = 40,
        .baseDefense = 35,
        .baseSpAttack = 55,
        .baseSpDefense = 40,
        .baseSpeed = 70,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_FLYING,
        .catchRate = 180,
        .expYield = 56,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_WINDPOWER,
        .ability2 = ABILITY_VOLTABSORB,
        .abilityHidden = ABILITY_COMPETITIVE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_940LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 4, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 7, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(11, MOVE_PLUCK),
    LEVEL_UP_MOVE(15, MOVE_SPARK),
    LEVEL_UP_MOVE(19, MOVE_UPROAR),
    LEVEL_UP_MOVE(23, MOVE_ROOST),
    LEVEL_UP_MOVE(27, MOVE_DUAL_WINGBEAT),
    LEVEL_UP_MOVE(32, MOVE_AGILITY),
    LEVEL_UP_MOVE(37, MOVE_VOLT_SWITCH),
    LEVEL_UP_MOVE(43, MOVE_DISCHARGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 280
// Types: TYPE_ELECTRIC / TYPE_FLYING
// Abilities: ABILITY_WINDPOWER, ABILITY_VOLTABSORB, ABILITY_COMPETITIVE
// Level Up Moves: 12
