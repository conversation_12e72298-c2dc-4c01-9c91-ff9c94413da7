// POKEMON_941 (#941) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_941] =
    {
        .baseHP = 70,
        .baseAttack = 70,
        .baseDefense = 60,
        .baseSpAttack = 105,
        .baseSpDefense = 60,
        .baseSpeed = 125,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_FLYING,
        .catchRate = 90,
        .expYield = 172,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_WINDPOWER,
        .ability2 = ABILITY_VOLTABSORB,
        .abilityHidden = ABILITY_COMPETITIVE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_941LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_ELECTRO_BALL),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 4, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 7, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(11, MOVE_PLUCK),
    LEVEL_UP_MOVE(15, MOVE_SPARK),
    LEVEL_UP_MOVE(19, MOVE_UPROAR),
    LEVEL_UP_MOVE(24, MOVE_ROOST),
    LEVEL_UP_MOVE(30, MOVE_DUAL_WINGBEAT),
    LEVEL_UP_MOVE(36, MOVE_AGILITY),
    LEVEL_UP_MOVE(43, MOVE_VOLT_SWITCH),
    LEVEL_UP_MOVE(48, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(55, MOVE_HURRICANE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 490
// Types: TYPE_ELECTRIC / TYPE_FLYING
// Abilities: ABILITY_WINDPOWER, ABILITY_VOLTABSORB, ABILITY_COMPETITIVE
// Level Up Moves: 14
