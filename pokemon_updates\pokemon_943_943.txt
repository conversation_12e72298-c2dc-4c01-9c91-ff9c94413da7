// POKEMON_943 (#943) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_943] =
    {
        .baseHP = 80,
        .baseAttack = 120,
        .baseDefense = 90,
        .baseSpAttack = 60,
        .baseSpDefense = 70,
        .baseSpeed = 85,
        .type1 = TYPE_DARK,
        .type2 = TYPE_DARK,
        .catchRate = 75,
        .expYield = 177,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_INTIMIDATE,
        .ability2 = ABILITY_GUARDDOG,
        .abilityHidden = ABILITY_STAKEOUT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_943LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_COMEUPPANCE),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE( 4, MOVE_LICK),
    LEVEL_UP_MOVE( 7, MOVE_SNARL),
    LEVEL_UP_MOVE(10, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(14, MOVE_BITE),
    LEVEL_UP_MOVE(18, MOVE_ROAR),
    LEVEL_UP_MOVE(22, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(26, MOVE_PAYBACK),
    LEVEL_UP_MOVE(34, MOVE_CRUNCH),
    LEVEL_UP_MOVE(39, MOVE_SWAGGER),
    LEVEL_UP_MOVE(43, MOVE_REVERSAL),
    LEVEL_UP_MOVE(48, MOVE_JAW_LOCK),
    LEVEL_UP_MOVE(55, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(60, MOVE_OUTRAGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 505
// Types: TYPE_DARK / TYPE_DARK
// Abilities: ABILITY_INTIMIDATE, ABILITY_GUARDDOG, ABILITY_STAKEOUT
// Level Up Moves: 17
