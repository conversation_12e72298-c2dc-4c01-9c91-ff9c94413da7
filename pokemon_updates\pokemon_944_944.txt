// POKEMON_944 (#944) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_944] =
    {
        .baseHP = 40,
        .baseAttack = 65,
        .baseDefense = 35,
        .baseSpAttack = 40,
        .baseSpDefense = 35,
        .baseSpeed = 75,
        .type1 = TYPE_POISON,
        .type2 = TYPE_NORMAL,
        .catchRate = 190,
        .expYield = 58,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_UNBURDEN,
        .ability2 = ABILITY_PICKPOCKET,
        .abilityHidden = ABILITY_PRANKSTER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_944LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 5, MOVE_ACID_SPRAY),
    LEVEL_UP_MOVE( 8, MOVE_BITE),
    LEVEL_UP_MOVE( 8, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(11, MOVE_SWITCHEROO),
    LEVEL_UP_MOVE(14, MOVE_POISON_FANG),
    LEVEL_UP_MOVE(18, MOVE_FLATTER),
    LEVEL_UP_MOVE(21, MOVE_SLASH),
    LEVEL_UP_MOVE(25, MOVE_U_TURN),
    LEVEL_UP_MOVE(29, MOVE_POISON_JAB),
    LEVEL_UP_MOVE(33, MOVE_TAUNT),
    LEVEL_UP_MOVE(36, MOVE_SUBSTITUTE),
    LEVEL_UP_MOVE(40, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(45, MOVE_GUNK_SHOT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 290
// Types: TYPE_POISON / TYPE_NORMAL
// Abilities: ABILITY_UNBURDEN, ABILITY_PICKPOCKET, ABILITY_PRANKSTER
// Level Up Moves: 15
