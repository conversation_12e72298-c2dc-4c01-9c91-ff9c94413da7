// POKEMON_948 (#948) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_948] =
    {
        .baseHP = 40,
        .baseAttack = 40,
        .baseDefense = 35,
        .baseSpAttack = 50,
        .baseSpDefense = 100,
        .baseSpeed = 70,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_GRASS,
        .catchRate = 190,
        .expYield = 67,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_MYCELIUMMIGHT,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_MYCELIUMMIGHT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_948LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WRAP),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 4, MOVE_ABSORB),
    LEVEL_UP_MOVE( 8, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE( 8, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE(12, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(15, MOVE_TACKLE),
    LEVEL_UP_MOVE(16, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(20, MOVE_SCREECH),
    LEVEL_UP_MOVE(24, MOVE_MUD_SHOT),
    LEVEL_UP_MOVE(28, MOVE_HEX),
    LEVEL_UP_MOVE(32, MOVE_SEED_BOMB),
    LEVEL_UP_MOVE(36, MOVE_SPORE),
    LEVEL_UP_MOVE(40, MOVE_GROWTH),
    LEVEL_UP_MOVE(44, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(48, MOVE_EARTH_POWER),
    LEVEL_UP_MOVE(52, MOVE_POWER_WHIP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 335
// Types: TYPE_GROUND / TYPE_GRASS
// Abilities: ABILITY_MYCELIUMMIGHT, ABILITY_NONE, ABILITY_MYCELIUMMIGHT
// Level Up Moves: 17
