// POKEMON_950 (#950) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_950] =
    {
        .baseHP = 70,
        .baseAttack = 100,
        .baseDefense = 115,
        .baseSpAttack = 35,
        .baseSpDefense = 55,
        .baseSpeed = 75,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_ROCK,
        .catchRate = 120,
        .expYield = 158,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 35,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_3,
        .eggGroup2 = EGG_GROUP_WATER_3,
        .ability1 = ABILITY_ANGERSHELL,
        .ability2 = ABILITY_SHELLARMOR,
        .abilityHidden = ABILITY_REGENERATOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_950LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE( 6, MOVE_HARDEN),
    LEVEL_UP_MOVE( 9, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE(13, MOVE_ROCK_TOMB),
    LEVEL_UP_MOVE(17, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(21, MOVE_PROTECT),
    LEVEL_UP_MOVE(24, MOVE_ROCK_BLAST),
    LEVEL_UP_MOVE(29, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(33, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(37, MOVE_FLAIL),
    LEVEL_UP_MOVE(42, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(47, MOVE_HIGH_HORSEPOWER),
    LEVEL_UP_MOVE(51, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(56, MOVE_GUILLOTINE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 450
// Types: TYPE_ROCK / TYPE_ROCK
// Abilities: ABILITY_ANGERSHELL, ABILITY_SHELLARMOR, ABILITY_REGENERATOR
// Level Up Moves: 14
