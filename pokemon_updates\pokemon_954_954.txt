// POKEMON_954 (#954) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_954] =
    {
        .baseHP = 75,
        .baseAttack = 50,
        .baseDefense = 85,
        .baseSpAttack = 115,
        .baseSpDefense = 100,
        .baseSpeed = 45,
        .type1 = TYPE_BUG,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 45,
        .expYield = 165,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_SYNCHRONIZE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_TELEPATHY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_954LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_REVIVAL_BLESSING),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 4, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 7, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE(11, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(15, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(20, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(24, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(29, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE(35, MOVE_LUNGE),
    LEVEL_UP_MOVE(40, MOVE_POWER_SWAP),
    LEVEL_UP_MOVE(40, MOVE_GUARD_SWAP),
    LEVEL_UP_MOVE(40, MOVE_SPEED_SWAP),
    LEVEL_UP_MOVE(45, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(50, MOVE_PSYCHIC),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 470
// Types: TYPE_BUG / TYPE_PSYCHIC
// Abilities: ABILITY_SYNCHRONIZE, ABILITY_NONE, ABILITY_TELEPATHY
// Level Up Moves: 17
