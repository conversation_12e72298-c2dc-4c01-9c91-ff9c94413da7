// POKEMON_960 (#960) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_960] =
    {
        .baseHP = 10,
        .baseAttack = 55,
        .baseDefense = 25,
        .baseSpAttack = 35,
        .baseSpDefense = 25,
        .baseSpeed = 95,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 255,
        .expYield = 49,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_3,
        .eggGroup2 = EGG_GROUP_WATER_3,
        .ability1 = ABILITY_GOOEY,
        .ability2 = ABILITY_RATTLED,
        .abilityHidden = ABILITY_SANDVEIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_960LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 4, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 8, MOVE_WRAP),
    LEVEL_UP_MOVE(12, MOVE_AQUA_JET),
    LEVEL_UP_MOVE(20, MOVE_SLAM),
    LEVEL_UP_MOVE(20, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(24, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(28, MOVE_DIG),
    LEVEL_UP_MOVE(32, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(36, MOVE_THROAT_CHOP),
    LEVEL_UP_MOVE(40, MOVE_LIQUIDATION),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 245
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_GOOEY, ABILITY_RATTLED, ABILITY_SANDVEIL
// Level Up Moves: 12
