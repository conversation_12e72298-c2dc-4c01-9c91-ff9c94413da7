// POKEMON_963 (#963) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_963] =
    {
        .baseHP = 70,
        .baseAttack = 45,
        .baseDefense = 40,
        .baseSpAttack = 45,
        .baseSpDefense = 40,
        .baseSpeed = 75,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 200,
        .expYield = 63,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 40,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_WATER_2,
        .ability1 = ABILITY_WATERVEIL,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_WATERVEIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_963LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 7, MOVE_ASTONISH),
    LEVEL_UP_MOVE(10, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(13, MOVE_AQUA_JET),
    LEVEL_UP_MOVE(17, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE(21, MOVE_DIVE),
    LEVEL_UP_MOVE(25, MOVE_CHARM),
    LEVEL_UP_MOVE(29, MOVE_ACROBATICS),
    LEVEL_UP_MOVE(34, MOVE_ENCORE),
    LEVEL_UP_MOVE(39, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(44, MOVE_MIST),
    LEVEL_UP_MOVE(50, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 315
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_WATERVEIL, ABILITY_NONE, ABILITY_WATERVEIL
// Level Up Moves: 13
