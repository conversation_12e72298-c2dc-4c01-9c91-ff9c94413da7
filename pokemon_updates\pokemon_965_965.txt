// POKEMON_965 (#965) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_965] =
    {
        .baseHP = 45,
        .baseAttack = 70,
        .baseDefense = 63,
        .baseSpAttack = 30,
        .baseSpDefense = 45,
        .baseSpeed = 47,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_POISON,
        .catchRate = 190,
        .expYield = 60,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_OVERCOAT,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_SLOWSTART,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_965LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LICK),
    LEVEL_UP_MOVE( 1, MOVE_POISON_GAS),
    LEVEL_UP_MOVE( 4, MOVE_SMOG),
    LEVEL_UP_MOVE( 7, MOVE_TAUNT),
    LEVEL_UP_MOVE(10, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(13, MOVE_SLUDGE),
    LEVEL_UP_MOVE(17, MOVE_GYRO_BALL),
    LEVEL_UP_MOVE(21, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(25, MOVE_SCREECH),
    LEVEL_UP_MOVE(28, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(32, MOVE_SWAGGER),
    LEVEL_UP_MOVE(36, MOVE_POISON_JAB),
    LEVEL_UP_MOVE(41, MOVE_UPROAR),
    LEVEL_UP_MOVE(46, MOVE_SPIN_OUT),
    LEVEL_UP_MOVE(50, MOVE_GUNK_SHOT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 300
// Types: TYPE_STEEL / TYPE_POISON
// Abilities: ABILITY_OVERCOAT, ABILITY_NONE, ABILITY_SLOWSTART
// Level Up Moves: 15
