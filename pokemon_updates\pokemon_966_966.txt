// POKEMON_966 (#966) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_966] =
    {
        .baseHP = 80,
        .baseAttack = 119,
        .baseDefense = 90,
        .baseSpAttack = 54,
        .baseSpDefense = 67,
        .baseSpeed = 90,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_POISON,
        .catchRate = 75,
        .expYield = 175,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_OVERCOAT,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_FILTER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_966LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_SHIFT_GEAR),
    LEVEL_UP_MOVE( 1, MOVE_LICK),
    LEVEL_UP_MOVE( 1, MOVE_POISON_GAS),
    LEVEL_UP_MOVE( 1, MOVE_MAGNET_RISE),
    LEVEL_UP_MOVE( 4, MOVE_SMOG),
    LEVEL_UP_MOVE( 7, MOVE_TAUNT),
    LEVEL_UP_MOVE(10, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(13, MOVE_SLUDGE),
    LEVEL_UP_MOVE(17, MOVE_GYRO_BALL),
    LEVEL_UP_MOVE(21, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(25, MOVE_SCREECH),
    LEVEL_UP_MOVE(28, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(32, MOVE_SWAGGER),
    LEVEL_UP_MOVE(36, MOVE_POISON_JAB),
    LEVEL_UP_MOVE(46, MOVE_UPROAR),
    LEVEL_UP_MOVE(52, MOVE_SPIN_OUT),
    LEVEL_UP_MOVE(58, MOVE_GUNK_SHOT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_STEEL / TYPE_POISON
// Abilities: ABILITY_OVERCOAT, ABILITY_NONE, ABILITY_FILTER
// Level Up Moves: 17
