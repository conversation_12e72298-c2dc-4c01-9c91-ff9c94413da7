// POKEMON_973 (#973) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_973] =
    {
        .baseHP = 82,
        .baseAttack = 115,
        .baseDefense = 74,
        .baseSpAttack = 75,
        .baseSpDefense = 64,
        .baseSpeed = 90,
        .type1 = TYPE_FLYING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 100,
        .expYield = 175,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_SCRAPPY,
        .ability2 = ABILITY_TANGLEDFEET,
        .abilityHidden = ABILITY_COSTAR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_973LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE( 5, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE( 9, MOVE_DETECT),
    LEVEL_UP_MOVE(12, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(15, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(18, MOVE_LOW_KICK),
    LEVEL_UP_MOVE(21, MOVE_FEINT),
    LEVEL_UP_MOVE(27, MOVE_PAYBACK),
    LEVEL_UP_MOVE(31, MOVE_ROOST),
    LEVEL_UP_MOVE(35, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(39, MOVE_MEGA_KICK),
    LEVEL_UP_MOVE(44, MOVE_WIDE_GUARD),
    LEVEL_UP_MOVE(48, MOVE_THROAT_CHOP),
    LEVEL_UP_MOVE(54, MOVE_BRAVE_BIRD),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_FLYING / TYPE_FIGHTING
// Abilities: ABILITY_SCRAPPY, ABILITY_TANGLEDFEET, ABILITY_COSTAR
// Level Up Moves: 15
