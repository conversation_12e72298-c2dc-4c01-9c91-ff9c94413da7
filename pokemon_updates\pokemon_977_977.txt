// POKEMON_977 (#977) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_977] =
    {
        .baseHP = 150,
        .baseAttack = 100,
        .baseDefense = 115,
        .baseSpAttack = 65,
        .baseSpDefense = 65,
        .baseSpeed = 35,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 25,
        .expYield = 265,
        .evYield_HP = 3,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 40,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_2,
        .eggGroup2 = EGG_GROUP_WATER_2,
        .ability1 = ABILITY_UNAWARE,
        .ability2 = ABILITY_OBLIVIOUS,
        .abilityHidden = ABILITY_WATERVEIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_977LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 5, MOVE_TICKLE),
    LEVEL_UP_MOVE(10, MOVE_FLAIL),
    LEVEL_UP_MOVE(15, MOVE_REST),
    LEVEL_UP_MOVE(15, MOVE_SLEEP_TALK),
    LEVEL_UP_MOVE(20, MOVE_DIVE),
    LEVEL_UP_MOVE(25, MOVE_NOBLE_ROAR),
    LEVEL_UP_MOVE(30, MOVE_SOAK),
    LEVEL_UP_MOVE(35, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(40, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(45, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(50, MOVE_ORDER_UP),
    LEVEL_UP_MOVE(55, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(60, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(65, MOVE_WAVE_CRASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 530
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_UNAWARE, ABILITY_OBLIVIOUS, ABILITY_WATERVEIL
// Level Up Moves: 17
