// POKEMON_983 (#983) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_983] =
    {
        .baseHP = 100,
        .baseAttack = 135,
        .baseDefense = 120,
        .baseSpAttack = 60,
        .baseSpDefense = 85,
        .baseSpeed = 50,
        .type1 = TYPE_DARK,
        .type2 = TYPE_STEEL,
        .catchRate = 25,
        .expYield = 275,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_HUMANSHAPE,
        .eggGroup2 = EGG_GROUP_HUMANSHAPE,
        .ability1 = ABILITY_DEFIANT,
        .ability2 = ABILITY_SUPREMEOVERLORD,
        .abilityHidden = ABILITY_PRESSURE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_983LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_KOWTOW_CLEAVE),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 1, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE( 1, MOVE_METAL_BURST),
    LEVEL_UP_MOVE(15, MOVE_TORMENT),
    LEVEL_UP_MOVE(20, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(25, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(30, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(35, MOVE_SLASH),
    LEVEL_UP_MOVE(40, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(45, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(50, MOVE_RETALIATE),
    LEVEL_UP_MOVE(57, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(64, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(71, MOVE_GUILLOTINE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 550
// Types: TYPE_DARK / TYPE_STEEL
// Abilities: ABILITY_DEFIANT, ABILITY_SUPREMEOVERLORD, ABILITY_PRESSURE
// Level Up Moves: 17
