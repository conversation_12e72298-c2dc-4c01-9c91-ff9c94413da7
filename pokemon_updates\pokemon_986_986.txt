// POKEMON_986 (#986) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_986] =
    {
        .baseHP = 111,
        .baseAttack = 127,
        .baseDefense = 99,
        .baseSpAttack = 79,
        .baseSpDefense = 99,
        .baseSpeed = 55,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_DARK,
        .catchRate = 50,
        .expYield = 285,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_PROTOSYNTHESIS,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_986LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 7, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE(14, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(21, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(28, MOVE_CLEAR_SMOG),
    LEVEL_UP_MOVE(35, MOVE_PAYBACK),
    LEVEL_UP_MOVE(42, MOVE_THRASH),
    LEVEL_UP_MOVE(49, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(56, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(63, MOVE_SPORE),
    LEVEL_UP_MOVE(70, MOVE_INGRAIN),
    LEVEL_UP_MOVE(77, MOVE_RAGE_POWDER),
    LEVEL_UP_MOVE(91, MOVE_SOLAR_BEAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_GRASS / TYPE_DARK
// Abilities: ABILITY_PROTOSYNTHESIS, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
