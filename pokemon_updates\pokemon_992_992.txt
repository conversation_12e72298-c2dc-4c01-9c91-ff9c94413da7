// POKEMON_992 (#992) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_992] =
    {
        .baseHP = 154,
        .baseAttack = 140,
        .baseDefense = 108,
        .baseSpAttack = 50,
        .baseSpDefense = 68,
        .baseSpeed = 50,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 50,
        .expYield = 285,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_QUARKDRIVE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_992LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE( 1, MOVE_ARM_THRUST),
    LEVEL_UP_MOVE( 7, MOVE_FAKE_OUT),
    LEVEL_UP_MOVE(14, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(21, MOVE_THUNDER_PUNCH),
    LEVEL_UP_MOVE(28, MOVE_SLAM),
    LEVEL_UP_MOVE(35, MOVE_FORCE_PALM),
    LEVEL_UP_MOVE(42, MOVE_SEISMIC_TOSS),
    LEVEL_UP_MOVE(49, MOVE_CHARGE),
    LEVEL_UP_MOVE(56, MOVE_WILD_CHARGE),
    LEVEL_UP_MOVE(63, MOVE_CLOSE_COMBAT),
    LEVEL_UP_MOVE(70, MOVE_DETECT),
    LEVEL_UP_MOVE(77, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(84, MOVE_BELLY_DRUM),
    LEVEL_UP_MOVE(91, MOVE_FOCUS_PUNCH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_FIGHTING / TYPE_ELECTRIC
// Abilities: ABILITY_QUARKDRIVE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 17
