// POKEMON_993 (#993) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_993] =
    {
        .baseHP = 94,
        .baseAttack = 80,
        .baseDefense = 86,
        .baseSpAttack = 122,
        .baseSpDefense = 80,
        .baseSpeed = 108,
        .type1 = TYPE_DARK,
        .type2 = TYPE_FLYING,
        .catchRate = 30,
        .expYield = 285,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 3,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_QUARKDRIVE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_993LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE( 1, MOVE_TRI_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_AIR_CUTTER),
    LEVEL_UP_MOVE( 1, MOVE_WORK_UP),
    LEVEL_UP_MOVE( 1, MOVE_ELECTRIC_TERRAIN),
    LEVEL_UP_MOVE( 7, MOVE_ROAR),
    LEVEL_UP_MOVE(14, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(21, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(28, MOVE_SNARL),
    LEVEL_UP_MOVE(35, MOVE_CRUNCH),
    LEVEL_UP_MOVE(42, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(56, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(63, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(70, MOVE_DARK_PULSE),
    LEVEL_UP_MOVE(77, MOVE_OUTRAGE),
    LEVEL_UP_MOVE(84, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(91, MOVE_HYPER_BEAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_DARK / TYPE_FLYING
// Abilities: ABILITY_QUARKDRIVE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 17
