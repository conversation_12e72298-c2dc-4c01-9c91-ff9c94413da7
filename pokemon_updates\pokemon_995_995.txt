// POKEMON_995 (#995) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_995] =
    {
        .baseHP = 100,
        .baseAttack = 134,
        .baseDefense = 110,
        .baseSpAttack = 70,
        .baseSpDefense = 84,
        .baseSpeed = 72,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 30,
        .expYield = 285,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_QUARKDRIVE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_995LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE( 1, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_FANG),
    LEVEL_UP_MOVE( 1, MOVE_ICE_FANG),
    LEVEL_UP_MOVE( 1, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE( 7, MOVE_SCREECH),
    LEVEL_UP_MOVE(21, MOVE_ROCK_TOMB),
    LEVEL_UP_MOVE(28, MOVE_BITE),
    LEVEL_UP_MOVE(35, MOVE_CHARGE),
    LEVEL_UP_MOVE(42, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(49, MOVE_SANDSTORM),
    LEVEL_UP_MOVE(56, MOVE_WILD_CHARGE),
    LEVEL_UP_MOVE(63, MOVE_PIN_MISSILE),
    LEVEL_UP_MOVE(70, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(77, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(84, MOVE_STONE_EDGE),
    LEVEL_UP_MOVE(91, MOVE_GIGA_IMPACT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_ROCK / TYPE_ELECTRIC
// Abilities: ABILITY_QUARKDRIVE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 17
