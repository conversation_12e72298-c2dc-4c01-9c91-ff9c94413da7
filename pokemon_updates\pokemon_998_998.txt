// POKEMON_998 (#998) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_998] =
    {
        .baseHP = 115,
        .baseAttack = 145,
        .baseDefense = 92,
        .baseSpAttack = 75,
        .baseSpDefense = 86,
        .baseSpeed = 87,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_ICE,
        .catchRate = 10,
        .expYield = 300,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 40,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_DRAGON,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_THERMALEXCHANGE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_ICEBODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spokemon_998LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_GLAIVE_RUSH),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_ICE_SHARD),
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE( 1, MOVE_BREAKING_SWIPE),
    LEVEL_UP_MOVE( 1, MOVE_SNOWSCAPE),
    LEVEL_UP_MOVE( 6, MOVE_ICY_WIND),
    LEVEL_UP_MOVE(12, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(18, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(24, MOVE_BITE),
    LEVEL_UP_MOVE(29, MOVE_ICE_FANG),
    LEVEL_UP_MOVE(35, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(42, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(48, MOVE_ICE_BEAM),
    LEVEL_UP_MOVE(55, MOVE_CRUNCH),
    LEVEL_UP_MOVE(62, MOVE_ICICLE_CRASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_DRAGON / TYPE_ICE
// Abilities: ABILITY_THERMALEXCHANGE, ABILITY_NONE, ABILITY_ICEBODY
// Level Up Moves: 17
