// POLIWAG (#060) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POLIWAG] =
    {
        .baseHP = 40,
        .baseAttack = 50,
        .baseDefense = 40,
        .baseSpAttack = 40,
        .baseSpDefense = 40,
        .baseSpeed = 90,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 255,
        .expYield = 60,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_WATER_1,
        .ability1 = ABILITY_WATERABSORB,
        .ability2 = ABILITY_DAMP,
        .abilityHidden = ABILITY_SWIFTSWIM,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spoliwagLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WATER_SPORT),
    LEVEL_UP_MOVE( 5, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 6, MOVE_POUND),
    LEVEL_UP_MOVE( 8, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE(11, MOVE_BUBBLE),
    LEVEL_UP_MOVE(15, MOVE_DOUBLE_SLAP),
    LEVEL_UP_MOVE(18, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(21, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(25, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(28, MOVE_MUD_SHOT),
    LEVEL_UP_MOVE(31, MOVE_BELLY_DRUM),
    LEVEL_UP_MOVE(35, MOVE_WAKE_UP_SLAP),
    LEVEL_UP_MOVE(36, MOVE_EARTH_POWER),
    LEVEL_UP_MOVE(38, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(41, MOVE_MUD_BOMB),
    LEVEL_UP_MOVE(54, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 300
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_WATERABSORB, ABILITY_DAMP, ABILITY_SWIFTSWIM
// Level Up Moves: 16
