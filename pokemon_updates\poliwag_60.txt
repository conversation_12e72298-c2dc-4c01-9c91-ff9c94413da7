// POLIWAG (#060) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POLIWAG] =
    {
        .baseHP = 40,
        .baseAttack = 50,
        .baseDefense = 40,
        .baseSpAttack = 40,
        .baseSpDefense = 40,
        .baseSpeed = 90,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 255,
        .expYield = 60,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_WATER_1,
        .ability1 = ABILITY_WATERABSORB,
        .ability2 = ABILITY_DAMP,
        .abilityHidden = ABILITY_SWIFTSWIM,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spoliwagLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE( 6, MOVE_POUND),
    LEVEL_UP_MOVE(12, MOVE_MUD_SHOT),
    LEVEL_UP_MOVE(18, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(24, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(30, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(36, MOVE_EARTH_POWER),
    LEVEL_UP_MOVE(42, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(48, MOVE_BELLY_DRUM),
    LEVEL_UP_MOVE(54, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 300
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_WATERABSORB, ABILITY_DAMP, ABILITY_SWIFTSWIM
// Level Up Moves: 11
