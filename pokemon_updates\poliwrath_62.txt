// POLIWRATH (#062) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POLIWRATH] =
    {
        .baseHP = 90,
        .baseAttack = 95,
        .baseDefense = 95,
        .baseSpAttack = 70,
        .baseSpDefense = 90,
        .baseSpeed = 70,
        .type1 = TYPE_WATER,
        .type2 = TYPE_FIGHTING,
        .catchRate = 45,
        .expYield = 255,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 3,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_KINGS_ROCK,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_WATER_1,
        .ability1 = ABILITY_WATERABSORB,
        .ability2 = ABILITY_DAMP,
        .hiddenAbility = ABILITY_SWIFTSWIM,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPoliwrathLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_DYNAMIC_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE( 1, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE( 1, MOVE_HYPNOSIS),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 510
// Types: TYPE_WATER / TYPE_FIGHTING
// Abilities: ABILITY_WATERABSORB, ABILITY_DAMP, ABILITY_SWIFTSWIM
// Level Up Moves: 4
