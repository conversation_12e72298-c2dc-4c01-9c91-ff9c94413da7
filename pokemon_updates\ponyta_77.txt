// PONYTA (#077) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_PONYTA] =
    {
        .baseHP = 50,
        .baseAttack = 85,
        .baseDefense = 55,
        .baseSpAttack = 65,
        .baseSpDefense = 65,
        .baseSpeed = 90,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 190,
        .expYield = 82,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SHUCA_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_RUNAWAY,
        .ability2 = ABILITY_FLASHFIRE,
        .hiddenAbility = ABILITY_FLAMEBODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPonytaLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 5, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE(10, MOVE_EMBER),
    LEVEL_UP_MOVE(15, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE(20, MOVE_AGILITY),
    LEVEL_UP_MOVE(25, MOVE_FLAME_WHEEL),
    LEVEL_UP_MOVE(30, MOVE_STOMP),
    LEVEL_UP_MOVE(35, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE(41, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(45, MOVE_INFERNO),
    LEVEL_UP_MOVE(50, MOVE_FIRE_BLAST),
    LEVEL_UP_MOVE(55, MOVE_FLARE_BLITZ),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 410
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_RUNAWAY, ABILITY_FLASHFIRE, ABILITY_FLAMEBODY
// Level Up Moves: 13
