// POOCHYENA (#261) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_POOCHYENA] =
    {
        .baseHP = 35,
        .baseAttack = 55,
        .baseDefense = 35,
        .baseSpAttack = 30,
        .baseSpDefense = 30,
        .baseSpeed = 35,
        .type1 = TYPE_DARK,
        .type2 = TYPE_DARK,
        .catchRate = 255,
        .expYield = 56,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_PECHA_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_RUNAWAY,
        .ability2 = ABILITY_QUICKFEET,
        .hiddenAbility = ABILITY_RATTLED,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPoochyenaLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 4, MOVE_HOWL),
    LEVEL_UP_MOVE( 7, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE(10, MOVE_BITE),
    LEVEL_UP_MOVE(13, MOVE_LEER),
    LEVEL_UP_MOVE(16, MOVE_ROAR),
    LEVEL_UP_MOVE(19, MOVE_SWAGGER),
    LEVEL_UP_MOVE(22, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(25, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(28, MOVE_TAUNT),
    LEVEL_UP_MOVE(31, MOVE_CRUNCH),
    LEVEL_UP_MOVE(34, MOVE_YAWN),
    LEVEL_UP_MOVE(36, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(40, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(44, MOVE_PLAY_ROUGH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 220
// Types: TYPE_DARK / TYPE_DARK
// Abilities: ABILITY_RUNAWAY, ABILITY_QUICKFEET, ABILITY_RATTLED
// Level Up Moves: 15
