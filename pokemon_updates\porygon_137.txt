// PORYGON (#137) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_PORYGON] =
    {
        .baseHP = 65,
        .baseAttack = 60,
        .baseDefense = 70,
        .baseSpAttack = 85,
        .baseSpDefense = 75,
        .baseSpeed = 40,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 45,
        .expYield = 79,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_TRACE,
        .ability2 = ABILITY_DOWNLOAD,
        .hiddenAbility = ABILITY_ANALYTIC,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPorygonLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_CONVERSION),
    LEVEL_UP_MOVE( 5, MOVE_RECYCLE),
    LEVEL_UP_MOVE(10, MOVE_MAGNET_RISE),
    LEVEL_UP_MOVE(15, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE(20, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(25, MOVE_CONVERSION_2),
    LEVEL_UP_MOVE(30, MOVE_AGILITY),
    LEVEL_UP_MOVE(35, MOVE_RECOVER),
    LEVEL_UP_MOVE(40, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(45, MOVE_TRI_ATTACK),
    LEVEL_UP_MOVE(50, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(55, MOVE_LOCK_ON),
    LEVEL_UP_MOVE(60, MOVE_ZAP_CANNON),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 395
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_TRACE, ABILITY_DOWNLOAD, ABILITY_ANALYTIC
// Level Up Moves: 14
