// PSYDUCK (#054) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_PSYDUCK] =
    {
        .baseHP = 50,
        .baseAttack = 52,
        .baseDefense = 48,
        .baseSpAttack = 65,
        .baseSpDefense = 50,
        .baseSpeed = 55,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 190,
        .expYield = 64,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_DAMP,
        .ability2 = ABILITY_CLOUDNINE,
        .abilityHidden = ABILITY_SWIFTSWIM,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove spsyduckLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_WATER_SPORT),
    LEVEL_UP_MOVE( 4, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 7, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(10, MOVE_CONFUSION),
    LEVEL_UP_MOVE(13, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(16, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(19, MOVE_DISABLE),
    LEVEL_UP_MOVE(22, MOVE_SCREECH),
    LEVEL_UP_MOVE(25, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(28, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(31, MOVE_SOAK),
    LEVEL_UP_MOVE(34, MOVE_PSYCH_UP),
    LEVEL_UP_MOVE(37, MOVE_AMNESIA),
    LEVEL_UP_MOVE(40, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(43, MOVE_WONDER_ROOM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 320
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_DAMP, ABILITY_CLOUDNINE, ABILITY_SWIFTSWIM
// Level Up Moves: 16
