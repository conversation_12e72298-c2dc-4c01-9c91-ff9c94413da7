// PSYDUCK (#054) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_PSYDUCK] =
    {
        .baseHP = 50,
        .baseAttack = 52,
        .baseDefense = 48,
        .baseSpAttack = 65,
        .baseSpDefense = 50,
        .baseSpeed = 55,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 190,
        .expYield = 64,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_DAMP,
        .ability2 = ABILITY_CLOUDNINE,
        .hiddenAbility = ABILITY_SWIFTSWIM,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPsyduckLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 3, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 6, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 9, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(12, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(15, MOVE_DISABLE),
    LEVEL_UP_MOVE(18, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(21, MOVE_SCREECH),
    LEVEL_UP_MOVE(24, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(27, MOVE_SOAK),
    LEVEL_UP_MOVE(30, MOVE_PSYCH_UP),
    LEVEL_UP_MOVE(34, MOVE_AMNESIA),
    LEVEL_UP_MOVE(39, MOVE_WONDER_ROOM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 320
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_DAMP, ABILITY_CLOUDNINE, ABILITY_SWIFTSWIM
// Level Up Moves: 14
