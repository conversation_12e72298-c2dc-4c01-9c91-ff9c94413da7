// RAICHU (#026) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_RAICHU] =
    {
        .baseHP = 60,
        .baseAttack = 90,
        .baseDefense = 55,
        .baseSpAttack = 90,
        .baseSpDefense = 80,
        .baseSpeed = 110,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 75,
        .expYield = 243,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 3,
        .item1 = ITEM_ORAN_BERRY,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 10,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_FAIRY,
        .ability1 = ABILITY_STATIC,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_LIGHTNINGROD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sRaichuLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_THUNDER_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER),
    LEVEL_UP_MOVE( 1, MOVE_AGILITY),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE( 1, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_KISS),
    LEVEL_UP_MOVE( 1, MOVE_CHARM),
    LEVEL_UP_MOVE( 1, MOVE_SPARK),
    LEVEL_UP_MOVE( 1, MOVE_IRON_TAIL),
    LEVEL_UP_MOVE( 1, MOVE_FEINT),
    LEVEL_UP_MOVE( 1, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE( 1, MOVE_DISCHARGE),
    LEVEL_UP_MOVE( 1, MOVE_ELECTRO_BALL),
    LEVEL_UP_MOVE( 1, MOVE_PLAY_NICE),
    LEVEL_UP_MOVE( 1, MOVE_NUZZLE),
    LEVEL_UP_MOVE( 5, MOVE_THUNDERBOLT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 485
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_STATIC, ABILITY_NONE, ABILITY_LIGHTNINGROD
// Level Up Moves: 21
