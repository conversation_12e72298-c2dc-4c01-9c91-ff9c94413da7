// RAPIDASH (#078) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_RAPIDASH] =
    {
        .baseHP = 65,
        .baseAttack = 100,
        .baseDefense = 70,
        .baseSpAttack = 80,
        .baseSpDefense = 80,
        .baseSpeed = 105,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 60,
        .expYield = 175,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SHUCA_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_RUNAWAY,
        .ability2 = ABILITY_FLASHFIRE,
        .abilityHidden = ABILITY_FLAMEBODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove srapidashLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_MEGAHORN),
    LEVEL_UP_MOVE( 1, MOVE_POISON_JAB),
    LEVEL_UP_MOVE(13, MOVE_FLAME_WHEEL),
    LEVEL_UP_MOVE(17, MOVE_STOMP),
    LEVEL_UP_MOVE(21, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE(25, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE(29, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(33, MOVE_INFERNO),
    LEVEL_UP_MOVE(37, MOVE_AGILITY),
    LEVEL_UP_MOVE(41, MOVE_FIRE_BLAST),
    LEVEL_UP_MOVE(45, MOVE_BOUNCE),
    LEVEL_UP_MOVE(49, MOVE_FLARE_BLITZ),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_RUNAWAY, ABILITY_FLASHFIRE, ABILITY_FLAMEBODY
// Level Up Moves: 18
