// RATICATE (#020) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_RATICATE] =
    {
        .baseHP = 55,
        .baseAttack = 81,
        .baseDefense = 60,
        .baseSpAttack = 50,
        .baseSpDefense = 70,
        .baseSpeed = 97,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 127,
        .expYield = 145,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_ORAN_BERRY,
        .item2 = ITEM_SITRUS_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_RUNAWAY,
        .ability2 = ABILITY_GUTS,
        .hiddenAbility = ABILITY_HUSTLE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sRaticateLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE( 1, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(10, MOVE_BITE),
    LEVEL_UP_MOVE(13, MOVE_PURSUIT),
    LEVEL_UP_MOVE(16, MOVE_HYPER_FANG),
    LEVEL_UP_MOVE(19, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(24, MOVE_CRUNCH),
    LEVEL_UP_MOVE(29, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(34, MOVE_SUPER_FANG),
    LEVEL_UP_MOVE(39, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(44, MOVE_ENDEAVOR),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 413
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_RUNAWAY, ABILITY_GUTS, ABILITY_HUSTLE
// Level Up Moves: 15
