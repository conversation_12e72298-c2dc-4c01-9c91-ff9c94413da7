// RATTATA (#019) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_RATTATA] =
    {
        .baseHP = 30,
        .baseAttack = 56,
        .baseDefense = 35,
        .baseSpAttack = 25,
        .baseSpDefense = 35,
        .baseSpeed = 72,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 255,
        .expYield = 51,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_CHILAN_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_RUNAWAY,
        .ability2 = ABILITY_GUTS,
        .hiddenAbility = ABILITY_HUSTLE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sRattataLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 4, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 7, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(10, MOVE_BITE),
    LEVEL_UP_MOVE(13, MOVE_PURSUIT),
    LEVEL_UP_MOVE(16, MOVE_HYPER_FANG),
    LEVEL_UP_MOVE(19, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(22, MOVE_CRUNCH),
    LEVEL_UP_MOVE(25, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(28, MOVE_SUPER_FANG),
    LEVEL_UP_MOVE(31, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(34, MOVE_ENDEAVOR),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 253
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_RUNAWAY, ABILITY_GUTS, ABILITY_HUSTLE
// Level Up Moves: 13
