// REGICE (#378) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_REGICE] =
    {
        .baseHP = 80,
        .baseAttack = 50,
        .baseDefense = 100,
        .baseSpAttack = 100,
        .baseSpDefense = 200,
        .baseSpeed = 50,
        .type1 = TYPE_ICE,
        .type2 = TYPE_ICE,
        .catchRate = 3,
        .expYield = 290,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 3,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 80,
        .friendship = 35,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_CLEARBODY,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_ICEBODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sregiceLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_STOMP),
    LEVEL_UP_MOVE( 1, MOVE_EXPLOSION),
    LEVEL_UP_MOVE( 1, MOVE_ICY_WIND),
    LEVEL_UP_MOVE( 1, MOVE_CHARGE_BEAM),
    LEVEL_UP_MOVE( 1, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(25, MOVE_CURSE),
    LEVEL_UP_MOVE(31, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(37, MOVE_AMNESIA),
    LEVEL_UP_MOVE(43, MOVE_ICE_BEAM),
    LEVEL_UP_MOVE(49, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE(55, MOVE_ZAP_CANNON),
    LEVEL_UP_MOVE(55, MOVE_LOCK_ON),
    LEVEL_UP_MOVE(61, MOVE_SUPERPOWER),
    LEVEL_UP_MOVE(67, MOVE_HYPER_BEAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 580
// Types: TYPE_ICE / TYPE_ICE
// Abilities: ABILITY_CLEARBODY, ABILITY_NONE, ABILITY_ICEBODY
// Level Up Moves: 14
