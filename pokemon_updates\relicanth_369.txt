// RELICANTH (#369) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_RELICANTH] =
    {
        .baseHP = 100,
        .baseAttack = 90,
        .baseDefense = 130,
        .baseSpAttack = 45,
        .baseSpDefense = 65,
        .baseSpeed = 55,
        .type1 = TYPE_WATER,
        .type2 = TYPE_ROCK,
        .catchRate = 25,
        .expYield = 170,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_DEEP_SEA_SCALE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 40,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_WATER_2,
        .ability1 = ABILITY_SWIFTSWIM,
        .ability2 = ABILITY_ROCKHEAD,
        .abilityHidden = ABILITY_STURDY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove srelicanthLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_FLAIL),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SPORT),
    LEVEL_UP_MOVE( 1, MOVE_HEAD_SMASH),
    LEVEL_UP_MOVE(15, MOVE_ROCK_TOMB),
    LEVEL_UP_MOVE(21, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(26, MOVE_DIVE),
    LEVEL_UP_MOVE(31, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(35, MOVE_YAWN),
    LEVEL_UP_MOVE(41, MOVE_REST),
    LEVEL_UP_MOVE(46, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(50, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 485
// Types: TYPE_WATER / TYPE_ROCK
// Abilities: ABILITY_SWIFTSWIM, ABILITY_ROCKHEAD, ABILITY_STURDY
// Level Up Moves: 14
