// RHYDON (#112) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_RHYDON] =
    {
        .baseHP = 105,
        .baseAttack = 130,
        .baseDefense = 120,
        .baseSpAttack = 45,
        .baseSpDefense = 45,
        .baseSpeed = 40,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_ROCK,
        .catchRate = 60,
        .expYield = 170,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_LIGHTNINGROD,
        .ability2 = ABILITY_ROCKHEAD,
        .abilityHidden = ABILITY_RECKLESS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove srhydonLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE( 1, MOVE_HORN_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_HORN_DRILL),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(13, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE(17, MOVE_STOMP),
    LEVEL_UP_MOVE(21, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(25, MOVE_CHIP_AWAY),
    LEVEL_UP_MOVE(29, MOVE_ROCK_BLAST),
    LEVEL_UP_MOVE(33, MOVE_DRILL_RUN),
    LEVEL_UP_MOVE(37, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(41, MOVE_STONE_EDGE),
    LEVEL_UP_MOVE(48, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(55, MOVE_MEGAHORN),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 485
// Types: TYPE_GROUND / TYPE_ROCK
// Abilities: ABILITY_LIGHTNINGROD, ABILITY_ROCKHEAD, ABILITY_RECKLESS
// Level Up Moves: 17
