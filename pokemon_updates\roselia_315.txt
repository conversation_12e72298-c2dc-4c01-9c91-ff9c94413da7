// ROSELIA (#315) - GE<PERSON>RA<PERSON><PERSON> IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_ROSELIA] =
    {
        .baseHP = 50,
        .baseAttack = 60,
        .baseDefense = 45,
        .baseSpAttack = 100,
        .baseSpDefense = 80,
        .baseSpeed = 65,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_POISON,
        .catchRate = 150,
        .expYield = 140,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_POISON_BARB,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FAIRY,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_NATURALCURE,
        .ability2 = ABILITY_POISONPOINT,
        .abilityHidden = ABILITY_LEAFGUARD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sroseliaLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 4, MOVE_GROWTH),
    LEVEL_UP_MOVE( 7, MOVE_POISON_STING),
    LEVEL_UP_MOVE(10, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE(13, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(16, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(19, MOVE_MAGICAL_LEAF),
    LEVEL_UP_MOVE(22, MOVE_GRASS_WHISTLE),
    LEVEL_UP_MOVE(25, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(28, MOVE_TOXIC_SPIKES),
    LEVEL_UP_MOVE(31, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(34, MOVE_INGRAIN),
    LEVEL_UP_MOVE(37, MOVE_PETAL_BLIZZARD),
    LEVEL_UP_MOVE(40, MOVE_TOXIC),
    LEVEL_UP_MOVE(43, MOVE_AROMATHERAPY),
    LEVEL_UP_MOVE(46, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(50, MOVE_PETAL_DANCE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 400
// Types: TYPE_GRASS / TYPE_POISON
// Abilities: ABILITY_NATURALCURE, ABILITY_POISONPOINT, ABILITY_LEAFGUARD
// Level Up Moves: 17
