// SALAMENCE (#373) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_SALAMENCE] =
    {
        .baseHP = 95,
        .baseAttack = 135,
        .baseDefense = 80,
        .baseSpAttack = 110,
        .baseSpDefense = 80,
        .baseSpeed = 100,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 300,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_DRAGON_FANG,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 40,
        .friendship = 35,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_DRAGON,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_INTIMIDATE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_MOXIE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sSalamenceLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_FLY),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_PROTECT),
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE( 1, MOVE_ROOST),
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE( 1, MOVE_DUAL_WINGBEAT),
    LEVEL_UP_MOVE(15, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(20, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(25, MOVE_CRUNCH),
    LEVEL_UP_MOVE(33, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(39, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(46, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(55, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(73, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_DRAGON / TYPE_FLYING
// Abilities: ABILITY_INTIMIDATE, ABILITY_NONE, ABILITY_MOXIE
// Level Up Moves: 17
