// SANDSLASH (#028) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_SANDSLASH] =
    {
        .baseHP = 75,
        .baseAttack = 100,
        .baseDefense = 110,
        .baseSpAttack = 45,
        .baseSpDefense = 55,
        .baseSpeed = 65,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_GROUND,
        .catchRate = 90,
        .expYield = 158,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 2,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SOFT_SAND,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_SANDVEIL,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_SANDRUSH,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove ssandslashLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_POISON_STING),
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_CRUSH_CLAW),
    LEVEL_UP_MOVE( 9, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(12, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE(15, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE(18, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(21, MOVE_SWIFT),
    LEVEL_UP_MOVE(26, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(31, MOVE_SAND_TOMB),
    LEVEL_UP_MOVE(36, MOVE_SLASH),
    LEVEL_UP_MOVE(41, MOVE_DIG),
    LEVEL_UP_MOVE(46, MOVE_GYRO_BALL),
    LEVEL_UP_MOVE(51, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(56, MOVE_SANDSTORM),
    LEVEL_UP_MOVE(61, MOVE_EARTHQUAKE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 450
// Types: TYPE_GROUND / TYPE_GROUND
// Abilities: ABILITY_SANDVEIL, ABILITY_NONE, ABILITY_SANDRUSH
// Level Up Moves: 18
