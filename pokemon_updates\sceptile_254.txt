// SCEPTILE (#254) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_SCEPTILE] =
    {
        .baseHP = 70,
        .baseAttack = 85,
        .baseDefense = 65,
        .baseSpAttack = 105,
        .baseSpDefense = 85,
        .baseSpeed = 120,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 45,
        .expYield = 64, // Placeholder - needs manual adjustment
        .evYield_HP = 0, // Placeholder - needs manual adjustment
        .evYield_Attack = 0, // Placeholder - needs manual adjustment
        .evYield_Defense = 0, // Placeholder - needs manual adjustment
        .evYield_SpAttack = 0, // Placeholder - needs manual adjustment
        .evYield_SpDefense = 0, // Placeholder - needs manual adjustment
        .evYield_Speed = 0, // Placeholder - needs manual adjustment
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_OVERGROW,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_UNBURDEN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove ssceptileLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_DUAL_CHOP),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 1, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE( 1, MOVE_LEAF_STORM),
    LEVEL_UP_MOVE( 1, MOVE_LEAFAGE),
    LEVEL_UP_MOVE(13, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(18, MOVE_PURSUIT),
    LEVEL_UP_MOVE(20, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(23, MOVE_LEAF_BLADE),
    LEVEL_UP_MOVE(28, MOVE_AGILITY),
    LEVEL_UP_MOVE(33, MOVE_SLAM),
    LEVEL_UP_MOVE(39, MOVE_DETECT),
    LEVEL_UP_MOVE(45, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(51, MOVE_FALSE_SWIPE),
    LEVEL_UP_MOVE(57, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(69, MOVE_SCREECH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 530
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_OVERGROW, ABILITY_NONE, ABILITY_UNBURDEN
// Level Up Moves: 20
