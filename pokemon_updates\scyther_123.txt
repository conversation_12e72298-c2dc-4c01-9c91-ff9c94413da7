// SCYTHER (#123) - GE<PERSON>RATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_SCYTHER] =
    {
        .baseHP = 70,
        .baseAttack = 110,
        .baseDefense = 80,
        .baseSpAttack = 55,
        .baseSpDefense = 80,
        .baseSpeed = 105,
        .type1 = TYPE_BUG,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 100,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_SWARM,
        .ability2 = ABILITY_TECHNICIAN,
        .hiddenAbility = ABILITY_STEADFAST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sScytherLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 4, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 8, MOVE_FALSE_SWIPE),
    LEVEL_UP_MOVE(12, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(16, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(20, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE(24, MOVE_SLASH),
    LEVEL_UP_MOVE(28, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(32, MOVE_AGILITY),
    LEVEL_UP_MOVE(36, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(40, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(44, MOVE_SWORDS_DANCE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_BUG / TYPE_FLYING
// Abilities: ABILITY_SWARM, ABILITY_TECHNICIAN, ABILITY_STEADFAST
// Level Up Moves: 13
