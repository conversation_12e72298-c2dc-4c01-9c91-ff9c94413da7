// SCYTHER (#123) - GE<PERSON>RATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_SCYTHER] =
    {
        .baseHP = 70,
        .baseAttack = 110,
        .baseDefense = 80,
        .baseSpAttack = 55,
        .baseSpDefense = 80,
        .baseSpeed = 105,
        .type1 = TYPE_BUG,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 100,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_SWARM,
        .ability2 = ABILITY_TECHNICIAN,
        .abilityHidden = ABILITY_STEADFAST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sscytherLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_VACUUM_WAVE),
    LEVEL_UP_MOVE( 5, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE( 9, MOVE_PURSUIT),
    LEVEL_UP_MOVE(13, MOVE_FALSE_SWIPE),
    LEVEL_UP_MOVE(17, MOVE_AGILITY),
    LEVEL_UP_MOVE(21, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(25, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE(29, MOVE_SLASH),
    LEVEL_UP_MOVE(33, MOVE_RAZOR_WIND),
    LEVEL_UP_MOVE(37, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(41, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(45, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(49, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE(50, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(57, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(61, MOVE_FEINT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_BUG / TYPE_FLYING
// Abilities: ABILITY_SWARM, ABILITY_TECHNICIAN, ABILITY_STEADFAST
// Level Up Moves: 18
