// SEADRA (#117) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_SEADRA] =
    {
        .baseHP = 55,
        .baseAttack = 65,
        .baseDefense = 95,
        .baseSpAttack = 95,
        .baseSpDefense = 45,
        .baseSpeed = 85,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 75,
        .expYield = 154,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_DRAGON_SCALE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_POISONPOINT,
        .ability2 = ABILITY_SNIPER,
        .hiddenAbility = ABILITY_DAMP,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sSeadraLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_SMOKESCREEN),
    LEVEL_UP_MOVE( 1, MOVE_TWISTER),
    LEVEL_UP_MOVE(15, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(20, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(25, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(30, MOVE_AGILITY),
    LEVEL_UP_MOVE(37, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(44, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(51, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(58, MOVE_DRAGON_DANCE),
    LEVEL_UP_MOVE(65, MOVE_RAIN_DANCE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 440
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_POISONPOINT, ABILITY_SNIPER, ABILITY_DAMP
// Level Up Moves: 13
