// SEAKING (#119) - <PERSON><PERSON><PERSON><PERSON><PERSON> IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_SEAKING] =
    {
        .baseHP = 80,
        .baseAttack = 92,
        .baseDefense = 65,
        .baseSpAttack = 65,
        .baseSpDefense = 80,
        .baseSpeed = 68,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 60,
        .expYield = 158,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_MYSTIC_WATER,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_2,
        .eggGroup2 = EGG_GROUP_WATER_2,
        .ability1 = ABILITY_SWIFTSWIM,
        .ability2 = ABILITY_WATERVEIL,
        .abilityHidden = ABILITY_LIGHTNINGROD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sseakingLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_MEGAHORN),
    LEVEL_UP_MOVE( 1, MOVE_WATER_SPORT),
    LEVEL_UP_MOVE( 1, MOVE_POISON_JAB),
    LEVEL_UP_MOVE( 8, MOVE_HORN_ATTACK),
    LEVEL_UP_MOVE(13, MOVE_FLAIL),
    LEVEL_UP_MOVE(16, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(21, MOVE_AQUA_RING),
    LEVEL_UP_MOVE(24, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(29, MOVE_AGILITY),
    LEVEL_UP_MOVE(32, MOVE_WATERFALL),
    LEVEL_UP_MOVE(40, MOVE_HORN_DRILL),
    LEVEL_UP_MOVE(46, MOVE_SOAK),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 450
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_SWIFTSWIM, ABILITY_WATERVEIL, ABILITY_LIGHTNINGROD
// Level Up Moves: 15
