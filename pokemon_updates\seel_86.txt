// SEEL (#086) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_SEEL] =
    {
        .baseHP = 65,
        .baseAttack = 45,
        .baseDefense = 55,
        .baseSpAttack = 45,
        .baseSpDefense = 70,
        .baseSpeed = 45,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 190,
        .expYield = 65,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_ASPEAR_BERRY,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_THICKFAT,
        .ability2 = ABILITY_HYDRATION,
        .hiddenAbility = ABILITY_ICEBODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sSeelLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_HEADBUTT),
    LEVEL_UP_MOVE( 3, MOVE_GROWL),
    LEVEL_UP_MOVE( 7, MOVE_CHARM),
    LEVEL_UP_MOVE(11, MOVE_ICY_WIND),
    LEVEL_UP_MOVE(13, MOVE_ENCORE),
    LEVEL_UP_MOVE(17, MOVE_ICE_SHARD),
    LEVEL_UP_MOVE(21, MOVE_REST),
    LEVEL_UP_MOVE(23, MOVE_AQUA_RING),
    LEVEL_UP_MOVE(27, MOVE_AURORA_BEAM),
    LEVEL_UP_MOVE(31, MOVE_AQUA_JET),
    LEVEL_UP_MOVE(33, MOVE_BRINE),
    LEVEL_UP_MOVE(37, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(41, MOVE_DIVE),
    LEVEL_UP_MOVE(43, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(47, MOVE_ICE_BEAM),
    LEVEL_UP_MOVE(51, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(53, MOVE_SNOWSCAPE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 325
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_THICKFAT, ABILITY_HYDRATION, ABILITY_ICEBODY
// Level Up Moves: 17
