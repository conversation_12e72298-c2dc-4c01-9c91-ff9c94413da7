// SEVIPER (#336) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_SEVIPER] =
    {
        .baseHP = 73,
        .baseAttack = 100,
        .baseDefense = 60,
        .baseSpAttack = 100,
        .baseSpDefense = 60,
        .baseSpeed = 65,
        .type1 = TYPE_POISON,
        .type2 = TYPE_POISON,
        .catchRate = 90,
        .expYield = 160,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_PERSIM_BERRY,
        .item2 = ITEM_SHED_SHELL,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_FLUCTUATING,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_SHEDSKIN,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_INFILTRATOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sSeviperLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WRAP),
    LEVEL_UP_MOVE( 1, MOVE_SWAGGER),
    LEVEL_UP_MOVE( 4, MOVE_BITE),
    LEVEL_UP_MOVE( 6, MOVE_LICK),
    LEVEL_UP_MOVE( 9, MOVE_POISON_TAIL),
    LEVEL_UP_MOVE(11, MOVE_FEINT),
    LEVEL_UP_MOVE(14, MOVE_SCREECH),
    LEVEL_UP_MOVE(19, MOVE_GLARE),
    LEVEL_UP_MOVE(21, MOVE_POISON_FANG),
    LEVEL_UP_MOVE(24, MOVE_VENOSHOCK),
    LEVEL_UP_MOVE(29, MOVE_GASTRO_ACID),
    LEVEL_UP_MOVE(31, MOVE_POISON_JAB),
    LEVEL_UP_MOVE(34, MOVE_HAZE),
    LEVEL_UP_MOVE(39, MOVE_CRUNCH),
    LEVEL_UP_MOVE(41, MOVE_BELCH),
    LEVEL_UP_MOVE(44, MOVE_COIL),
    LEVEL_UP_MOVE(46, MOVE_SLUDGE_BOMB),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 458
// Types: TYPE_POISON / TYPE_POISON
// Abilities: ABILITY_SHEDSKIN, ABILITY_NONE, ABILITY_INFILTRATOR
// Level Up Moves: 17
