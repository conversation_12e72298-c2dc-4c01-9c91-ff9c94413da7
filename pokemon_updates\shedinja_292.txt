// SHEDINJA (#292) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_SHEDINJA] =
    {
        .baseHP = 1,
        .baseAttack = 90,
        .baseDefense = 45,
        .baseSpAttack = 30,
        .baseSpDefense = 30,
        .baseSpeed = 40,
        .type1 = TYPE_BUG,
        .type2 = TYPE_GHOST,
        .catchRate = 45,
        .expYield = 83,
        .evYield_HP = 2,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_ERRATIC,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_WONDERGUARD,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sshedinjaLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_DIG),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(13, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(17, MOVE_SPITE),
    LEVEL_UP_MOVE(21, MOVE_SHADOW_SNEAK),
    LEVEL_UP_MOVE(25, MOVE_MIND_READER),
    LEVEL_UP_MOVE(29, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(33, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(37, MOVE_GRUDGE),
    LEVEL_UP_MOVE(41, MOVE_HEAL_BLOCK),
    LEVEL_UP_MOVE(45, MOVE_PHANTOM_FORCE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 236
// Types: TYPE_BUG / TYPE_GHOST
// Abilities: ABILITY_WONDERGUARD, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
