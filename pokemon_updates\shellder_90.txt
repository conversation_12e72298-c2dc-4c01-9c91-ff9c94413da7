// SHELLDER (#090) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_SHELLDER] =
    {
        .baseHP = 30,
        .baseAttack = 65,
        .baseDefense = 100,
        .baseSpAttack = 45,
        .baseSpDefense = 25,
        .baseSpeed = 40,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 190,
        .expYield = 61,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_PEARL,
        .item2 = ITEM_BIG_PEARL,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_3,
        .eggGroup2 = EGG_GROUP_WATER_3,
        .ability1 = ABILITY_SHELLARMOR,
        .ability2 = ABILITY_SKILLLINK,
        .abilityHidden = ABILITY_OVERCOAT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sshellderLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 4, MOVE_WITHDRAW),
    LEVEL_UP_MOVE( 8, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(13, MOVE_ICICLE_SPEAR),
    LEVEL_UP_MOVE(16, MOVE_PROTECT),
    LEVEL_UP_MOVE(20, MOVE_LEER),
    LEVEL_UP_MOVE(25, MOVE_CLAMP),
    LEVEL_UP_MOVE(28, MOVE_ICE_SHARD),
    LEVEL_UP_MOVE(32, MOVE_RAZOR_SHELL),
    LEVEL_UP_MOVE(37, MOVE_AURORA_BEAM),
    LEVEL_UP_MOVE(40, MOVE_WHIRLPOOL),
    LEVEL_UP_MOVE(44, MOVE_BRINE),
    LEVEL_UP_MOVE(49, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(52, MOVE_ICE_BEAM),
    LEVEL_UP_MOVE(56, MOVE_SHELL_SMASH),
    LEVEL_UP_MOVE(61, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 305
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_SHELLARMOR, ABILITY_SKILLLINK, ABILITY_OVERCOAT
// Level Up Moves: 17
