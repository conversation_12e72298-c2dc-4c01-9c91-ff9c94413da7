// SHUPPET (#353) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_SHUPPET] =
    {
        .baseHP = 44,
        .baseAttack = 75,
        .baseDefense = 35,
        .baseSpAttack = 63,
        .baseSpDefense = 33,
        .baseSpeed = 45,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_GHOST,
        .catchRate = 225,
        .expYield = 59,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SPELL_TAG,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 25,
        .friendship = 35,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_INDETERMINATE,
        .eggGroup2 = EGG_GROUP_INDETERMINATE,
        .ability1 = ABILITY_INSOMNIA,
        .ability2 = ABILITY_FRISK,
        .hiddenAbility = ABILITY_CURSEDBODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sShuppetLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 4, MOVE_SCREECH),
    LEVEL_UP_MOVE( 7, MOVE_NIGHT_SHADE),
    LEVEL_UP_MOVE(10, MOVE_SPITE),
    LEVEL_UP_MOVE(16, MOVE_WILL_O_WISP),
    LEVEL_UP_MOVE(19, MOVE_SHADOW_SNEAK),
    LEVEL_UP_MOVE(22, MOVE_HEX),
    LEVEL_UP_MOVE(26, MOVE_CURSE),
    LEVEL_UP_MOVE(30, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(34, MOVE_ROLE_PLAY),
    LEVEL_UP_MOVE(38, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(42, MOVE_TRICK),
    LEVEL_UP_MOVE(48, MOVE_PHANTOM_FORCE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 295
// Types: TYPE_GHOST / TYPE_GHOST
// Abilities: ABILITY_INSOMNIA, ABILITY_FRISK, ABILITY_CURSEDBODY
// Level Up Moves: 13
