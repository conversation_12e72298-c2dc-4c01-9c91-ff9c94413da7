// SLAKING (#289) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_SLAKING] =
    {
        .baseHP = 150,
        .baseAttack = 160,
        .baseDefense = 100,
        .baseSpAttack = 95,
        .baseSpDefense = 65,
        .baseSpeed = 100,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 45,
        .expYield = 252,
        .evYield_HP = 3,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_TRUANT,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sslakingLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_SWAGGER),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_ENCORE),
    LEVEL_UP_MOVE( 1, MOVE_YAWN),
    LEVEL_UP_MOVE( 1, MOVE_SLACK_OFF),
    LEVEL_UP_MOVE( 1, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE( 1, MOVE_FLING),
    LEVEL_UP_MOVE( 1, MOVE_PUNISHMENT),
    LEVEL_UP_MOVE( 1, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(14, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE(17, MOVE_AMNESIA),
    LEVEL_UP_MOVE(23, MOVE_COVET),
    LEVEL_UP_MOVE(27, MOVE_CHIP_AWAY),
    LEVEL_UP_MOVE(27, MOVE_THROAT_CHOP),
    LEVEL_UP_MOVE(33, MOVE_COUNTER),
    LEVEL_UP_MOVE(39, MOVE_FLAIL),
    LEVEL_UP_MOVE(52, MOVE_MEGA_KICK),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 670
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_TRUANT, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 17
