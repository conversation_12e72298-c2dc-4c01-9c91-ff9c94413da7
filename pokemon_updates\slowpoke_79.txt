// SLOWPOKE (#079) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_SLOWPOKE] =
    {
        .baseHP = 90,
        .baseAttack = 65,
        .baseDefense = 65,
        .baseSpAttack = 40,
        .baseSpDefense = 40,
        .baseSpeed = 15,
        .type1 = TYPE_WATER,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 190,
        .expYield = 63,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_LAGGING_TAIL,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_WATER_1,
        .ability1 = ABILITY_OBLIVIOUS,
        .ability2 = ABILITY_OWNTEMPO,
        .hiddenAbility = ABILITY_REGENERATOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sSlowpokeLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_CURSE),
    LEVEL_UP_MOVE( 3, MOVE_GROWL),
    LEVEL_UP_MOVE( 6, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 9, MOVE_YAWN),
    LEVEL_UP_MOVE(12, MOVE_CONFUSION),
    LEVEL_UP_MOVE(15, MOVE_DISABLE),
    LEVEL_UP_MOVE(18, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(21, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(24, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(27, MOVE_AMNESIA),
    LEVEL_UP_MOVE(30, MOVE_SURF),
    LEVEL_UP_MOVE(33, MOVE_SLACK_OFF),
    LEVEL_UP_MOVE(36, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(39, MOVE_PSYCH_UP),
    LEVEL_UP_MOVE(42, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(45, MOVE_HEAL_PULSE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 315
// Types: TYPE_WATER / TYPE_PSYCHIC
// Abilities: ABILITY_OBLIVIOUS, ABILITY_OWNTEMPO, ABILITY_REGENERATOR
// Level Up Moves: 17
