// SNORLAX (#143) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_SNORLAX] =
    {
        .baseHP = 160,
        .baseAttack = 110,
        .baseDefense = 65,
        .baseSpAttack = 65,
        .baseSpDefense = 110,
        .baseSpeed = 30,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 25,
        .expYield = 189,
        .evYield_HP = 2,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_CHESTO_BERRY,
        .item2 = ITEM_LEFTOVERS,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 40,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_MONSTER,
        .ability1 = ABILITY_IMMUNITY,
        .ability2 = ABILITY_THICKFAT,
        .abilityHidden = ABILITY_GLUTTONY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove ssnorlaxLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_SCREECH),
    LEVEL_UP_MOVE( 1, MOVE_METRONOME),
    LEVEL_UP_MOVE( 1, MOVE_FLAIL),
    LEVEL_UP_MOVE( 1, MOVE_STOCKPILE),
    LEVEL_UP_MOVE( 1, MOVE_SWALLOW),
    LEVEL_UP_MOVE( 4, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 9, MOVE_AMNESIA),
    LEVEL_UP_MOVE(12, MOVE_LICK),
    LEVEL_UP_MOVE(16, MOVE_BITE),
    LEVEL_UP_MOVE(17, MOVE_CHIP_AWAY),
    LEVEL_UP_MOVE(20, MOVE_YAWN),
    LEVEL_UP_MOVE(25, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(28, MOVE_REST),
    LEVEL_UP_MOVE(28, MOVE_SNORE),
    LEVEL_UP_MOVE(33, MOVE_SLEEP_TALK),
    LEVEL_UP_MOVE(35, MOVE_GIGA_IMPACT),
    LEVEL_UP_MOVE(36, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(41, MOVE_BLOCK),
    LEVEL_UP_MOVE(44, MOVE_BELLY_DRUM),
    LEVEL_UP_MOVE(44, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE(49, MOVE_CRUNCH),
    LEVEL_UP_MOVE(50, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(57, MOVE_HIGH_HORSEPOWER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 540
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_IMMUNITY, ABILITY_THICKFAT, ABILITY_GLUTTONY
// Level Up Moves: 24
