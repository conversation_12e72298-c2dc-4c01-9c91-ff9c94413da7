// SOLROCK (#338) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_SOLROCK] =
    {
        .baseHP = 90,
        .baseAttack = 95,
        .baseDefense = 85,
        .baseSpAttack = 55,
        .baseSpDefense = 65,
        .baseSpeed = 70,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 45,
        .expYield = 161,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_STARDUST,
        .item2 = ITEM_SUN_STONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_MINERAL,
        .eggGroup2 = EGG_GROUP_MINERAL,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sSolrockLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_MORNING_SUN),
    LEVEL_UP_MOVE( 1, MOVE_FLARE_BLITZ),
    LEVEL_UP_MOVE( 5, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE(10, MOVE_ROCK_POLISH),
    LEVEL_UP_MOVE(15, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(20, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(25, MOVE_COSMIC_POWER),
    LEVEL_UP_MOVE(30, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(35, MOVE_STONE_EDGE),
    LEVEL_UP_MOVE(40, MOVE_SOLAR_BEAM),
    LEVEL_UP_MOVE(45, MOVE_WONDER_ROOM),
    LEVEL_UP_MOVE(50, MOVE_EXPLOSION),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 460
// Types: TYPE_ROCK / TYPE_PSYCHIC
// Abilities: ABILITY_LEVITATE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
