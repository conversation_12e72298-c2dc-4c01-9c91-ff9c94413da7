// SPEAROW (#021) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_SPEAROW] =
    {
        .baseHP = 40,
        .baseAttack = 60,
        .baseDefense = 30,
        .baseSpAttack = 31,
        .baseSpDefense = 31,
        .baseSpeed = 70,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 255,
        .expYield = 52,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SHARP_BEAK,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_KEENEYE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SNIPER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sSpearowLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 4, MOVE_LEER),
    LEVEL_UP_MOVE( 8, MOVE_PURSUIT),
    LEVEL_UP_MOVE(11, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(15, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(18, MOVE_MIRROR_MOVE),
    LEVEL_UP_MOVE(22, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(25, MOVE_AGILITY),
    LEVEL_UP_MOVE(29, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(32, MOVE_ROOST),
    LEVEL_UP_MOVE(36, MOVE_DRILL_PECK),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 262
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_KEENEYE, ABILITY_NONE, ABILITY_SNIPER
// Level Up Moves: 12
