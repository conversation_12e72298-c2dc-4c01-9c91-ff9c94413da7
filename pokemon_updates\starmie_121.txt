// STARMIE (#121) - GE<PERSON>RATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_STARMIE] =
    {
        .baseHP = 60,
        .baseAttack = 75,
        .baseDefense = 85,
        .baseSpAttack = 100,
        .baseSpDefense = 85,
        .baseSpeed = 115,
        .type1 = TYPE_WATER,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 60,
        .expYield = 182,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_STARDUST,
        .item2 = ITEM_STAR_PIECE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_3,
        .eggGroup2 = EGG_GROUP_WATER_3,
        .ability1 = ABILITY_ILLUMINATE,
        .ability2 = ABILITY_NATURALCURE,
        .hiddenAbility = ABILITY_ANALYTIC,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sStarmieLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE( 1, MOVE_SURF),
    LEVEL_UP_MOVE( 1, MOVE_PSYBEAM),
    LEVEL_UP_MOVE( 1, MOVE_PSYCHIC),
    LEVEL_UP_MOVE( 1, MOVE_RECOVER),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_MINIMIZE),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE( 1, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE( 1, MOVE_SWIFT),
    LEVEL_UP_MOVE( 1, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE( 1, MOVE_COSMIC_POWER),
    LEVEL_UP_MOVE( 1, MOVE_BRINE),
    LEVEL_UP_MOVE( 1, MOVE_POWER_GEM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 520
// Types: TYPE_WATER / TYPE_PSYCHIC
// Abilities: ABILITY_ILLUMINATE, ABILITY_NATURALCURE, ABILITY_ANALYTIC
// Level Up Moves: 16
