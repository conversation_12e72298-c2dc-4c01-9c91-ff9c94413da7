// SWAMPERT (#260) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_SWAMPERT] =
    {
        .baseHP = 100,
        .baseAttack = 110,
        .baseDefense = 90,
        .baseSpAttack = 85,
        .baseSpDefense = 90,
        .baseSpeed = 60,
        .type1 = TYPE_WATER,
        .type2 = TYPE_GROUND,
        .catchRate = 45,
        .expYield = 268,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_WATER_1,
        .ability1 = ABILITY_TORRENT,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_DAMP,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sSwampertLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SHOT),
    LEVEL_UP_MOVE( 9, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE(12, MOVE_PROTECT),
    LEVEL_UP_MOVE(15, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(20, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(25, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(30, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(35, MOVE_AMNESIA),
    LEVEL_UP_MOVE(42, MOVE_MUDDY_WATER),
    LEVEL_UP_MOVE(49, MOVE_SCREECH),
    LEVEL_UP_MOVE(56, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(63, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 535
// Types: TYPE_WATER / TYPE_GROUND
// Abilities: ABILITY_TORRENT, ABILITY_NONE, ABILITY_DAMP
// Level Up Moves: 15
