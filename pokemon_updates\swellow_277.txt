// SWELLOW (#277) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_SWELLOW] =
    {
        .baseHP = 60,
        .baseAttack = 85,
        .baseDefense = 60,
        .baseSpAttack = 75,
        .baseSpDefense = 50,
        .baseSpeed = 125,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 159,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_CHARTI_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_GUTS,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SCRAPPY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sSwellowLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE( 1, MOVE_PLUCK),
    LEVEL_UP_MOVE( 1, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE( 1, MOVE_BRAVE_BIRD),
    LEVEL_UP_MOVE(13, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(17, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(21, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(27, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(33, MOVE_AGILITY),
    LEVEL_UP_MOVE(45, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(57, MOVE_REVERSAL),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 455
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_GUTS, ABILITY_NONE, ABILITY_SCRAPPY
// Level Up Moves: 14
