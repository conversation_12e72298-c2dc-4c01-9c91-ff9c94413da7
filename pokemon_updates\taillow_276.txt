// TAILLOW (#276) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_TAILLOW] =
    {
        .baseHP = 40,
        .baseAttack = 55,
        .baseDefense = 30,
        .baseSpAttack = 30,
        .baseSpDefense = 30,
        .baseSpeed = 85,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 200,
        .expYield = 54,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_CHARTI_BERRY,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FLYING,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_GUTS,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SCRAPPY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sTaillowLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 5, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE( 9, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(13, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(17, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(21, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(25, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(29, MOVE_AGILITY),
    LEVEL_UP_MOVE(33, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(37, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(41, MOVE_BRAVE_BIRD),
    LEVEL_UP_MOVE(45, MOVE_REVERSAL),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 270
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_GUTS, ABILITY_NONE, ABILITY_SCRAPPY
// Level Up Moves: 13
