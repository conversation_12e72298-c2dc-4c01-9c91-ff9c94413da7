// TAUROS (#128) - <PERSON><PERSON><PERSON><PERSON><PERSON> IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_TAUROS] =
    {
        .baseHP = 75,
        .baseAttack = 100,
        .baseDefense = 95,
        .baseSpAttack = 40,
        .baseSpDefense = 70,
        .baseSpeed = 110,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 45,
        .expYield = 172,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_INTIMIDATE,
        .ability2 = ABILITY_ANGERPOINT,
        .abilityHidden = ABILITY_SHEERFORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove staurosLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 3, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 5, MOVE_RAGE),
    LEVEL_UP_MOVE( 8, MOVE_HORN_ATTACK),
    LEVEL_UP_MOVE(11, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(15, MOVE_PURSUIT),
    LEVEL_UP_MOVE(15, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(19, MOVE_REST),
    LEVEL_UP_MOVE(24, MOVE_PAYBACK),
    LEVEL_UP_MOVE(29, MOVE_WORK_UP),
    LEVEL_UP_MOVE(35, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(35, MOVE_RAGING_BULL),
    LEVEL_UP_MOVE(41, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(48, MOVE_SWAGGER),
    LEVEL_UP_MOVE(55, MOVE_THRASH),
    LEVEL_UP_MOVE(63, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(71, MOVE_GIGA_IMPACT),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 490
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_INTIMIDATE, ABILITY_ANGERPOINT, ABILITY_SHEERFORCE
// Level Up Moves: 17
