// TENTACRUEL (#073) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_TENTACRUEL] =
    {
        .baseHP = 80,
        .baseAttack = 70,
        .baseDefense = 65,
        .baseSpAttack = 80,
        .baseSpDefense = 120,
        .baseSpeed = 100,
        .type1 = TYPE_WATER,
        .type2 = TYPE_POISON,
        .catchRate = 60,
        .expYield = 180,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 2,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_POISON_BARB,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_WATER_3,
        .eggGroup2 = EGG_GROUP_WATER_3,
        .ability1 = ABILITY_CLEARBODY,
        .ability2 = ABILITY_LIQUIDOOZE,
        .hiddenAbility = ABILITY_RAINDISH,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sTentacruelLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WRAP),
    LEVEL_UP_MOVE( 1, MOVE_POISON_STING),
    LEVEL_UP_MOVE( 1, MOVE_ACID),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_REFLECT_TYPE),
    LEVEL_UP_MOVE(12, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(16, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(20, MOVE_SCREECH),
    LEVEL_UP_MOVE(24, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(28, MOVE_HEX),
    LEVEL_UP_MOVE(34, MOVE_ACID_ARMOR),
    LEVEL_UP_MOVE(40, MOVE_POISON_JAB),
    LEVEL_UP_MOVE(46, MOVE_SURF),
    LEVEL_UP_MOVE(52, MOVE_SLUDGE_WAVE),
    LEVEL_UP_MOVE(58, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 515
// Types: TYPE_WATER / TYPE_POISON
// Abilities: ABILITY_CLEARBODY, ABILITY_LIQUIDOOZE, ABILITY_RAINDISH
// Level Up Moves: 15
