// TORCHIC (#255) - GE<PERSON><PERSON><PERSON><PERSON> IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_TORCHIC] =
    {
        .baseHP = 45,
        .baseAttack = 60,
        .baseDefense = 40,
        .baseSpAttack = 70,
        .baseSpDefense = 50,
        .baseSpeed = 45,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 45,
        .expYield = 62,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 1,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_BLAZE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SPEEDBOOST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sTorchicLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 3, MOVE_EMBER),
    LEVEL_UP_MOVE( 6, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 9, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE(12, MOVE_DETECT),
    LEVEL_UP_MOVE(15, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE(18, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(21, MOVE_SLASH),
    LEVEL_UP_MOVE(24, MOVE_BOUNCE),
    LEVEL_UP_MOVE(27, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(30, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(33, MOVE_FEATHER_DANCE),
    LEVEL_UP_MOVE(36, MOVE_REVERSAL),
    LEVEL_UP_MOVE(39, MOVE_FLARE_BLITZ),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 310
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_BLAZE, ABILITY_NONE, ABILITY_SPEEDBOOST
// Level Up Moves: 15
