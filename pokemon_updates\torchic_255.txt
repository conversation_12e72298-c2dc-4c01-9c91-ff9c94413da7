// TORCHIC (#255) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_TORCHIC] =
    {
        .baseHP = 45,
        .baseAttack = 60,
        .baseDefense = 40,
        .baseSpAttack = 70,
        .baseSpDefense = 50,
        .baseSpeed = 45,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 45,
        .expYield = 64, // Placeholder - needs manual adjustment
        .evYield_HP = 0, // Placeholder - needs manual adjustment
        .evYield_Attack = 0, // Placeholder - needs manual adjustment
        .evYield_Defense = 0, // Placeholder - needs manual adjustment
        .evYield_SpAttack = 0, // Placeholder - needs manual adjustment
        .evYield_SpDefense = 0, // Placeholder - needs manual adjustment
        .evYield_Speed = 0, // Placeholder - needs manual adjustment
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_BLAZE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_SPEEDBOOST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove storchicLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 5, MOVE_EMBER),
    LEVEL_UP_MOVE(10, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE(12, MOVE_DETECT),
    LEVEL_UP_MOVE(14, MOVE_PECK),
    LEVEL_UP_MOVE(19, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE(23, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(28, MOVE_FLAME_BURST),
    LEVEL_UP_MOVE(32, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(37, MOVE_SLASH),
    LEVEL_UP_MOVE(39, MOVE_FLARE_BLITZ),
    LEVEL_UP_MOVE(41, MOVE_MIRROR_MOVE),
    LEVEL_UP_MOVE(46, MOVE_FLAMETHROWER),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 310
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_BLAZE, ABILITY_NONE, ABILITY_SPEEDBOOST
// Level Up Moves: 14
