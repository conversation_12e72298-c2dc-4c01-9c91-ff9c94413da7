// TRAPINCH (#328) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_TRAPINCH] =
    {
        .baseHP = 45,
        .baseAttack = 100,
        .baseDefense = 45,
        .baseSpAttack = 45,
        .baseSpDefense = 45,
        .baseSpeed = 10,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_GROUND,
        .catchRate = 255,
        .expYield = 58,
        .evYield_HP = 0,
        .evYield_Attack = 1,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_SOFT_SAND,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_HYPERCUTTER,
        .ability2 = ABILITY_ARENATRAP,
        .hiddenAbility = ABILITY_SHEERFORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sTrapinchLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 8, MOVE_BITE),
    LEVEL_UP_MOVE(12, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE(16, MOVE_SAND_TOMB),
    LEVEL_UP_MOVE(20, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(24, MOVE_DIG),
    LEVEL_UP_MOVE(28, MOVE_CRUNCH),
    LEVEL_UP_MOVE(32, MOVE_SANDSTORM),
    LEVEL_UP_MOVE(36, MOVE_EARTH_POWER),
    LEVEL_UP_MOVE(40, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(44, MOVE_SUPERPOWER),
    LEVEL_UP_MOVE(48, MOVE_FISSURE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 290
// Types: TYPE_GROUND / TYPE_GROUND
// Abilities: ABILITY_HYPERCUTTER, ABILITY_ARENATRAP, ABILITY_SHEERFORCE
// Level Up Moves: 13
