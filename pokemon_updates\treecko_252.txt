// TREECKO (#252) - GE<PERSON>RATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_TREECKO] =
    {
        .baseHP = 40,
        .baseAttack = 45,
        .baseDefense = 35,
        .baseSpAttack = 65,
        .baseSpDefense = 55,
        .baseSpeed = 70,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 45,
        .expYield = 62,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_DRAGON,
        .ability1 = ABILITY_OVERGROW,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_UNBURDEN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sTreeckoLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 3, MOVE_LEAFAGE),
    LEVEL_UP_MOVE( 6, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 9, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(12, MOVE_DETECT),
    LEVEL_UP_MOVE(15, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(18, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(21, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(24, MOVE_SLAM),
    LEVEL_UP_MOVE(27, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(30, MOVE_ENERGY_BALL),
    LEVEL_UP_MOVE(33, MOVE_SCREECH),
    LEVEL_UP_MOVE(36, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(39, MOVE_LEAF_STORM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 310
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_OVERGROW, ABILITY_NONE, ABILITY_UNBURDEN
// Level Up Moves: 15
