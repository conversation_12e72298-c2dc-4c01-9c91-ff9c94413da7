// VAPOREON (#134) - GE<PERSON>RATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_VAPOREON] =
    {
        .baseHP = 130,
        .baseAttack = 65,
        .baseDefense = 60,
        .baseSpAttack = 110,
        .baseSpDefense = 95,
        .baseSpeed = 65,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 45,
        .expYield = 184,
        .evYield_HP = 2,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 35,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_WATERABSORB,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_HYDRATION,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove svaporeonLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE( 1, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_SWIFT),
    LEVEL_UP_MOVE( 1, MOVE_CHARM),
    LEVEL_UP_MOVE( 1, MOVE_BATON_PASS),
    LEVEL_UP_MOVE( 1, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE( 5, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 9, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(13, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(17, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(20, MOVE_AURORA_BEAM),
    LEVEL_UP_MOVE(25, MOVE_AQUA_RING),
    LEVEL_UP_MOVE(29, MOVE_ACID_ARMOR),
    LEVEL_UP_MOVE(33, MOVE_HAZE),
    LEVEL_UP_MOVE(37, MOVE_MUDDY_WATER),
    LEVEL_UP_MOVE(41, MOVE_LAST_RESORT),
    LEVEL_UP_MOVE(45, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 525
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_WATERABSORB, ABILITY_NONE, ABILITY_HYDRATION
// Level Up Moves: 23
