// VENONAT (#048) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_VENONAT] =
    {
        .baseHP = 60,
        .baseAttack = 55,
        .baseDefense = 50,
        .baseSpAttack = 40,
        .baseSpDefense = 55,
        .baseSpeed = 45,
        .type1 = TYPE_BUG,
        .type2 = TYPE_POISON,
        .catchRate = 190,
        .expYield = 61,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_COMPOUNDEYES,
        .ability2 = ABILITY_TINTEDLENS,
        .hiddenAbility = ABILITY_RUNAWAY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sVenonatLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_DISABLE),
    LEVEL_UP_MOVE( 5, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(11, MOVE_CONFUSION),
    LEVEL_UP_MOVE(13, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE(17, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(23, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE(25, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(29, MOVE_SLEEP_POWDER),
    LEVEL_UP_MOVE(35, MOVE_LEECH_LIFE),
    LEVEL_UP_MOVE(37, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(41, MOVE_POISON_FANG),
    LEVEL_UP_MOVE(47, MOVE_PSYCHIC),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 305
// Types: TYPE_BUG / TYPE_POISON
// Abilities: ABILITY_COMPOUNDEYES, ABILITY_TINTEDLENS, ABILITY_RUNAWAY
// Level Up Moves: 13
