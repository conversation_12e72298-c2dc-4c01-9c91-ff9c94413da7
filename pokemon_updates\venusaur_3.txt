// VENUSAUR (#003) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_VENUSAUR] =
    {
        .baseHP = 80,
        .baseAttack = 82,
        .baseDefense = 83,
        .baseSpAttack = 100,
        .baseSpDefense = 100,
        .baseSpeed = 80,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_POISON,
        .catchRate = 45,
        .expYield = 263,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 2,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_OVERGROW,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_CHLOROPHYLL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sVenusaurLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_PETAL_BLIZZARD),
    LEVEL_UP_MOVE( 1, MOVE_VINE_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 1, MOVE_PETAL_DANCE),
    LEVEL_UP_MOVE( 9, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(12, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(15, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE(15, MOVE_SLEEP_POWDER),
    LEVEL_UP_MOVE(20, MOVE_SEED_BOMB),
    LEVEL_UP_MOVE(25, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(30, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(37, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(44, MOVE_WORRY_SEED),
    LEVEL_UP_MOVE(51, MOVE_POWER_WHIP),
    LEVEL_UP_MOVE(58, MOVE_SOLAR_BEAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 525
// Types: TYPE_GRASS / TYPE_POISON
// Abilities: ABILITY_OVERGROW, ABILITY_NONE, ABILITY_CHLOROPHYLL
// Level Up Moves: 17
