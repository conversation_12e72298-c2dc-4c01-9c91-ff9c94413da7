// VICTREEBEL (#071) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_VICTREEBEL] =
    {
        .baseHP = 80,
        .baseAttack = 105,
        .baseDefense = 65,
        .baseSpAttack = 100,
        .baseSpDefense = 70,
        .baseSpeed = 70,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_POISON,
        .catchRate = 45,
        .expYield = 221,
        .evYield_HP = 0,
        .evYield_Attack = 3,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_CHLOROPHYLL,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_GLUTTONY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove svictreebelLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_LEAF_TORNADO),
    LEVEL_UP_MOVE( 1, MOVE_VINE_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE( 1, MOVE_SLEEP_POWDER),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE( 1, MOVE_STOCKPILE),
    LEVEL_UP_MOVE( 1, MOVE_SPIT_UP),
    LEVEL_UP_MOVE( 1, MOVE_SWALLOW),
    LEVEL_UP_MOVE(32, MOVE_LEAF_STORM),
    LEVEL_UP_MOVE(44, MOVE_LEAF_BLADE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 490
// Types: TYPE_GRASS / TYPE_POISON
// Abilities: ABILITY_CHLOROPHYLL, ABILITY_NONE, ABILITY_GLUTTONY
// Level Up Moves: 10
