// VIGOROTH (#288) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_VIGOROTH] =
    {
        .baseHP = 80,
        .baseAttack = 80,
        .baseDefense = 80,
        .baseSpAttack = 55,
        .baseSpDefense = 55,
        .baseSpeed = 90,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 120,
        .expYield = 154,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 2,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_VITALSPIRIT,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove svigorothLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE( 1, MOVE_REVERSAL),
    LEVEL_UP_MOVE( 1, MOVE_ENCORE),
    LEVEL_UP_MOVE( 1, MOVE_UPROAR),
    LEVEL_UP_MOVE(14, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(17, MOVE_ENDURE),
    LEVEL_UP_MOVE(23, MOVE_SLASH),
    LEVEL_UP_MOVE(27, MOVE_CHIP_AWAY),
    LEVEL_UP_MOVE(27, MOVE_THROAT_CHOP),
    LEVEL_UP_MOVE(33, MOVE_COUNTER),
    LEVEL_UP_MOVE(37, MOVE_FOCUS_PUNCH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 440
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_VITALSPIRIT, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 12
