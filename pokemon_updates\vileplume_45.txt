// VILEPLUME (#045) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_VILEPLUME] =
    {
        .baseHP = 75,
        .baseAttack = 80,
        .baseDefense = 85,
        .baseSpAttack = 110,
        .baseSpDefense = 90,
        .baseSpeed = 50,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_POISON,
        .catchRate = 45,
        .expYield = 245,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 3,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_ABSORB_BULB,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_CHLOROPHYLL,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_EFFECTSPORE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove svileplumeLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_PETAL_BLIZZARD),
    LEVEL_UP_MOVE( 1, MOVE_ACID),
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 1, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE( 1, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE( 1, MOVE_SLEEP_POWDER),
    LEVEL_UP_MOVE( 1, MOVE_PETAL_DANCE),
    LEVEL_UP_MOVE( 1, MOVE_TOXIC),
    LEVEL_UP_MOVE( 1, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE( 1, MOVE_MOONLIGHT),
    LEVEL_UP_MOVE( 1, MOVE_GRASSY_TERRAIN),
    LEVEL_UP_MOVE( 1, MOVE_MOONBLAST),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 490
// Types: TYPE_GRASS / TYPE_POISON
// Abilities: ABILITY_CHLOROPHYLL, ABILITY_NONE, ABILITY_EFFECTSPORE
// Level Up Moves: 15
