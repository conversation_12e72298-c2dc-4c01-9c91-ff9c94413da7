// VULPIX (#037) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_VULPIX] =
    {
        .baseHP = 38,
        .baseAttack = 41,
        .baseDefense = 40,
        .baseSpAttack = 50,
        .baseSpDefense = 65,
        .baseSpeed = 65,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 190,
        .expYield = 60,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_RAWST_BERRY,
        .item2 = ITEM_CHARCOAL,
        .genderRatio = PERCENT_FEMALE(75),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_FLASHFIRE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_DROUGHT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove svulpixLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 4, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 7, MOVE_ROAR),
    LEVEL_UP_MOVE( 9, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(10, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(12, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(15, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE(16, MOVE_INCINERATE),
    LEVEL_UP_MOVE(18, MOVE_PAYBACK),
    LEVEL_UP_MOVE(20, MOVE_WILL_O_WISP),
    LEVEL_UP_MOVE(23, MOVE_FEINT_ATTACK),
    LEVEL_UP_MOVE(26, MOVE_HEX),
    LEVEL_UP_MOVE(28, MOVE_FLAME_BURST),
    LEVEL_UP_MOVE(31, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE(34, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(36, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(39, MOVE_IMPRISON),
    LEVEL_UP_MOVE(42, MOVE_FIRE_BLAST),
    LEVEL_UP_MOVE(44, MOVE_GRUDGE),
    LEVEL_UP_MOVE(47, MOVE_CAPTIVATE),
    LEVEL_UP_MOVE(50, MOVE_INFERNO),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 299
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_FLASHFIRE, ABILITY_NONE, ABILITY_DROUGHT
// Level Up Moves: 21
