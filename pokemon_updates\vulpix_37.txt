// VULPIX (#037) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_VULPIX] =
    {
        .baseHP = 38,
        .baseAttack = 41,
        .baseDefense = 40,
        .baseSpAttack = 50,
        .baseSpDefense = 65,
        .baseSpeed = 65,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 190,
        .expYield = 60,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_RAWST_BERRY,
        .item2 = ITEM_CHARCOAL,
        .genderRatio = PERCENT_FEMALE(75),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_GROUND,
        .ability1 = ABILITY_FLASHFIRE,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_DROUGHT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove svulpixLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 4, MOVE_DISABLE),
    LEVEL_UP_MOVE( 8, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(12, MOVE_SPITE),
    LEVEL_UP_MOVE(16, MOVE_INCINERATE),
    LEVEL_UP_MOVE(20, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(24, MOVE_WILL_O_WISP),
    LEVEL_UP_MOVE(28, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE(32, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(36, MOVE_IMPRISON),
    LEVEL_UP_MOVE(40, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE(44, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(48, MOVE_INFERNO),
    LEVEL_UP_MOVE(52, MOVE_FIRE_BLAST),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 299
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_FLASHFIRE, ABILITY_NONE, ABILITY_DROUGHT
// Level Up Moves: 15
