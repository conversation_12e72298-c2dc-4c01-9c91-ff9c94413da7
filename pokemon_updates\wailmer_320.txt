// WAILMER (#320) - GE<PERSON>RATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_WAILMER] =
    {
        .baseHP = 130,
        .baseAttack = 70,
        .baseDefense = 35,
        .baseSpAttack = 70,
        .baseSpDefense = 35,
        .baseSpeed = 60,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 125,
        .expYield = 80,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 40,
        .friendship = 50,
        .growthRate = GROWTH_FLUCTUATING,
        .eggGroup1 = EGG_GROUP_GROUND,
        .eggGroup2 = EGG_GROUP_WATER_2,
        .ability1 = ABILITY_WATERVEIL,
        .ability2 = ABILITY_OBLIVIOUS,
        .abilityHidden = ABILITY_PRESSURE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove swailmerLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SPLASH),
    LEVEL_UP_MOVE( 4, MOVE_GROWL),
    LEVEL_UP_MOVE( 7, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(10, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(13, MOVE_WHIRLPOOL),
    LEVEL_UP_MOVE(16, MOVE_ASTONISH),
    LEVEL_UP_MOVE(19, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(22, MOVE_MIST),
    LEVEL_UP_MOVE(25, MOVE_BRINE),
    LEVEL_UP_MOVE(29, MOVE_REST),
    LEVEL_UP_MOVE(33, MOVE_WATER_SPOUT),
    LEVEL_UP_MOVE(37, MOVE_AMNESIA),
    LEVEL_UP_MOVE(41, MOVE_DIVE),
    LEVEL_UP_MOVE(45, MOVE_BOUNCE),
    LEVEL_UP_MOVE(49, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(53, MOVE_HEAVY_SLAM),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 400
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_WATERVEIL, ABILITY_OBLIVIOUS, ABILITY_PRESSURE
// Level Up Moves: 16
