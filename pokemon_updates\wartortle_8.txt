// WARTORTLE (#008) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_WARTORTLE] =
    {
        .baseHP = 59,
        .baseAttack = 63,
        .baseDefense = 80,
        .baseSpAttack = 65,
        .baseSpDefense = 80,
        .baseSpeed = 58,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 45,
        .expYield = 142,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 1,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 1,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_MONSTER,
        .eggGroup2 = EGG_GROUP_WATER_1,
        .ability1 = ABILITY_TORRENT,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_RAINDISH,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove swartortleLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_WITHDRAW),
    LEVEL_UP_MOVE( 9, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE(12, MOVE_BITE),
    LEVEL_UP_MOVE(15, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(20, MOVE_PROTECT),
    LEVEL_UP_MOVE(25, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(30, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(35, MOVE_SHELL_SMASH),
    LEVEL_UP_MOVE(40, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(45, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(50, MOVE_WAVE_CRASH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 405
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_TORRENT, ABILITY_NONE, ABILITY_RAINDISH
// Level Up Moves: 14
