// WEEDLE (#013) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_WEEDLE] =
    {
        .baseHP = 40,
        .baseAttack = 35,
        .baseDefense = 30,
        .baseSpAttack = 20,
        .baseSpDefense = 20,
        .baseSpeed = 50,
        .type1 = TYPE_BUG,
        .type2 = TYPE_POISON,
        .catchRate = 255,
        .expYield = 39,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_SHIELDDUST,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_RUNAWAY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sWeedleLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POISON_STING),
    LEVEL_UP_MOVE( 1, MOVE_STRING_SHOT),
    LEVEL_UP_MOVE( 9, MOVE_BUG_BITE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 195
// Types: TYPE_BUG / TYPE_POISON
// Abilities: ABILITY_SHIELDDUST, ABILITY_NONE, ABILITY_RUNAWAY
// Level Up Moves: 3
