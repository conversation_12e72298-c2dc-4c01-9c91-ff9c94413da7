// WEEPINBELL (#070) - GE<PERSON>RATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_WEEPINBELL] =
    {
        .baseHP = 65,
        .baseAttack = 90,
        .baseDefense = 50,
        .baseSpAttack = 85,
        .baseSpDefense = 45,
        .baseSpeed = 55,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_POISON,
        .catchRate = 120,
        .expYield = 137,
        .evYield_HP = 0,
        .evYield_Attack = 2,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_PLANT,
        .eggGroup2 = EGG_GROUP_PLANT,
        .ability1 = ABILITY_CHLOROPHYLL,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_GLUTTONY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sweepinbellLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_VINE_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_WRAP),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE(13, MOVE_SLEEP_POWDER),
    LEVEL_UP_MOVE(15, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE(17, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE(24, MOVE_ACID),
    LEVEL_UP_MOVE(29, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(32, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(39, MOVE_GASTRO_ACID),
    LEVEL_UP_MOVE(44, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(47, MOVE_POISON_JAB),
    LEVEL_UP_MOVE(54, MOVE_SLAM),
    LEVEL_UP_MOVE(58, MOVE_WRING_OUT),
    LEVEL_UP_MOVE(58, MOVE_POWER_WHIP),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 390
// Types: TYPE_GRASS / TYPE_POISON
// Abilities: ABILITY_CHLOROPHYLL, ABILITY_NONE, ABILITY_GLUTTONY
// Level Up Moves: 15
