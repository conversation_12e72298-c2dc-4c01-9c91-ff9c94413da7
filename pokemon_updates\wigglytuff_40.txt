// WIGGLYTUFF (#040) - GENERATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_WIGGLYTUFF] =
    {
        .baseHP = 140,
        .baseAttack = 70,
        .baseDefense = 45,
        .baseSpAttack = 85,
        .baseSpDefense = 50,
        .baseSpeed = 45,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FAIRY,
        .catchRate = 50,
        .expYield = 218,
        .evYield_HP = 3,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_ORAN_BERRY,
        .item2 = ITEM_MOON_STONE,
        .genderRatio = PERCENT_FEMALE(75),
        .eggCycles = 10,
        .friendship = 50,
        .growthRate = GROWTH_FAST,
        .eggGroup1 = EGG_GROUP_FAIRY,
        .eggGroup2 = EGG_GROUP_FAIRY,
        .ability1 = ABILITY_CUTECHARM,
        .ability2 = ABILITY_COMPETITIVE,
        .hiddenAbility = ABILITY_FRISK,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sWigglytuffLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE( 1, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE( 1, MOVE_SING),
    LEVEL_UP_MOVE( 1, MOVE_DISABLE),
    LEVEL_UP_MOVE( 1, MOVE_MIMIC),
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_REST),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_KISS),
    LEVEL_UP_MOVE( 1, MOVE_CHARM),
    LEVEL_UP_MOVE( 1, MOVE_STOCKPILE),
    LEVEL_UP_MOVE( 1, MOVE_SPIT_UP),
    LEVEL_UP_MOVE( 1, MOVE_SWALLOW),
    LEVEL_UP_MOVE( 1, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE( 1, MOVE_COVET),
    LEVEL_UP_MOVE( 1, MOVE_GYRO_BALL),
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE( 1, MOVE_ROUND),
    LEVEL_UP_MOVE( 1, MOVE_ECHOED_VOICE),
    LEVEL_UP_MOVE( 1, MOVE_DISARMING_VOICE),
    LEVEL_UP_MOVE( 5, MOVE_PLAY_ROUGH),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 435
// Types: TYPE_NORMAL / TYPE_FAIRY
// Abilities: ABILITY_CUTECHARM, ABILITY_COMPETITIVE, ABILITY_FRISK
// Level Up Moves: 21
