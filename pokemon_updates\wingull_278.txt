// WINGULL (#278) - GE<PERSON>RATION IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_WINGULL] =
    {
        .baseHP = 40,
        .baseAttack = 30,
        .baseDefense = 30,
        .baseSpAttack = 55,
        .baseSpDefense = 30,
        .baseSpeed = 85,
        .type1 = TYPE_WATER,
        .type2 = TYPE_FLYING,
        .catchRate = 190,
        .expYield = 54,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 1,
        .item1 = ITEM_PRETTY_WING,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_WATER_1,
        .eggGroup2 = EGG_GROUP_FLYING,
        .ability1 = ABILITY_KEENEYE,
        .ability2 = ABILITY_HYDRATION,
        .hiddenAbility = ABILITY_RAINDISH,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sWingullLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 5, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(10, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(15, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(20, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(26, MOVE_AGILITY),
    LEVEL_UP_MOVE(30, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(35, MOVE_MIST),
    LEVEL_UP_MOVE(40, MOVE_ROOST),
    LEVEL_UP_MOVE(45, MOVE_HURRICANE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 270
// Types: TYPE_WATER / TYPE_FLYING
// Abilities: ABILITY_KEENEYE, ABILITY_HYDRATION, ABILITY_RAINDISH
// Level Up Moves: 11
