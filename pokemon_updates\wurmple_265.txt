// WURMPLE (#265) - GE<PERSON>RA<PERSON><PERSON> IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_WURMPLE] =
    {
        .baseHP = 45,
        .baseAttack = 45,
        .baseDefense = 35,
        .baseSpAttack = 20,
        .baseSpDefense = 30,
        .baseSpeed = 20,
        .type1 = TYPE_BUG,
        .type2 = TYPE_BUG,
        .catchRate = 255,
        .expYield = 56,
        .evYield_HP = 1,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_PECHA_BERRY,
        .item2 = ITEM_BRIGHT_POWDER,
        .genderRatio = PERCENT_FEMALE(50),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_FAST,
        .eggGroup1 = EGG_GROUP_BUG,
        .eggGroup2 = EGG_GROUP_BUG,
        .ability1 = ABILITY_SHIELDDUST,
        .ability2 = ABILITY_NONE,
        .abilityHidden = ABILITY_RUNAWAY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove swurmpleLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_STRING_SHOT),
    LEVEL_UP_MOVE( 5, MOVE_POISON_STING),
    LEVEL_UP_MOVE(15, MOVE_BUG_BITE),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 195
// Types: TYPE_BUG / TYPE_BUG
// Abilities: ABILITY_SHIELDDUST, ABILITY_NONE, ABILITY_RUNAWAY
// Level Up Moves: 4
