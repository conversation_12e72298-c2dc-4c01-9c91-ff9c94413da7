// ZAPDOS (#145) - GE<PERSON>RA<PERSON><PERSON> IX UPDATE

// BASE STATS ENTRY:
    [SPECIES_ZAPDOS] =
    {
        .baseHP = 90,
        .baseAttack = 90,
        .baseDefense = 85,
        .baseSpAttack = 125,
        .baseSpDefense = 90,
        .baseSpeed = 100,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_FLYING,
        .catchRate = 3,
        .expYield = 290,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 3,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(255),
        .eggCycles = 80,
        .friendship = 35,
        .growthRate = GROWTH_SLOW,
        .eggGroup1 = EGG_GROUP_NO-EGGS,
        .eggGroup2 = EGG_GROUP_NO-EGGS,
        .ability1 = ABILITY_PRESSURE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_STATIC,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sZapdosLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE( 5, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE(10, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(15, MOVE_PLUCK),
    LEVEL_UP_MOVE(20, MOVE_AGILITY),
    LEVEL_UP_MOVE(25, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(30, MOVE_CHARGE),
    LEVEL_UP_MOVE(35, MOVE_DRILL_PECK),
    LEVEL_UP_MOVE(40, MOVE_ROOST),
    LEVEL_UP_MOVE(45, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(50, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(55, MOVE_THUNDER),
    LEVEL_UP_MOVE(60, MOVE_DETECT),
    LEVEL_UP_MOVE(65, MOVE_MAGNETIC_FLUX),
    LEVEL_UP_MOVE(70, MOVE_ZAP_CANNON),
    LEVEL_UP_END
}};

// SUMMARY:
// Stats Total: 580
// Types: TYPE_ELECTRIC / TYPE_FLYING
// Abilities: ABILITY_PRESSURE, ABILITY_NONE, ABILITY_STATIC
// Level Up Moves: 16
