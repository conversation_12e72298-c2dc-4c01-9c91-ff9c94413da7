// POKEMON_1003 (#1003) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1003] =
    {
        .baseHP = 155,
        .baseAttack = 110,
        .baseDefense = 125,
        .baseSpAttack = 55,
        .baseSpDefense = 80,
        .baseSpeed = 45,
        .type1 = TYPE_DARK,
        .type2 = TYPE_GROUND,
        .catchRate = 6,
        .expYield = 255,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_VESSEL-OF-RUIN,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-1003LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_MEAN_LOOK),
    LEVEL_UP_MOVE( 1, MOVE_SAND_TOMB),
    LEVEL_UP_MOVE( 1, MOVE_SPITE),
    LEVEL_UP_MOVE( 5, MOVE_SPIKES),
    LEVEL_UP_MOVE(10, MOVE_PAYBACK),
    LEVEL_UP_MOVE(15, MOVE_STOMP),
    LEVEL_UP_MOVE(20, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(25, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(30, MOVE_TAUNT),
    LEVEL_UP_MOVE(35, MOVE_THRASH),
    LEVEL_UP_MOVE(40, MOVE_DARK_PULSE),
    LEVEL_UP_MOVE(45, MOVE_STOMPING_TANTRUM),
    LEVEL_UP_MOVE(50, MOVE_RUINATION),
    LEVEL_UP_MOVE(55, MOVE_THROAT_CHOP),
    LEVEL_UP_MOVE(60, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(65, MOVE_MEMENTO),
    LEVEL_UP_MOVE(70, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(75, MOVE_FISSURE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_DARK / TYPE_GROUND
// Abilities: ABILITY_VESSEL-OF-RUIN, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 18
// Generation: 9

