// POKEMON_1004 (#1004) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1004] =
    {
        .baseHP = 55,
        .baseAttack = 80,
        .baseDefense = 80,
        .baseSpAttack = 135,
        .baseSpDefense = 120,
        .baseSpeed = 100,
        .type1 = TYPE_DARK,
        .type2 = TYPE_FIRE,
        .catchRate = 6,
        .expYield = 135,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BEADS-OF-RUIN,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-1004LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_MEAN_LOOK),
    LEVEL_UP_MOVE( 1, MOVE_SPITE),
    LEVEL_UP_MOVE( 5, MOVE_FLAME_WHEEL),
    LEVEL_UP_MOVE(10, MOVE_PAYBACK),
    LEVEL_UP_MOVE(15, MOVE_WILL_O_WISP),
    LEVEL_UP_MOVE(20, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE(25, MOVE_INCINERATE),
    LEVEL_UP_MOVE(30, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(35, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(40, MOVE_DARK_PULSE),
    LEVEL_UP_MOVE(45, MOVE_LAVA_PLUME),
    LEVEL_UP_MOVE(50, MOVE_RUINATION),
    LEVEL_UP_MOVE(55, MOVE_BOUNCE),
    LEVEL_UP_MOVE(60, MOVE_SWAGGER),
    LEVEL_UP_MOVE(65, MOVE_INFERNO),
    LEVEL_UP_MOVE(70, MOVE_MEMENTO),
    LEVEL_UP_MOVE(75, MOVE_OVERHEAT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_DARK / TYPE_FIRE
// Abilities: ABILITY_BEADS-OF-RUIN, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 18
// Generation: 9

