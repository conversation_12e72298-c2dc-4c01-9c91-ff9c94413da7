// POKEMON_1007 (#1007) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1007] =
    {
        .baseHP = 100,
        .baseAttack = 135,
        .baseDefense = 115,
        .baseSpAttack = 85,
        .baseSpDefense = 100,
        .baseSpeed = 135,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_DRAGON,
        .catchRate = 3,
        .expYield = 235,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_ORICHALCUM-PULSE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-1007LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BREAKING_SWIPE),
    LEVEL_UP_MOVE( 1, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE( 7, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE(14, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(21, MOVE_DRAIN_PUNCH),
    LEVEL_UP_MOVE(28, MOVE_BRICK_BREAK),
    LEVEL_UP_MOVE(35, MOVE_AGILITY),
    LEVEL_UP_MOVE(42, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(49, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(56, MOVE_COLLISION_COURSE),
    LEVEL_UP_MOVE(63, MOVE_SCREECH),
    LEVEL_UP_MOVE(70, MOVE_COUNTER),
    LEVEL_UP_MOVE(77, MOVE_OUTRAGE),
    LEVEL_UP_MOVE(84, MOVE_CLOSE_COMBAT),
    LEVEL_UP_MOVE(91, MOVE_FLARE_BLITZ),
    LEVEL_UP_MOVE(98, MOVE_GIGA_IMPACT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 670
// Types: TYPE_FIGHTING / TYPE_DRAGON
// Abilities: ABILITY_ORICHALCUM-PULSE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
// Generation: 9

