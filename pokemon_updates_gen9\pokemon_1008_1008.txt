// POKEMON_1008 (#1008) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1008] =
    {
        .baseHP = 100,
        .baseAttack = 85,
        .baseDefense = 100,
        .baseSpAttack = 135,
        .baseSpDefense = 115,
        .baseSpeed = 135,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_DRAGON,
        .catchRate = 3,
        .expYield = 185,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_HADRON-ENGINE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-1008LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE( 1, MOVE_ELECTRIC_TERRAIN),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 7, MOVE_SHOCK_WAVE),
    LEVEL_UP_MOVE(14, MOVE_CHARGE),
    LEVEL_UP_MOVE(21, MOVE_PARABOLIC_CHARGE),
    LEVEL_UP_MOVE(28, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(35, MOVE_AGILITY),
    LEVEL_UP_MOVE(42, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(56, MOVE_ELECTRO_DRIFT),
    LEVEL_UP_MOVE(63, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(70, MOVE_MIRROR_COAT),
    LEVEL_UP_MOVE(77, MOVE_OUTRAGE),
    LEVEL_UP_MOVE(84, MOVE_THUNDER),
    LEVEL_UP_MOVE(91, MOVE_OVERHEAT),
    LEVEL_UP_MOVE(98, MOVE_HYPER_BEAM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 670
// Types: TYPE_ELECTRIC / TYPE_DRAGON
// Abilities: ABILITY_HADRON-ENGINE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
// Generation: 9

