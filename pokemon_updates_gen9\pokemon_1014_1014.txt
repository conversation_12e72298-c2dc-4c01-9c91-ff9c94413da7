// POKEMON_1014 (#1014) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1014] =
    {
        .baseHP = 88,
        .baseAttack = 128,
        .baseDefense = 115,
        .baseSpAttack = 58,
        .baseSpDefense = 86,
        .baseSpeed = 80,
        .type1 = TYPE_POISON,
        .type2 = TYPE_FIGHTING,
        .catchRate = 3,
        .expYield = 216,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_TOXIC-CHAIN,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_GUARD-DOG,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-1014LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_BULK_UP),
    LEVEL_UP_MOVE( 1, MOVE_LOW_KICK),
    LEVEL_UP_MOVE( 8, MOVE_HOWL),
    LEVEL_UP_MOVE(16, MOVE_POISON_FANG),
    LEVEL_UP_MOVE(24, MOVE_FORCE_PALM),
    LEVEL_UP_MOVE(32, MOVE_COUNTER),
    LEVEL_UP_MOVE(40, MOVE_POISON_JAB),
    LEVEL_UP_MOVE(48, MOVE_BRUTAL_SWING),
    LEVEL_UP_MOVE(56, MOVE_CRUNCH),
    LEVEL_UP_MOVE(64, MOVE_SUPERPOWER),
    LEVEL_UP_MOVE(72, MOVE_GIGA_IMPACT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 555
// Types: TYPE_POISON / TYPE_FIGHTING
// Abilities: ABILITY_TOXIC-CHAIN, ABILITY_NONE, ABILITY_GUARD-DOG
// Level Up Moves: 12
// Generation: 9

