// POKEMON_1019 (#1019) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1019] =
    {
        .baseHP = 106,
        .baseAttack = 80,
        .baseDefense = 110,
        .baseSpAttack = 120,
        .baseSpDefense = 80,
        .baseSpeed = 44,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_DRAGON,
        .catchRate = 10,
        .expYield = 186,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SUPERSWEET-SYRUP,
        .ability2 = ABILITY_REGENERATOR,
        .hiddenAbility = ABILITY_STICKY-HOLD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-1019LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_FICKLE_BEAM),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_RECYCLE),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE( 1, MOVE_WITHDRAW),
    LEVEL_UP_MOVE( 4, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE( 8, MOVE_GROWTH),
    LEVEL_UP_MOVE(12, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(16, MOVE_PROTECT),
    LEVEL_UP_MOVE(20, MOVE_BULLET_SEED),
    LEVEL_UP_MOVE(28, MOVE_SYRUP_BOMB),
    LEVEL_UP_MOVE(32, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(36, MOVE_RECOVER),
    LEVEL_UP_MOVE(40, MOVE_ENERGY_BALL),
    LEVEL_UP_MOVE(44, MOVE_SUBSTITUTE),
    LEVEL_UP_MOVE(54, MOVE_POWER_WHIP),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 540
// Types: TYPE_GRASS / TYPE_DRAGON
// Abilities: ABILITY_SUPERSWEET-SYRUP, ABILITY_REGENERATOR, ABILITY_STICKY-HOLD
// Level Up Moves: 16
// Generation: 9

