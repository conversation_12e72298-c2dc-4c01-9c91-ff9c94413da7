// POKEMON_1020 (#1020) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1020] =
    {
        .baseHP = 105,
        .baseAttack = 115,
        .baseDefense = 121,
        .baseSpAttack = 65,
        .baseSpDefense = 93,
        .baseSpeed = 91,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_DRAGON,
        .catchRate = 10,
        .expYield = 220,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PROTOSYNTHESIS,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-1020LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_INCINERATE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_STOMP),
    LEVEL_UP_MOVE( 1, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE( 7, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE(14, MOVE_HOWL),
    LEVEL_UP_MOVE(21, MOVE_BITE),
    LEVEL_UP_MOVE(28, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(35, MOVE_CRUSH_CLAW),
    LEVEL_UP_MOVE(42, MOVE_MORNING_SUN),
    LEVEL_UP_MOVE(49, MOVE_BURNING_BULWARK),
    LEVEL_UP_MOVE(56, MOVE_DRAGON_RUSH),
    LEVEL_UP_MOVE(63, MOVE_FIRE_BLAST),
    LEVEL_UP_MOVE(70, MOVE_LAVA_PLUME),
    LEVEL_UP_MOVE(77, MOVE_OUTRAGE),
    LEVEL_UP_MOVE(84, MOVE_FLARE_BLITZ),
    LEVEL_UP_MOVE(91, MOVE_RAGING_FURY),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 590
// Types: TYPE_FIRE / TYPE_DRAGON
// Abilities: ABILITY_PROTOSYNTHESIS, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 17
// Generation: 9

