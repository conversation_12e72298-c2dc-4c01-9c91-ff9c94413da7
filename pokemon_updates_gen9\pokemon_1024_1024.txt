// POKEMON_1024 (#1024) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_1024] =
    {
        .baseHP = 90,
        .baseAttack = 65,
        .baseDefense = 85,
        .baseSpAttack = 65,
        .baseSpDefense = 85,
        .baseSpeed = 60,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 255,
        .expYield = 155,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 5,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_TERA-SHIFT,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-1024LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE( 1, MOVE_TRI_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_WITHDRAW),
    LEVEL_UP_MOVE(10, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(20, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(30, MOVE_PROTECT),
    LEVEL_UP_MOVE(40, MOVE_EARTH_POWER),
    LEVEL_UP_MOVE(50, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(60, MOVE_TERA_STARSTORM),
    LEVEL_UP_MOVE(70, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(80, MOVE_ROCK_POLISH),
    LEVEL_UP_MOVE(90, MOVE_GYRO_BALL),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 450
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_TERA-SHIFT, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 12
// Generation: 9

