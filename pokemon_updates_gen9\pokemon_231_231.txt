// POKEMON_231 (#231) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_231] =
    {
        .baseHP = 90,
        .baseAttack = 60,
        .baseDefense = 60,
        .baseSpAttack = 40,
        .baseSpDefense = 40,
        .baseSpeed = 40,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_GROUND,
        .catchRate = 120,
        .expYield = 150,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PICKUP,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SAND-VEIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-231LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 6, MOVE_FLAIL),
    LEVEL_UP_MOVE(10, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(15, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(19, MOVE_ENDURE),
    LEVEL_UP_MOVE(24, MOVE_SLAM),
    LEVEL_UP_MOVE(28, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(33, MOVE_CHARM),
    LEVEL_UP_MOVE(37, MOVE_LAST_RESORT),
    LEVEL_UP_MOVE(42, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 330
// Types: TYPE_GROUND / TYPE_GROUND
// Abilities: ABILITY_PICKUP, ABILITY_NONE, ABILITY_SAND-VEIL
// Level Up Moves: 12
// Generation: 9

