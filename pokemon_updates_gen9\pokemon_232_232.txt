// POKEMON_232 (#232) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_232] =
    {
        .baseHP = 90,
        .baseAttack = 120,
        .baseDefense = 120,
        .baseSpAttack = 60,
        .baseSpDefense = 60,
        .baseSpeed = 50,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_GROUND,
        .catchRate = 60,
        .expYield = 210,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STURDY,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SAND-VEIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-232LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_BULLDOZE),
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_HORN_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_FANG),
    LEVEL_UP_MOVE( 6, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE(10, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(15, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(19, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(24, MOVE_SLAM),
    LEVEL_UP_MOVE(30, MOVE_STOMPING_TANTRUM),
    LEVEL_UP_MOVE(37, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(43, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(50, MOVE_GIGA_IMPACT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_GROUND / TYPE_GROUND
// Abilities: ABILITY_STURDY, ABILITY_NONE, ABILITY_SAND-VEIL
// Level Up Moves: 16
// Generation: 9

