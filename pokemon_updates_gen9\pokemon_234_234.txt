// POKEMON_234 (#234) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_234] =
    {
        .baseHP = 73,
        .baseAttack = 95,
        .baseDefense = 62,
        .baseSpAttack = 85,
        .baseSpDefense = 65,
        .baseSpeed = 85,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 45,
        .expYield = 168,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_INTIMIDATE,
        .ability2 = ABILITY_FRISK,
        .hiddenAbility = ABILITY_SAP-SIPPER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-234LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 3, MOVE_LEER),
    LEVEL_UP_MOVE( 7, MOVE_ASTONISH),
    LEVEL_UP_MOVE(10, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE(13, MOVE_STOMP),
    LEVEL_UP_MOVE(16, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE(21, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(23, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(27, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(32, MOVE_ROLE_PLAY),
    LEVEL_UP_MOVE(37, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(49, MOVE_IMPRISON),
    LEVEL_UP_MOVE(55, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 465
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_INTIMIDATE, ABILITY_FRISK, ABILITY_SAP-SIPPER
// Level Up Moves: 13
// Generation: 9

