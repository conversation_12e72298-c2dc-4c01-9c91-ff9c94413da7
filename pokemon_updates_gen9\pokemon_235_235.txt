// POKEMON_235 (#235) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_235] =
    {
        .baseHP = 55,
        .baseAttack = 20,
        .baseDefense = 35,
        .baseSpAttack = 20,
        .baseSpDefense = 45,
        .baseSpeed = 75,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 45,
        .expYield = 75,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_OWN-TEMPO,
        .ability2 = ABILITY_TECHNICIAN,
        .hiddenAbility = ABILITY_MOODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-235LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SKETCH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 250
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_OWN-TEMPO, ABILITY_TECHNICIAN, ABILITY_MOODY
// Level Up Moves: 1
// Generation: 9

