// POKEMON_236 (#236) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_236] =
    {
        .baseHP = 35,
        .baseAttack = 35,
        .baseDefense = 35,
        .baseSpAttack = 35,
        .baseSpDefense = 35,
        .baseSpeed = 35,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 75,
        .expYield = 70,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(0.0),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_GUTS,
        .ability2 = ABILITY_STEADFAST,
        .hiddenAbility = ABILITY_VITAL-SPIRIT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-236LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_FAKE_OUT),
    LEVEL_UP_MOVE( 1, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE( 1, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 210
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_GUTS, ABILITY_STEADFAST, ABILITY_VITAL-SPIRIT
// Level Up Moves: 4
// Generation: 9

