// POKEMON_238 (#238) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_238] =
    {
        .baseHP = 45,
        .baseAttack = 30,
        .baseDefense = 15,
        .baseSpAttack = 85,
        .baseSpDefense = 65,
        .baseSpeed = 65,
        .type1 = TYPE_ICE,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 45,
        .expYield = 75,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(100.0),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_OBLIVIOUS,
        .ability2 = ABILITY_FOREWARN,
        .hiddenAbility = ABILITY_HYDRATION,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-238LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LICK),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 4, MOVE_POWDER_SNOW),
    LEVEL_UP_MOVE( 8, MOVE_COPYCAT),
    LEVEL_UP_MOVE(12, MOVE_CONFUSION),
    LEVEL_UP_MOVE(16, MOVE_COVET),
    LEVEL_UP_MOVE(20, MOVE_SING),
    LEVEL_UP_MOVE(24, MOVE_FAKE_TEARS),
    LEVEL_UP_MOVE(28, MOVE_ICE_PUNCH),
    LEVEL_UP_MOVE(32, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(36, MOVE_SWEET_KISS),
    LEVEL_UP_MOVE(40, MOVE_MEAN_LOOK),
    LEVEL_UP_MOVE(44, MOVE_PERISH_SONG),
    LEVEL_UP_MOVE(48, MOVE_BLIZZARD),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 305
// Types: TYPE_ICE / TYPE_PSYCHIC
// Abilities: ABILITY_OBLIVIOUS, ABILITY_FOREWARN, ABILITY_HYDRATION
// Level Up Moves: 14
// Generation: 8

