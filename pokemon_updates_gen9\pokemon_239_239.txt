// POKEMON_239 (#239) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_239] =
    {
        .baseHP = 45,
        .baseAttack = 63,
        .baseDefense = 37,
        .baseSpAttack = 65,
        .baseSpDefense = 55,
        .baseSpeed = 95,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 45,
        .expYield = 108,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(25.0),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STATIC,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_VITAL-SPIRIT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-239LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 4, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 8, MOVE_CHARGE),
    LEVEL_UP_MOVE(12, MOVE_SWIFT),
    LEVEL_UP_MOVE(16, MOVE_SHOCK_WAVE),
    LEVEL_UP_MOVE(20, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE(24, MOVE_SCREECH),
    LEVEL_UP_MOVE(28, MOVE_THUNDER_PUNCH),
    LEVEL_UP_MOVE(32, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(36, MOVE_LOW_KICK),
    LEVEL_UP_MOVE(40, MOVE_THUNDERBOLT),
    LEVEL_UP_MOVE(44, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(48, MOVE_THUNDER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 360
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_STATIC, ABILITY_NONE, ABILITY_VITAL-SPIRIT
// Level Up Moves: 14
// Generation: 9

