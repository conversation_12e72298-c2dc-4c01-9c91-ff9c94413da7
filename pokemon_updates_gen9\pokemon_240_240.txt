// POKEMON_240 (#240) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_240] =
    {
        .baseHP = 45,
        .baseAttack = 75,
        .baseDefense = 37,
        .baseSpAttack = 70,
        .baseSpDefense = 55,
        .baseSpeed = 83,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 45,
        .expYield = 120,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(25.0),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_FLAME-BODY,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_VITAL-SPIRIT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-240LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_SMOG),
    LEVEL_UP_MOVE( 4, MOVE_EMBER),
    LEVEL_UP_MOVE( 8, MOVE_SMOKESCREEN),
    LEVEL_UP_MOVE(12, MOVE_CLEAR_SMOG),
    LEVEL_UP_MOVE(16, MOVE_FLAME_WHEEL),
    LEVEL_UP_MOVE(20, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(24, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(28, MOVE_FIRE_PUNCH),
    LEVEL_UP_MOVE(32, MOVE_LAVA_PLUME),
    LEVEL_UP_MOVE(36, MOVE_LOW_KICK),
    LEVEL_UP_MOVE(40, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(44, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE(48, MOVE_FIRE_BLAST),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 365
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_FLAME-BODY, ABILITY_NONE, ABILITY_VITAL-SPIRIT
// Level Up Moves: 14
// Generation: 9

