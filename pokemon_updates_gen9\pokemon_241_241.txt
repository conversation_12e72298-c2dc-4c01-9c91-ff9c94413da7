// POKEMON_241 (#241) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_241] =
    {
        .baseHP = 95,
        .baseAttack = 80,
        .baseDefense = 105,
        .baseSpAttack = 40,
        .baseSpDefense = 70,
        .baseSpeed = 100,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 45,
        .expYield = 175,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(100.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_THICK-FAT,
        .ability2 = ABILITY_SCRAPPY,
        .hiddenAbility = ABILITY_SAP-SIPPER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-241LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(10, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE(15, MOVE_STOMP),
    LEVEL_UP_MOVE(20, MOVE_HEAL_BELL),
    LEVEL_UP_MOVE(25, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(30, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(35, MOVE_MILK_DRINK),
    LEVEL_UP_MOVE(40, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(45, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(50, MOVE_CHARM),
    LEVEL_UP_MOVE(55, MOVE_HIGH_HORSEPOWER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 490
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_THICK-FAT, ABILITY_SCRAPPY, ABILITY_SAP-SIPPER
// Level Up Moves: 13
// Generation: 8

