// POKEMON_244 (#244) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_244] =
    {
        .baseHP = 115,
        .baseAttack = 115,
        .baseDefense = 85,
        .baseSpAttack = 90,
        .baseSpDefense = 75,
        .baseSpeed = 100,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 3,
        .expYield = 230,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 80,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PRESSURE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_INNER-FOCUS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-244LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_SMOKESCREEN),
    LEVEL_UP_MOVE( 1, MOVE_STOMP),
    LEVEL_UP_MOVE( 6, MOVE_FLAME_WHEEL),
    LEVEL_UP_MOVE(12, MOVE_BITE),
    LEVEL_UP_MOVE(18, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(24, MOVE_ROAR),
    LEVEL_UP_MOVE(30, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE(36, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(42, MOVE_CRUNCH),
    LEVEL_UP_MOVE(48, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE(54, MOVE_LAVA_PLUME),
    LEVEL_UP_MOVE(60, MOVE_SWAGGER),
    LEVEL_UP_MOVE(66, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE(72, MOVE_FIRE_BLAST),
    LEVEL_UP_MOVE(78, MOVE_ERUPTION),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 580
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_PRESSURE, ABILITY_NONE, ABILITY_INNER-FOCUS
// Level Up Moves: 17
// Generation: 9

