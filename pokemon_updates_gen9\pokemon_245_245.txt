// POKEMON_245 (#245) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_245] =
    {
        .baseHP = 100,
        .baseAttack = 75,
        .baseDefense = 115,
        .baseSpAttack = 90,
        .baseSpDefense = 115,
        .baseSpeed = 85,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 3,
        .expYield = 175,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 80,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PRESSURE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_INNER-FOCUS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-245LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_MIST),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 6, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(12, MOVE_BITE),
    LEVEL_UP_MOVE(18, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(24, MOVE_ROAR),
    LEVEL_UP_MOVE(30, MOVE_ICE_FANG),
    LEVEL_UP_MOVE(36, MOVE_TAILWIND),
    LEVEL_UP_MOVE(42, MOVE_CRUNCH),
    LEVEL_UP_MOVE(48, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE(54, MOVE_SURF),
    LEVEL_UP_MOVE(60, MOVE_MIRROR_COAT),
    LEVEL_UP_MOVE(66, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(72, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(78, MOVE_BLIZZARD),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 580
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_PRESSURE, ABILITY_NONE, ABILITY_INNER-FOCUS
// Level Up Moves: 17
// Generation: 9

