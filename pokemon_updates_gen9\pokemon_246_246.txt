// POKEMON_246 (#246) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_246] =
    {
        .baseHP = 50,
        .baseAttack = 64,
        .baseDefense = 50,
        .baseSpAttack = 45,
        .baseSpDefense = 50,
        .baseSpeed = 41,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_GROUND,
        .catchRate = 45,
        .expYield = 114,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 40,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_GUTS,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SAND-VEIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-246LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 3, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE( 6, MOVE_PAYBACK),
    LEVEL_UP_MOVE( 9, MOVE_BITE),
    LEVEL_UP_MOVE(12, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(15, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(18, MOVE_STOMPING_TANTRUM),
    LEVEL_UP_MOVE(21, MOVE_SCREECH),
    LEVEL_UP_MOVE(24, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE(27, MOVE_CRUNCH),
    LEVEL_UP_MOVE(31, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(33, MOVE_STONE_EDGE),
    LEVEL_UP_MOVE(36, MOVE_THRASH),
    LEVEL_UP_MOVE(39, MOVE_SANDSTORM),
    LEVEL_UP_MOVE(42, MOVE_HYPER_BEAM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 300
// Types: TYPE_ROCK / TYPE_GROUND
// Abilities: ABILITY_GUTS, ABILITY_NONE, ABILITY_SAND-VEIL
// Level Up Moves: 16
// Generation: 9

