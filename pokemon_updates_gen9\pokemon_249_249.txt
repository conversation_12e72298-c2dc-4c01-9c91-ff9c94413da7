// POKEMON_249 (#249) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_249] =
    {
        .baseHP = 106,
        .baseAttack = 90,
        .baseDefense = 130,
        .baseSpAttack = 90,
        .baseSpDefense = 154,
        .baseSpeed = 110,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_FLYING,
        .catchRate = 3,
        .expYield = 196,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PRESSURE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_MULTISCALE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-249LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 1, MOVE_WEATHER_BALL),
    LEVEL_UP_MOVE( 1, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE( 9, MOVE_MIST),
    LEVEL_UP_MOVE(18, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(27, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(36, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE(45, MOVE_RECOVER),
    LEVEL_UP_MOVE(54, MOVE_AEROBLAST),
    LEVEL_UP_MOVE(63, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(72, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(81, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(90, MOVE_SKY_ATTACK),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 680
// Types: TYPE_PSYCHIC / TYPE_FLYING
// Abilities: ABILITY_PRESSURE, ABILITY_NONE, ABILITY_MULTISCALE
// Level Up Moves: 14
// Generation: 9

