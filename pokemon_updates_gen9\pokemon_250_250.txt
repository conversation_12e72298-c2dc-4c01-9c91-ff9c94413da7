// POKEMON_250 (#250) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_250] =
    {
        .baseHP = 106,
        .baseAttack = 130,
        .baseDefense = 90,
        .baseSpAttack = 110,
        .baseSpDefense = 154,
        .baseSpeed = 90,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FLYING,
        .catchRate = 3,
        .expYield = 236,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PRESSURE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_REGENERATOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-250LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 1, MOVE_WEATHER_BALL),
    LEVEL_UP_MOVE( 1, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE( 9, MOVE_LIFE_DEW),
    LEVEL_UP_MOVE(18, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(27, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(36, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE(45, MOVE_RECOVER),
    LEVEL_UP_MOVE(54, MOVE_SACRED_FIRE),
    LEVEL_UP_MOVE(63, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE(72, MOVE_FIRE_BLAST),
    LEVEL_UP_MOVE(81, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(90, MOVE_SKY_ATTACK),
    LEVEL_UP_MOVE(99, MOVE_OVERHEAT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 680
// Types: TYPE_FIRE / TYPE_FLYING
// Abilities: ABILITY_PRESSURE, ABILITY_NONE, ABILITY_REGENERATOR
// Level Up Moves: 15
// Generation: 9

