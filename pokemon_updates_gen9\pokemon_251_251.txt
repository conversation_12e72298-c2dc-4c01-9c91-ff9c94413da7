// POKEMON_251 (#251) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_251] =
    {
        .baseHP = 100,
        .baseAttack = 100,
        .baseDefense = 100,
        .baseSpAttack = 100,
        .baseSpDefense = 100,
        .baseSpeed = 100,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_GRASS,
        .catchRate = 45,
        .expYield = 200,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 100,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_NATURAL-CURE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-251LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_HEAL_BELL),
    LEVEL_UP_MOVE(10, MOVE_MAGICAL_LEAF),
    LEVEL_UP_MOVE(20, MOVE_BATON_PASS),
    LEVEL_UP_MOVE(30, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(40, MOVE_LIFE_DEW),
    LEVEL_UP_MOVE(50, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(60, MOVE_RECOVER),
    LEVEL_UP_MOVE(70, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(80, MOVE_HEALING_WISH),
    LEVEL_UP_MOVE(90, MOVE_LEAF_STORM),
    LEVEL_UP_MOVE(100, MOVE_PERISH_SONG),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_PSYCHIC / TYPE_GRASS
// Abilities: ABILITY_NATURAL-CURE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 12
// Generation: 8

