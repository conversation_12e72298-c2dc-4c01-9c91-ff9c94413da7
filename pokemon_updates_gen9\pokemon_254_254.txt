// POKEMON_254 (#254) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_254] =
    {
        .baseHP = 70,
        .baseAttack = 85,
        .baseDefense = 65,
        .baseSpAttack = 105,
        .baseSpDefense = 85,
        .baseSpeed = 120,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 45,
        .expYield = 155,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_OVERGROW,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_UNBURDEN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-254LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_LEAF_BLADE),
    LEVEL_UP_MOVE( 1, MOVE_LEAFAGE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 5, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(12, MOVE_DETECT),
    LEVEL_UP_MOVE(15, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(20, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(25, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(30, MOVE_SLAM),
    LEVEL_UP_MOVE(35, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(42, MOVE_SCREECH),
    LEVEL_UP_MOVE(49, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(56, MOVE_LEAF_STORM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 530
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_OVERGROW, ABILITY_NONE, ABILITY_UNBURDEN
// Level Up Moves: 15
// Generation: 9

