// POKEMON_258 (#258) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_258] =
    {
        .baseHP = 50,
        .baseAttack = 70,
        .baseDefense = 50,
        .baseSpAttack = 50,
        .baseSpDefense = 50,
        .baseSpeed = 40,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 45,
        .expYield = 120,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_TORRENT,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_DAMP,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-258LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 3, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 6, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE( 9, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE(12, MOVE_PROTECT),
    LEVEL_UP_MOVE(15, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(18, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(21, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(24, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(27, MOVE_AMNESIA),
    LEVEL_UP_MOVE(30, MOVE_SURF),
    LEVEL_UP_MOVE(33, MOVE_SCREECH),
    LEVEL_UP_MOVE(36, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(39, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 310
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_TORRENT, ABILITY_NONE, ABILITY_DAMP
// Level Up Moves: 15
// Generation: 9

