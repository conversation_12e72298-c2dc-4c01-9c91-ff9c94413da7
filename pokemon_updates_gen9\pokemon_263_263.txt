// POKEMON_263 (#263) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_263] =
    {
        .baseHP = 38,
        .baseAttack = 30,
        .baseDefense = 41,
        .baseSpAttack = 30,
        .baseSpDefense = 41,
        .baseSpeed = 60,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 255,
        .expYield = 68,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PICKUP,
        .ability2 = ABILITY_GLUTTONY,
        .hiddenAbility = ABILITY_QUICK-FEET,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-263LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 3, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 6, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 9, MOVE_COVET),
    LEVEL_UP_MOVE(12, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(15, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(18, MOVE_PIN_MISSILE),
    LEVEL_UP_MOVE(21, MOVE_REST),
    LEVEL_UP_MOVE(24, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(27, MOVE_FLING),
    LEVEL_UP_MOVE(30, MOVE_FLAIL),
    LEVEL_UP_MOVE(33, MOVE_BELLY_DRUM),
    LEVEL_UP_MOVE(36, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 240
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_PICKUP, ABILITY_GLUTTONY, ABILITY_QUICK-FEET
// Level Up Moves: 14
// Generation: 8

