// POKEMON_270 (#270) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_270] =
    {
        .baseHP = 40,
        .baseAttack = 30,
        .baseDefense = 30,
        .baseSpAttack = 40,
        .baseSpDefense = 50,
        .baseSpeed = 30,
        .type1 = TYPE_WATER,
        .type2 = TYPE_GRASS,
        .catchRate = 255,
        .expYield = 70,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SWIFT-SWIM,
        .ability2 = ABILITY_RAIN-DISH,
        .hiddenAbility = ABILITY_OWN-TEMPO,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-270LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 3, MOVE_ABSORB),
    LEVEL_UP_MOVE( 6, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 9, MOVE_MIST),
    LEVEL_UP_MOVE(12, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(16, MOVE_FLAIL),
    LEVEL_UP_MOVE(20, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(24, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(28, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(33, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(38, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(43, MOVE_ENERGY_BALL),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 220
// Types: TYPE_WATER / TYPE_GRASS
// Abilities: ABILITY_SWIFT-SWIM, ABILITY_RAIN-DISH, ABILITY_OWN-TEMPO
// Level Up Moves: 13
// Generation: 9

