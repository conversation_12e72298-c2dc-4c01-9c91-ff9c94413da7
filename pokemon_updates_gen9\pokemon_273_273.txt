// POKEMON_273 (#273) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_273] =
    {
        .baseHP = 40,
        .baseAttack = 40,
        .baseDefense = 50,
        .baseSpAttack = 30,
        .baseSpDefense = 30,
        .baseSpeed = 30,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 255,
        .expYield = 80,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_CHLOROPHYLL,
        .ability2 = ABILITY_EARLY-BIRD,
        .hiddenAbility = ABILITY_PICKPOCKET,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-273LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 3, MOVE_ABSORB),
    LEVEL_UP_MOVE( 6, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 9, MOVE_GROWTH),
    LEVEL_UP_MOVE(12, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(15, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(18, MOVE_PAYBACK),
    LEVEL_UP_MOVE(21, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(24, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE(27, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(30, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(33, MOVE_EXPLOSION),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 220
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_CHLOROPHYLL, ABILITY_EARLY-BIRD, ABILITY_PICKPOCKET
// Level Up Moves: 13
// Generation: 9

