// POKEMON_280 (#280) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_280] =
    {
        .baseHP = 28,
        .baseAttack = 25,
        .baseDefense = 25,
        .baseSpAttack = 45,
        .baseSpDefense = 35,
        .baseSpeed = 40,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_FAIRY,
        .catchRate = 235,
        .expYield = 53,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SYNCHRONIZE,
        .ability2 = ABILITY_TRACE,
        .hiddenAbility = ABILITY_TELEPATHY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-280LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DISARMING_VOICE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 3, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE( 6, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 9, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE(12, MOVE_DRAINING_KISS),
    LEVEL_UP_MOVE(15, MOVE_TELEPORT),
    LEVEL_UP_MOVE(18, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(21, MOVE_LIFE_DEW),
    LEVEL_UP_MOVE(24, MOVE_CHARM),
    LEVEL_UP_MOVE(27, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(30, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(33, MOVE_HEAL_PULSE),
    LEVEL_UP_MOVE(36, MOVE_DREAM_EATER),
    LEVEL_UP_MOVE(39, MOVE_FUTURE_SIGHT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 198
// Types: TYPE_PSYCHIC / TYPE_FAIRY
// Abilities: ABILITY_SYNCHRONIZE, ABILITY_TRACE, ABILITY_TELEPATHY
// Level Up Moves: 15
// Generation: 9

