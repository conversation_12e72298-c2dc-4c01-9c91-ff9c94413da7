// POKEMON_281 (#281) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_281] =
    {
        .baseHP = 38,
        .baseAttack = 35,
        .baseDefense = 35,
        .baseSpAttack = 65,
        .baseSpDefense = 55,
        .baseSpeed = 50,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_FAIRY,
        .catchRate = 120,
        .expYield = 73,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SYNCHRONIZE,
        .ability2 = ABILITY_TRACE,
        .hiddenAbility = ABILITY_TELEPATHY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-281LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_DISARMING_VOICE),
    LEVEL_UP_MOVE( 1, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 9, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE(12, MOVE_DRAINING_KISS),
    LEVEL_UP_MOVE(15, MOVE_TELEPORT),
    LEVEL_UP_MOVE(18, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(23, MOVE_LIFE_DEW),
    LEVEL_UP_MOVE(28, MOVE_CHARM),
    LEVEL_UP_MOVE(33, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(38, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(43, MOVE_HEAL_PULSE),
    LEVEL_UP_MOVE(48, MOVE_DREAM_EATER),
    LEVEL_UP_MOVE(53, MOVE_FUTURE_SIGHT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 278
// Types: TYPE_PSYCHIC / TYPE_FAIRY
// Abilities: ABILITY_SYNCHRONIZE, ABILITY_TRACE, ABILITY_TELEPATHY
// Level Up Moves: 15
// Generation: 9

