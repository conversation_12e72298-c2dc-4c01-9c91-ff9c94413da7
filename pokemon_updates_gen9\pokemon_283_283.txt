// POKEMON_283 (#283) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_283] =
    {
        .baseHP = 40,
        .baseAttack = 30,
        .baseDefense = 32,
        .baseSpAttack = 50,
        .baseSpDefense = 52,
        .baseSpeed = 65,
        .type1 = TYPE_BUG,
        .type2 = TYPE_WATER,
        .catchRate = 200,
        .expYield = 70,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SWIFT-SWIM,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_RAIN-DISH,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-283LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 6, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 9, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(14, MOVE_SOAK),
    LEVEL_UP_MOVE(17, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(22, MOVE_AGILITY),
    LEVEL_UP_MOVE(25, MOVE_HAZE),
    LEVEL_UP_MOVE(25, MOVE_MIST),
    LEVEL_UP_MOVE(35, MOVE_BATON_PASS),
    LEVEL_UP_MOVE(38, MOVE_STICKY_WEB),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 269
// Types: TYPE_BUG / TYPE_WATER
// Abilities: ABILITY_SWIFT-SWIM, ABILITY_NONE, ABILITY_RAIN-DISH
// Level Up Moves: 10
// Generation: 9

