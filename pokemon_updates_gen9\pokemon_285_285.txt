// POKEMON_285 (#285) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_285] =
    {
        .baseHP = 60,
        .baseAttack = 40,
        .baseDefense = 60,
        .baseSpAttack = 40,
        .baseSpDefense = 60,
        .baseSpeed = 35,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 255,
        .expYield = 100,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_EFFECT-SPORE,
        .ability2 = ABILITY_POISON-HEAL,
        .hiddenAbility = ABILITY_QUICK-FEET,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-285LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE( 8, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(12, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(15, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(19, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE(26, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(29, MOVE_GROWTH),
    LEVEL_UP_MOVE(33, MOVE_TOXIC),
    LEVEL_UP_MOVE(36, MOVE_SEED_BOMB),
    LEVEL_UP_MOVE(40, MOVE_SPORE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 295
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_EFFECT-SPORE, ABILITY_POISON-HEAL, ABILITY_QUICK-FEET
// Level Up Moves: 12
// Generation: 9

