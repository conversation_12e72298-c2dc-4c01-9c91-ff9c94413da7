// POKEMON_292 (#292) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_292] =
    {
        .baseHP = 1,
        .baseAttack = 90,
        .baseDefense = 45,
        .baseSpAttack = 30,
        .baseSpDefense = 30,
        .baseSpeed = 40,
        .type1 = TYPE_BUG,
        .type2 = TYPE_GHOST,
        .catchRate = 45,
        .expYield = 91,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_WONDER-GUARD,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-292LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DIG),
    LEVEL_UP_MOVE( 1, MOVE_FALSE_SWIPE),
    LEVEL_UP_MOVE( 1, MOVE_GRUDGE),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_SHADOW_CLAW),
    LEVEL_UP_MOVE(15, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(23, MOVE_ABSORB),
    LEVEL_UP_MOVE(29, MOVE_SHADOW_SNEAK),
    LEVEL_UP_MOVE(36, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(43, MOVE_MIND_READER),
    LEVEL_UP_MOVE(50, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(57, MOVE_SPITE),
    LEVEL_UP_MOVE(64, MOVE_PHANTOM_FORCE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 236
// Types: TYPE_BUG / TYPE_GHOST
// Abilities: ABILITY_WONDER-GUARD, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 17
// Generation: 8

