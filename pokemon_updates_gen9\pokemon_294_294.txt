// POKEMON_294 (#294) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_294] =
    {
        .baseHP = 84,
        .baseAttack = 71,
        .baseDefense = 43,
        .baseSpAttack = 71,
        .baseSpDefense = 43,
        .baseSpeed = 48,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 120,
        .expYield = 155,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SOUNDPROOF,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SCRAPPY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-294LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_ECHOED_VOICE),
    LEVEL_UP_MOVE( 1, MOVE_HOWL),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE(15, MOVE_REST),
    LEVEL_UP_MOVE(15, MOVE_SLEEP_TALK),
    LEVEL_UP_MOVE(23, MOVE_STOMP),
    LEVEL_UP_MOVE(29, MOVE_ROAR),
    LEVEL_UP_MOVE(36, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(43, MOVE_UPROAR),
    LEVEL_UP_MOVE(50, MOVE_SCREECH),
    LEVEL_UP_MOVE(57, MOVE_HYPER_VOICE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 360
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_SOUNDPROOF, ABILITY_NONE, ABILITY_SCRAPPY
// Level Up Moves: 14
// Generation: 8

