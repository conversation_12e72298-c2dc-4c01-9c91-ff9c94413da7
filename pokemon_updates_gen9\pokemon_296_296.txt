// POKEMON_296 (#296) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_296] =
    {
        .baseHP = 72,
        .baseAttack = 60,
        .baseDefense = 30,
        .baseSpAttack = 20,
        .baseSpDefense = 30,
        .baseSpeed = 25,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 180,
        .expYield = 132,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(25.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_THICK-FAT,
        .ability2 = ABILITY_GUTS,
        .hiddenAbility = ABILITY_SHEER-FORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-296LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 4, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 7, MOVE_ARM_THRUST),
    LEVEL_UP_MOVE(10, MOVE_FAKE_OUT),
    LEVEL_UP_MOVE(13, MOVE_FORCE_PALM),
    LEVEL_UP_MOVE(16, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(19, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(22, MOVE_BULK_UP),
    LEVEL_UP_MOVE(25, MOVE_BELLY_DRUM),
    LEVEL_UP_MOVE(28, MOVE_DETECT),
    LEVEL_UP_MOVE(31, MOVE_SEISMIC_TOSS),
    LEVEL_UP_MOVE(34, MOVE_FOCUS_PUNCH),
    LEVEL_UP_MOVE(37, MOVE_ENDURE),
    LEVEL_UP_MOVE(40, MOVE_CLOSE_COMBAT),
    LEVEL_UP_MOVE(43, MOVE_REVERSAL),
    LEVEL_UP_MOVE(46, MOVE_HEAVY_SLAM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 237
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_THICK-FAT, ABILITY_GUTS, ABILITY_SHEER-FORCE
// Level Up Moves: 17
// Generation: 9

