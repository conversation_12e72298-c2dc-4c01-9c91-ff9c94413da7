// POKEMON_298 (#298) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_298] =
    {
        .baseHP = 50,
        .baseAttack = 20,
        .baseDefense = 40,
        .baseSpAttack = 20,
        .baseSpDefense = 40,
        .baseSpeed = 20,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FAIRY,
        .catchRate = 150,
        .expYield = 70,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(75.0),
        .eggCycles = 10,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_THICK-FAT,
        .ability2 = ABILITY_HUGE-POWER,
        .hiddenAbility = ABILITY_SAP-SIPPER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-298LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SPLASH),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 3, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 6, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE( 9, MOVE_CHARM),
    LEVEL_UP_MOVE(12, MOVE_SLAM),
    LEVEL_UP_MOVE(15, MOVE_BOUNCE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 190
// Types: TYPE_NORMAL / TYPE_FAIRY
// Abilities: ABILITY_THICK-FAT, ABILITY_HUGE-POWER, ABILITY_SAP-SIPPER
// Level Up Moves: 8
// Generation: 9

