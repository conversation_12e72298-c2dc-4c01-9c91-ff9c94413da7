// POKEMON_303 (#303) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_303] =
    {
        .baseHP = 50,
        .baseAttack = 85,
        .baseDefense = 85,
        .baseSpAttack = 55,
        .baseSpDefense = 55,
        .baseSpeed = 50,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_FAIRY,
        .catchRate = 45,
        .expYield = 135,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_HYPER-CUTTER,
        .ability2 = ABILITY_INTIMIDATE,
        .hiddenAbility = ABILITY_SHEER-FORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-303LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 4, MOVE_FAIRY_WIND),
    LEVEL_UP_MOVE( 8, MOVE_BATON_PASS),
    LEVEL_UP_MOVE(12, MOVE_BITE),
    LEVEL_UP_MOVE(16, MOVE_SPIT_UP),
    LEVEL_UP_MOVE(16, MOVE_STOCKPILE),
    LEVEL_UP_MOVE(16, MOVE_SWALLOW),
    LEVEL_UP_MOVE(20, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(24, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(28, MOVE_CRUNCH),
    LEVEL_UP_MOVE(32, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(36, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(40, MOVE_TAUNT),
    LEVEL_UP_MOVE(44, MOVE_FAKE_TEARS),
    LEVEL_UP_MOVE(48, MOVE_PLAY_ROUGH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 380
// Types: TYPE_STEEL / TYPE_FAIRY
// Abilities: ABILITY_HYPER-CUTTER, ABILITY_INTIMIDATE, ABILITY_SHEER-FORCE
// Level Up Moves: 16
// Generation: 8

