// POKEMON_304 (#304) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_304] =
    {
        .baseHP = 50,
        .baseAttack = 70,
        .baseDefense = 100,
        .baseSpAttack = 40,
        .baseSpDefense = 40,
        .baseSpeed = 30,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_ROCK,
        .catchRate = 180,
        .expYield = 120,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 35,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STURDY,
        .ability2 = ABILITY_ROCK-HEAD,
        .hiddenAbility = ABILITY_HEAVY-METAL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-304LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 4, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE( 8, MOVE_ROCK_TOMB),
    LEVEL_UP_MOVE(12, MOVE_ROAR),
    LEVEL_UP_MOVE(16, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(20, MOVE_PROTECT),
    LEVEL_UP_MOVE(24, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(28, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(33, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(36, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(40, MOVE_AUTOTOMIZE),
    LEVEL_UP_MOVE(44, MOVE_IRON_TAIL),
    LEVEL_UP_MOVE(48, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(52, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(56, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(60, MOVE_METAL_BURST),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 330
// Types: TYPE_STEEL / TYPE_ROCK
// Abilities: ABILITY_STURDY, ABILITY_ROCK-HEAD, ABILITY_HEAVY-METAL
// Level Up Moves: 17
// Generation: 8

