// POKEMON_310 (#310) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_310] =
    {
        .baseHP = 70,
        .baseAttack = 75,
        .baseDefense = 60,
        .baseSpAttack = 105,
        .baseSpDefense = 60,
        .baseSpeed = 105,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 45,
        .expYield = 145,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STATIC,
        .ability2 = ABILITY_LIGHTNING-ROD,
        .hiddenAbility = ABILITY_MINUS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-310LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE( 1, MOVE_HOWL),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE(12, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(16, MOVE_SHOCK_WAVE),
    LEVEL_UP_MOVE(20, MOVE_BITE),
    LEVEL_UP_MOVE(24, MOVE_THUNDER_FANG),
    LEVEL_UP_MOVE(30, MOVE_ROAR),
    LEVEL_UP_MOVE(36, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(42, MOVE_CHARGE),
    LEVEL_UP_MOVE(48, MOVE_WILD_CHARGE),
    LEVEL_UP_MOVE(54, MOVE_THUNDER),
    LEVEL_UP_MOVE(60, MOVE_ELECTRIC_TERRAIN),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 475
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_STATIC, ABILITY_LIGHTNING-ROD, ABILITY_MINUS
// Level Up Moves: 15
// Generation: 8

