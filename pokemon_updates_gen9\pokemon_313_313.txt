// POKEMON_313 (#313) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_313] =
    {
        .baseHP = 65,
        .baseAttack = 73,
        .baseDefense = 75,
        .baseSpAttack = 47,
        .baseSpDefense = 85,
        .baseSpeed = 85,
        .type1 = TYPE_BUG,
        .type2 = TYPE_BUG,
        .catchRate = 150,
        .expYield = 138,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(0.0),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_ILLUMINATE,
        .ability2 = ABILITY_SWARM,
        .hiddenAbility = ABILITY_PRANKSTER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-313LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE( 8, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(12, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(15, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE(19, MOVE_MOONLIGHT),
    LEVEL_UP_MOVE(22, MOVE_TAIL_GLOW),
    LEVEL_UP_MOVE(26, MOVE_PROTECT),
    LEVEL_UP_MOVE(29, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(33, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(36, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(40, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(43, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(47, MOVE_INFESTATION),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 430
// Types: TYPE_BUG / TYPE_BUG
// Abilities: ABILITY_ILLUMINATE, ABILITY_SWARM, ABILITY_PRANKSTER
// Level Up Moves: 14
// Generation: 9

