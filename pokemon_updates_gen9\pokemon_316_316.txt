// POKEMON_316 (#316) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_316] =
    {
        .baseHP = 70,
        .baseAttack = 43,
        .baseDefense = 53,
        .baseSpAttack = 43,
        .baseSpDefense = 53,
        .baseSpeed = 40,
        .type1 = TYPE_POISON,
        .type2 = TYPE_POISON,
        .catchRate = 225,
        .expYield = 113,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_LIQUID-OOZE,
        .ability2 = ABILITY_STICKY-HOLD,
        .hiddenAbility = ABILITY_GLUTTONY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-316LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 5, MOVE_YAWN),
    LEVEL_UP_MOVE( 8, MOVE_POISON_GAS),
    LEVEL_UP_MOVE(10, MOVE_SLUDGE),
    LEVEL_UP_MOVE(12, MOVE_AMNESIA),
    LEVEL_UP_MOVE(17, MOVE_ACID_SPRAY),
    LEVEL_UP_MOVE(20, MOVE_ENCORE),
    LEVEL_UP_MOVE(25, MOVE_TOXIC),
    LEVEL_UP_MOVE(28, MOVE_SPIT_UP),
    LEVEL_UP_MOVE(28, MOVE_STOCKPILE),
    LEVEL_UP_MOVE(28, MOVE_SWALLOW),
    LEVEL_UP_MOVE(33, MOVE_SLUDGE_BOMB),
    LEVEL_UP_MOVE(36, MOVE_GASTRO_ACID),
    LEVEL_UP_MOVE(41, MOVE_BELCH),
    LEVEL_UP_MOVE(44, MOVE_PAIN_SPLIT),
    LEVEL_UP_MOVE(49, MOVE_GUNK_SHOT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 302
// Types: TYPE_POISON / TYPE_POISON
// Abilities: ABILITY_LIQUID-OOZE, ABILITY_STICKY-HOLD, ABILITY_GLUTTONY
// Level Up Moves: 16
// Generation: 9

