// POKEMON_318 (#318) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_318] =
    {
        .baseHP = 45,
        .baseAttack = 90,
        .baseDefense = 20,
        .baseSpAttack = 65,
        .baseSpDefense = 20,
        .baseSpeed = 65,
        .type1 = TYPE_WATER,
        .type2 = TYPE_DARK,
        .catchRate = 225,
        .expYield = 135,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_ROUGH-SKIN,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SPEED-BOOST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-318LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_AQUA_JET),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 4, MOVE_POISON_FANG),
    LEVEL_UP_MOVE( 8, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(12, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(16, MOVE_BITE),
    LEVEL_UP_MOVE(20, MOVE_ICE_FANG),
    LEVEL_UP_MOVE(24, MOVE_SCREECH),
    LEVEL_UP_MOVE(28, MOVE_SWAGGER),
    LEVEL_UP_MOVE(32, MOVE_CRUNCH),
    LEVEL_UP_MOVE(36, MOVE_AGILITY),
    LEVEL_UP_MOVE(40, MOVE_LIQUIDATION),
    LEVEL_UP_MOVE(44, MOVE_TAKE_DOWN),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 305
// Types: TYPE_WATER / TYPE_DARK
// Abilities: ABILITY_ROUGH-SKIN, ABILITY_NONE, ABILITY_SPEED-BOOST
// Level Up Moves: 13
// Generation: 8

