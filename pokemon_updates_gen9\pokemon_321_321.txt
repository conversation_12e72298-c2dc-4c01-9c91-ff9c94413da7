// POKEMON_321 (#321) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_321] =
    {
        .baseHP = 170,
        .baseAttack = 90,
        .baseDefense = 45,
        .baseSpAttack = 90,
        .baseSpDefense = 45,
        .baseSpeed = 60,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 60,
        .expYield = 255,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 40,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_WATER-VEIL,
        .ability2 = ABILITY_OBLIVIOUS,
        .hiddenAbility = ABILITY_PRESSURE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-321LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_NOBLE_ROAR),
    LEVEL_UP_MOVE( 1, MOVE_SOAK),
    LEVEL_UP_MOVE( 1, MOVE_SPLASH),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(15, MOVE_MIST),
    LEVEL_UP_MOVE(18, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(21, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(24, MOVE_BRINE),
    LEVEL_UP_MOVE(27, MOVE_WHIRLPOOL),
    LEVEL_UP_MOVE(30, MOVE_DIVE),
    LEVEL_UP_MOVE(33, MOVE_BOUNCE),
    LEVEL_UP_MOVE(36, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(39, MOVE_REST),
    LEVEL_UP_MOVE(44, MOVE_AMNESIA),
    LEVEL_UP_MOVE(49, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(54, MOVE_WATER_SPOUT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_WATER-VEIL, ABILITY_OBLIVIOUS, ABILITY_PRESSURE
// Level Up Moves: 18
// Generation: 8

