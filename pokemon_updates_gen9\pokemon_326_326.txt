// POKEMON_326 (#326) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_326] =
    {
        .baseHP = 80,
        .baseAttack = 45,
        .baseDefense = 65,
        .baseSpAttack = 90,
        .baseSpDefense = 110,
        .baseSpeed = 80,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 60,
        .expYield = 125,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_THICK-FAT,
        .ability2 = ABILITY_OWN-TEMPO,
        .hiddenAbility = ABILITY_GLUTTONY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-326LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_TEETER_DANCE),
    LEVEL_UP_MOVE( 1, MOVE_BELCH),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_PSYBEAM),
    LEVEL_UP_MOVE( 1, MOVE_SPLASH),
    LEVEL_UP_MOVE(18, MOVE_PSYCH_UP),
    LEVEL_UP_MOVE(22, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(26, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(29, MOVE_POWER_GEM),
    LEVEL_UP_MOVE(35, MOVE_REST),
    LEVEL_UP_MOVE(35, MOVE_SNORE),
    LEVEL_UP_MOVE(42, MOVE_PSYSHOCK),
    LEVEL_UP_MOVE(46, MOVE_PAYBACK),
    LEVEL_UP_MOVE(52, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(60, MOVE_BOUNCE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 470
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_THICK-FAT, ABILITY_OWN-TEMPO, ABILITY_GLUTTONY
// Level Up Moves: 15
// Generation: 9

