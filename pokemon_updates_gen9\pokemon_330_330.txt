// POKEMON_330 (#330) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_330] =
    {
        .baseHP = 80,
        .baseAttack = 100,
        .baseDefense = 80,
        .baseSpAttack = 80,
        .baseSpDefense = 80,
        .baseSpeed = 100,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 180,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-330LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE( 1, MOVE_BULLDOZE),
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(12, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE(16, MOVE_SAND_TOMB),
    LEVEL_UP_MOVE(20, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE(24, MOVE_SCREECH),
    LEVEL_UP_MOVE(28, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(32, MOVE_SANDSTORM),
    LEVEL_UP_MOVE(38, MOVE_EARTH_POWER),
    LEVEL_UP_MOVE(44, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(52, MOVE_UPROAR),
    LEVEL_UP_MOVE(60, MOVE_DRAGON_RUSH),
    LEVEL_UP_MOVE(68, MOVE_BOOMBURST),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 520
// Types: TYPE_GROUND / TYPE_DRAGON
// Abilities: ABILITY_LEVITATE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
// Generation: 9

