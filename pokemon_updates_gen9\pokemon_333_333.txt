// POKEMON_333 (#333) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_333] =
    {
        .baseHP = 45,
        .baseAttack = 40,
        .baseDefense = 60,
        .baseSpAttack = 40,
        .baseSpDefense = 75,
        .baseSpeed = 50,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 255,
        .expYield = 85,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_NATURAL-CURE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_CLOUD-NINE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-333LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 4, MOVE_DISARMING_VOICE),
    LEVEL_UP_MOVE( 8, MOVE_MIST),
    LEVEL_UP_MOVE(12, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(16, MOVE_ROUND),
    LEVEL_UP_MOVE(20, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(24, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(28, MOVE_SING),
    LEVEL_UP_MOVE(32, MOVE_COTTON_GUARD),
    LEVEL_UP_MOVE(36, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(40, MOVE_MOONBLAST),
    LEVEL_UP_MOVE(44, MOVE_PERISH_SONG),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 310
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_NATURAL-CURE, ABILITY_NONE, ABILITY_CLOUD-NINE
// Level Up Moves: 13
// Generation: 9

