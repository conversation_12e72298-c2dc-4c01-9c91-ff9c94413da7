// POKEMON_335 (#335) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_335] =
    {
        .baseHP = 73,
        .baseAttack = 115,
        .baseDefense = 60,
        .baseSpAttack = 60,
        .baseSpDefense = 60,
        .baseSpeed = 90,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 90,
        .expYield = 188,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_IMMUNITY,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_TOXIC-BOOST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-335LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 5, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 8, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE(12, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(15, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(19, MOVE_SLASH),
    LEVEL_UP_MOVE(22, MOVE_POWER_TRIP),
    LEVEL_UP_MOVE(26, MOVE_CRUSH_CLAW),
    LEVEL_UP_MOVE(29, MOVE_FALSE_SWIPE),
    LEVEL_UP_MOVE(33, MOVE_SWITCHEROO),
    LEVEL_UP_MOVE(36, MOVE_DETECT),
    LEVEL_UP_MOVE(40, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(43, MOVE_TAUNT),
    LEVEL_UP_MOVE(47, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(50, MOVE_CLOSE_COMBAT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 458
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_IMMUNITY, ABILITY_NONE, ABILITY_TOXIC-BOOST
// Level Up Moves: 16
// Generation: 9

