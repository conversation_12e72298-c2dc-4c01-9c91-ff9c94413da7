// POKEMON_337 (#337) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_337] =
    {
        .baseHP = 90,
        .baseAttack = 55,
        .baseDefense = 65,
        .baseSpAttack = 95,
        .baseSpDefense = 85,
        .baseSpeed = 70,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 45,
        .expYield = 145,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-337LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_MOONBLAST),
    LEVEL_UP_MOVE( 1, MOVE_MOONLIGHT),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE(10, MOVE_ROCK_POLISH),
    LEVEL_UP_MOVE(15, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(20, MOVE_PSYSHOCK),
    LEVEL_UP_MOVE(25, MOVE_COSMIC_POWER),
    LEVEL_UP_MOVE(30, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(35, MOVE_STONE_EDGE),
    LEVEL_UP_MOVE(40, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(45, MOVE_MAGIC_ROOM),
    LEVEL_UP_MOVE(50, MOVE_EXPLOSION),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 460
// Types: TYPE_ROCK / TYPE_PSYCHIC
// Abilities: ABILITY_LEVITATE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
// Generation: 8

