// POKEMON_339 (#339) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_339] =
    {
        .baseHP = 50,
        .baseAttack = 48,
        .baseDefense = 43,
        .baseSpAttack = 46,
        .baseSpDefense = 41,
        .baseSpeed = 60,
        .type1 = TYPE_WATER,
        .type2 = TYPE_GROUND,
        .catchRate = 190,
        .expYield = 98,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_OBLIVIOUS,
        .ability2 = ABILITY_ANTICIPATION,
        .hiddenAbility = ABILITY_HYDRATION,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-339LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 6, MOVE_REST),
    LEVEL_UP_MOVE( 6, MOVE_SNORE),
    LEVEL_UP_MOVE(12, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(18, MOVE_AMNESIA),
    LEVEL_UP_MOVE(24, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(31, MOVE_MUDDY_WATER),
    LEVEL_UP_MOVE(36, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(42, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(48, MOVE_FISSURE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 288
// Types: TYPE_WATER / TYPE_GROUND
// Abilities: ABILITY_OBLIVIOUS, ABILITY_ANTICIPATION, ABILITY_HYDRATION
// Level Up Moves: 11
// Generation: 9

