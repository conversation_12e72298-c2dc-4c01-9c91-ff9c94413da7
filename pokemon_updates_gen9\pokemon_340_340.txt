// POKEMON_340 (#340) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_340] =
    {
        .baseHP = 110,
        .baseAttack = 78,
        .baseDefense = 73,
        .baseSpAttack = 76,
        .baseSpDefense = 71,
        .baseSpeed = 60,
        .type1 = TYPE_WATER,
        .type2 = TYPE_GROUND,
        .catchRate = 75,
        .expYield = 188,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_OBLIVIOUS,
        .ability2 = ABILITY_ANTICIPATION,
        .hiddenAbility = ABILITY_HYDRATION,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-340LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_THRASH),
    LEVEL_UP_MOVE( 1, MOVE_BELCH),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_REST),
    LEVEL_UP_MOVE( 1, MOVE_SNORE),
    LEVEL_UP_MOVE( 1, MOVE_TICKLE),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(12, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(18, MOVE_AMNESIA),
    LEVEL_UP_MOVE(24, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(33, MOVE_MUDDY_WATER),
    LEVEL_UP_MOVE(40, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(48, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(56, MOVE_FISSURE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 468
// Types: TYPE_WATER / TYPE_GROUND
// Abilities: ABILITY_OBLIVIOUS, ABILITY_ANTICIPATION, ABILITY_HYDRATION
// Level Up Moves: 15
// Generation: 9

