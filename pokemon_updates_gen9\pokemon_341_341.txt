// POKEMON_341 (#341) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_341] =
    {
        .baseHP = 43,
        .baseAttack = 80,
        .baseDefense = 65,
        .baseSpAttack = 50,
        .baseSpDefense = 35,
        .baseSpeed = 35,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 205,
        .expYield = 123,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_HYPER-CUTTER,
        .ability2 = ABILITY_SHELL-ARMOR,
        .hiddenAbility = ABILITY_ADAPTABILITY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-341LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 4, MOVE_LEER),
    LEVEL_UP_MOVE( 8, MOVE_TAUNT),
    LEVEL_UP_MOVE(12, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(16, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(20, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE(24, MOVE_PROTECT),
    LEVEL_UP_MOVE(28, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(32, MOVE_RAZOR_SHELL),
    LEVEL_UP_MOVE(36, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(40, MOVE_CRUNCH),
    LEVEL_UP_MOVE(44, MOVE_CRABHAMMER),
    LEVEL_UP_MOVE(48, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(52, MOVE_GUILLOTINE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 308
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_HYPER-CUTTER, ABILITY_SHELL-ARMOR, ABILITY_ADAPTABILITY
// Level Up Moves: 15
// Generation: 9

