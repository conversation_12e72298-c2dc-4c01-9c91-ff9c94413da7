// POKEMON_345 (#345) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_345] =
    {
        .baseHP = 66,
        .baseAttack = 41,
        .baseDefense = 77,
        .baseSpAttack = 61,
        .baseSpDefense = 87,
        .baseSpeed = 23,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_GRASS,
        .catchRate = 45,
        .expYield = 107,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SUCTION-CUPS,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_STORM-DRAIN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-345LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_WRAP),
    LEVEL_UP_MOVE( 4, MOVE_ACID),
    LEVEL_UP_MOVE( 8, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(12, MOVE_INGRAIN),
    LEVEL_UP_MOVE(16, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(20, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(24, MOVE_BRINE),
    LEVEL_UP_MOVE(28, MOVE_AMNESIA),
    LEVEL_UP_MOVE(32, MOVE_GASTRO_ACID),
    LEVEL_UP_MOVE(36, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(41, MOVE_SPIT_UP),
    LEVEL_UP_MOVE(41, MOVE_STOCKPILE),
    LEVEL_UP_MOVE(41, MOVE_SWALLOW),
    LEVEL_UP_MOVE(44, MOVE_ENERGY_BALL),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 355
// Types: TYPE_ROCK / TYPE_GRASS
// Abilities: ABILITY_SUCTION-CUPS, ABILITY_NONE, ABILITY_STORM-DRAIN
// Level Up Moves: 15
// Generation: 8

