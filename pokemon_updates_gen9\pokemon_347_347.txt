// POKEMON_347 (#347) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_347] =
    {
        .baseHP = 45,
        .baseAttack = 95,
        .baseDefense = 50,
        .baseSpAttack = 40,
        .baseSpDefense = 50,
        .baseSpeed = 75,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_BUG,
        .catchRate = 45,
        .expYield = 140,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BATTLE-ARMOR,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SWIFT-SWIM,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-347LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 4, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 8, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE(12, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(16, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(20, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(24, MOVE_BRINE),
    LEVEL_UP_MOVE(28, MOVE_SLASH),
    LEVEL_UP_MOVE(32, MOVE_CRUSH_CLAW),
    LEVEL_UP_MOVE(36, MOVE_ROCK_BLAST),
    LEVEL_UP_MOVE(41, MOVE_PROTECT),
    LEVEL_UP_MOVE(44, MOVE_X_SCISSOR),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 355
// Types: TYPE_ROCK / TYPE_BUG
// Abilities: ABILITY_BATTLE-ARMOR, ABILITY_NONE, ABILITY_SWIFT-SWIM
// Level Up Moves: 13
// Generation: 8

