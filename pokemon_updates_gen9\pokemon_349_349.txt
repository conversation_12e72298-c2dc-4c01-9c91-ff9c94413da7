// POKEMON_349 (#349) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_349] =
    {
        .baseHP = 20,
        .baseAttack = 15,
        .baseDefense = 20,
        .baseSpAttack = 10,
        .baseSpDefense = 55,
        .baseSpeed = 80,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 255,
        .expYield = 35,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SWIFT-SWIM,
        .ability2 = ABILITY_OBLIVIOUS,
        .hiddenAbility = ABILITY_ADAPTABILITY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-349LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SPLASH),
    LEVEL_UP_MOVE(15, MOVE_TACKLE),
    LEVEL_UP_MOVE(25, MOVE_FLAIL),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 200
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_SWIFT-SWIM, ABILITY_OBLIVIOUS, ABILITY_ADAPTABILITY
// Level Up Moves: 3
// Generation: 9

