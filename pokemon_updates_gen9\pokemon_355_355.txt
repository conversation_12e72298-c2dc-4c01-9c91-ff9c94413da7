// POKEMON_355 (#355) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_355] =
    {
        .baseHP = 20,
        .baseAttack = 40,
        .baseDefense = 90,
        .baseSpAttack = 30,
        .baseSpDefense = 90,
        .baseSpeed = 25,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_GHOST,
        .catchRate = 190,
        .expYield = 60,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 25,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_FRISK,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-355LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 4, MOVE_DISABLE),
    LEVEL_UP_MOVE( 8, MOVE_SHADOW_SNEAK),
    LEVEL_UP_MOVE(12, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(16, MOVE_NIGHT_SHADE),
    LEVEL_UP_MOVE(20, MOVE_PAYBACK),
    LEVEL_UP_MOVE(24, MOVE_WILL_O_WISP),
    LEVEL_UP_MOVE(28, MOVE_MEAN_LOOK),
    LEVEL_UP_MOVE(32, MOVE_HEX),
    LEVEL_UP_MOVE(36, MOVE_CURSE),
    LEVEL_UP_MOVE(40, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(44, MOVE_FUTURE_SIGHT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 295
// Types: TYPE_GHOST / TYPE_GHOST
// Abilities: ABILITY_LEVITATE, ABILITY_NONE, ABILITY_FRISK
// Level Up Moves: 13
// Generation: 9

