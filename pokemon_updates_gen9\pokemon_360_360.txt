// POKEMON_360 (#360) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_360] =
    {
        .baseHP = 95,
        .baseAttack = 23,
        .baseDefense = 48,
        .baseSpAttack = 23,
        .baseSpDefense = 48,
        .baseSpeed = 23,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 125,
        .expYield = 118,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SHADOW-TAG,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_TELEPATHY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-360LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_AMNESIA),
    LEVEL_UP_MOVE( 1, MOVE_CHARM),
    LEVEL_UP_MOVE( 1, MOVE_COUNTER),
    LEVEL_UP_MOVE( 1, MOVE_DESTINY_BOND),
    LEVEL_UP_MOVE( 1, MOVE_ENCORE),
    LEVEL_UP_MOVE( 1, MOVE_MIRROR_COAT),
    LEVEL_UP_MOVE( 1, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE( 1, MOVE_SPLASH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 260
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_SHADOW-TAG, ABILITY_NONE, ABILITY_TELEPATHY
// Level Up Moves: 8
// Generation: 8

