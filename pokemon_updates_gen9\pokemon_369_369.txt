// POKEMON_369 (#369) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_369] =
    {
        .baseHP = 100,
        .baseAttack = 90,
        .baseDefense = 130,
        .baseSpAttack = 45,
        .baseSpDefense = 65,
        .baseSpeed = 55,
        .type1 = TYPE_WATER,
        .type2 = TYPE_ROCK,
        .catchRate = 25,
        .expYield = 190,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 40,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SWIFT-SWIM,
        .ability2 = ABILITY_ROCK-HEAD,
        .hiddenAbility = ABILITY_STURDY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-369LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(10, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(15, MOVE_YAWN),
    LEVEL_UP_MOVE(20, MOVE_DIVE),
    LEVEL_UP_MOVE(25, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(30, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(35, MOVE_REST),
    LEVEL_UP_MOVE(40, MOVE_FLAIL),
    LEVEL_UP_MOVE(45, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(50, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(55, MOVE_HEAD_SMASH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 485
// Types: TYPE_WATER / TYPE_ROCK
// Abilities: ABILITY_SWIFT-SWIM, ABILITY_ROCK-HEAD, ABILITY_STURDY
// Level Up Moves: 13
// Generation: 8

