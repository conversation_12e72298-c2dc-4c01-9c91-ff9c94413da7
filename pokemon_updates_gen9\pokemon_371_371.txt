// POKEMON_371 (#371) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_371] =
    {
        .baseHP = 45,
        .baseAttack = 75,
        .baseDefense = 60,
        .baseSpAttack = 40,
        .baseSpDefense = 30,
        .baseSpeed = 50,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 120,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 40,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_ROCK-HEAD,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SHEER-FORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-371LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 5, MOVE_BITE),
    LEVEL_UP_MOVE(10, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(15, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(20, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(25, MOVE_CRUNCH),
    LEVEL_UP_MOVE(31, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(35, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(40, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(45, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(50, MOVE_OUTRAGE),
    LEVEL_UP_MOVE(55, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 300
// Types: TYPE_DRAGON / TYPE_DRAGON
// Abilities: ABILITY_ROCK-HEAD, ABILITY_NONE, ABILITY_SHEER-FORCE
// Level Up Moves: 13
// Generation: 9

