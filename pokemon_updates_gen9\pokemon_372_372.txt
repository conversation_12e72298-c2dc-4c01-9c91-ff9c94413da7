// POKEMON_372 (#372) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_372] =
    {
        .baseHP = 65,
        .baseAttack = 95,
        .baseDefense = 100,
        .baseSpAttack = 60,
        .baseSpDefense = 50,
        .baseSpeed = 50,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 160,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 40,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_ROCK-HEAD,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_OVERCOAT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-372LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_PROTECT),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE(15, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(20, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(25, MOVE_CRUNCH),
    LEVEL_UP_MOVE(33, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(39, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(46, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(53, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(60, MOVE_OUTRAGE),
    LEVEL_UP_MOVE(67, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 420
// Types: TYPE_DRAGON / TYPE_DRAGON
// Abilities: ABILITY_ROCK-HEAD, ABILITY_NONE, ABILITY_OVERCOAT
// Level Up Moves: 14
// Generation: 9

