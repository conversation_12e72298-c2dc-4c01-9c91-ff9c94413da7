// POKEMON_374 (#374) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_374] =
    {
        .baseHP = 40,
        .baseAttack = 55,
        .baseDefense = 80,
        .baseSpAttack = 35,
        .baseSpDefense = 60,
        .baseSpeed = 30,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 3,
        .expYield = 95,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 40,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_CLEAR-BODY,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_LIGHT-METAL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-374LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 300
// Types: TYPE_STEEL / TYPE_PSYCHIC
// Abilities: ABILITY_CLEAR-BODY, ABILITY_NONE, ABILITY_LIGHT-METAL
// Level Up Moves: 1
// Generation: 9

