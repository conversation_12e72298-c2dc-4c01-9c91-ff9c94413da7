// POKEMON_375 (#375) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_375] =
    {
        .baseHP = 60,
        .baseAttack = 75,
        .baseDefense = 100,
        .baseSpAttack = 55,
        .baseSpDefense = 80,
        .baseSpeed = 50,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 3,
        .expYield = 135,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 40,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_CLEAR-BODY,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_LIGHT-METAL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-375LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 0, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE( 1, MOVE_BULLET_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 6, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(12, MOVE_MAGNET_RISE),
    LEVEL_UP_MOVE(18, MOVE_FLASH_CANNON),
    LEVEL_UP_MOVE(26, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(34, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(42, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(50, MOVE_METEOR_MASH),
    LEVEL_UP_MOVE(58, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(66, MOVE_AGILITY),
    LEVEL_UP_MOVE(74, MOVE_HYPER_BEAM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 420
// Types: TYPE_STEEL / TYPE_PSYCHIC
// Abilities: ABILITY_CLEAR-BODY, ABILITY_NONE, ABILITY_LIGHT-METAL
// Level Up Moves: 15
// Generation: 9

