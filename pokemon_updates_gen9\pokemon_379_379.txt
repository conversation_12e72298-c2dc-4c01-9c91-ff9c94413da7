// POKEMON_379 (#379) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_379] =
    {
        .baseHP = 80,
        .baseAttack = 75,
        .baseDefense = 150,
        .baseSpAttack = 75,
        .baseSpDefense = 150,
        .baseSpeed = 50,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_STEEL,
        .catchRate = 3,
        .expYield = 155,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 80,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_CLEAR-BODY,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_LIGHT-METAL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-379LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CHARGE_BEAM),
    LEVEL_UP_MOVE( 1, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE( 6, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(12, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(18, MOVE_STOMP),
    LEVEL_UP_MOVE(24, MOVE_FLASH_CANNON),
    LEVEL_UP_MOVE(24, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(30, MOVE_CURSE),
    LEVEL_UP_MOVE(36, MOVE_AMNESIA),
    LEVEL_UP_MOVE(36, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(42, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE(48, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(54, MOVE_SUPERPOWER),
    LEVEL_UP_MOVE(60, MOVE_LOCK_ON),
    LEVEL_UP_MOVE(66, MOVE_ZAP_CANNON),
    LEVEL_UP_MOVE(72, MOVE_HYPER_BEAM),
    LEVEL_UP_MOVE(78, MOVE_EXPLOSION),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 580
// Types: TYPE_STEEL / TYPE_STEEL
// Abilities: ABILITY_CLEAR-BODY, ABILITY_NONE, ABILITY_LIGHT-METAL
// Level Up Moves: 17
// Generation: 9

