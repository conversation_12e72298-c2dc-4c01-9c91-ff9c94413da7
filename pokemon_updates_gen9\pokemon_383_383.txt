// POKEMON_383 (#383) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_383] =
    {
        .baseHP = 100,
        .baseAttack = 150,
        .baseDefense = 140,
        .baseSpAttack = 100,
        .baseSpDefense = 90,
        .baseSpeed = 90,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_GROUND,
        .catchRate = 3,
        .expYield = 250,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_DROUGHT,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-383LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE( 1, MOVE_LAVA_PLUME),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SHOT),
    LEVEL_UP_MOVE( 1, MOVE_PRECIPICE_BLADES),
    LEVEL_UP_MOVE( 1, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE( 9, MOVE_EARTH_POWER),
    LEVEL_UP_MOVE(18, MOVE_BULK_UP),
    LEVEL_UP_MOVE(27, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(36, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE(45, MOVE_FISSURE),
    LEVEL_UP_MOVE(54, MOVE_REST),
    LEVEL_UP_MOVE(72, MOVE_FIRE_BLAST),
    LEVEL_UP_MOVE(81, MOVE_SOLAR_BEAM),
    LEVEL_UP_MOVE(90, MOVE_ERUPTION),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 670
// Types: TYPE_GROUND / TYPE_GROUND
// Abilities: ABILITY_DROUGHT, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
// Generation: 9

