// POKEMON_384 (#384) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_384] =
    {
        .baseHP = 105,
        .baseAttack = 150,
        .baseDefense = 90,
        .baseSpAttack = 150,
        .baseSpDefense = 90,
        .baseSpeed = 95,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 255,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_AIR-LOCK,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-384LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE( 1, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_ASCENT),
    LEVEL_UP_MOVE( 1, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE( 1, MOVE_TWISTER),
    LEVEL_UP_MOVE( 9, MOVE_CRUNCH),
    LEVEL_UP_MOVE(18, MOVE_DRAGON_DANCE),
    LEVEL_UP_MOVE(27, MOVE_EXTREME_SPEED),
    LEVEL_UP_MOVE(36, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(45, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(54, MOVE_REST),
    LEVEL_UP_MOVE(63, MOVE_FLY),
    LEVEL_UP_MOVE(72, MOVE_HURRICANE),
    LEVEL_UP_MOVE(81, MOVE_OUTRAGE),
    LEVEL_UP_MOVE(90, MOVE_HYPER_BEAM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 680
// Types: TYPE_DRAGON / TYPE_FLYING
// Abilities: ABILITY_AIR-LOCK, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
// Generation: 9

