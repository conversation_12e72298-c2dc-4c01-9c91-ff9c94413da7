// POKEMON_385 (#385) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_385] =
    {
        .baseHP = 100,
        .baseAttack = 100,
        .baseDefense = 100,
        .baseSpAttack = 100,
        .baseSpDefense = 100,
        .baseSpeed = 100,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 3,
        .expYield = 200,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 100,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SERENE-GRACE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-385LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_WISH),
    LEVEL_UP_MOVE( 7, MOVE_SWIFT),
    LEVEL_UP_MOVE(21, MOVE_LIFE_DEW),
    LEVEL_UP_MOVE(28, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(35, MOVE_GRAVITY),
    LEVEL_UP_MOVE(42, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(49, MOVE_METEOR_MASH),
    LEVEL_UP_MOVE(56, MOVE_HEALING_WISH),
    LEVEL_UP_MOVE(63, MOVE_REST),
    LEVEL_UP_MOVE(70, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(77, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(84, MOVE_COSMIC_POWER),
    LEVEL_UP_MOVE(91, MOVE_LAST_RESORT),
    LEVEL_UP_MOVE(98, MOVE_DOOM_DESIRE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_STEEL / TYPE_PSYCHIC
// Abilities: ABILITY_SERENE-GRACE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
// Generation: 9

