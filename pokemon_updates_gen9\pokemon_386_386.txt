// POKEMON_386 (#386) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_386] =
    {
        .baseHP = 50,
        .baseAttack = 150,
        .baseDefense = 50,
        .baseSpAttack = 150,
        .baseSpDefense = 50,
        .baseSpeed = 150,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 3,
        .expYield = 200,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PRESSURE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-386LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_WRAP),
    LEVEL_UP_MOVE( 7, MOVE_NIGHT_SHADE),
    LEVEL_UP_MOVE(13, MOVE_TELEPORT),
    LEVEL_UP_MOVE(19, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(25, MOVE_PSYSHOCK),
    LEVEL_UP_MOVE(31, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(37, MOVE_GRAVITY),
    LEVEL_UP_MOVE(43, MOVE_SKILL_SWAP),
    LEVEL_UP_MOVE(49, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(55, MOVE_COSMIC_POWER),
    LEVEL_UP_MOVE(61, MOVE_RECOVER),
    LEVEL_UP_MOVE(67, MOVE_PSYCHO_BOOST),
    LEVEL_UP_MOVE(73, MOVE_HYPER_BEAM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_PRESSURE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
// Generation: 9

