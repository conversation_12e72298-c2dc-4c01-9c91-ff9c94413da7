// POKEMON_389 (#389) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_389] =
    {
        .baseHP = 95,
        .baseAttack = 109,
        .baseDefense = 105,
        .baseSpAttack = 75,
        .baseSpDefense = 85,
        .baseSpeed = 56,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GROUND,
        .catchRate = 45,
        .expYield = 204,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_OVERGROW,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SHELL-ARMOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-389LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_WITHDRAW),
    LEVEL_UP_MOVE( 1, MOVE_WOOD_HAMMER),
    LEVEL_UP_MOVE(17, MOVE_CURSE),
    LEVEL_UP_MOVE(22, MOVE_BITE),
    LEVEL_UP_MOVE(27, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(33, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(39, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(45, MOVE_CRUNCH),
    LEVEL_UP_MOVE(51, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(57, MOVE_LEAF_STORM),
    LEVEL_UP_MOVE(63, MOVE_HEADLONG_RUSH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 525
// Types: TYPE_GRASS / TYPE_GROUND
// Abilities: ABILITY_OVERGROW, ABILITY_NONE, ABILITY_SHELL-ARMOR
// Level Up Moves: 15
// Generation: 9

