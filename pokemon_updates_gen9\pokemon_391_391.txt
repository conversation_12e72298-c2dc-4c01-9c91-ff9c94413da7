// POKEMON_391 (#391) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_391] =
    {
        .baseHP = 64,
        .baseAttack = 78,
        .baseDefense = 52,
        .baseSpAttack = 78,
        .baseSpDefense = 52,
        .baseSpeed = 81,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIGHTING,
        .catchRate = 45,
        .expYield = 142,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BLAZE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_IRON-FIST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-391LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_MACH_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 9, MOVE_TAUNT),
    LEVEL_UP_MOVE(16, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(19, MOVE_FLAME_WHEEL),
    LEVEL_UP_MOVE(26, MOVE_FEINT),
    LEVEL_UP_MOVE(29, MOVE_TORMENT),
    LEVEL_UP_MOVE(36, MOVE_CLOSE_COMBAT),
    LEVEL_UP_MOVE(39, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE(46, MOVE_ACROBATICS),
    LEVEL_UP_MOVE(49, MOVE_SLACK_OFF),
    LEVEL_UP_MOVE(56, MOVE_FLARE_BLITZ),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 405
// Types: TYPE_FIRE / TYPE_FIGHTING
// Abilities: ABILITY_BLAZE, ABILITY_NONE, ABILITY_IRON-FIST
// Level Up Moves: 14
// Generation: 9

