// POKEMON_395 (#395) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_395] =
    {
        .baseHP = 84,
        .baseAttack = 86,
        .baseDefense = 88,
        .baseSpAttack = 111,
        .baseSpDefense = 101,
        .baseSpeed = 60,
        .type1 = TYPE_WATER,
        .type2 = TYPE_STEEL,
        .catchRate = 45,
        .expYield = 170,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_TORRENT,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_COMPETITIVE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-395LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_AQUA_JET),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(11, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(15, MOVE_PECK),
    LEVEL_UP_MOVE(19, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(24, MOVE_SWAGGER),
    LEVEL_UP_MOVE(28, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(33, MOVE_BRINE),
    LEVEL_UP_MOVE(39, MOVE_WHIRLPOOL),
    LEVEL_UP_MOVE(46, MOVE_MIST),
    LEVEL_UP_MOVE(52, MOVE_DRILL_PECK),
    LEVEL_UP_MOVE(59, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(66, MOVE_WAVE_CRASH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 530
// Types: TYPE_WATER / TYPE_STEEL
// Abilities: ABILITY_TORRENT, ABILITY_NONE, ABILITY_COMPETITIVE
// Level Up Moves: 16
// Generation: 9

