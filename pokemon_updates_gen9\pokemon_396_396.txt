// POKEMON_396 (#396) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_396] =
    {
        .baseHP = 40,
        .baseAttack = 55,
        .baseDefense = 30,
        .baseSpAttack = 30,
        .baseSpDefense = 30,
        .baseSpeed = 60,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 255,
        .expYield = 95,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_KEEN-EYE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_RECKLESS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-396LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 9, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(13, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(17, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(21, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(25, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(29, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(33, MOVE_AGILITY),
    LEVEL_UP_MOVE(37, MOVE_BRAVE_BIRD),
    LEVEL_UP_MOVE(41, MOVE_FINAL_GAMBIT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 245
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_KEEN-EYE, ABILITY_NONE, ABILITY_RECKLESS
// Level Up Moves: 12
// Generation: 9

