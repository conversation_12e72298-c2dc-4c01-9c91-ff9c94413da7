// POKEMON_401 (#401) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_401] =
    {
        .baseHP = 37,
        .baseAttack = 25,
        .baseDefense = 41,
        .baseSpAttack = 25,
        .baseSpDefense = 41,
        .baseSpeed = 25,
        .type1 = TYPE_BUG,
        .type2 = TYPE_BUG,
        .catchRate = 255,
        .expYield = 62,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SHED-SKIN,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_RUN-AWAY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-401LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 6, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE(16, MOVE_BUG_BITE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 194
// Types: TYPE_BUG / TYPE_BUG
// Abilities: ABILITY_SHED-SKIN, ABILITY_NONE, ABILITY_RUN-AWAY
// Level Up Moves: 4
// Generation: 9

