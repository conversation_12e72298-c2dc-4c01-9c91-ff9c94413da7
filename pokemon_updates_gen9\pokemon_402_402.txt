// POKEMON_402 (#402) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_402] =
    {
        .baseHP = 77,
        .baseAttack = 85,
        .baseDefense = 51,
        .baseSpAttack = 55,
        .baseSpDefense = 51,
        .baseSpeed = 65,
        .type1 = TYPE_BUG,
        .type2 = TYPE_BUG,
        .catchRate = 45,
        .expYield = 162,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SWARM,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_TECHNICIAN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-402LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE(14, MOVE_ABSORB),
    LEVEL_UP_MOVE(18, MOVE_SING),
    LEVEL_UP_MOVE(22, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(26, MOVE_SLASH),
    LEVEL_UP_MOVE(30, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(34, MOVE_SCREECH),
    LEVEL_UP_MOVE(36, MOVE_FELL_STINGER),
    LEVEL_UP_MOVE(38, MOVE_TAUNT),
    LEVEL_UP_MOVE(42, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(44, MOVE_STICKY_WEB),
    LEVEL_UP_MOVE(46, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(50, MOVE_PERISH_SONG),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 384
// Types: TYPE_BUG / TYPE_BUG
// Abilities: ABILITY_SWARM, ABILITY_NONE, ABILITY_TECHNICIAN
// Level Up Moves: 15
// Generation: 9

