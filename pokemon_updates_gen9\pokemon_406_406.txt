// POKEMON_406 (#406) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_406] =
    {
        .baseHP = 40,
        .baseAttack = 30,
        .baseDefense = 35,
        .baseSpAttack = 50,
        .baseSpDefense = 70,
        .baseSpeed = 55,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_POISON,
        .catchRate = 255,
        .expYield = 70,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_NATURAL-CURE,
        .ability2 = ABILITY_POISON-POINT,
        .hiddenAbility = ABILITY_LEAF-GUARD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-406LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 1, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE( 1, MOVE_WORRY_SEED),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 280
// Types: TYPE_GRASS / TYPE_POISON
// Abilities: ABILITY_NATURAL-CURE, ABILITY_POISON-POINT, ABILITY_LEAF-GUARD
// Level Up Moves: 4
// Generation: 8

