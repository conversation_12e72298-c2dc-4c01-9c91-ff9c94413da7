// POKEMON_408 (#408) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_408] =
    {
        .baseHP = 67,
        .baseAttack = 125,
        .baseDefense = 40,
        .baseSpAttack = 30,
        .baseSpDefense = 30,
        .baseSpeed = 58,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_ROCK,
        .catchRate = 45,
        .expYield = 192,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 30,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_MOLD-BREAKER,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SHEER-FORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-408LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_HEADBUTT),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 6, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(10, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE(15, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(19, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(24, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(28, MOVE_SLAM),
    LEVEL_UP_MOVE(33, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(37, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(42, MOVE_SCREECH),
    LEVEL_UP_MOVE(46, MOVE_HEAD_SMASH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 350
// Types: TYPE_ROCK / TYPE_ROCK
// Abilities: ABILITY_MOLD-BREAKER, ABILITY_NONE, ABILITY_SHEER-FORCE
// Level Up Moves: 12
// Generation: 9

