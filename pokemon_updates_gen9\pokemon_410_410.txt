// POKEMON_410 (#410) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_410] =
    {
        .baseHP = 30,
        .baseAttack = 42,
        .baseDefense = 118,
        .baseSpAttack = 42,
        .baseSpDefense = 88,
        .baseSpeed = 30,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_STEEL,
        .catchRate = 45,
        .expYield = 72,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 30,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STURDY,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SOUNDPROOF,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-410LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_PROTECT),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 6, MOVE_TAUNT),
    LEVEL_UP_MOVE(10, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(15, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(19, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(24, MOVE_SWAGGER),
    LEVEL_UP_MOVE(28, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(33, MOVE_ENDURE),
    LEVEL_UP_MOVE(37, MOVE_METAL_BURST),
    LEVEL_UP_MOVE(42, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(46, MOVE_HEAVY_SLAM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 350
// Types: TYPE_ROCK / TYPE_STEEL
// Abilities: ABILITY_STURDY, ABILITY_NONE, ABILITY_SOUNDPROOF
// Level Up Moves: 12
// Generation: 9

