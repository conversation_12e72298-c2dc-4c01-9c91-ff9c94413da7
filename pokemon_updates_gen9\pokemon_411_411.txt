// POKEMON_411 (#411) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_411] =
    {
        .baseHP = 60,
        .baseAttack = 52,
        .baseDefense = 168,
        .baseSpAttack = 47,
        .baseSpDefense = 138,
        .baseSpeed = 30,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_STEEL,
        .catchRate = 45,
        .expYield = 112,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 30,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STURDY,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SOUNDPROOF,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-411LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_BLOCK),
    LEVEL_UP_MOVE( 1, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE( 1, MOVE_PROTECT),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAUNT),
    LEVEL_UP_MOVE(15, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(19, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(24, MOVE_SWAGGER),
    LEVEL_UP_MOVE(28, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(36, MOVE_ENDURE),
    LEVEL_UP_MOVE(43, MOVE_METAL_BURST),
    LEVEL_UP_MOVE(51, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(58, MOVE_HEAVY_SLAM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 495
// Types: TYPE_ROCK / TYPE_STEEL
// Abilities: ABILITY_STURDY, ABILITY_NONE, ABILITY_SOUNDPROOF
// Level Up Moves: 13
// Generation: 9

