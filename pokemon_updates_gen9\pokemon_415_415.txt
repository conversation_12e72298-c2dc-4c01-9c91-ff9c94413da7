// POKEMON_415 (#415) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_415] =
    {
        .baseHP = 30,
        .baseAttack = 30,
        .baseDefense = 42,
        .baseSpAttack = 30,
        .baseSpDefense = 42,
        .baseSpeed = 70,
        .type1 = TYPE_BUG,
        .type2 = TYPE_FLYING,
        .catchRate = 120,
        .expYield = 60,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_HONEY-GATHER,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_HUSTLE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-415LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BUG_BITE),
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 1, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_SCENT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 244
// Types: TYPE_BUG / TYPE_FLYING
// Abilities: ABILITY_HONEY-GATHER, ABILITY_NONE, ABILITY_HUSTLE
// Level Up Moves: 4
// Generation: 9

