// POKEMON_417 (#417) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_417] =
    {
        .baseHP = 60,
        .baseAttack = 45,
        .baseDefense = 70,
        .baseSpAttack = 45,
        .baseSpDefense = 90,
        .baseSpeed = 95,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 200,
        .expYield = 105,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 10,
        .friendship = 100,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_RUN-AWAY,
        .ability2 = ABILITY_PICKUP,
        .hiddenAbility = ABILITY_VOLT-ABSORB,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-417LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 5, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 9, MOVE_CHARM),
    LEVEL_UP_MOVE(13, MOVE_SPARK),
    LEVEL_UP_MOVE(17, MOVE_ENDURE),
    LEVEL_UP_MOVE(19, MOVE_NUZZLE),
    LEVEL_UP_MOVE(21, MOVE_SWIFT),
    LEVEL_UP_MOVE(25, MOVE_ELECTRO_BALL),
    LEVEL_UP_MOVE(29, MOVE_SWEET_KISS),
    LEVEL_UP_MOVE(33, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE(37, MOVE_SUPER_FANG),
    LEVEL_UP_MOVE(41, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(45, MOVE_LAST_RESORT),
    LEVEL_UP_MOVE(49, MOVE_THUNDER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 405
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_RUN-AWAY, ABILITY_PICKUP, ABILITY_VOLT-ABSORB
// Level Up Moves: 15
// Generation: 9

