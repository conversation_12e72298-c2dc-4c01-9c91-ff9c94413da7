// POKEMON_418 (#418) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_418] =
    {
        .baseHP = 55,
        .baseAttack = 65,
        .baseDefense = 35,
        .baseSpAttack = 60,
        .baseSpDefense = 30,
        .baseSpeed = 85,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 190,
        .expYield = 120,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SWIFT-SWIM,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_WATER-VEIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-418LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 4, MOVE_GROWL),
    LEVEL_UP_MOVE( 7, MOVE_SOAK),
    LEVEL_UP_MOVE(11, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(15, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(18, MOVE_BITE),
    LEVEL_UP_MOVE(21, MOVE_SWIFT),
    LEVEL_UP_MOVE(24, MOVE_AQUA_JET),
    LEVEL_UP_MOVE(27, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE(31, MOVE_WHIRLPOOL),
    LEVEL_UP_MOVE(35, MOVE_LIQUIDATION),
    LEVEL_UP_MOVE(38, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(41, MOVE_AGILITY),
    LEVEL_UP_MOVE(45, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(49, MOVE_WAVE_CRASH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 330
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_SWIFT-SWIM, ABILITY_NONE, ABILITY_WATER-VEIL
// Level Up Moves: 15
// Generation: 9

