// POKEMON_420 (#420) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_420] =
    {
        .baseHP = 45,
        .baseAttack = 35,
        .baseDefense = 45,
        .baseSpAttack = 62,
        .baseSpDefense = 53,
        .baseSpeed = 35,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 190,
        .expYield = 80,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_CHLOROPHYLL,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-420LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_MORNING_SUN),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_LEAFAGE),
    LEVEL_UP_MOVE(10, MOVE_GROWTH),
    LEVEL_UP_MOVE(15, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(20, MOVE_MAGICAL_LEAF),
    LEVEL_UP_MOVE(26, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(30, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(35, MOVE_PETAL_BLIZZARD),
    LEVEL_UP_MOVE(40, MOVE_WORRY_SEED),
    LEVEL_UP_MOVE(45, MOVE_SOLAR_BEAM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 275
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_CHLOROPHYLL, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 11
// Generation: 8

