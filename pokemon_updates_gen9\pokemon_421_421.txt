// POKEMON_421 (#421) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_421] =
    {
        .baseHP = 70,
        .baseAttack = 60,
        .baseDefense = 70,
        .baseSpAttack = 87,
        .baseSpDefense = 78,
        .baseSpeed = 85,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 75,
        .expYield = 130,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_FLOWER-GIFT,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-421LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE( 1, MOVE_FLOWER_SHIELD),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 1, MOVE_LEAFAGE),
    LEVEL_UP_MOVE( 1, MOVE_MORNING_SUN),
    LEVEL_UP_MOVE( 1, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE(15, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(20, MOVE_MAGICAL_LEAF),
    LEVEL_UP_MOVE(28, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(34, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(41, MOVE_PETAL_BLIZZARD),
    LEVEL_UP_MOVE(48, MOVE_WORRY_SEED),
    LEVEL_UP_MOVE(55, MOVE_SOLAR_BEAM),
    LEVEL_UP_MOVE(62, MOVE_PETAL_DANCE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 450
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_FLOWER-GIFT, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
// Generation: 8

