// POKEMON_422 (#422) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_422] =
    {
        .baseHP = 76,
        .baseAttack = 48,
        .baseDefense = 48,
        .baseSpAttack = 57,
        .baseSpDefense = 62,
        .baseSpeed = 34,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 190,
        .expYield = 124,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STICKY-HOLD,
        .ability2 = ABILITY_STORM-DRAIN,
        .hiddenAbility = ABILITY_SAND-FORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-422LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 5, MOVE_HARDEN),
    LEVEL_UP_MOVE(10, MOVE_RECOVER),
    LEVEL_UP_MOVE(15, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(20, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(25, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(31, MOVE_MUDDY_WATER),
    LEVEL_UP_MOVE(35, MOVE_EARTH_POWER),
    LEVEL_UP_MOVE(40, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(45, MOVE_MEMENTO),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 325
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_STICKY-HOLD, ABILITY_STORM-DRAIN, ABILITY_SAND-FORCE
// Level Up Moves: 11
// Generation: 9

