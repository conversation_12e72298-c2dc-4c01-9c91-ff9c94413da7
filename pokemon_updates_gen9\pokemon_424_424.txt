// POKEMON_424 (#424) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_424] =
    {
        .baseHP = 75,
        .baseAttack = 100,
        .baseDefense = 66,
        .baseSpAttack = 60,
        .baseSpDefense = 66,
        .baseSpeed = 115,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 45,
        .expYield = 175,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 100,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_TECHNICIAN,
        .ability2 = ABILITY_PICKUP,
        .hiddenAbility = ABILITY_SKILL-LINK,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-424LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE(11, MOVE_BATON_PASS),
    LEVEL_UP_MOVE(15, MOVE_TICKLE),
    LEVEL_UP_MOVE(18, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(22, MOVE_SWIFT),
    LEVEL_UP_MOVE(25, MOVE_SCREECH),
    LEVEL_UP_MOVE(29, MOVE_AGILITY),
    LEVEL_UP_MOVE(32, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE(36, MOVE_FLING),
    LEVEL_UP_MOVE(39, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(43, MOVE_LAST_RESORT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 482
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_TECHNICIAN, ABILITY_PICKUP, ABILITY_SKILL-LINK
// Level Up Moves: 14
// Generation: 9

