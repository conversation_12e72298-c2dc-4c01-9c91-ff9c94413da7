// POKEMON_425 (#425) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_425] =
    {
        .baseHP = 90,
        .baseAttack = 50,
        .baseDefense = 34,
        .baseSpAttack = 60,
        .baseSpDefense = 44,
        .baseSpeed = 70,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_FLYING,
        .catchRate = 125,
        .expYield = 140,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_AFTERMATH,
        .ability2 = ABILITY_UNBURDEN,
        .hiddenAbility = ABILITY_FLARE-BOOST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-425LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_MINIMIZE),
    LEVEL_UP_MOVE( 4, MOVE_GUST),
    LEVEL_UP_MOVE( 8, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(12, MOVE_PAYBACK),
    LEVEL_UP_MOVE(16, MOVE_HEX),
    LEVEL_UP_MOVE(20, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(24, MOVE_SPIT_UP),
    LEVEL_UP_MOVE(24, MOVE_STOCKPILE),
    LEVEL_UP_MOVE(24, MOVE_SWALLOW),
    LEVEL_UP_MOVE(29, MOVE_SELF_DESTRUCT),
    LEVEL_UP_MOVE(32, MOVE_DESTINY_BOND),
    LEVEL_UP_MOVE(36, MOVE_BATON_PASS),
    LEVEL_UP_MOVE(40, MOVE_TAILWIND),
    LEVEL_UP_MOVE(44, MOVE_EXPLOSION),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 348
// Types: TYPE_GHOST / TYPE_FLYING
// Abilities: ABILITY_AFTERMATH, ABILITY_UNBURDEN, ABILITY_FLARE-BOOST
// Level Up Moves: 15
// Generation: 9

