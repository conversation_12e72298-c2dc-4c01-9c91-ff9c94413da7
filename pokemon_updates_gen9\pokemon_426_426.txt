// POKEMON_426 (#426) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_426] =
    {
        .baseHP = 150,
        .baseAttack = 80,
        .baseDefense = 44,
        .baseSpAttack = 90,
        .baseSpDefense = 54,
        .baseSpeed = 80,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_FLYING,
        .catchRate = 60,
        .expYield = 230,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_AFTERMATH,
        .ability2 = ABILITY_UNBURDEN,
        .hiddenAbility = ABILITY_FLARE-BOOST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-426LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_PHANTOM_FORCE),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 1, MOVE_MINIMIZE),
    LEVEL_UP_MOVE( 1, MOVE_STRENGTH_SAP),
    LEVEL_UP_MOVE(12, MOVE_PAYBACK),
    LEVEL_UP_MOVE(16, MOVE_HEX),
    LEVEL_UP_MOVE(20, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(24, MOVE_SPIT_UP),
    LEVEL_UP_MOVE(24, MOVE_STOCKPILE),
    LEVEL_UP_MOVE(24, MOVE_SWALLOW),
    LEVEL_UP_MOVE(31, MOVE_SELF_DESTRUCT),
    LEVEL_UP_MOVE(36, MOVE_DESTINY_BOND),
    LEVEL_UP_MOVE(42, MOVE_BATON_PASS),
    LEVEL_UP_MOVE(48, MOVE_TAILWIND),
    LEVEL_UP_MOVE(54, MOVE_EXPLOSION),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 498
// Types: TYPE_GHOST / TYPE_FLYING
// Abilities: ABILITY_AFTERMATH, ABILITY_UNBURDEN, ABILITY_FLARE-BOOST
// Level Up Moves: 17
// Generation: 9

