// POKEMON_429 (#429) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_429] =
    {
        .baseHP = 60,
        .baseAttack = 60,
        .baseDefense = 60,
        .baseSpAttack = 105,
        .baseSpDefense = 105,
        .baseSpeed = 105,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_GHOST,
        .catchRate = 45,
        .expYield = 120,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 25,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-429LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_MAGICAL_LEAF),
    LEVEL_UP_MOVE( 1, MOVE_MYSTICAL_FIRE),
    LEVEL_UP_MOVE( 1, MOVE_PHANTOM_FORCE),
    LEVEL_UP_MOVE( 1, MOVE_POWER_GEM),
    LEVEL_UP_MOVE( 1, MOVE_SPITE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 495
// Types: TYPE_GHOST / TYPE_GHOST
// Abilities: ABILITY_LEVITATE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 7
// Generation: 9

