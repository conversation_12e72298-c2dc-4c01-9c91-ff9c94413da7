// POKEMON_435 (#435) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_435] =
    {
        .baseHP = 103,
        .baseAttack = 93,
        .baseDefense = 67,
        .baseSpAttack = 71,
        .baseSpDefense = 61,
        .baseSpeed = 84,
        .type1 = TYPE_POISON,
        .type2 = TYPE_DARK,
        .catchRate = 60,
        .expYield = 196,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STENCH,
        .ability2 = ABILITY_AFTERMATH,
        .hiddenAbility = ABILITY_KEEN-EYE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-435LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE( 1, MOVE_FEINT),
    LEVEL_UP_MOVE( 1, MOVE_POISON_GAS),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_SMOKESCREEN),
    LEVEL_UP_MOVE(12, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(15, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(18, MOVE_BITE),
    LEVEL_UP_MOVE(21, MOVE_VENOSHOCK),
    LEVEL_UP_MOVE(24, MOVE_SCREECH),
    LEVEL_UP_MOVE(27, MOVE_TOXIC),
    LEVEL_UP_MOVE(30, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(33, MOVE_MEMENTO),
    LEVEL_UP_MOVE(38, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(43, MOVE_BELCH),
    LEVEL_UP_MOVE(48, MOVE_EXPLOSION),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 479
// Types: TYPE_POISON / TYPE_DARK
// Abilities: ABILITY_STENCH, ABILITY_AFTERMATH, ABILITY_KEEN-EYE
// Level Up Moves: 16
// Generation: 9

