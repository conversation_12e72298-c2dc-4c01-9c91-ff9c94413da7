// POKEMON_437 (#437) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_437] =
    {
        .baseHP = 67,
        .baseAttack = 89,
        .baseDefense = 116,
        .baseSpAttack = 79,
        .baseSpDefense = 116,
        .baseSpeed = 33,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 90,
        .expYield = 156,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_HEATPROOF,
        .hiddenAbility = ABILITY_HEAVY-METAL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-437LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_BLOCK),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_PAYBACK),
    LEVEL_UP_MOVE( 1, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_WEATHER_BALL),
    LEVEL_UP_MOVE(12, MOVE_IMPRISON),
    LEVEL_UP_MOVE(16, MOVE_GYRO_BALL),
    LEVEL_UP_MOVE(20, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE(24, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(28, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE(32, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(38, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(44, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(50, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(56, MOVE_RAIN_DANCE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_STEEL / TYPE_PSYCHIC
// Abilities: ABILITY_LEVITATE, ABILITY_HEATPROOF, ABILITY_HEAVY-METAL
// Level Up Moves: 17
// Generation: 9

