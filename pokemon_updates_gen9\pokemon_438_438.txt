// POKEMON_438 (#438) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_438] =
    {
        .baseHP = 50,
        .baseAttack = 80,
        .baseDefense = 95,
        .baseSpAttack = 10,
        .baseSpDefense = 45,
        .baseSpeed = 10,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_ROCK,
        .catchRate = 255,
        .expYield = 130,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STURDY,
        .ability2 = ABILITY_ROCK-HEAD,
        .hiddenAbility = ABILITY_RATTLED,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-438LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE( 1, MOVE_FAKE_TEARS),
    LEVEL_UP_MOVE( 4, MOVE_FLAIL),
    LEVEL_UP_MOVE( 8, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE(12, MOVE_BLOCK),
    LEVEL_UP_MOVE(16, MOVE_MIMIC),
    LEVEL_UP_MOVE(20, MOVE_ROCK_TOMB),
    LEVEL_UP_MOVE(24, MOVE_TEARFUL_LOOK),
    LEVEL_UP_MOVE(28, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(32, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(36, MOVE_LOW_KICK),
    LEVEL_UP_MOVE(40, MOVE_COUNTER),
    LEVEL_UP_MOVE(44, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 290
// Types: TYPE_ROCK / TYPE_ROCK
// Abilities: ABILITY_STURDY, ABILITY_ROCK-HEAD, ABILITY_RATTLED
// Level Up Moves: 13
// Generation: 9

