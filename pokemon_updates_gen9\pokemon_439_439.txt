// POKEMON_439 (#439) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_439] =
    {
        .baseHP = 20,
        .baseAttack = 25,
        .baseDefense = 45,
        .baseSpAttack = 70,
        .baseSpDefense = 90,
        .baseSpeed = 60,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_FAIRY,
        .catchRate = 145,
        .expYield = 45,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SOUNDPROOF,
        .ability2 = ABILITY_FILTER,
        .hiddenAbility = ABILITY_TECHNICIAN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-439LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 4, MOVE_BATON_PASS),
    LEVEL_UP_MOVE( 8, MOVE_ENCORE),
    LEVEL_UP_MOVE(12, MOVE_CONFUSION),
    LEVEL_UP_MOVE(16, MOVE_ROLE_PLAY),
    LEVEL_UP_MOVE(20, MOVE_PROTECT),
    LEVEL_UP_MOVE(24, MOVE_RECYCLE),
    LEVEL_UP_MOVE(28, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(32, MOVE_MIMIC),
    LEVEL_UP_MOVE(36, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(36, MOVE_REFLECT),
    LEVEL_UP_MOVE(36, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(40, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(44, MOVE_DAZZLING_GLEAM),
    LEVEL_UP_MOVE(48, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(52, MOVE_TEETER_DANCE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 310
// Types: TYPE_PSYCHIC / TYPE_FAIRY
// Abilities: ABILITY_SOUNDPROOF, ABILITY_FILTER, ABILITY_TECHNICIAN
// Level Up Moves: 17
// Generation: 8

