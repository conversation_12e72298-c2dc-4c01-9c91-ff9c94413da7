// POKEMON_440 (#440) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_440] =
    {
        .baseHP = 100,
        .baseAttack = 5,
        .baseDefense = 5,
        .baseSpAttack = 15,
        .baseSpDefense = 65,
        .baseSpeed = 30,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 130,
        .expYield = 105,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(100.0),
        .eggCycles = 40,
        .friendship = 140,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_NATURAL-CURE,
        .ability2 = ABILITY_SERENE-GRACE,
        .hiddenAbility = ABILITY_FRIEND-GUARD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-440LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 4, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 8, MOVE_SWEET_KISS),
    LEVEL_UP_MOVE(12, MOVE_DISARMING_VOICE),
    LEVEL_UP_MOVE(16, MOVE_COVET),
    LEVEL_UP_MOVE(20, MOVE_CHARM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 220
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_NATURAL-CURE, ABILITY_SERENE-GRACE, ABILITY_FRIEND-GUARD
// Level Up Moves: 7
// Generation: 9

