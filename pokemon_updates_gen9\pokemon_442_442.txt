// POKEMON_442 (#442) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_442] =
    {
        .baseHP = 50,
        .baseAttack = 92,
        .baseDefense = 108,
        .baseSpAttack = 92,
        .baseSpDefense = 108,
        .baseSpeed = 35,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_DARK,
        .catchRate = 100,
        .expYield = 142,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PRESSURE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_INFILTRATOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-442LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE( 1, MOVE_NIGHT_SHADE),
    LEVEL_UP_MOVE( 5, MOVE_SHADOW_SNEAK),
    LEVEL_UP_MOVE(10, MOVE_SPITE),
    LEVEL_UP_MOVE(15, MOVE_PAYBACK),
    LEVEL_UP_MOVE(20, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(25, MOVE_HEX),
    LEVEL_UP_MOVE(30, MOVE_MEMENTO),
    LEVEL_UP_MOVE(35, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(40, MOVE_CURSE),
    LEVEL_UP_MOVE(45, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(50, MOVE_DARK_PULSE),
    LEVEL_UP_MOVE(55, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE(60, MOVE_DREAM_EATER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 485
// Types: TYPE_GHOST / TYPE_DARK
// Abilities: ABILITY_PRESSURE, ABILITY_NONE, ABILITY_INFILTRATOR
// Level Up Moves: 14
// Generation: 9

