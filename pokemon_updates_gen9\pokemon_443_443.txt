// POKEMON_443 (#443) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_443] =
    {
        .baseHP = 58,
        .baseAttack = 70,
        .baseDefense = 45,
        .baseSpAttack = 40,
        .baseSpDefense = 45,
        .baseSpeed = 42,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_GROUND,
        .catchRate = 45,
        .expYield = 128,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 40,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SAND-VEIL,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_ROUGH-SKIN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-443LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SAND_TOMB),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 6, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE(12, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(18, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(25, MOVE_BITE),
    LEVEL_UP_MOVE(30, MOVE_SLASH),
    LEVEL_UP_MOVE(36, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(42, MOVE_DIG),
    LEVEL_UP_MOVE(48, MOVE_SANDSTORM),
    LEVEL_UP_MOVE(54, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(60, MOVE_DRAGON_RUSH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 300
// Types: TYPE_DRAGON / TYPE_GROUND
// Abilities: ABILITY_SAND-VEIL, ABILITY_NONE, ABILITY_ROUGH-SKIN
// Level Up Moves: 12
// Generation: 9

