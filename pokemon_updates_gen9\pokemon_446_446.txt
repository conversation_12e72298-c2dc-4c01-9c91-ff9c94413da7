// POKEMON_446 (#446) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_446] =
    {
        .baseHP = 135,
        .baseAttack = 85,
        .baseDefense = 40,
        .baseSpAttack = 40,
        .baseSpDefense = 85,
        .baseSpeed = 5,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 50,
        .expYield = 220,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 40,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PICKUP,
        .ability2 = ABILITY_THICK-FAT,
        .hiddenAbility = ABILITY_GLUTTONY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-446LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LICK),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 4, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 8, MOVE_RECYCLE),
    LEVEL_UP_MOVE(12, MOVE_COVET),
    LEVEL_UP_MOVE(16, MOVE_BITE),
    LEVEL_UP_MOVE(20, MOVE_STOCKPILE),
    LEVEL_UP_MOVE(20, MOVE_SWALLOW),
    LEVEL_UP_MOVE(24, MOVE_SCREECH),
    LEVEL_UP_MOVE(28, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(32, MOVE_FLING),
    LEVEL_UP_MOVE(36, MOVE_AMNESIA),
    LEVEL_UP_MOVE(40, MOVE_METRONOME),
    LEVEL_UP_MOVE(44, MOVE_FLAIL),
    LEVEL_UP_MOVE(48, MOVE_BELLY_DRUM),
    LEVEL_UP_MOVE(52, MOVE_LAST_RESORT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 390
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_PICKUP, ABILITY_THICK-FAT, ABILITY_GLUTTONY
// Level Up Moves: 16
// Generation: 9

