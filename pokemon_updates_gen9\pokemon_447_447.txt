// POKEMON_447 (#447) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_447] =
    {
        .baseHP = 40,
        .baseAttack = 70,
        .baseDefense = 40,
        .baseSpAttack = 35,
        .baseSpDefense = 40,
        .baseSpeed = 60,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 75,
        .expYield = 110,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STEADFAST,
        .ability2 = ABILITY_INNER-FOCUS,
        .hiddenAbility = ABILITY_PRANKSTER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-447LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ENDURE),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 4, MOVE_FEINT),
    LEVEL_UP_MOVE( 8, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(12, MOVE_COUNTER),
    LEVEL_UP_MOVE(16, MOVE_WORK_UP),
    LEVEL_UP_MOVE(20, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE(24, MOVE_VACUUM_WAVE),
    LEVEL_UP_MOVE(28, MOVE_SCREECH),
    LEVEL_UP_MOVE(32, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(36, MOVE_FORCE_PALM),
    LEVEL_UP_MOVE(40, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(44, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(48, MOVE_COPYCAT),
    LEVEL_UP_MOVE(52, MOVE_FINAL_GAMBIT),
    LEVEL_UP_MOVE(56, MOVE_REVERSAL),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 285
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_STEADFAST, ABILITY_INNER-FOCUS, ABILITY_PRANKSTER
// Level Up Moves: 16
// Generation: 9

