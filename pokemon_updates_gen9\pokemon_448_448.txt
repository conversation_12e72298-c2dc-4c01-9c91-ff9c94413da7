// POKEMON_448 (#448) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_448] =
    {
        .baseHP = 70,
        .baseAttack = 110,
        .baseDefense = 70,
        .baseSpAttack = 115,
        .baseSpDefense = 70,
        .baseSpeed = 90,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_STEEL,
        .catchRate = 45,
        .expYield = 180,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STEADFAST,
        .ability2 = ABILITY_INNER-FOCUS,
        .hiddenAbility = ABILITY_JUSTIFIED,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-448LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_AURA_SPHERE),
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE( 1, MOVE_DETECT),
    LEVEL_UP_MOVE( 1, MOVE_FEINT),
    LEVEL_UP_MOVE( 1, MOVE_FINAL_GAMBIT),
    LEVEL_UP_MOVE( 1, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 1, MOVE_LIFE_DEW),
    LEVEL_UP_MOVE( 1, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_REVERSAL),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE( 1, MOVE_SCREECH),
    LEVEL_UP_MOVE( 1, MOVE_VACUUM_WAVE),
    LEVEL_UP_MOVE(12, MOVE_COUNTER),
    LEVEL_UP_MOVE(16, MOVE_WORK_UP),
    LEVEL_UP_MOVE(20, MOVE_FORCE_PALM),
    LEVEL_UP_MOVE(24, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(28, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(32, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(36, MOVE_BONE_RUSH),
    LEVEL_UP_MOVE(40, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(44, MOVE_HEAL_PULSE),
    LEVEL_UP_MOVE(48, MOVE_METEOR_MASH),
    LEVEL_UP_MOVE(52, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(56, MOVE_EXTREME_SPEED),
    LEVEL_UP_MOVE(60, MOVE_CLOSE_COMBAT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 525
// Types: TYPE_FIGHTING / TYPE_STEEL
// Abilities: ABILITY_STEADFAST, ABILITY_INNER-FOCUS, ABILITY_JUSTIFIED
// Level Up Moves: 26
// Generation: 9

