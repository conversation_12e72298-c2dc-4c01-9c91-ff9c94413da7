// POKEMON_449 (#449) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_449] =
    {
        .baseHP = 68,
        .baseAttack = 72,
        .baseDefense = 78,
        .baseSpAttack = 38,
        .baseSpDefense = 42,
        .baseSpeed = 32,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_GROUND,
        .catchRate = 140,
        .expYield = 140,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SAND-STREAM,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SAND-FORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-449LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 4, MOVE_BITE),
    LEVEL_UP_MOVE( 8, MOVE_YAWN),
    LEVEL_UP_MOVE(12, MOVE_SAND_TOMB),
    LEVEL_UP_MOVE(16, MOVE_DIG),
    LEVEL_UP_MOVE(20, MOVE_CRUNCH),
    LEVEL_UP_MOVE(24, MOVE_SANDSTORM),
    LEVEL_UP_MOVE(28, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(32, MOVE_ROAR),
    LEVEL_UP_MOVE(36, MOVE_REST),
    LEVEL_UP_MOVE(40, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(44, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(48, MOVE_FISSURE),
    LEVEL_UP_MOVE(52, MOVE_SLACK_OFF),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 330
// Types: TYPE_GROUND / TYPE_GROUND
// Abilities: ABILITY_SAND-STREAM, ABILITY_NONE, ABILITY_SAND-FORCE
// Level Up Moves: 15
// Generation: 9

