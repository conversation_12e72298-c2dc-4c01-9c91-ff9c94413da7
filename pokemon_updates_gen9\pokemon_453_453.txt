// POKEMON_453 (#453) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_453] =
    {
        .baseHP = 48,
        .baseAttack = 61,
        .baseDefense = 40,
        .baseSpAttack = 61,
        .baseSpDefense = 40,
        .baseSpeed = 50,
        .type1 = TYPE_POISON,
        .type2 = TYPE_FIGHTING,
        .catchRate = 140,
        .expYield = 109,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 10,
        .friendship = 100,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_ANTICIPATION,
        .ability2 = ABILITY_DRY-SKIN,
        .hiddenAbility = ABILITY_POISON-TOUCH,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-453LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_POISON_STING),
    LEVEL_UP_MOVE( 4, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 8, MOVE_TAUNT),
    LEVEL_UP_MOVE(12, MOVE_FLATTER),
    LEVEL_UP_MOVE(16, MOVE_LOW_KICK),
    LEVEL_UP_MOVE(20, MOVE_VENOSHOCK),
    LEVEL_UP_MOVE(24, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(28, MOVE_SWAGGER),
    LEVEL_UP_MOVE(32, MOVE_POISON_JAB),
    LEVEL_UP_MOVE(36, MOVE_TOXIC),
    LEVEL_UP_MOVE(40, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(44, MOVE_SLUDGE_BOMB),
    LEVEL_UP_MOVE(48, MOVE_BELCH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 300
// Types: TYPE_POISON / TYPE_FIGHTING
// Abilities: ABILITY_ANTICIPATION, ABILITY_DRY-SKIN, ABILITY_POISON-TOUCH
// Level Up Moves: 14
// Generation: 9

