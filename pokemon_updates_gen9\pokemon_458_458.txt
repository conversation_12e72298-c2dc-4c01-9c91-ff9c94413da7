// POKEMON_458 (#458) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_458] =
    {
        .baseHP = 45,
        .baseAttack = 20,
        .baseDefense = 50,
        .baseSpAttack = 60,
        .baseSpDefense = 120,
        .baseSpeed = 50,
        .type1 = TYPE_WATER,
        .type2 = TYPE_FLYING,
        .catchRate = 25,
        .expYield = 65,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SWIFT-SWIM,
        .ability2 = ABILITY_WATER-ABSORB,
        .hiddenAbility = ABILITY_WATER-VEIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-458LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 4, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE( 8, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(12, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(16, MOVE_WIDE_GUARD),
    LEVEL_UP_MOVE(20, MOVE_AGILITY),
    LEVEL_UP_MOVE(24, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(28, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(32, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(36, MOVE_AQUA_RING),
    LEVEL_UP_MOVE(40, MOVE_BOUNCE),
    LEVEL_UP_MOVE(44, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(48, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 345
// Types: TYPE_WATER / TYPE_FLYING
// Abilities: ABILITY_SWIFT-SWIM, ABILITY_WATER-ABSORB, ABILITY_WATER-VEIL
// Level Up Moves: 14
// Generation: 8

