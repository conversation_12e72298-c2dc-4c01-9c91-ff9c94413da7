// POKEMON_459 (#459) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_459] =
    {
        .baseHP = 60,
        .baseAttack = 62,
        .baseDefense = 50,
        .baseSpAttack = 62,
        .baseSpDefense = 60,
        .baseSpeed = 40,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_ICE,
        .catchRate = 120,
        .expYield = 122,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SNOW-WARNING,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SOUNDPROOF,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-459LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_POWDER_SNOW),
    LEVEL_UP_MOVE( 5, MOVE_LEAFAGE),
    LEVEL_UP_MOVE(10, MOVE_MIST),
    LEVEL_UP_MOVE(15, MOVE_ICE_SHARD),
    LEVEL_UP_MOVE(20, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(25, MOVE_ICY_WIND),
    LEVEL_UP_MOVE(30, MOVE_SWAGGER),
    LEVEL_UP_MOVE(35, MOVE_INGRAIN),
    LEVEL_UP_MOVE(41, MOVE_WOOD_HAMMER),
    LEVEL_UP_MOVE(45, MOVE_BLIZZARD),
    LEVEL_UP_MOVE(50, MOVE_SHEER_COLD),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 334
// Types: TYPE_GRASS / TYPE_ICE
// Abilities: ABILITY_SNOW-WARNING, ABILITY_NONE, ABILITY_SOUNDPROOF
// Level Up Moves: 12
// Generation: 9

