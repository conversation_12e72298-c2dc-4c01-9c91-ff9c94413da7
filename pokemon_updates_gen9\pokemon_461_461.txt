// POKEMON_461 (#461) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_461] =
    {
        .baseHP = 70,
        .baseAttack = 120,
        .baseDefense = 65,
        .baseSpAttack = 45,
        .baseSpDefense = 85,
        .baseSpeed = 125,
        .type1 = TYPE_DARK,
        .type2 = TYPE_ICE,
        .catchRate = 45,
        .expYield = 190,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PRESSURE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_PICKPOCKET,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-461LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_AGILITY),
    LEVEL_UP_MOVE( 1, MOVE_ASSURANCE),
    LEVEL_UP_MOVE( 1, MOVE_BEAT_UP),
    LEVEL_UP_MOVE( 1, MOVE_ICE_SHARD),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_SLASH),
    LEVEL_UP_MOVE( 1, MOVE_TAUNT),
    LEVEL_UP_MOVE(18, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(24, MOVE_ICY_WIND),
    LEVEL_UP_MOVE(30, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(36, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(42, MOVE_FLING),
    LEVEL_UP_MOVE(48, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(54, MOVE_SCREECH),
    LEVEL_UP_MOVE(60, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(66, MOVE_DARK_PULSE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 510
// Types: TYPE_DARK / TYPE_ICE
// Abilities: ABILITY_PRESSURE, ABILITY_NONE, ABILITY_PICKPOCKET
// Level Up Moves: 18
// Generation: 9

