// POKEMON_463 (#463) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_463] =
    {
        .baseHP = 110,
        .baseAttack = 85,
        .baseDefense = 95,
        .baseSpAttack = 80,
        .baseSpDefense = 95,
        .baseSpeed = 50,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 30,
        .expYield = 195,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_OWN-TEMPO,
        .ability2 = ABILITY_OBLIVIOUS,
        .hiddenAbility = ABILITY_CLOUD-NINE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-463LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_LICK),
    LEVEL_UP_MOVE( 1, MOVE_ROLLOUT),
    LEVEL_UP_MOVE( 1, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(18, MOVE_WRAP),
    LEVEL_UP_MOVE(24, MOVE_DISABLE),
    LEVEL_UP_MOVE(30, MOVE_STOMP),
    LEVEL_UP_MOVE(36, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(42, MOVE_SCREECH),
    LEVEL_UP_MOVE(48, MOVE_SLAM),
    LEVEL_UP_MOVE(54, MOVE_POWER_WHIP),
    LEVEL_UP_MOVE(60, MOVE_BELLY_DRUM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 515
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_OWN-TEMPO, ABILITY_OBLIVIOUS, ABILITY_CLOUD-NINE
// Level Up Moves: 12
// Generation: 8

