// POKEMON_464 (#464) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_464] =
    {
        .baseHP = 115,
        .baseAttack = 140,
        .baseDefense = 130,
        .baseSpAttack = 55,
        .baseSpDefense = 55,
        .baseSpeed = 40,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_ROCK,
        .catchRate = 30,
        .expYield = 255,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_LIGHTNING-ROD,
        .ability2 = ABILITY_SOLID-ROCK,
        .hiddenAbility = ABILITY_RECKLESS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-464LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BULLDOZE),
    LEVEL_UP_MOVE( 1, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE( 1, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE(15, MOVE_HORN_ATTACK),
    LEVEL_UP_MOVE(20, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(25, MOVE_STOMP),
    LEVEL_UP_MOVE(30, MOVE_ROCK_BLAST),
    LEVEL_UP_MOVE(35, MOVE_DRILL_RUN),
    LEVEL_UP_MOVE(40, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(47, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(54, MOVE_STONE_EDGE),
    LEVEL_UP_MOVE(61, MOVE_MEGAHORN),
    LEVEL_UP_MOVE(68, MOVE_HORN_DRILL),
    LEVEL_UP_MOVE(75, MOVE_ROCK_WRECKER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 535
// Types: TYPE_GROUND / TYPE_ROCK
// Abilities: ABILITY_LIGHTNING-ROD, ABILITY_SOLID-ROCK, ABILITY_RECKLESS
// Level Up Moves: 16
// Generation: 9

