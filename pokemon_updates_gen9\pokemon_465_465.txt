// POKEMON_465 (#465) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_465] =
    {
        .baseHP = 100,
        .baseAttack = 100,
        .baseDefense = 125,
        .baseSpAttack = 110,
        .baseSpDefense = 50,
        .baseSpeed = 50,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 30,
        .expYield = 200,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_CHLOROPHYLL,
        .ability2 = ABILITY_LEAF-GUARD,
        .hiddenAbility = ABILITY_REGENERATOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-465LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_BIND),
    LEVEL_UP_MOVE( 1, MOVE_BLOCK),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 1, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE(12, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(16, MOVE_VINE_WHIP),
    LEVEL_UP_MOVE(20, MOVE_POISON_POWDER),
    LEVEL_UP_MOVE(24, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(28, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(32, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(36, MOVE_SLEEP_POWDER),
    LEVEL_UP_MOVE(40, MOVE_SLAM),
    LEVEL_UP_MOVE(44, MOVE_TICKLE),
    LEVEL_UP_MOVE(48, MOVE_POWER_WHIP),
    LEVEL_UP_MOVE(52, MOVE_INGRAIN),
    LEVEL_UP_MOVE(56, MOVE_GRASSY_TERRAIN),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 535
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_CHLOROPHYLL, ABILITY_LEAF-GUARD, ABILITY_REGENERATOR
// Level Up Moves: 17
// Generation: 8

