// POKEMON_466 (#466) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_466] =
    {
        .baseHP = 75,
        .baseAttack = 123,
        .baseDefense = 67,
        .baseSpAttack = 95,
        .baseSpDefense = 85,
        .baseSpeed = 95,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 30,
        .expYield = 198,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(25.0),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_MOTOR-DRIVE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_VITAL-SPIRIT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-466LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CHARGE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE(12, MOVE_SWIFT),
    LEVEL_UP_MOVE(16, MOVE_SHOCK_WAVE),
    LEVEL_UP_MOVE(20, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE(24, MOVE_SCREECH),
    LEVEL_UP_MOVE(28, MOVE_THUNDER_PUNCH),
    LEVEL_UP_MOVE(34, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(40, MOVE_LOW_KICK),
    LEVEL_UP_MOVE(46, MOVE_THUNDERBOLT),
    LEVEL_UP_MOVE(52, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(58, MOVE_THUNDER),
    LEVEL_UP_MOVE(64, MOVE_GIGA_IMPACT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 540
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_MOTOR-DRIVE, ABILITY_NONE, ABILITY_VITAL-SPIRIT
// Level Up Moves: 15
// Generation: 9

