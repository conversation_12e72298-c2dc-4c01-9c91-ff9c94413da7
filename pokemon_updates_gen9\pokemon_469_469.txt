// POKEMON_469 (#469) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_469] =
    {
        .baseHP = 86,
        .baseAttack = 76,
        .baseDefense = 86,
        .baseSpAttack = 116,
        .baseSpDefense = 56,
        .baseSpeed = 95,
        .type1 = TYPE_BUG,
        .type2 = TYPE_FLYING,
        .catchRate = 30,
        .expYield = 162,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SPEED-BOOST,
        .ability2 = ABILITY_TINTED-LENS,
        .hiddenAbility = ABILITY_FRISK,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-469LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE( 1, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE( 1, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE( 1, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE(14, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(17, MOVE_DETECT),
    LEVEL_UP_MOVE(22, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(27, MOVE_UPROAR),
    LEVEL_UP_MOVE(30, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(33, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(38, MOVE_FEINT),
    LEVEL_UP_MOVE(43, MOVE_SLASH),
    LEVEL_UP_MOVE(46, MOVE_SCREECH),
    LEVEL_UP_MOVE(49, MOVE_U_TURN),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 515
// Types: TYPE_BUG / TYPE_FLYING
// Abilities: ABILITY_SPEED-BOOST, ABILITY_TINTED-LENS, ABILITY_FRISK
// Level Up Moves: 15
// Generation: 9

