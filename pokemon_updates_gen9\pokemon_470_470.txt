// POKEMON_470 (#470) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_470] =
    {
        .baseHP = 65,
        .baseAttack = 110,
        .baseDefense = 130,
        .baseSpAttack = 60,
        .baseSpDefense = 65,
        .baseSpeed = 95,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 45,
        .expYield = 175,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 35,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_LEAF-GUARD,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_CHLOROPHYLL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-470LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE( 1, MOVE_BATON_PASS),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_CHARM),
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE( 1, MOVE_COVET),
    LEVEL_UP_MOVE( 1, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 1, MOVE_SWIFT),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE( 5, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE(10, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(15, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(20, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(25, MOVE_MAGICAL_LEAF),
    LEVEL_UP_MOVE(30, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(35, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE(40, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(45, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(50, MOVE_LEAF_BLADE),
    LEVEL_UP_MOVE(55, MOVE_LAST_RESORT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 525
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_LEAF-GUARD, ABILITY_NONE, ABILITY_CHLOROPHYLL
// Level Up Moves: 24
// Generation: 9

