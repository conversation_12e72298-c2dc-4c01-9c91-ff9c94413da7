// POKEMON_472 (#472) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_472] =
    {
        .baseHP = 75,
        .baseAttack = 95,
        .baseDefense = 125,
        .baseSpAttack = 45,
        .baseSpDefense = 75,
        .baseSpeed = 95,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_FLYING,
        .catchRate = 30,
        .expYield = 170,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_HYPER-CUTTER,
        .ability2 = ABILITY_SAND-VEIL,
        .hiddenAbility = ABILITY_POISON-HEAL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-472LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_ICE_FANG),
    LEVEL_UP_MOVE( 1, MOVE_POISON_JAB),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_FANG),
    LEVEL_UP_MOVE(13, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(16, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE(19, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(22, MOVE_ACROBATICS),
    LEVEL_UP_MOVE(27, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(30, MOVE_U_TURN),
    LEVEL_UP_MOVE(35, MOVE_SCREECH),
    LEVEL_UP_MOVE(40, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(45, MOVE_CRABHAMMER),
    LEVEL_UP_MOVE(50, MOVE_SWORDS_DANCE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 510
// Types: TYPE_GROUND / TYPE_FLYING
// Abilities: ABILITY_HYPER-CUTTER, ABILITY_SAND-VEIL, ABILITY_POISON-HEAL
// Level Up Moves: 16
// Generation: 9

