// POKEMON_473 (#473) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_473] =
    {
        .baseHP = 110,
        .baseAttack = 130,
        .baseDefense = 80,
        .baseSpAttack = 70,
        .baseSpDefense = 60,
        .baseSpeed = 80,
        .type1 = TYPE_ICE,
        .type2 = TYPE_GROUND,
        .catchRate = 50,
        .expYield = 240,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_OBLIVIOUS,
        .ability2 = ABILITY_SNOW-CLOAK,
        .hiddenAbility = ABILITY_THICK-FAT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-473LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE( 1, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE( 1, MOVE_FLAIL),
    LEVEL_UP_MOVE( 1, MOVE_ICE_FANG),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_POWDER_SNOW),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE(15, MOVE_ICE_SHARD),
    LEVEL_UP_MOVE(20, MOVE_MIST),
    LEVEL_UP_MOVE(25, MOVE_ENDURE),
    LEVEL_UP_MOVE(30, MOVE_ICY_WIND),
    LEVEL_UP_MOVE(37, MOVE_AMNESIA),
    LEVEL_UP_MOVE(44, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(51, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(58, MOVE_BLIZZARD),
    LEVEL_UP_MOVE(65, MOVE_THRASH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 530
// Types: TYPE_ICE / TYPE_GROUND
// Abilities: ABILITY_OBLIVIOUS, ABILITY_SNOW-CLOAK, ABILITY_THICK-FAT
// Level Up Moves: 16
// Generation: 9

