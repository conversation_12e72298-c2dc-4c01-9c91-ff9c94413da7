// POKEMON_475 (#475) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_475] =
    {
        .baseHP = 68,
        .baseAttack = 125,
        .baseDefense = 65,
        .baseSpAttack = 65,
        .baseSpDefense = 115,
        .baseSpeed = 80,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_FIGHTING,
        .catchRate = 45,
        .expYield = 193,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(0.0),
        .eggCycles = 20,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STEADFAST,
        .ability2 = ABILITY_SHARPNESS,
        .hiddenAbility = ABILITY_JUSTIFIED,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-475LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_SLASH),
    LEVEL_UP_MOVE( 1, MOVE_AQUA_CUTTER),
    LEVEL_UP_MOVE( 1, MOVE_CALM_MIND),
    LEVEL_UP_MOVE( 1, MOVE_CHARM),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_DISARMING_VOICE),
    LEVEL_UP_MOVE( 1, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE( 1, MOVE_DRAINING_KISS),
    LEVEL_UP_MOVE( 1, MOVE_DREAM_EATER),
    LEVEL_UP_MOVE( 1, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 1, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE( 1, MOVE_IMPRISON),
    LEVEL_UP_MOVE( 1, MOVE_LEAF_BLADE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_LIFE_DEW),
    LEVEL_UP_MOVE( 1, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE( 1, MOVE_PSYBEAM),
    LEVEL_UP_MOVE( 1, MOVE_PSYCHIC),
    LEVEL_UP_MOVE( 1, MOVE_SACRED_SWORD),
    LEVEL_UP_MOVE( 9, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(12, MOVE_FEINT),
    LEVEL_UP_MOVE(15, MOVE_TELEPORT),
    LEVEL_UP_MOVE(18, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(23, MOVE_FALSE_SWIPE),
    LEVEL_UP_MOVE(28, MOVE_PROTECT),
    LEVEL_UP_MOVE(35, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(42, MOVE_PSYCHO_CUT),
    LEVEL_UP_MOVE(49, MOVE_HEAL_PULSE),
    LEVEL_UP_MOVE(56, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(56, MOVE_WIDE_GUARD),
    LEVEL_UP_MOVE(63, MOVE_CLOSE_COMBAT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 518
// Types: TYPE_PSYCHIC / TYPE_FIGHTING
// Abilities: ABILITY_STEADFAST, ABILITY_SHARPNESS, ABILITY_JUSTIFIED
// Level Up Moves: 33
// Generation: 9

