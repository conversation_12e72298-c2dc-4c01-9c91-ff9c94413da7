// POKEMON_480 (#480) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_480] =
    {
        .baseHP = 75,
        .baseAttack = 75,
        .baseDefense = 130,
        .baseSpAttack = 75,
        .baseSpDefense = 130,
        .baseSpeed = 95,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 3,
        .expYield = 150,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 80,
        .friendship = 140,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-480LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_REST),
    LEVEL_UP_MOVE( 7, MOVE_SWIFT),
    LEVEL_UP_MOVE(14, MOVE_ENDURE),
    LEVEL_UP_MOVE(21, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(28, MOVE_IMPRISON),
    LEVEL_UP_MOVE(35, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE(42, MOVE_AMNESIA),
    LEVEL_UP_MOVE(49, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(56, MOVE_YAWN),
    LEVEL_UP_MOVE(63, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(70, MOVE_FLAIL),
    LEVEL_UP_MOVE(77, MOVE_MEMENTO),
    LEVEL_UP_MOVE(84, MOVE_MYSTICAL_POWER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 580
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_LEVITATE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
// Generation: 9

