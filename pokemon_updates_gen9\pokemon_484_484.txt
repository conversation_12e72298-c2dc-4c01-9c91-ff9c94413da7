// POKEMON_484 (#484) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_484] =
    {
        .baseHP = 90,
        .baseAttack = 120,
        .baseDefense = 100,
        .baseSpAttack = 150,
        .baseSpDefense = 120,
        .baseSpeed = 100,
        .type1 = TYPE_WATER,
        .type2 = TYPE_DRAGON,
        .catchRate = 3,
        .expYield = 210,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PRESSURE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_TELEPATHY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-484LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE( 1, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE( 8, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(16, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(24, MOVE_SLASH),
    LEVEL_UP_MOVE(32, MOVE_AQUA_RING),
    LEVEL_UP_MOVE(48, MOVE_AURA_SPHERE),
    LEVEL_UP_MOVE(56, MOVE_POWER_GEM),
    LEVEL_UP_MOVE(64, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(72, MOVE_EARTH_POWER),
    LEVEL_UP_MOVE(80, MOVE_SPACIAL_REND),
    LEVEL_UP_MOVE(88, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 680
// Types: TYPE_WATER / TYPE_DRAGON
// Abilities: ABILITY_PRESSURE, ABILITY_NONE, ABILITY_TELEPATHY
// Level Up Moves: 12
// Generation: 9

