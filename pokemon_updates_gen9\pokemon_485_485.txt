// POKEMON_485 (#485) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_485] =
    {
        .baseHP = 91,
        .baseAttack = 90,
        .baseDefense = 106,
        .baseSpAttack = 130,
        .baseSpDefense = 106,
        .baseSpeed = 77,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_STEEL,
        .catchRate = 3,
        .expYield = 181,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 10,
        .friendship = 100,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_FLASH-FIRE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_FLAME-BODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-485LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 6, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(12, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(18, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE(24, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(30, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(36, MOVE_CRUNCH),
    LEVEL_UP_MOVE(42, MOVE_LAVA_PLUME),
    LEVEL_UP_MOVE(48, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(54, MOVE_EARTH_POWER),
    LEVEL_UP_MOVE(60, MOVE_HEAT_WAVE),
    LEVEL_UP_MOVE(66, MOVE_STONE_EDGE),
    LEVEL_UP_MOVE(72, MOVE_MAGMA_STORM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_FIRE / TYPE_STEEL
// Abilities: ABILITY_FLASH-FIRE, ABILITY_NONE, ABILITY_FLAME-BODY
// Level Up Moves: 14
// Generation: 9

