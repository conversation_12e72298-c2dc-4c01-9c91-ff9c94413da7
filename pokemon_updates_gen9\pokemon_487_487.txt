// POKEMON_487 (#487) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_487] =
    {
        .baseHP = 150,
        .baseAttack = 100,
        .baseDefense = 120,
        .baseSpAttack = 100,
        .baseSpDefense = 120,
        .baseSpeed = 90,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_DRAGON,
        .catchRate = 3,
        .expYield = 250,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PRESSURE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_TELEPATHY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-487LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DEFOG),
    LEVEL_UP_MOVE( 1, MOVE_SHADOW_SNEAK),
    LEVEL_UP_MOVE( 7, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(14, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(21, MOVE_HEX),
    LEVEL_UP_MOVE(28, MOVE_SLASH),
    LEVEL_UP_MOVE(35, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(42, MOVE_SHADOW_CLAW),
    LEVEL_UP_MOVE(49, MOVE_PAIN_SPLIT),
    LEVEL_UP_MOVE(56, MOVE_AURA_SPHERE),
    LEVEL_UP_MOVE(63, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(70, MOVE_EARTH_POWER),
    LEVEL_UP_MOVE(77, MOVE_SHADOW_FORCE),
    LEVEL_UP_MOVE(84, MOVE_DESTINY_BOND),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 680
// Types: TYPE_GHOST / TYPE_DRAGON
// Abilities: ABILITY_PRESSURE, ABILITY_NONE, ABILITY_TELEPATHY
// Level Up Moves: 14
// Generation: 9

