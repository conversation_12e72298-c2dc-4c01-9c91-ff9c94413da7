// POKEMON_493 (#493) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_493] =
    {
        .baseHP = 120,
        .baseAttack = 120,
        .baseDefense = 120,
        .baseSpAttack = 120,
        .baseSpDefense = 120,
        .baseSpeed = 120,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 3,
        .expYield = 240,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_MULTITYPE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-493LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_COSMIC_POWER),
    LEVEL_UP_MOVE( 1, MOVE_SEISMIC_TOSS),
    LEVEL_UP_MOVE(10, MOVE_GRAVITY),
    LEVEL_UP_MOVE(20, MOVE_EARTH_POWER),
    LEVEL_UP_MOVE(30, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(40, MOVE_EXTREME_SPEED),
    LEVEL_UP_MOVE(50, MOVE_HEALING_WISH),
    LEVEL_UP_MOVE(60, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(70, MOVE_RECOVER),
    LEVEL_UP_MOVE(80, MOVE_HYPER_BEAM),
    LEVEL_UP_MOVE(90, MOVE_PERISH_SONG),
    LEVEL_UP_MOVE(100, MOVE_JUDGMENT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 720
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_MULTITYPE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 12
// Generation: 9

