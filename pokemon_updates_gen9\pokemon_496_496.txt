// POKEMON_496 (#496) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_496] =
    {
        .baseHP = 60,
        .baseAttack = 60,
        .baseDefense = 75,
        .baseSpAttack = 60,
        .baseSpDefense = 75,
        .baseSpeed = 83,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 45,
        .expYield = 120,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_OVERGROW,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_CONTRARY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-496LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_VINE_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_WRAP),
    LEVEL_UP_MOVE(13, MOVE_GROWTH),
    LEVEL_UP_MOVE(16, MOVE_MAGICAL_LEAF),
    LEVEL_UP_MOVE(20, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(24, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(28, MOVE_SLAM),
    LEVEL_UP_MOVE(32, MOVE_LEAF_BLADE),
    LEVEL_UP_MOVE(36, MOVE_COIL),
    LEVEL_UP_MOVE(40, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(44, MOVE_GASTRO_ACID),
    LEVEL_UP_MOVE(48, MOVE_LEAF_STORM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 413
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_OVERGROW, ABILITY_NONE, ABILITY_CONTRARY
// Level Up Moves: 14
// Generation: 9

