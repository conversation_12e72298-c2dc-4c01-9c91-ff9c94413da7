// POKEMON_498 (#498) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_498] =
    {
        .baseHP = 65,
        .baseAttack = 63,
        .baseDefense = 45,
        .baseSpAttack = 45,
        .baseSpDefense = 45,
        .baseSpeed = 45,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 45,
        .expYield = 128,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BLAZE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_THICK-FAT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-498LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 3, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 7, MOVE_EMBER),
    LEVEL_UP_MOVE( 9, MOVE_ENDURE),
    LEVEL_UP_MOVE(13, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE(15, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE(19, MOVE_SMOG),
    LEVEL_UP_MOVE(21, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(25, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(27, MOVE_HEAT_CRASH),
    LEVEL_UP_MOVE(31, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(33, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(37, MOVE_HEAD_SMASH),
    LEVEL_UP_MOVE(39, MOVE_ROAR),
    LEVEL_UP_MOVE(43, MOVE_FLARE_BLITZ),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 308
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_BLAZE, ABILITY_NONE, ABILITY_THICK-FAT
// Level Up Moves: 15
// Generation: 9

