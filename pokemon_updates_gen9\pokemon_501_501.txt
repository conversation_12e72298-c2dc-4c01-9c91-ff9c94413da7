// POKEMON_501 (#501) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_501] =
    {
        .baseHP = 55,
        .baseAttack = 55,
        .baseDefense = 45,
        .baseSpAttack = 63,
        .baseSpDefense = 45,
        .baseSpeed = 45,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 45,
        .expYield = 110,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_TORRENT,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SHELL-ARMOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-501LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 7, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(11, MOVE_SOAK),
    LEVEL_UP_MOVE(13, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(17, MOVE_RAZOR_SHELL),
    LEVEL_UP_MOVE(19, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE(23, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(25, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(29, MOVE_AQUA_JET),
    LEVEL_UP_MOVE(31, MOVE_ENCORE),
    LEVEL_UP_MOVE(35, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(37, MOVE_RETALIATE),
    LEVEL_UP_MOVE(41, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(43, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 308
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_TORRENT, ABILITY_NONE, ABILITY_SHELL-ARMOR
// Level Up Moves: 15
// Generation: 9

