// POKEMON_503 (#503) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_503] =
    {
        .baseHP = 95,
        .baseAttack = 100,
        .baseDefense = 85,
        .baseSpAttack = 108,
        .baseSpDefense = 70,
        .baseSpeed = 70,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 45,
        .expYield = 195,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_TORRENT,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SHELL-ARMOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-503LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_SLASH),
    LEVEL_UP_MOVE( 1, MOVE_MEGAHORN),
    LEVEL_UP_MOVE( 1, MOVE_SOAK),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(13, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(18, MOVE_RAZOR_SHELL),
    LEVEL_UP_MOVE(21, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE(25, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(29, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(34, MOVE_AQUA_JET),
    LEVEL_UP_MOVE(39, MOVE_ENCORE),
    LEVEL_UP_MOVE(46, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(51, MOVE_RETALIATE),
    LEVEL_UP_MOVE(58, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(63, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 528
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_TORRENT, ABILITY_NONE, ABILITY_SHELL-ARMOR
// Level Up Moves: 17
// Generation: 9

