// POKEMON_509 (#509) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_509] =
    {
        .baseHP = 41,
        .baseAttack = 50,
        .baseDefense = 37,
        .baseSpAttack = 50,
        .baseSpDefense = 37,
        .baseSpeed = 66,
        .type1 = TYPE_DARK,
        .type2 = TYPE_DARK,
        .catchRate = 255,
        .expYield = 91,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_LIMBER,
        .ability2 = ABILITY_UNBURDEN,
        .hiddenAbility = ABILITY_PRANKSTER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-509LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 4, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 5, MOVE_FAKE_OUT),
    LEVEL_UP_MOVE(12, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(16, MOVE_TORMENT),
    LEVEL_UP_MOVE(21, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(24, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(28, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(32, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(36, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(40, MOVE_PLAY_ROUGH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 281
// Types: TYPE_DARK / TYPE_DARK
// Abilities: ABILITY_LIMBER, ABILITY_UNBURDEN, ABILITY_PRANKSTER
// Level Up Moves: 12
// Generation: 8

