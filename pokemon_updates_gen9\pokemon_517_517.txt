// POKEMON_517 (#517) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_517] =
    {
        .baseHP = 76,
        .baseAttack = 25,
        .baseDefense = 45,
        .baseSpAttack = 67,
        .baseSpDefense = 55,
        .baseSpeed = 24,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 190,
        .expYield = 101,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 10,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_FOREWARN,
        .ability2 = ABILITY_SYNCHRONIZE,
        .hiddenAbility = ABILITY_TELEPATHY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-517LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_STORED_POWER),
    LEVEL_UP_MOVE( 4, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE( 8, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(12, MOVE_IMPRISON),
    LEVEL_UP_MOVE(16, MOVE_MOONLIGHT),
    LEVEL_UP_MOVE(20, MOVE_MAGIC_COAT),
    LEVEL_UP_MOVE(24, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(28, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(32, MOVE_YAWN),
    LEVEL_UP_MOVE(36, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(40, MOVE_MOONBLAST),
    LEVEL_UP_MOVE(44, MOVE_DREAM_EATER),
    LEVEL_UP_MOVE(48, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(52, MOVE_WONDER_ROOM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 292
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_FOREWARN, ABILITY_SYNCHRONIZE, ABILITY_TELEPATHY
// Level Up Moves: 15
// Generation: 8

