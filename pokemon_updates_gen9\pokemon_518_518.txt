// POKEMON_518 (#518) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_518] =
    {
        .baseHP = 116,
        .baseAttack = 55,
        .baseDefense = 85,
        .baseSpAttack = 107,
        .baseSpDefense = 95,
        .baseSpeed = 29,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 75,
        .expYield = 171,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 10,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_FOREWARN,
        .ability2 = ABILITY_SYNCHRONIZE,
        .hiddenAbility = ABILITY_TELEPATHY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-518LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CALM_MIND),
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_DREAM_EATER),
    LEVEL_UP_MOVE( 1, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE( 1, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE( 1, MOVE_IMPRISON),
    LEVEL_UP_MOVE( 1, MOVE_MAGIC_COAT),
    LEVEL_UP_MOVE( 1, MOVE_MOONBLAST),
    LEVEL_UP_MOVE( 1, MOVE_MOONLIGHT),
    LEVEL_UP_MOVE( 1, MOVE_PSYBEAM),
    LEVEL_UP_MOVE( 1, MOVE_PSYCHIC),
    LEVEL_UP_MOVE( 1, MOVE_PSYCHIC_TERRAIN),
    LEVEL_UP_MOVE( 1, MOVE_STORED_POWER),
    LEVEL_UP_MOVE( 1, MOVE_WONDER_ROOM),
    LEVEL_UP_MOVE( 1, MOVE_YAWN),
    LEVEL_UP_MOVE( 1, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 487
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_FOREWARN, ABILITY_SYNCHRONIZE, ABILITY_TELEPATHY
// Level Up Moves: 16
// Generation: 8

