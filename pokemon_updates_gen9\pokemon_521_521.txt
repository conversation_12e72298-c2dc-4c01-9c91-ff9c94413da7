// POKEMON_521 (#521) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_521] =
    {
        .baseHP = 80,
        .baseAttack = 115,
        .baseDefense = 80,
        .baseSpAttack = 65,
        .baseSpDefense = 55,
        .baseSpeed = 93,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 195,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BIG-PECKS,
        .ability2 = ABILITY_SUPER-LUCK,
        .hiddenAbility = ABILITY_RIVALRY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-521LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(12, MOVE_TAUNT),
    LEVEL_UP_MOVE(16, MOVE_AIR_CUTTER),
    LEVEL_UP_MOVE(20, MOVE_SWAGGER),
    LEVEL_UP_MOVE(26, MOVE_FEATHER_DANCE),
    LEVEL_UP_MOVE(36, MOVE_DETECT),
    LEVEL_UP_MOVE(42, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(50, MOVE_ROOST),
    LEVEL_UP_MOVE(58, MOVE_TAILWIND),
    LEVEL_UP_MOVE(66, MOVE_SKY_ATTACK),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 488
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_BIG-PECKS, ABILITY_SUPER-LUCK, ABILITY_RIVALRY
// Level Up Moves: 13
// Generation: 8

