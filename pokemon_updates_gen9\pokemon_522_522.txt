// POKEMON_522 (#522) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_522] =
    {
        .baseHP = 45,
        .baseAttack = 60,
        .baseDefense = 32,
        .baseSpAttack = 50,
        .baseSpDefense = 32,
        .baseSpeed = 76,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 190,
        .expYield = 105,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_LIGHTNING-ROD,
        .ability2 = ABILITY_MOTOR-DRIVE,
        .hiddenAbility = ABILITY_SAP-SIPPER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-522LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 4, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 8, MOVE_CHARGE),
    LEVEL_UP_MOVE(11, MOVE_SHOCK_WAVE),
    LEVEL_UP_MOVE(15, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE(18, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE(22, MOVE_SPARK),
    LEVEL_UP_MOVE(25, MOVE_STOMP),
    LEVEL_UP_MOVE(29, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(33, MOVE_AGILITY),
    LEVEL_UP_MOVE(35, MOVE_WILD_CHARGE),
    LEVEL_UP_MOVE(40, MOVE_THRASH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 295
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_LIGHTNING-ROD, ABILITY_MOTOR-DRIVE, ABILITY_SAP-SIPPER
// Level Up Moves: 12
// Generation: 9

