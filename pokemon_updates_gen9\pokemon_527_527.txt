// POKEMON_527 (#527) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_527] =
    {
        .baseHP = 65,
        .baseAttack = 45,
        .baseDefense = 43,
        .baseSpAttack = 55,
        .baseSpDefense = 43,
        .baseSpeed = 72,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_FLYING,
        .catchRate = 190,
        .expYield = 110,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_UNAWARE,
        .ability2 = ABILITY_KLUTZ,
        .hiddenAbility = ABILITY_SIMPLE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-527LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ATTRACT),
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 5, MOVE_CONFUSION),
    LEVEL_UP_MOVE(10, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(15, MOVE_AIR_CUTTER),
    LEVEL_UP_MOVE(20, MOVE_IMPRISON),
    LEVEL_UP_MOVE(25, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(30, MOVE_AMNESIA),
    LEVEL_UP_MOVE(35, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(40, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(45, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(50, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(55, MOVE_SIMPLE_BEAM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 323
// Types: TYPE_PSYCHIC / TYPE_FLYING
// Abilities: ABILITY_UNAWARE, ABILITY_KLUTZ, ABILITY_SIMPLE
// Level Up Moves: 13
// Generation: 8

