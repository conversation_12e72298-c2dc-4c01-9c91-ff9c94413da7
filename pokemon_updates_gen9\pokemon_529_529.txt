// POKEMON_529 (#529) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_529] =
    {
        .baseHP = 60,
        .baseAttack = 85,
        .baseDefense = 40,
        .baseSpAttack = 30,
        .baseSpDefense = 45,
        .baseSpeed = 68,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_GROUND,
        .catchRate = 120,
        .expYield = 145,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SAND-RUSH,
        .ability2 = ABILITY_SAND-FORCE,
        .hiddenAbility = ABILITY_MOLD-BREAKER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-529LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE( 4, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 8, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(12, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(16, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(20, MOVE_SANDSTORM),
    LEVEL_UP_MOVE(24, MOVE_CRUSH_CLAW),
    LEVEL_UP_MOVE(28, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(32, MOVE_DIG),
    LEVEL_UP_MOVE(36, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(40, MOVE_DRILL_RUN),
    LEVEL_UP_MOVE(44, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(48, MOVE_FISSURE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 328
// Types: TYPE_GROUND / TYPE_GROUND
// Abilities: ABILITY_SAND-RUSH, ABILITY_SAND-FORCE, ABILITY_MOLD-BREAKER
// Level Up Moves: 14
// Generation: 9

