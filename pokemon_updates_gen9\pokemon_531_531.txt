// POKEMON_531 (#531) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_531] =
    {
        .baseHP = 103,
        .baseAttack = 60,
        .baseDefense = 86,
        .baseSpAttack = 60,
        .baseSpDefense = 86,
        .baseSpeed = 50,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 255,
        .expYield = 163,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_HEALER,
        .ability2 = ABILITY_REGENERATOR,
        .hiddenAbility = ABILITY_KLUTZ,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-531LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_PLAY_NICE),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 4, MOVE_DISARMING_VOICE),
    LEVEL_UP_MOVE( 9, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(12, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(16, MOVE_GROWL),
    LEVEL_UP_MOVE(20, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(24, MOVE_LIFE_DEW),
    LEVEL_UP_MOVE(28, MOVE_AFTER_YOU),
    LEVEL_UP_MOVE(32, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(36, MOVE_SIMPLE_BEAM),
    LEVEL_UP_MOVE(40, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(44, MOVE_HEAL_PULSE),
    LEVEL_UP_MOVE(48, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(52, MOVE_ENTRAINMENT),
    LEVEL_UP_MOVE(56, MOVE_MISTY_TERRAIN),
    LEVEL_UP_MOVE(60, MOVE_LAST_RESORT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 445
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_HEALER, ABILITY_REGENERATOR, ABILITY_KLUTZ
// Level Up Moves: 17
// Generation: 8

