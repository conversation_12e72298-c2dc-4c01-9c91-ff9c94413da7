// POKEMON_532 (#532) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_532] =
    {
        .baseHP = 75,
        .baseAttack = 80,
        .baseDefense = 55,
        .baseSpAttack = 25,
        .baseSpDefense = 35,
        .baseSpeed = 35,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 180,
        .expYield = 155,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(25.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_GUTS,
        .ability2 = ABILITY_SHEER-FORCE,
        .hiddenAbility = ABILITY_IRON-FIST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-532LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 4, MOVE_LOW_KICK),
    LEVEL_UP_MOVE( 8, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE(12, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(16, MOVE_BULK_UP),
    LEVEL_UP_MOVE(20, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(24, MOVE_SLAM),
    LEVEL_UP_MOVE(28, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(32, MOVE_DYNAMIC_PUNCH),
    LEVEL_UP_MOVE(36, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE(40, MOVE_STONE_EDGE),
    LEVEL_UP_MOVE(44, MOVE_SUPERPOWER),
    LEVEL_UP_MOVE(48, MOVE_FOCUS_PUNCH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 305
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_GUTS, ABILITY_SHEER-FORCE, ABILITY_IRON-FIST
// Level Up Moves: 14
// Generation: 9

