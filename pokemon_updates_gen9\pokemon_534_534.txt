// POKEMON_534 (#534) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_534] =
    {
        .baseHP = 105,
        .baseAttack = 140,
        .baseDefense = 95,
        .baseSpAttack = 55,
        .baseSpDefense = 65,
        .baseSpeed = 45,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 45,
        .expYield = 245,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(25.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_GUTS,
        .ability2 = ABILITY_SHEER-FORCE,
        .hiddenAbility = ABILITY_IRON-FIST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-534LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_LOW_KICK),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE(12, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(16, MOVE_BULK_UP),
    LEVEL_UP_MOVE(20, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(24, MOVE_SLAM),
    LEVEL_UP_MOVE(30, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(36, MOVE_DYNAMIC_PUNCH),
    LEVEL_UP_MOVE(42, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE(48, MOVE_STONE_EDGE),
    LEVEL_UP_MOVE(54, MOVE_SUPERPOWER),
    LEVEL_UP_MOVE(60, MOVE_FOCUS_PUNCH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 505
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_GUTS, ABILITY_SHEER-FORCE, ABILITY_IRON-FIST
// Level Up Moves: 14
// Generation: 9

