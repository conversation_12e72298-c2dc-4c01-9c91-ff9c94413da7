// POKEMON_536 (#536) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_536] =
    {
        .baseHP = 75,
        .baseAttack = 65,
        .baseDefense = 55,
        .baseSpAttack = 65,
        .baseSpDefense = 55,
        .baseSpeed = 69,
        .type1 = TYPE_WATER,
        .type2 = TYPE_GROUND,
        .catchRate = 120,
        .expYield = 140,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SWIFT-SWIM,
        .ability2 = ABILITY_HYDRATION,
        .hiddenAbility = ABILITY_WATER-ABSORB,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-536LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ACID),
    LEVEL_UP_MOVE( 1, MOVE_ECHOED_VOICE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(12, MOVE_MUD_SHOT),
    LEVEL_UP_MOVE(16, MOVE_ROUND),
    LEVEL_UP_MOVE(20, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(24, MOVE_FLAIL),
    LEVEL_UP_MOVE(30, MOVE_UPROAR),
    LEVEL_UP_MOVE(37, MOVE_AQUA_RING),
    LEVEL_UP_MOVE(42, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(48, MOVE_MUDDY_WATER),
    LEVEL_UP_MOVE(54, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(60, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 384
// Types: TYPE_WATER / TYPE_GROUND
// Abilities: ABILITY_SWIFT-SWIM, ABILITY_HYDRATION, ABILITY_WATER-ABSORB
// Level Up Moves: 14
// Generation: 8

