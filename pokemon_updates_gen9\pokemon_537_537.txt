// POKEMON_537 (#537) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_537] =
    {
        .baseHP = 105,
        .baseAttack = 95,
        .baseDefense = 75,
        .baseSpAttack = 85,
        .baseSpDefense = 75,
        .baseSpeed = 74,
        .type1 = TYPE_WATER,
        .type2 = TYPE_GROUND,
        .catchRate = 45,
        .expYield = 200,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SWIFT-SWIM,
        .ability2 = ABILITY_POISON-TOUCH,
        .hiddenAbility = ABILITY_WATER-ABSORB,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-537LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_DRAIN_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_ACID),
    LEVEL_UP_MOVE( 1, MOVE_DRAIN_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_ECHOED_VOICE),
    LEVEL_UP_MOVE( 1, MOVE_GASTRO_ACID),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(12, MOVE_MUD_SHOT),
    LEVEL_UP_MOVE(16, MOVE_ROUND),
    LEVEL_UP_MOVE(20, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(24, MOVE_FLAIL),
    LEVEL_UP_MOVE(30, MOVE_UPROAR),
    LEVEL_UP_MOVE(39, MOVE_AQUA_RING),
    LEVEL_UP_MOVE(46, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(54, MOVE_MUDDY_WATER),
    LEVEL_UP_MOVE(62, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(70, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 509
// Types: TYPE_WATER / TYPE_GROUND
// Abilities: ABILITY_SWIFT-SWIM, ABILITY_POISON-TOUCH, ABILITY_WATER-ABSORB
// Level Up Moves: 17
// Generation: 8

