// POKEMON_538 (#538) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_538] =
    {
        .baseHP = 120,
        .baseAttack = 100,
        .baseDefense = 85,
        .baseSpAttack = 30,
        .baseSpDefense = 85,
        .baseSpeed = 45,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 45,
        .expYield = 220,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(0.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_GUTS,
        .ability2 = ABILITY_INNER-FOCUS,
        .hiddenAbility = ABILITY_MOLD-BREAKER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-538LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BIND),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 5, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(10, MOVE_CIRCLE_THROW),
    LEVEL_UP_MOVE(15, MOVE_WIDE_GUARD),
    LEVEL_UP_MOVE(20, MOVE_REVENGE),
    LEVEL_UP_MOVE(25, MOVE_BULK_UP),
    LEVEL_UP_MOVE(30, MOVE_STORM_THROW),
    LEVEL_UP_MOVE(35, MOVE_VITAL_THROW),
    LEVEL_UP_MOVE(40, MOVE_SEISMIC_TOSS),
    LEVEL_UP_MOVE(45, MOVE_ENDURE),
    LEVEL_UP_MOVE(50, MOVE_REVERSAL),
    LEVEL_UP_MOVE(55, MOVE_SUPERPOWER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 465
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_GUTS, ABILITY_INNER-FOCUS, ABILITY_MOLD-BREAKER
// Level Up Moves: 13
// Generation: 8

