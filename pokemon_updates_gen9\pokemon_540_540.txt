// POKEMON_540 (#540) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_540] =
    {
        .baseHP = 45,
        .baseAttack = 53,
        .baseDefense = 70,
        .baseSpAttack = 40,
        .baseSpDefense = 60,
        .baseSpeed = 42,
        .type1 = TYPE_BUG,
        .type2 = TYPE_GRASS,
        .catchRate = 255,
        .expYield = 98,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SWARM,
        .ability2 = ABILITY_CHLOROPHYLL,
        .hiddenAbility = ABILITY_OVERCOAT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-540LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_STRING_SHOT),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 8, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(15, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(22, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE(29, MOVE_ENDURE),
    LEVEL_UP_MOVE(31, MOVE_STICKY_WEB),
    LEVEL_UP_MOVE(36, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(43, MOVE_FLAIL),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 310
// Types: TYPE_BUG / TYPE_GRASS
// Abilities: ABILITY_SWARM, ABILITY_CHLOROPHYLL, ABILITY_OVERCOAT
// Level Up Moves: 9
// Generation: 9

