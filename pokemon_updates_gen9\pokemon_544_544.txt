// POKEMON_544 (#544) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_544] =
    {
        .baseHP = 40,
        .baseAttack = 55,
        .baseDefense = 99,
        .baseSpAttack = 40,
        .baseSpDefense = 79,
        .baseSpeed = 47,
        .type1 = TYPE_BUG,
        .type2 = TYPE_POISON,
        .catchRate = 120,
        .expYield = 95,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_POISON-POINT,
        .ability2 = ABILITY_SWARM,
        .hiddenAbility = ABILITY_SPEED-BOOST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-544LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE( 1, MOVE_POISON_STING),
    LEVEL_UP_MOVE( 1, MOVE_PROTECT),
    LEVEL_UP_MOVE( 1, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(12, MOVE_POISON_TAIL),
    LEVEL_UP_MOVE(16, MOVE_SCREECH),
    LEVEL_UP_MOVE(20, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(26, MOVE_VENOSHOCK),
    LEVEL_UP_MOVE(32, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(38, MOVE_AGILITY),
    LEVEL_UP_MOVE(44, MOVE_TOXIC),
    LEVEL_UP_MOVE(50, MOVE_VENOM_DRENCH),
    LEVEL_UP_MOVE(56, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 360
// Types: TYPE_BUG / TYPE_POISON
// Abilities: ABILITY_POISON-POINT, ABILITY_SWARM, ABILITY_SPEED-BOOST
// Level Up Moves: 15
// Generation: 8

