// POKEMON_548 (#548) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_548] =
    {
        .baseHP = 45,
        .baseAttack = 35,
        .baseDefense = 50,
        .baseSpAttack = 70,
        .baseSpDefense = 50,
        .baseSpeed = 30,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 190,
        .expYield = 80,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(100.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_CHLOROPHYLL,
        .ability2 = ABILITY_OWN-TEMPO,
        .hiddenAbility = ABILITY_LEAF-GUARD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-548LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 3, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 6, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE( 9, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(12, MOVE_CHARM),
    LEVEL_UP_MOVE(15, MOVE_MAGICAL_LEAF),
    LEVEL_UP_MOVE(18, MOVE_SLEEP_POWDER),
    LEVEL_UP_MOVE(21, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(24, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(27, MOVE_AFTER_YOU),
    LEVEL_UP_MOVE(30, MOVE_ENERGY_BALL),
    LEVEL_UP_MOVE(33, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(36, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE(39, MOVE_ENTRAINMENT),
    LEVEL_UP_MOVE(42, MOVE_LEAF_STORM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 280
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_CHLOROPHYLL, ABILITY_OWN-TEMPO, ABILITY_LEAF-GUARD
// Level Up Moves: 16
// Generation: 9

