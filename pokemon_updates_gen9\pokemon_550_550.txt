// POKEMON_550 (#550) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_550] =
    {
        .baseHP = 70,
        .baseAttack = 92,
        .baseDefense = 65,
        .baseSpAttack = 80,
        .baseSpDefense = 55,
        .baseSpeed = 98,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 25,
        .expYield = 162,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 40,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_RECKLESS,
        .ability2 = ABILITY_ADAPTABILITY,
        .hiddenAbility = ABILITY_MOLD-BREAKER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-550LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 4, MOVE_TACKLE),
    LEVEL_UP_MOVE( 8, MOVE_FLAIL),
    LEVEL_UP_MOVE(12, MOVE_AQUA_JET),
    LEVEL_UP_MOVE(16, MOVE_BITE),
    LEVEL_UP_MOVE(20, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(24, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(28, MOVE_SOAK),
    LEVEL_UP_MOVE(32, MOVE_CRUNCH),
    LEVEL_UP_MOVE(36, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(40, MOVE_FINAL_GAMBIT),
    LEVEL_UP_MOVE(44, MOVE_WAVE_CRASH),
    LEVEL_UP_MOVE(48, MOVE_THRASH),
    LEVEL_UP_MOVE(52, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(56, MOVE_HEAD_SMASH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 460
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_RECKLESS, ABILITY_ADAPTABILITY, ABILITY_MOLD-BREAKER
// Level Up Moves: 16
// Generation: 9

