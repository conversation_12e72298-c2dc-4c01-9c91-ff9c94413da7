// POKEMON_552 (#552) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_552] =
    {
        .baseHP = 60,
        .baseAttack = 82,
        .baseDefense = 45,
        .baseSpAttack = 45,
        .baseSpDefense = 45,
        .baseSpeed = 74,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_DARK,
        .catchRate = 90,
        .expYield = 142,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_INTIMIDATE,
        .ability2 = ABILITY_MOXIE,
        .hiddenAbility = ABILITY_ANGER-POINT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-552LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_POWER_TRIP),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 9, MOVE_SAND_TOMB),
    LEVEL_UP_MOVE(12, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(15, MOVE_BITE),
    LEVEL_UP_MOVE(18, MOVE_TORMENT),
    LEVEL_UP_MOVE(21, MOVE_DIG),
    LEVEL_UP_MOVE(24, MOVE_SWAGGER),
    LEVEL_UP_MOVE(27, MOVE_CRUNCH),
    LEVEL_UP_MOVE(32, MOVE_SANDSTORM),
    LEVEL_UP_MOVE(35, MOVE_FOUL_PLAY),
    LEVEL_UP_MOVE(42, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(47, MOVE_THRASH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 351
// Types: TYPE_GROUND / TYPE_DARK
// Abilities: ABILITY_INTIMIDATE, ABILITY_MOXIE, ABILITY_ANGER-POINT
// Level Up Moves: 15
// Generation: 9

