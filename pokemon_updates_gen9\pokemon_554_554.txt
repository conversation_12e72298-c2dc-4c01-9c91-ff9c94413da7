// POKEMON_554 (#554) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_554] =
    {
        .baseHP = 70,
        .baseAttack = 90,
        .baseDefense = 45,
        .baseSpAttack = 15,
        .baseSpDefense = 45,
        .baseSpeed = 50,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 120,
        .expYield = 160,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_HUSTLE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_INNER-FOCUS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-554LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 4, MOVE_TAUNT),
    LEVEL_UP_MOVE( 8, MOVE_BITE),
    LEVEL_UP_MOVE(12, MOVE_INCINERATE),
    LEVEL_UP_MOVE(16, MOVE_WORK_UP),
    LEVEL_UP_MOVE(20, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE(24, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(28, MOVE_FIRE_PUNCH),
    LEVEL_UP_MOVE(32, MOVE_UPROAR),
    LEVEL_UP_MOVE(36, MOVE_BELLY_DRUM),
    LEVEL_UP_MOVE(40, MOVE_FLARE_BLITZ),
    LEVEL_UP_MOVE(44, MOVE_THRASH),
    LEVEL_UP_MOVE(48, MOVE_SUPERPOWER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 315
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_HUSTLE, ABILITY_NONE, ABILITY_INNER-FOCUS
// Level Up Moves: 14
// Generation: 8

