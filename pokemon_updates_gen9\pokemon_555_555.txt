// POKEMON_555 (#555) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_555] =
    {
        .baseHP = 105,
        .baseAttack = 140,
        .baseDefense = 55,
        .baseSpAttack = 30,
        .baseSpDefense = 55,
        .baseSpeed = 95,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 60,
        .expYield = 245,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SHEER-FORCE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_ZEN-MODE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-555LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAUNT),
    LEVEL_UP_MOVE(12, MOVE_INCINERATE),
    LEVEL_UP_MOVE(16, MOVE_WORK_UP),
    LEVEL_UP_MOVE(20, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE(24, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(28, MOVE_FIRE_PUNCH),
    LEVEL_UP_MOVE(32, MOVE_UPROAR),
    LEVEL_UP_MOVE(38, MOVE_BELLY_DRUM),
    LEVEL_UP_MOVE(44, MOVE_FLARE_BLITZ),
    LEVEL_UP_MOVE(50, MOVE_THRASH),
    LEVEL_UP_MOVE(56, MOVE_SUPERPOWER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 480
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_SHEER-FORCE, ABILITY_NONE, ABILITY_ZEN-MODE
// Level Up Moves: 16
// Generation: 8

