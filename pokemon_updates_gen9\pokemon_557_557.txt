// POKEMON_557 (#557) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_557] =
    {
        .baseHP = 50,
        .baseAttack = 65,
        .baseDefense = 85,
        .baseSpAttack = 35,
        .baseSpDefense = 35,
        .baseSpeed = 55,
        .type1 = TYPE_BUG,
        .type2 = TYPE_ROCK,
        .catchRate = 190,
        .expYield = 115,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STURDY,
        .ability2 = ABILITY_SHELL-ARMOR,
        .hiddenAbility = ABILITY_WEAK-ARMOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-557LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 4, MOVE_WITHDRAW),
    LEVEL_UP_MOVE( 8, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE(12, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(16, MOVE_FLAIL),
    LEVEL_UP_MOVE(20, MOVE_SLASH),
    LEVEL_UP_MOVE(24, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(28, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(32, MOVE_ROCK_BLAST),
    LEVEL_UP_MOVE(36, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(40, MOVE_ROCK_POLISH),
    LEVEL_UP_MOVE(44, MOVE_SHELL_SMASH),
    LEVEL_UP_MOVE(48, MOVE_ROCK_WRECKER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 325
// Types: TYPE_BUG / TYPE_ROCK
// Abilities: ABILITY_STURDY, ABILITY_SHELL-ARMOR, ABILITY_WEAK-ARMOR
// Level Up Moves: 14
// Generation: 8

