// POKEMON_561 (#561) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_561] =
    {
        .baseHP = 72,
        .baseAttack = 58,
        .baseDefense = 80,
        .baseSpAttack = 103,
        .baseSpDefense = 80,
        .baseSpeed = 97,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 130,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_WONDER-SKIN,
        .ability2 = ABILITY_MAGIC-GUARD,
        .hiddenAbility = ABILITY_TINTED-LENS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-561LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 5, MOVE_GRAVITY),
    LEVEL_UP_MOVE(10, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE(15, MOVE_AIR_CUTTER),
    LEVEL_UP_MOVE(20, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(25, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(30, MOVE_COSMIC_POWER),
    LEVEL_UP_MOVE(35, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(40, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(45, MOVE_TAILWIND),
    LEVEL_UP_MOVE(50, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(50, MOVE_REFLECT),
    LEVEL_UP_MOVE(55, MOVE_SKY_ATTACK),
    LEVEL_UP_MOVE(60, MOVE_SKILL_SWAP),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 490
// Types: TYPE_PSYCHIC / TYPE_FLYING
// Abilities: ABILITY_WONDER-SKIN, ABILITY_MAGIC-GUARD, ABILITY_TINTED-LENS
// Level Up Moves: 15
// Generation: 8

