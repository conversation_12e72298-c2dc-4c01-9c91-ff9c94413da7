// POKEMON_562 (#562) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_562] =
    {
        .baseHP = 38,
        .baseAttack = 30,
        .baseDefense = 85,
        .baseSpAttack = 55,
        .baseSpDefense = 65,
        .baseSpeed = 30,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_GHOST,
        .catchRate = 190,
        .expYield = 68,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_MUMMY,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-562LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_PROTECT),
    LEVEL_UP_MOVE( 4, MOVE_HAZE),
    LEVEL_UP_MOVE( 8, MOVE_NIGHT_SHADE),
    LEVEL_UP_MOVE(12, MOVE_DISABLE),
    LEVEL_UP_MOVE(16, MOVE_WILL_O_WISP),
    LEVEL_UP_MOVE(20, MOVE_CRAFTY_SHIELD),
    LEVEL_UP_MOVE(24, MOVE_HEX),
    LEVEL_UP_MOVE(28, MOVE_MEAN_LOOK),
    LEVEL_UP_MOVE(32, MOVE_GRUDGE),
    LEVEL_UP_MOVE(36, MOVE_CURSE),
    LEVEL_UP_MOVE(40, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(44, MOVE_DARK_PULSE),
    LEVEL_UP_MOVE(48, MOVE_GUARD_SPLIT),
    LEVEL_UP_MOVE(48, MOVE_POWER_SPLIT),
    LEVEL_UP_MOVE(52, MOVE_DESTINY_BOND),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 303
// Types: TYPE_GHOST / TYPE_GHOST
// Abilities: ABILITY_MUMMY, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
// Generation: 8

