// POKEMON_564 (#564) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_564] =
    {
        .baseHP = 54,
        .baseAttack = 78,
        .baseDefense = 103,
        .baseSpAttack = 53,
        .baseSpDefense = 45,
        .baseSpeed = 22,
        .type1 = TYPE_WATER,
        .type2 = TYPE_ROCK,
        .catchRate = 45,
        .expYield = 132,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SOLID-ROCK,
        .ability2 = ABILITY_STURDY,
        .hiddenAbility = ABILITY_SWIFT-SWIM,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-564LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_WITHDRAW),
    LEVEL_UP_MOVE( 3, MOVE_PROTECT),
    LEVEL_UP_MOVE( 6, MOVE_AQUA_JET),
    LEVEL_UP_MOVE( 9, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE(12, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(15, MOVE_BITE),
    LEVEL_UP_MOVE(18, MOVE_WIDE_GUARD),
    LEVEL_UP_MOVE(21, MOVE_BRINE),
    LEVEL_UP_MOVE(24, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(27, MOVE_CRUNCH),
    LEVEL_UP_MOVE(30, MOVE_CURSE),
    LEVEL_UP_MOVE(33, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(36, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(39, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(42, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(45, MOVE_SHELL_SMASH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 355
// Types: TYPE_WATER / TYPE_ROCK
// Abilities: ABILITY_SOLID-ROCK, ABILITY_STURDY, ABILITY_SWIFT-SWIM
// Level Up Moves: 17
// Generation: 8

