// POKEMON_570 (#570) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_570] =
    {
        .baseHP = 40,
        .baseAttack = 65,
        .baseDefense = 40,
        .baseSpAttack = 80,
        .baseSpDefense = 40,
        .baseSpeed = 65,
        .type1 = TYPE_DARK,
        .type2 = TYPE_DARK,
        .catchRate = 75,
        .expYield = 105,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_ILLUSION,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-570LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 4, MOVE_TORMENT),
    LEVEL_UP_MOVE( 8, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(12, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(16, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(20, MOVE_TAUNT),
    LEVEL_UP_MOVE(24, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(28, MOVE_FAKE_TEARS),
    LEVEL_UP_MOVE(32, MOVE_AGILITY),
    LEVEL_UP_MOVE(36, MOVE_IMPRISON),
    LEVEL_UP_MOVE(40, MOVE_NIGHT_DAZE),
    LEVEL_UP_MOVE(44, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(48, MOVE_FOUL_PLAY),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 330
// Types: TYPE_DARK / TYPE_DARK
// Abilities: ABILITY_ILLUSION, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
// Generation: 9

