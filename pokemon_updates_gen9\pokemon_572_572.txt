// POKEMON_572 (#572) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_572] =
    {
        .baseHP = 55,
        .baseAttack = 50,
        .baseDefense = 40,
        .baseSpAttack = 40,
        .baseSpDefense = 40,
        .baseSpeed = 75,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 255,
        .expYield = 105,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(75.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_CUTE-CHARM,
        .ability2 = ABILITY_TECHNICIAN,
        .hiddenAbility = ABILITY_SKILL-LINK,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-572LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 4, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 8, MOVE_ECHOED_VOICE),
    LEVEL_UP_MOVE(12, MOVE_SING),
    LEVEL_UP_MOVE(16, MOVE_CHARM),
    LEVEL_UP_MOVE(20, MOVE_SWIFT),
    LEVEL_UP_MOVE(24, MOVE_ENCORE),
    LEVEL_UP_MOVE(28, MOVE_AFTER_YOU),
    LEVEL_UP_MOVE(32, MOVE_TAIL_SLAP),
    LEVEL_UP_MOVE(36, MOVE_TICKLE),
    LEVEL_UP_MOVE(40, MOVE_SLAM),
    LEVEL_UP_MOVE(44, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(48, MOVE_LAST_RESORT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 300
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_CUTE-CHARM, ABILITY_TECHNICIAN, ABILITY_SKILL-LINK
// Level Up Moves: 14
// Generation: 9

