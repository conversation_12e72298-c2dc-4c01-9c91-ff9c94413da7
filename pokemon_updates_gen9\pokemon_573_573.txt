// POKEMON_573 (#573) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_573] =
    {
        .baseHP = 75,
        .baseAttack = 95,
        .baseDefense = 60,
        .baseSpAttack = 65,
        .baseSpDefense = 60,
        .baseSpeed = 115,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 60,
        .expYield = 170,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(75.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_CUTE-CHARM,
        .ability2 = ABILITY_TECHNICIAN,
        .hiddenAbility = ABILITY_SKILL-LINK,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-573LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_TAIL_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_BULLET_SEED),
    LEVEL_UP_MOVE( 1, MOVE_CHARM),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_SING),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 470
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_CUTE-CHARM, ABILITY_TECHNICIAN, ABILITY_SKILL-LINK
// Level Up Moves: 5
// Generation: 9

