// POKEMON_575 (#575) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_575] =
    {
        .baseHP = 60,
        .baseAttack = 45,
        .baseDefense = 70,
        .baseSpAttack = 75,
        .baseSpDefense = 85,
        .baseSpeed = 55,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 100,
        .expYield = 105,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(75.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_FRISK,
        .ability2 = ABILITY_COMPETITIVE,
        .hiddenAbility = ABILITY_SHADOW-TAG,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-575LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_PLAY_NICE),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_TICKLE),
    LEVEL_UP_MOVE(12, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(16, MOVE_CHARM),
    LEVEL_UP_MOVE(20, MOVE_PSYSHOCK),
    LEVEL_UP_MOVE(24, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE(28, MOVE_FAKE_TEARS),
    LEVEL_UP_MOVE(35, MOVE_PSYCH_UP),
    LEVEL_UP_MOVE(46, MOVE_FLATTER),
    LEVEL_UP_MOVE(52, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(58, MOVE_MAGIC_ROOM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 390
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_FRISK, ABILITY_COMPETITIVE, ABILITY_SHADOW-TAG
// Level Up Moves: 13
// Generation: 9

