// POKEMON_576 (#576) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_576] =
    {
        .baseHP = 70,
        .baseAttack = 55,
        .baseDefense = 95,
        .baseSpAttack = 95,
        .baseSpDefense = 110,
        .baseSpeed = 65,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 50,
        .expYield = 125,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(75.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_FRISK,
        .ability2 = ABILITY_COMPETITIVE,
        .hiddenAbility = ABILITY_SHADOW-TAG,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-576LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_PLAY_NICE),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_TICKLE),
    LEVEL_UP_MOVE(12, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(16, MOVE_CHARM),
    LEVEL_UP_MOVE(20, MOVE_PSYSHOCK),
    LEVEL_UP_MOVE(24, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE(28, MOVE_FAKE_TEARS),
    LEVEL_UP_MOVE(35, MOVE_PSYCH_UP),
    LEVEL_UP_MOVE(40, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(48, MOVE_FLATTER),
    LEVEL_UP_MOVE(56, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(64, MOVE_MAGIC_ROOM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 490
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_FRISK, ABILITY_COMPETITIVE, ABILITY_SHADOW-TAG
// Level Up Moves: 14
// Generation: 9

