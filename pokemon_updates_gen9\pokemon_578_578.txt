// POKEMON_578 (#578) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_578] =
    {
        .baseHP = 65,
        .baseAttack = 40,
        .baseDefense = 50,
        .baseSpAttack = 125,
        .baseSpDefense = 60,
        .baseSpeed = 30,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 100,
        .expYield = 105,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_OVERCOAT,
        .ability2 = ABILITY_MAGIC-GUARD,
        .hiddenAbility = ABILITY_REGENERATOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-578LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE( 1, MOVE_PROTECT),
    LEVEL_UP_MOVE( 1, MOVE_RECOVER),
    LEVEL_UP_MOVE(12, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(16, MOVE_CHARM),
    LEVEL_UP_MOVE(20, MOVE_PSYSHOCK),
    LEVEL_UP_MOVE(24, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(24, MOVE_REFLECT),
    LEVEL_UP_MOVE(28, MOVE_ALLY_SWITCH),
    LEVEL_UP_MOVE(35, MOVE_PAIN_SPLIT),
    LEVEL_UP_MOVE(40, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(46, MOVE_SKILL_SWAP),
    LEVEL_UP_MOVE(52, MOVE_FUTURE_SIGHT),
    LEVEL_UP_MOVE(58, MOVE_WONDER_ROOM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 370
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_OVERCOAT, ABILITY_MAGIC-GUARD, ABILITY_REGENERATOR
// Level Up Moves: 15
// Generation: 9

