// POKEMON_581 (#581) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_581] =
    {
        .baseHP = 75,
        .baseAttack = 87,
        .baseDefense = 63,
        .baseSpAttack = 87,
        .baseSpDefense = 63,
        .baseSpeed = 98,
        .type1 = TYPE_WATER,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 162,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_KEEN-EYE,
        .ability2 = ABILITY_BIG-PECKS,
        .hiddenAbility = ABILITY_HYDRATION,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-581LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DEFOG),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(13, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(15, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(19, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(21, MOVE_FEATHER_DANCE),
    LEVEL_UP_MOVE(24, MOVE_AQUA_RING),
    LEVEL_UP_MOVE(27, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(30, MOVE_ROOST),
    LEVEL_UP_MOVE(34, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(40, MOVE_TAILWIND),
    LEVEL_UP_MOVE(47, MOVE_BRAVE_BIRD),
    LEVEL_UP_MOVE(55, MOVE_HURRICANE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 473
// Types: TYPE_WATER / TYPE_FLYING
// Abilities: ABILITY_KEEN-EYE, ABILITY_BIG-PECKS, ABILITY_HYDRATION
// Level Up Moves: 14
// Generation: 9

