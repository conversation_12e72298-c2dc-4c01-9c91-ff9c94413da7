// POKEMON_583 (#583) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_583] =
    {
        .baseHP = 51,
        .baseAttack = 65,
        .baseDefense = 65,
        .baseSpAttack = 80,
        .baseSpDefense = 75,
        .baseSpeed = 59,
        .type1 = TYPE_ICE,
        .type2 = TYPE_ICE,
        .catchRate = 120,
        .expYield = 116,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_ICE-BODY,
        .ability2 = ABILITY_SNOW-CLOAK,
        .hiddenAbility = ABILITY_WEAK-ARMOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-583LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_MIST),
    LEVEL_UP_MOVE( 1, MOVE_TAUNT),
    LEVEL_UP_MOVE(12, MOVE_ICY_WIND),
    LEVEL_UP_MOVE(16, MOVE_AVALANCHE),
    LEVEL_UP_MOVE(20, MOVE_HAIL),
    LEVEL_UP_MOVE(24, MOVE_ICICLE_SPEAR),
    LEVEL_UP_MOVE(28, MOVE_UPROAR),
    LEVEL_UP_MOVE(32, MOVE_ACID_ARMOR),
    LEVEL_UP_MOVE(38, MOVE_MIRROR_COAT),
    LEVEL_UP_MOVE(44, MOVE_ICE_BEAM),
    LEVEL_UP_MOVE(50, MOVE_BLIZZARD),
    LEVEL_UP_MOVE(56, MOVE_SHEER_COLD),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 395
// Types: TYPE_ICE / TYPE_ICE
// Abilities: ABILITY_ICE-BODY, ABILITY_SNOW-CLOAK, ABILITY_WEAK-ARMOR
// Level Up Moves: 14
// Generation: 8

