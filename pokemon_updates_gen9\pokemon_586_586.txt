// POKEMON_586 (#586) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_586] =
    {
        .baseHP = 80,
        .baseAttack = 100,
        .baseDefense = 70,
        .baseSpAttack = 60,
        .baseSpDefense = 70,
        .baseSpeed = 95,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_GRASS,
        .catchRate = 75,
        .expYield = 180,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_CHLOROPHYLL,
        .ability2 = ABILITY_SAP-SIPPER,
        .hiddenAbility = ABILITY_SERENE-GRACE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-586LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_HORN_LEECH),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_MEGAHORN),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE(10, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(13, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(16, MOVE_BULLET_SEED),
    LEVEL_UP_MOVE(20, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(24, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(28, MOVE_ENERGY_BALL),
    LEVEL_UP_MOVE(36, MOVE_CHARM),
    LEVEL_UP_MOVE(44, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(52, MOVE_SOLAR_BEAM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 475
// Types: TYPE_NORMAL / TYPE_GRASS
// Abilities: ABILITY_CHLOROPHYLL, ABILITY_SAP-SIPPER, ABILITY_SERENE-GRACE
// Level Up Moves: 14
// Generation: 9

