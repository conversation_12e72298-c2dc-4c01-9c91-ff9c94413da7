// POKEMON_587 (#587) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_587] =
    {
        .baseHP = 55,
        .baseAttack = 75,
        .baseDefense = 60,
        .baseSpAttack = 75,
        .baseSpDefense = 60,
        .baseSpeed = 103,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_FLYING,
        .catchRate = 200,
        .expYield = 130,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STATIC,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_MOTOR-DRIVE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-587LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_NUZZLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 5, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(10, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(15, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE(20, MOVE_CHARGE),
    LEVEL_UP_MOVE(25, MOVE_ACROBATICS),
    LEVEL_UP_MOVE(30, MOVE_SPARK),
    LEVEL_UP_MOVE(35, MOVE_ENCORE),
    LEVEL_UP_MOVE(40, MOVE_VOLT_SWITCH),
    LEVEL_UP_MOVE(45, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(50, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(55, MOVE_AGILITY),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 428
// Types: TYPE_ELECTRIC / TYPE_FLYING
// Abilities: ABILITY_STATIC, ABILITY_NONE, ABILITY_MOTOR-DRIVE
// Level Up Moves: 13
// Generation: 8

