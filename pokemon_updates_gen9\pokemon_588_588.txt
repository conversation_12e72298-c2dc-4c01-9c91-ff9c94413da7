// POKEMON_588 (#588) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_588] =
    {
        .baseHP = 50,
        .baseAttack = 75,
        .baseDefense = 45,
        .baseSpAttack = 40,
        .baseSpDefense = 45,
        .baseSpeed = 60,
        .type1 = TYPE_BUG,
        .type2 = TYPE_BUG,
        .catchRate = 200,
        .expYield = 125,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SWARM,
        .ability2 = ABILITY_SHED-SKIN,
        .hiddenAbility = ABILITY_NO-GUARD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-588LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 4, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 8, MOVE_ENDURE),
    LEVEL_UP_MOVE(12, MOVE_FALSE_SWIPE),
    LEVEL_UP_MOVE(16, MOVE_ACID_SPRAY),
    LEVEL_UP_MOVE(20, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(24, MOVE_FLAIL),
    LEVEL_UP_MOVE(28, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(32, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(36, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(40, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(44, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(48, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 315
// Types: TYPE_BUG / TYPE_BUG
// Abilities: ABILITY_SWARM, ABILITY_SHED-SKIN, ABILITY_NO-GUARD
// Level Up Moves: 14
// Generation: 8

