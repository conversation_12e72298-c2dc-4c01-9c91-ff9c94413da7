// POKEMON_590 (#590) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_590] =
    {
        .baseHP = 69,
        .baseAttack = 55,
        .baseDefense = 45,
        .baseSpAttack = 55,
        .baseSpDefense = 55,
        .baseSpeed = 15,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_POISON,
        .catchRate = 190,
        .expYield = 124,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_EFFECT-SPORE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_REGENERATOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-590LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 4, MOVE_GROWTH),
    LEVEL_UP_MOVE( 8, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE(12, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(16, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(20, MOVE_CLEAR_SMOG),
    LEVEL_UP_MOVE(24, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(28, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(32, MOVE_INGRAIN),
    LEVEL_UP_MOVE(36, MOVE_TOXIC),
    LEVEL_UP_MOVE(40, MOVE_RAGE_POWDER),
    LEVEL_UP_MOVE(44, MOVE_SOLAR_BEAM),
    LEVEL_UP_MOVE(48, MOVE_SPORE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 294
// Types: TYPE_GRASS / TYPE_POISON
// Abilities: ABILITY_EFFECT-SPORE, ABILITY_NONE, ABILITY_REGENERATOR
// Level Up Moves: 14
// Generation: 9

