// POKEMON_593 (#593) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_593] =
    {
        .baseHP = 100,
        .baseAttack = 60,
        .baseDefense = 70,
        .baseSpAttack = 85,
        .baseSpDefense = 105,
        .baseSpeed = 60,
        .type1 = TYPE_WATER,
        .type2 = TYPE_GHOST,
        .catchRate = 60,
        .expYield = 160,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_WATER-ABSORB,
        .ability2 = ABILITY_CURSED-BODY,
        .hiddenAbility = ABILITY_DAMP,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-593LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_ACID_ARMOR),
    LEVEL_UP_MOVE( 1, MOVE_NIGHT_SHADE),
    LEVEL_UP_MOVE( 1, MOVE_POISON_STING),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(12, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(16, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(20, MOVE_HEX),
    LEVEL_UP_MOVE(24, MOVE_BRINE),
    LEVEL_UP_MOVE(28, MOVE_RECOVER),
    LEVEL_UP_MOVE(32, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(36, MOVE_WHIRLPOOL),
    LEVEL_UP_MOVE(43, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(48, MOVE_DESTINY_BOND),
    LEVEL_UP_MOVE(54, MOVE_WATER_SPOUT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 480
// Types: TYPE_WATER / TYPE_GHOST
// Abilities: ABILITY_WATER-ABSORB, ABILITY_CURSED-BODY, ABILITY_DAMP
// Level Up Moves: 15
// Generation: 8

