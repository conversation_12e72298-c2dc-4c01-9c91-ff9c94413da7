// POKEMON_594 (#594) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_594] =
    {
        .baseHP = 165,
        .baseAttack = 75,
        .baseDefense = 80,
        .baseSpAttack = 40,
        .baseSpDefense = 45,
        .baseSpeed = 65,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 75,
        .expYield = 240,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 40,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_HEALER,
        .ability2 = ABILITY_HYDRATION,
        .hiddenAbility = ABILITY_REGENERATOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-594LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_PLAY_NICE),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 5, MOVE_AQUA_RING),
    LEVEL_UP_MOVE( 9, MOVE_AQUA_JET),
    LEVEL_UP_MOVE(13, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(13, MOVE_WIDE_GUARD),
    LEVEL_UP_MOVE(21, MOVE_PROTECT),
    LEVEL_UP_MOVE(25, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(29, MOVE_HEALING_WISH),
    LEVEL_UP_MOVE(33, MOVE_SOAK),
    LEVEL_UP_MOVE(37, MOVE_WISH),
    LEVEL_UP_MOVE(41, MOVE_BRINE),
    LEVEL_UP_MOVE(45, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(49, MOVE_WHIRLPOOL),
    LEVEL_UP_MOVE(55, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 470
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_HEALER, ABILITY_HYDRATION, ABILITY_REGENERATOR
// Level Up Moves: 15
// Generation: 9

