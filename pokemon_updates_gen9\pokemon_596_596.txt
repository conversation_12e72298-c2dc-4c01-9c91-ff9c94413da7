// POKEMON_596 (#596) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_596] =
    {
        .baseHP = 70,
        .baseAttack = 77,
        .baseDefense = 60,
        .baseSpAttack = 97,
        .baseSpDefense = 60,
        .baseSpeed = 108,
        .type1 = TYPE_BUG,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 75,
        .expYield = 147,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_COMPOUND-EYES,
        .ability2 = ABILITY_UNNERVE,
        .hiddenAbility = ABILITY_SWARM,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-596LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_STICKY_WEB),
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_BUG_BITE),
    LEVEL_UP_MOVE( 1, MOVE_ELECTROWEB),
    LEVEL_UP_MOVE( 1, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE(12, MOVE_STRING_SHOT),
    LEVEL_UP_MOVE(16, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE(20, MOVE_ELECTRO_BALL),
    LEVEL_UP_MOVE(24, MOVE_AGILITY),
    LEVEL_UP_MOVE(28, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(32, MOVE_SLASH),
    LEVEL_UP_MOVE(39, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(44, MOVE_SCREECH),
    LEVEL_UP_MOVE(50, MOVE_GASTRO_ACID),
    LEVEL_UP_MOVE(56, MOVE_BUG_BUZZ),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 472
// Types: TYPE_BUG / TYPE_ELECTRIC
// Abilities: ABILITY_COMPOUND-EYES, ABILITY_UNNERVE, ABILITY_SWARM
// Level Up Moves: 15
// Generation: 9

