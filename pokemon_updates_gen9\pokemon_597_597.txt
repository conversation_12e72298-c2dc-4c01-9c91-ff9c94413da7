// POKEMON_597 (#597) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_597] =
    {
        .baseHP = 44,
        .baseAttack = 50,
        .baseDefense = 91,
        .baseSpAttack = 24,
        .baseSpDefense = 86,
        .baseSpeed = 10,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_STEEL,
        .catchRate = 255,
        .expYield = 94,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_IRON-BARBS,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-597LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(10, MOVE_PIN_MISSILE),
    LEVEL_UP_MOVE(15, MOVE_INGRAIN),
    LEVEL_UP_MOVE(20, MOVE_FLASH_CANNON),
    LEVEL_UP_MOVE(25, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(30, MOVE_SELF_DESTRUCT),
    LEVEL_UP_MOVE(35, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(41, MOVE_CURSE),
    LEVEL_UP_MOVE(45, MOVE_GYRO_BALL),
    LEVEL_UP_MOVE(50, MOVE_EXPLOSION),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 305
// Types: TYPE_GRASS / TYPE_STEEL
// Abilities: ABILITY_IRON-BARBS, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 12
// Generation: 8

