// POKEMON_598 (#598) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_598] =
    {
        .baseHP = 74,
        .baseAttack = 94,
        .baseDefense = 131,
        .baseSpAttack = 54,
        .baseSpDefense = 116,
        .baseSpeed = 20,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_STEEL,
        .catchRate = 90,
        .expYield = 168,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_IRON-BARBS,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_ANTICIPATION,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-598LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_POWER_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE( 1, MOVE_PIN_MISSILE),
    LEVEL_UP_MOVE( 1, MOVE_POWER_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE(15, MOVE_INGRAIN),
    LEVEL_UP_MOVE(20, MOVE_FLASH_CANNON),
    LEVEL_UP_MOVE(25, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(30, MOVE_SELF_DESTRUCT),
    LEVEL_UP_MOVE(35, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(43, MOVE_CURSE),
    LEVEL_UP_MOVE(49, MOVE_GYRO_BALL),
    LEVEL_UP_MOVE(56, MOVE_EXPLOSION),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 489
// Types: TYPE_GRASS / TYPE_STEEL
// Abilities: ABILITY_IRON-BARBS, ABILITY_NONE, ABILITY_ANTICIPATION
// Level Up Moves: 14
// Generation: 8

