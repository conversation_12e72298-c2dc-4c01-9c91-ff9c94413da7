// POKEMON_604 (#604) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_604] =
    {
        .baseHP = 85,
        .baseAttack = 115,
        .baseDefense = 80,
        .baseSpAttack = 105,
        .baseSpDefense = 80,
        .baseSpeed = 50,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 30,
        .expYield = 200,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-604LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ACID),
    LEVEL_UP_MOVE( 1, MOVE_COIL),
    LEVEL_UP_MOVE( 1, MOVE_CRUNCH),
    LEVEL_UP_MOVE( 1, MOVE_CRUSH_CLAW),
    LEVEL_UP_MOVE( 1, MOVE_DISCHARGE),
    LEVEL_UP_MOVE( 1, MOVE_GASTRO_ACID),
    LEVEL_UP_MOVE( 1, MOVE_HEADBUTT),
    LEVEL_UP_MOVE( 1, MOVE_THRASH),
    LEVEL_UP_MOVE( 1, MOVE_ZAP_CANNON),
    LEVEL_UP_MOVE( 5, MOVE_WILD_CHARGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 515
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_LEVITATE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 10
// Generation: 9

