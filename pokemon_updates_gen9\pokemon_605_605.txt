// POKEMON_605 (#605) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_605] =
    {
        .baseHP = 55,
        .baseAttack = 55,
        .baseDefense = 55,
        .baseSpAttack = 85,
        .baseSpDefense = 55,
        .baseSpeed = 30,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 255,
        .expYield = 110,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_TELEPATHY,
        .ability2 = ABILITY_SYNCHRONIZE,
        .hiddenAbility = ABILITY_ANALYTIC,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-605LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 6, MOVE_IMPRISON),
    LEVEL_UP_MOVE(12, MOVE_TELEPORT),
    LEVEL_UP_MOVE(18, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(24, MOVE_GUARD_SPLIT),
    LEVEL_UP_MOVE(24, MOVE_POWER_SPLIT),
    LEVEL_UP_MOVE(30, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(36, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(43, MOVE_RECOVER),
    LEVEL_UP_MOVE(48, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(54, MOVE_WONDER_ROOM),
    LEVEL_UP_MOVE(60, MOVE_PSYCHIC),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 335
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_TELEPATHY, ABILITY_SYNCHRONIZE, ABILITY_ANALYTIC
// Level Up Moves: 13
// Generation: 8

