// POKEMON_606 (#606) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_606] =
    {
        .baseHP = 75,
        .baseAttack = 75,
        .baseDefense = 75,
        .baseSpAttack = 125,
        .baseSpDefense = 95,
        .baseSpeed = 40,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 90,
        .expYield = 150,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_TELEPATHY,
        .ability2 = ABILITY_SYNCHRONIZE,
        .hiddenAbility = ABILITY_ANALYTIC,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-606LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_IMPRISON),
    LEVEL_UP_MOVE( 1, MOVE_PSYCHIC_TERRAIN),
    LEVEL_UP_MOVE( 1, MOVE_TELEPORT),
    LEVEL_UP_MOVE(18, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(24, MOVE_GUARD_SPLIT),
    LEVEL_UP_MOVE(24, MOVE_POWER_SPLIT),
    LEVEL_UP_MOVE(30, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(36, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(45, MOVE_RECOVER),
    LEVEL_UP_MOVE(52, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(60, MOVE_WONDER_ROOM),
    LEVEL_UP_MOVE(68, MOVE_PSYCHIC),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 485
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_TELEPATHY, ABILITY_SYNCHRONIZE, ABILITY_ANALYTIC
// Level Up Moves: 14
// Generation: 8

