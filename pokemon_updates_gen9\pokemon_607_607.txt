// POKEMON_607 (#607) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_607] =
    {
        .baseHP = 50,
        .baseAttack = 30,
        .baseDefense = 55,
        .baseSpAttack = 65,
        .baseSpDefense = 55,
        .baseSpeed = 20,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_FIRE,
        .catchRate = 190,
        .expYield = 80,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_FLASH-FIRE,
        .ability2 = ABILITY_FLAME-BODY,
        .hiddenAbility = ABILITY_INFILTRATOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-607LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_SMOG),
    LEVEL_UP_MOVE( 4, MOVE_EMBER),
    LEVEL_UP_MOVE( 8, MOVE_MINIMIZE),
    LEVEL_UP_MOVE(12, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(16, MOVE_HEX),
    LEVEL_UP_MOVE(20, MOVE_WILL_O_WISP),
    LEVEL_UP_MOVE(24, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE(28, MOVE_NIGHT_SHADE),
    LEVEL_UP_MOVE(32, MOVE_CURSE),
    LEVEL_UP_MOVE(36, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(40, MOVE_INFERNO),
    LEVEL_UP_MOVE(44, MOVE_IMPRISON),
    LEVEL_UP_MOVE(48, MOVE_PAIN_SPLIT),
    LEVEL_UP_MOVE(52, MOVE_OVERHEAT),
    LEVEL_UP_MOVE(56, MOVE_MEMENTO),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 275
// Types: TYPE_GHOST / TYPE_FIRE
// Abilities: ABILITY_FLASH-FIRE, ABILITY_FLAME-BODY, ABILITY_INFILTRATOR
// Level Up Moves: 16
// Generation: 9

