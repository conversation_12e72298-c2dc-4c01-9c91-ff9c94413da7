// POKEMON_613 (#613) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_613] =
    {
        .baseHP = 55,
        .baseAttack = 70,
        .baseDefense = 40,
        .baseSpAttack = 60,
        .baseSpDefense = 40,
        .baseSpeed = 40,
        .type1 = TYPE_ICE,
        .type2 = TYPE_ICE,
        .catchRate = 120,
        .expYield = 125,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SNOW-CLOAK,
        .ability2 = ABILITY_SLUSH-RUSH,
        .hiddenAbility = ABILITY_RATTLED,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-613LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_POWDER_SNOW),
    LEVEL_UP_MOVE( 3, MOVE_ENDURE),
    LEVEL_UP_MOVE( 6, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE( 9, MOVE_ICY_WIND),
    LEVEL_UP_MOVE(12, MOVE_PLAY_NICE),
    LEVEL_UP_MOVE(15, MOVE_BRINE),
    LEVEL_UP_MOVE(18, MOVE_FROST_BREATH),
    LEVEL_UP_MOVE(21, MOVE_SLASH),
    LEVEL_UP_MOVE(24, MOVE_FLAIL),
    LEVEL_UP_MOVE(27, MOVE_CHARM),
    LEVEL_UP_MOVE(30, MOVE_SNOWSCAPE),
    LEVEL_UP_MOVE(33, MOVE_THRASH),
    LEVEL_UP_MOVE(36, MOVE_REST),
    LEVEL_UP_MOVE(39, MOVE_BLIZZARD),
    LEVEL_UP_MOVE(42, MOVE_SHEER_COLD),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 305
// Types: TYPE_ICE / TYPE_ICE
// Abilities: ABILITY_SNOW-CLOAK, ABILITY_SLUSH-RUSH, ABILITY_RATTLED
// Level Up Moves: 16
// Generation: 9

