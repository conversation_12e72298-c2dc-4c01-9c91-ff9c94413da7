// POKEMON_615 (#615) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_615] =
    {
        .baseHP = 80,
        .baseAttack = 50,
        .baseDefense = 50,
        .baseSpAttack = 95,
        .baseSpDefense = 135,
        .baseSpeed = 105,
        .type1 = TYPE_ICE,
        .type2 = TYPE_ICE,
        .catchRate = 25,
        .expYield = 130,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-615LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BIND),
    LEVEL_UP_MOVE( 1, MOVE_ICE_SHARD),
    LEVEL_UP_MOVE( 4, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE( 8, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE(12, MOVE_ICY_WIND),
    LEVEL_UP_MOVE(16, MOVE_HAZE),
    LEVEL_UP_MOVE(16, MOVE_MIST),
    LEVEL_UP_MOVE(20, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(24, MOVE_AURORA_BEAM),
    LEVEL_UP_MOVE(28, MOVE_SLASH),
    LEVEL_UP_MOVE(32, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(36, MOVE_FREEZE_DRY),
    LEVEL_UP_MOVE(40, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(40, MOVE_REFLECT),
    LEVEL_UP_MOVE(44, MOVE_RECOVER),
    LEVEL_UP_MOVE(48, MOVE_ICE_BEAM),
    LEVEL_UP_MOVE(52, MOVE_ACID_ARMOR),
    LEVEL_UP_MOVE(56, MOVE_SOLAR_BEAM),
    LEVEL_UP_MOVE(60, MOVE_SHEER_COLD),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 515
// Types: TYPE_ICE / TYPE_ICE
// Abilities: ABILITY_LEVITATE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 19
// Generation: 9

