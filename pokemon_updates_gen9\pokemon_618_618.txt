// POKEMON_618 (#618) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_618] =
    {
        .baseHP = 109,
        .baseAttack = 66,
        .baseDefense = 84,
        .baseSpAttack = 81,
        .baseSpDefense = 99,
        .baseSpeed = 32,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 75,
        .expYield = 175,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STATIC,
        .ability2 = ABILITY_LIMBER,
        .hiddenAbility = ABILITY_SAND-VEIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-618LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 5, MOVE_ENDURE),
    LEVEL_UP_MOVE(10, MOVE_MUD_SHOT),
    LEVEL_UP_MOVE(15, MOVE_REVENGE),
    LEVEL_UP_MOVE(20, MOVE_CHARGE),
    LEVEL_UP_MOVE(25, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(30, MOVE_ELECTRIC_TERRAIN),
    LEVEL_UP_MOVE(35, MOVE_BOUNCE),
    LEVEL_UP_MOVE(40, MOVE_MUDDY_WATER),
    LEVEL_UP_MOVE(45, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(50, MOVE_FLAIL),
    LEVEL_UP_MOVE(55, MOVE_FISSURE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 471
// Types: TYPE_GROUND / TYPE_ELECTRIC
// Abilities: ABILITY_STATIC, ABILITY_LIMBER, ABILITY_SAND-VEIL
// Level Up Moves: 15
// Generation: 8

