// POKEMON_619 (#619) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_619] =
    {
        .baseHP = 45,
        .baseAttack = 85,
        .baseDefense = 50,
        .baseSpAttack = 55,
        .baseSpDefense = 50,
        .baseSpeed = 65,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 180,
        .expYield = 130,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_INNER-FOCUS,
        .ability2 = ABILITY_REGENERATOR,
        .hiddenAbility = ABILITY_RECKLESS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-619LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DETECT),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 5, MOVE_FAKE_OUT),
    LEVEL_UP_MOVE(10, MOVE_REVERSAL),
    LEVEL_UP_MOVE(15, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(20, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(25, MOVE_FORCE_PALM),
    LEVEL_UP_MOVE(30, MOVE_U_TURN),
    LEVEL_UP_MOVE(35, MOVE_DRAIN_PUNCH),
    LEVEL_UP_MOVE(40, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(45, MOVE_AURA_SPHERE),
    LEVEL_UP_MOVE(51, MOVE_BOUNCE),
    LEVEL_UP_MOVE(55, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(60, MOVE_HIGH_JUMP_KICK),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 350
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_INNER-FOCUS, ABILITY_REGENERATOR, ABILITY_RECKLESS
// Level Up Moves: 14
// Generation: 9

