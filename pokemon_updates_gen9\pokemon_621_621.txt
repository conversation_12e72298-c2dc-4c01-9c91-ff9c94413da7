// POKEMON_621 (#621) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_621] =
    {
        .baseHP = 77,
        .baseAttack = 120,
        .baseDefense = 90,
        .baseSpAttack = 60,
        .baseSpDefense = 90,
        .baseSpeed = 48,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 197,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_ROUGH-SKIN,
        .ability2 = ABILITY_SHEER-FORCE,
        .hiddenAbility = ABILITY_MOLD-BREAKER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-621LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 5, MOVE_BITE),
    LEVEL_UP_MOVE(10, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE(15, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(20, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(25, MOVE_SLASH),
    LEVEL_UP_MOVE(30, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(35, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(40, MOVE_CRUNCH),
    LEVEL_UP_MOVE(45, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(50, MOVE_OUTRAGE),
    LEVEL_UP_MOVE(55, MOVE_SUPERPOWER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 485
// Types: TYPE_DRAGON / TYPE_DRAGON
// Abilities: ABILITY_ROUGH-SKIN, ABILITY_SHEER-FORCE, ABILITY_MOLD-BREAKER
// Level Up Moves: 13
// Generation: 8

