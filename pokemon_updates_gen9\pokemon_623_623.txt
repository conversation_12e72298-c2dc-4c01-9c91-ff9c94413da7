// POKEMON_623 (#623) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_623] =
    {
        .baseHP = 89,
        .baseAttack = 124,
        .baseDefense = 80,
        .baseSpAttack = 55,
        .baseSpDefense = 80,
        .baseSpeed = 55,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_GHOST,
        .catchRate = 90,
        .expYield = 213,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_IRON-FIST,
        .ability2 = ABILITY_KLUTZ,
        .hiddenAbility = ABILITY_NO-GUARD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-623LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_HIGH_HORSEPOWER),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE(12, MOVE_SHADOW_PUNCH),
    LEVEL_UP_MOVE(16, MOVE_CURSE),
    LEVEL_UP_MOVE(20, MOVE_NIGHT_SHADE),
    LEVEL_UP_MOVE(24, MOVE_STOMPING_TANTRUM),
    LEVEL_UP_MOVE(28, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(32, MOVE_MEGA_PUNCH),
    LEVEL_UP_MOVE(36, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(40, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(46, MOVE_PHANTOM_FORCE),
    LEVEL_UP_MOVE(52, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE(58, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(64, MOVE_DYNAMIC_PUNCH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 483
// Types: TYPE_GROUND / TYPE_GHOST
// Abilities: ABILITY_IRON-FIST, ABILITY_KLUTZ, ABILITY_NO-GUARD
// Level Up Moves: 16
// Generation: 9

