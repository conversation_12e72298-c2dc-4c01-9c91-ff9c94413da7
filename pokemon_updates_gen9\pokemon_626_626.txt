// POKEMON_626 (#626) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_626] =
    {
        .baseHP = 95,
        .baseAttack = 110,
        .baseDefense = 95,
        .baseSpAttack = 40,
        .baseSpDefense = 95,
        .baseSpeed = 55,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 45,
        .expYield = 205,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_RECKLESS,
        .ability2 = ABILITY_SAP-SIPPER,
        .hiddenAbility = ABILITY_SOUNDPROOF,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-626LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(10, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(15, MOVE_REVENGE),
    LEVEL_UP_MOVE(20, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(25, MOVE_HORN_ATTACK),
    LEVEL_UP_MOVE(30, MOVE_REVERSAL),
    LEVEL_UP_MOVE(35, MOVE_THROAT_CHOP),
    LEVEL_UP_MOVE(40, MOVE_HEAD_CHARGE),
    LEVEL_UP_MOVE(45, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(50, MOVE_MEGAHORN),
    LEVEL_UP_MOVE(55, MOVE_GIGA_IMPACT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 490
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_RECKLESS, ABILITY_SAP-SIPPER, ABILITY_SOUNDPROOF
// Level Up Moves: 13
// Generation: 8

