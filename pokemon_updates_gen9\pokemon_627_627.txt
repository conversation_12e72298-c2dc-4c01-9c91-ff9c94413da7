// POKEMON_627 (#627) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_627] =
    {
        .baseHP = 70,
        .baseAttack = 83,
        .baseDefense = 50,
        .baseSpAttack = 37,
        .baseSpDefense = 50,
        .baseSpeed = 60,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 190,
        .expYield = 153,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(0.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_KEEN-EYE,
        .ability2 = ABILITY_SHEER-FORCE,
        .hiddenAbility = ABILITY_HUSTLE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-627LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 6, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(12, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(18, MOVE_TAILWIND),
    LEVEL_UP_MOVE(24, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(30, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(36, MOVE_SLASH),
    LEVEL_UP_MOVE(42, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(48, MOVE_CRUSH_CLAW),
    LEVEL_UP_MOVE(55, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(60, MOVE_DEFOG),
    LEVEL_UP_MOVE(66, MOVE_THRASH),
    LEVEL_UP_MOVE(72, MOVE_BRAVE_BIRD),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 350
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_KEEN-EYE, ABILITY_SHEER-FORCE, ABILITY_HUSTLE
// Level Up Moves: 14
// Generation: 9

