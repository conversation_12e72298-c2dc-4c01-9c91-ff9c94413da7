// POKEMON_628 (#628) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_628] =
    {
        .baseHP = 100,
        .baseAttack = 123,
        .baseDefense = 75,
        .baseSpAttack = 57,
        .baseSpDefense = 75,
        .baseSpeed = 80,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 60,
        .expYield = 223,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(0.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_KEEN-EYE,
        .ability2 = ABILITY_SHEER-FORCE,
        .hiddenAbility = ABILITY_DEFIANT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-628LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_SUPERPOWER),
    LEVEL_UP_MOVE( 1, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_SKY_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(18, MOVE_TAILWIND),
    LEVEL_UP_MOVE(24, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(30, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(36, MOVE_SLASH),
    LEVEL_UP_MOVE(42, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(48, MOVE_CRUSH_CLAW),
    LEVEL_UP_MOVE(57, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(64, MOVE_DEFOG),
    LEVEL_UP_MOVE(72, MOVE_THRASH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 510
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_KEEN-EYE, ABILITY_SHEER-FORCE, ABILITY_DEFIANT
// Level Up Moves: 15
// Generation: 9

