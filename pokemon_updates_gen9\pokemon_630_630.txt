// POKEMON_630 (#630) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_630] =
    {
        .baseHP = 110,
        .baseAttack = 65,
        .baseDefense = 105,
        .baseSpAttack = 55,
        .baseSpDefense = 95,
        .baseSpeed = 80,
        .type1 = TYPE_DARK,
        .type2 = TYPE_FLYING,
        .catchRate = 60,
        .expYield = 175,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(100.0),
        .eggCycles = 20,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BIG-PECKS,
        .ability2 = ABILITY_OVERCOAT,
        .hiddenAbility = ABILITY_WEAK-ARMOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-630LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_BONE_RUSH),
    LEVEL_UP_MOVE( 1, MOVE_FLATTER),
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_PLUCK),
    LEVEL_UP_MOVE( 1, MOVE_SKY_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_TOXIC),
    LEVEL_UP_MOVE(18, MOVE_TAILWIND),
    LEVEL_UP_MOVE(24, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(30, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(36, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(42, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(48, MOVE_DARK_PULSE),
    LEVEL_UP_MOVE(57, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(64, MOVE_DEFOG),
    LEVEL_UP_MOVE(72, MOVE_ATTRACT),
    LEVEL_UP_MOVE(80, MOVE_BRAVE_BIRD),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 510
// Types: TYPE_DARK / TYPE_FLYING
// Abilities: ABILITY_BIG-PECKS, ABILITY_OVERCOAT, ABILITY_WEAK-ARMOR
// Level Up Moves: 17
// Generation: 9

