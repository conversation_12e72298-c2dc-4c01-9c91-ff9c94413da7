// POKEMON_631 (#631) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_631] =
    {
        .baseHP = 85,
        .baseAttack = 97,
        .baseDefense = 66,
        .baseSpAttack = 105,
        .baseSpDefense = 66,
        .baseSpeed = 65,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 90,
        .expYield = 182,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_GLUTTONY,
        .ability2 = ABILITY_FLASH-FIRE,
        .hiddenAbility = ABILITY_WHITE-SMOKE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-631LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LICK),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(10, MOVE_INCINERATE),
    LEVEL_UP_MOVE(15, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(20, MOVE_SPIT_UP),
    LEVEL_UP_MOVE(20, MOVE_STOCKPILE),
    LEVEL_UP_MOVE(20, MOVE_SWALLOW),
    LEVEL_UP_MOVE(25, MOVE_SLASH),
    LEVEL_UP_MOVE(30, MOVE_BIND),
    LEVEL_UP_MOVE(35, MOVE_FIRE_LASH),
    LEVEL_UP_MOVE(40, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(45, MOVE_AMNESIA),
    LEVEL_UP_MOVE(50, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE(55, MOVE_INFERNO),
    LEVEL_UP_MOVE(60, MOVE_FLARE_BLITZ),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 484
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_GLUTTONY, ABILITY_FLASH-FIRE, ABILITY_WHITE-SMOKE
// Level Up Moves: 16
// Generation: 8

