// POKEMON_632 (#632) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_632] =
    {
        .baseHP = 58,
        .baseAttack = 109,
        .baseDefense = 112,
        .baseSpAttack = 48,
        .baseSpDefense = 48,
        .baseSpeed = 109,
        .type1 = TYPE_BUG,
        .type2 = TYPE_STEEL,
        .catchRate = 90,
        .expYield = 167,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SWARM,
        .ability2 = ABILITY_HUSTLE,
        .hiddenAbility = ABILITY_TRUANT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-632LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 4, MOVE_VICE_GRIP),
    LEVEL_UP_MOVE( 8, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(12, MOVE_BEAT_UP),
    LEVEL_UP_MOVE(16, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(20, MOVE_BITE),
    LEVEL_UP_MOVE(24, MOVE_AGILITY),
    LEVEL_UP_MOVE(28, MOVE_DIG),
    LEVEL_UP_MOVE(32, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(36, MOVE_CRUNCH),
    LEVEL_UP_MOVE(40, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(44, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(48, MOVE_ENTRAINMENT),
    LEVEL_UP_MOVE(52, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(56, MOVE_GUILLOTINE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 484
// Types: TYPE_BUG / TYPE_STEEL
// Abilities: ABILITY_SWARM, ABILITY_HUSTLE, ABILITY_TRUANT
// Level Up Moves: 16
// Generation: 8

