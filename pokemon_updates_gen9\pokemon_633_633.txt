// POKEMON_633 (#633) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_633] =
    {
        .baseHP = 52,
        .baseAttack = 65,
        .baseDefense = 50,
        .baseSpAttack = 45,
        .baseSpDefense = 50,
        .baseSpeed = 38,
        .type1 = TYPE_DARK,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 117,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 40,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_HUSTLE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-633LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 4, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE( 8, MOVE_BITE),
    LEVEL_UP_MOVE(12, MOVE_ROAR),
    LEVEL_UP_MOVE(16, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(20, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(24, MOVE_WORK_UP),
    LEVEL_UP_MOVE(28, MOVE_SLAM),
    LEVEL_UP_MOVE(32, MOVE_CRUNCH),
    LEVEL_UP_MOVE(36, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(40, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(44, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(48, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(52, MOVE_DRAGON_RUSH),
    LEVEL_UP_MOVE(56, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(60, MOVE_OUTRAGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 300
// Types: TYPE_DARK / TYPE_DRAGON
// Abilities: ABILITY_HUSTLE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 17
// Generation: 9

