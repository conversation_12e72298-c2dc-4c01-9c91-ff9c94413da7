// POKEMON_639 (#639) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_639] =
    {
        .baseHP = 91,
        .baseAttack = 129,
        .baseDefense = 90,
        .baseSpAttack = 72,
        .baseSpDefense = 90,
        .baseSpeed = 108,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_FIGHTING,
        .catchRate = 3,
        .expYield = 220,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 80,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_JUSTIFIED,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-639LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_WORK_UP),
    LEVEL_UP_MOVE( 7, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE(14, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(21, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(28, MOVE_RETALIATE),
    LEVEL_UP_MOVE(35, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(42, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(49, MOVE_SACRED_SWORD),
    LEVEL_UP_MOVE(56, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(63, MOVE_STONE_EDGE),
    LEVEL_UP_MOVE(70, MOVE_CLOSE_COMBAT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 580
// Types: TYPE_ROCK / TYPE_FIGHTING
// Abilities: ABILITY_JUSTIFIED, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
// Generation: 9

