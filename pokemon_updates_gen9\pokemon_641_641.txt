// POKEMON_641 (#641) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_641] =
    {
        .baseHP = 79,
        .baseAttack = 115,
        .baseDefense = 70,
        .baseSpAttack = 125,
        .baseSpDefense = 80,
        .baseSpeed = 111,
        .type1 = TYPE_FLYING,
        .type2 = TYPE_FLYING,
        .catchRate = 3,
        .expYield = 194,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(0.0),
        .eggCycles = 120,
        .friendship = 90,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PRANKSTER,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_DEFIANT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-641LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 5, MOVE_LEER),
    LEVEL_UP_MOVE(10, MOVE_SWAGGER),
    LEVEL_UP_MOVE(15, MOVE_BITE),
    LEVEL_UP_MOVE(20, MOVE_AIR_CUTTER),
    LEVEL_UP_MOVE(25, MOVE_AGILITY),
    LEVEL_UP_MOVE(30, MOVE_TAILWIND),
    LEVEL_UP_MOVE(35, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(40, MOVE_CRUNCH),
    LEVEL_UP_MOVE(45, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE(50, MOVE_UPROAR),
    LEVEL_UP_MOVE(55, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE(60, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(65, MOVE_HURRICANE),
    LEVEL_UP_MOVE(70, MOVE_THRASH),
    LEVEL_UP_MOVE(77, MOVE_BLEAKWIND_STORM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 580
// Types: TYPE_FLYING / TYPE_FLYING
// Abilities: ABILITY_PRANKSTER, ABILITY_NONE, ABILITY_DEFIANT
// Level Up Moves: 17
// Generation: 9

