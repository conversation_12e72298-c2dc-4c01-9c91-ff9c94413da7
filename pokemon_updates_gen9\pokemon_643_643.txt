// POKEMON_643 (#643) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_643] =
    {
        .baseHP = 100,
        .baseAttack = 120,
        .baseDefense = 100,
        .baseSpAttack = 150,
        .baseSpDefense = 120,
        .baseSpeed = 90,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_FIRE,
        .catchRate = 3,
        .expYield = 220,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_TURBOBLAZE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-643LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE( 1, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE( 1, MOVE_NOBLE_ROAR),
    LEVEL_UP_MOVE( 8, MOVE_SLASH),
    LEVEL_UP_MOVE(16, MOVE_CRUNCH),
    LEVEL_UP_MOVE(24, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE(32, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(40, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(48, MOVE_FUSION_FLARE),
    LEVEL_UP_MOVE(56, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(64, MOVE_FIRE_BLAST),
    LEVEL_UP_MOVE(72, MOVE_IMPRISON),
    LEVEL_UP_MOVE(80, MOVE_OUTRAGE),
    LEVEL_UP_MOVE(88, MOVE_BLUE_FLARE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 680
// Types: TYPE_DRAGON / TYPE_FIRE
// Abilities: ABILITY_TURBOBLAZE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
// Generation: 9

