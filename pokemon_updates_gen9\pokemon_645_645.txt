// POKEMON_645 (#645) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_645] =
    {
        .baseHP = 89,
        .baseAttack = 125,
        .baseDefense = 90,
        .baseSpAttack = 115,
        .baseSpDefense = 80,
        .baseSpeed = 101,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_FLYING,
        .catchRate = 3,
        .expYield = 214,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(0.0),
        .eggCycles = 120,
        .friendship = 90,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SAND-FORCE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SHEER-FORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-645LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SAND_TOMB),
    LEVEL_UP_MOVE( 1, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE( 5, MOVE_LEER),
    LEVEL_UP_MOVE(10, MOVE_BLOCK),
    LEVEL_UP_MOVE(15, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(20, MOVE_ROCK_TOMB),
    LEVEL_UP_MOVE(30, MOVE_IMPRISON),
    LEVEL_UP_MOVE(35, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(40, MOVE_EARTH_POWER),
    LEVEL_UP_MOVE(45, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE(50, MOVE_STONE_EDGE),
    LEVEL_UP_MOVE(55, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE(60, MOVE_SANDSTORM),
    LEVEL_UP_MOVE(65, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(70, MOVE_OUTRAGE),
    LEVEL_UP_MOVE(75, MOVE_FISSURE),
    LEVEL_UP_MOVE(80, MOVE_SANDSEAR_STORM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_GROUND / TYPE_FLYING
// Abilities: ABILITY_SAND-FORCE, ABILITY_NONE, ABILITY_SHEER-FORCE
// Level Up Moves: 17
// Generation: 9

