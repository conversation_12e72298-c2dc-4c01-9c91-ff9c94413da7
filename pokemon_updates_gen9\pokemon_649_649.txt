// POKEMON_649 (#649) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_649] =
    {
        .baseHP = 71,
        .baseAttack = 120,
        .baseDefense = 95,
        .baseSpAttack = 120,
        .baseSpDefense = 95,
        .baseSpeed = 99,
        .type1 = TYPE_BUG,
        .type2 = TYPE_STEEL,
        .catchRate = 3,
        .expYield = 191,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_DOWNLOAD,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-649LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 7, MOVE_SCREECH),
    LEVEL_UP_MOVE(14, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE(21, MOVE_FELL_STINGER),
    LEVEL_UP_MOVE(28, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE(35, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(42, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(49, MOVE_MAGNET_RISE),
    LEVEL_UP_MOVE(56, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(63, MOVE_SIMPLE_BEAM),
    LEVEL_UP_MOVE(70, MOVE_ZAP_CANNON),
    LEVEL_UP_MOVE(77, MOVE_LOCK_ON),
    LEVEL_UP_MOVE(84, MOVE_TECHNO_BLAST),
    LEVEL_UP_MOVE(91, MOVE_SELF_DESTRUCT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_BUG / TYPE_STEEL
// Abilities: ABILITY_DOWNLOAD, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
// Generation: 8

