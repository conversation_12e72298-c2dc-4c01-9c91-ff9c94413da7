// POKEMON_653 (#653) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_653] =
    {
        .baseHP = 40,
        .baseAttack = 45,
        .baseDefense = 40,
        .baseSpAttack = 62,
        .baseSpDefense = 60,
        .baseSpeed = 60,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 45,
        .expYield = 85,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BLAZE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_MAGICIAN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-653LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 5, MOVE_EMBER),
    LEVEL_UP_MOVE(11, MOVE_HOWL),
    LEVEL_UP_MOVE(14, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE(17, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(20, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE(25, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(31, MOVE_PSYSHOCK),
    LEVEL_UP_MOVE(35, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(38, MOVE_WILL_O_WISP),
    LEVEL_UP_MOVE(41, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(43, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE(48, MOVE_FIRE_BLAST),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 307
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_BLAZE, ABILITY_NONE, ABILITY_MAGICIAN
// Level Up Moves: 14
// Generation: 9

