// POKEMON_654 (#654) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_654] =
    {
        .baseHP = 59,
        .baseAttack = 59,
        .baseDefense = 58,
        .baseSpAttack = 90,
        .baseSpDefense = 70,
        .baseSpeed = 73,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 45,
        .expYield = 118,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BLAZE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_MAGICIAN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-654LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE(11, MOVE_HOWL),
    LEVEL_UP_MOVE(14, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE(18, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(22, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE(28, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(36, MOVE_PSYSHOCK),
    LEVEL_UP_MOVE(41, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(45, MOVE_WILL_O_WISP),
    LEVEL_UP_MOVE(49, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(52, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE(56, MOVE_MAGIC_ROOM),
    LEVEL_UP_MOVE(59, MOVE_FIRE_BLAST),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 409
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_BLAZE, ABILITY_NONE, ABILITY_MAGICIAN
// Level Up Moves: 15
// Generation: 9

