// POKEMON_657 (#657) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_657] =
    {
        .baseHP = 54,
        .baseAttack = 63,
        .baseDefense = 52,
        .baseSpAttack = 83,
        .baseSpDefense = 56,
        .baseSpeed = 97,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 45,
        .expYield = 117,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_TORRENT,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_PROTEAN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-657LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 8, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(10, MOVE_LICK),
    LEVEL_UP_MOVE(14, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(19, MOVE_SMOKESCREEN),
    LEVEL_UP_MOVE(23, MOVE_ROUND),
    LEVEL_UP_MOVE(28, MOVE_FLING),
    LEVEL_UP_MOVE(33, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE(40, MOVE_SUBSTITUTE),
    LEVEL_UP_MOVE(45, MOVE_BOUNCE),
    LEVEL_UP_MOVE(50, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(56, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 405
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_TORRENT, ABILITY_NONE, ABILITY_PROTEAN
// Level Up Moves: 14
// Generation: 9

