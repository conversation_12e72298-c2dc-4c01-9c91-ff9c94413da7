// POKEMON_659 (#659) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_659] =
    {
        .baseHP = 38,
        .baseAttack = 36,
        .baseDefense = 38,
        .baseSpAttack = 32,
        .baseSpDefense = 36,
        .baseSpeed = 57,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 255,
        .expYield = 74,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PICKUP,
        .ability2 = ABILITY_CHEEK-POUCH,
        .hiddenAbility = ABILITY_HUGE-POWER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-659LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 3, MOVE_TACKLE),
    LEVEL_UP_MOVE( 6, MOVE_LASER_FOCUS),
    LEVEL_UP_MOVE( 9, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(12, MOVE_MUD_SHOT),
    LEVEL_UP_MOVE(15, MOVE_FLAIL),
    LEVEL_UP_MOVE(18, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(21, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(24, MOVE_DIG),
    LEVEL_UP_MOVE(27, MOVE_BOUNCE),
    LEVEL_UP_MOVE(30, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(33, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(36, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(39, MOVE_SUPER_FANG),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 237
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_PICKUP, ABILITY_CHEEK-POUCH, ABILITY_HUGE-POWER
// Level Up Moves: 15
// Generation: 8

