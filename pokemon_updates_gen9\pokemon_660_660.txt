// POKEMON_660 (#660) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_660] =
    {
        .baseHP = 85,
        .baseAttack = 56,
        .baseDefense = 77,
        .baseSpAttack = 50,
        .baseSpDefense = 77,
        .baseSpeed = 78,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_GROUND,
        .catchRate = 127,
        .expYield = 141,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PICKUP,
        .ability2 = ABILITY_CHEEK-POUCH,
        .hiddenAbility = ABILITY_HUGE-POWER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-660LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LASER_FOCUS),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 9, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(12, MOVE_MUD_SHOT),
    LEVEL_UP_MOVE(15, MOVE_FLAIL),
    LEVEL_UP_MOVE(18, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(23, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(28, MOVE_DIG),
    LEVEL_UP_MOVE(33, MOVE_BOUNCE),
    LEVEL_UP_MOVE(38, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(43, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(48, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(53, MOVE_SUPER_FANG),
    LEVEL_UP_MOVE(58, MOVE_HAMMER_ARM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 423
// Types: TYPE_NORMAL / TYPE_GROUND
// Abilities: ABILITY_PICKUP, ABILITY_CHEEK-POUCH, ABILITY_HUGE-POWER
// Level Up Moves: 16
// Generation: 8

