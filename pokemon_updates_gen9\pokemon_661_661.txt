// POKEMON_661 (#661) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_661] =
    {
        .baseHP = 45,
        .baseAttack = 50,
        .baseDefense = 43,
        .baseSpAttack = 40,
        .baseSpDefense = 38,
        .baseSpeed = 62,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 255,
        .expYield = 95,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BIG-PECKS,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_GALE-WINGS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-661LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 5, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(10, MOVE_EMBER),
    LEVEL_UP_MOVE(15, MOVE_FLAIL),
    LEVEL_UP_MOVE(20, MOVE_ACROBATICS),
    LEVEL_UP_MOVE(25, MOVE_AGILITY),
    LEVEL_UP_MOVE(30, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(35, MOVE_TAILWIND),
    LEVEL_UP_MOVE(40, MOVE_STEEL_WING),
    LEVEL_UP_MOVE(45, MOVE_ROOST),
    LEVEL_UP_MOVE(50, MOVE_FLY),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 278
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_BIG-PECKS, ABILITY_NONE, ABILITY_GALE-WINGS
// Level Up Moves: 12
// Generation: 9

