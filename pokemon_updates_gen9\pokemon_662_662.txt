// POKEMON_662 (#662) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_662] =
    {
        .baseHP = 62,
        .baseAttack = 73,
        .baseDefense = 55,
        .baseSpAttack = 56,
        .baseSpDefense = 52,
        .baseSpeed = 84,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FLYING,
        .catchRate = 120,
        .expYield = 135,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_FLAME-BODY,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_GALE-WINGS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-662LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_FEINT),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(15, MOVE_FLAIL),
    LEVEL_UP_MOVE(22, MOVE_ACROBATICS),
    LEVEL_UP_MOVE(29, MOVE_AGILITY),
    LEVEL_UP_MOVE(36, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(43, MOVE_TAILWIND),
    LEVEL_UP_MOVE(50, MOVE_STEEL_WING),
    LEVEL_UP_MOVE(57, MOVE_ROOST),
    LEVEL_UP_MOVE(64, MOVE_FLY),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 382
// Types: TYPE_FIRE / TYPE_FLYING
// Abilities: ABILITY_FLAME-BODY, ABILITY_NONE, ABILITY_GALE-WINGS
// Level Up Moves: 14
// Generation: 9

