// POKEMON_663 (#663) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_663] =
    {
        .baseHP = 78,
        .baseAttack = 81,
        .baseDefense = 71,
        .baseSpAttack = 74,
        .baseSpDefense = 69,
        .baseSpeed = 126,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 159,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_FLAME-BODY,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_GALE-WINGS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-663LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_FEINT),
    LEVEL_UP_MOVE( 1, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE( 1, MOVE_FLARE_BLITZ),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(15, MOVE_FLAIL),
    LEVEL_UP_MOVE(22, MOVE_ACROBATICS),
    LEVEL_UP_MOVE(29, MOVE_AGILITY),
    LEVEL_UP_MOVE(38, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(47, MOVE_TAILWIND),
    LEVEL_UP_MOVE(56, MOVE_STEEL_WING),
    LEVEL_UP_MOVE(65, MOVE_ROOST),
    LEVEL_UP_MOVE(74, MOVE_FLY),
    LEVEL_UP_MOVE(83, MOVE_BRAVE_BIRD),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 499
// Types: TYPE_FIRE / TYPE_FLYING
// Abilities: ABILITY_FLAME-BODY, ABILITY_NONE, ABILITY_GALE-WINGS
// Level Up Moves: 16
// Generation: 9

