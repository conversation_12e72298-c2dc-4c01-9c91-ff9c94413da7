// POKEMON_664 (#664) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_664] =
    {
        .baseHP = 38,
        .baseAttack = 35,
        .baseDefense = 40,
        .baseSpAttack = 27,
        .baseSpDefense = 25,
        .baseSpeed = 35,
        .type1 = TYPE_BUG,
        .type2 = TYPE_BUG,
        .catchRate = 255,
        .expYield = 73,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SHIELD-DUST,
        .ability2 = ABILITY_COMPOUND-EYES,
        .hiddenAbility = ABILITY_FRIEND-GUARD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-664LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_STRING_SHOT),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 6, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE(15, MOVE_BUG_BITE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 200
// Types: TYPE_BUG / TYPE_BUG
// Abilities: ABILITY_SHIELD-DUST, ABILITY_COMPOUND-EYES, ABILITY_FRIEND-GUARD
// Level Up Moves: 4
// Generation: 9

