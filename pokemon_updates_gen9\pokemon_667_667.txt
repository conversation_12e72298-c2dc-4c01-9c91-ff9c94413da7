// POKEMON_667 (#667) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_667] =
    {
        .baseHP = 62,
        .baseAttack = 50,
        .baseDefense = 58,
        .baseSpAttack = 73,
        .baseSpDefense = 54,
        .baseSpeed = 72,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_NORMAL,
        .catchRate = 220,
        .expYield = 112,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(87.5),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_RIVALRY,
        .ability2 = ABILITY_UNNERVE,
        .hiddenAbility = ABILITY_MOXIE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-667LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_EMBER),
    LEVEL_UP_MOVE( 8, MOVE_WORK_UP),
    LEVEL_UP_MOVE(11, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(15, MOVE_NOBLE_ROAR),
    LEVEL_UP_MOVE(20, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(23, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE(28, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(33, MOVE_ECHOED_VOICE),
    LEVEL_UP_MOVE(36, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(39, MOVE_CRUNCH),
    LEVEL_UP_MOVE(43, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(46, MOVE_INCINERATE),
    LEVEL_UP_MOVE(50, MOVE_OVERHEAT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 369
// Types: TYPE_FIRE / TYPE_NORMAL
// Abilities: ABILITY_RIVALRY, ABILITY_UNNERVE, ABILITY_MOXIE
// Level Up Moves: 15
// Generation: 9

