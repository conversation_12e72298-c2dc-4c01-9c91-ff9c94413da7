// POKEMON_668 (#668) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_668] =
    {
        .baseHP = 86,
        .baseAttack = 68,
        .baseDefense = 72,
        .baseSpAttack = 109,
        .baseSpDefense = 66,
        .baseSpeed = 106,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_NORMAL,
        .catchRate = 65,
        .expYield = 154,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(87.5),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_RIVALRY,
        .ability2 = ABILITY_UNNERVE,
        .hiddenAbility = ABILITY_MOXIE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-668LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_HYPER_BEAM),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_WORK_UP),
    LEVEL_UP_MOVE(11, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(15, MOVE_NOBLE_ROAR),
    LEVEL_UP_MOVE(20, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(23, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE(28, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(33, MOVE_ECHOED_VOICE),
    LEVEL_UP_MOVE(38, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(42, MOVE_CRUNCH),
    LEVEL_UP_MOVE(48, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(51, MOVE_INCINERATE),
    LEVEL_UP_MOVE(57, MOVE_OVERHEAT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 507
// Types: TYPE_FIRE / TYPE_NORMAL
// Abilities: ABILITY_RIVALRY, ABILITY_UNNERVE, ABILITY_MOXIE
// Level Up Moves: 16
// Generation: 9

