// POKEMON_669 (#669) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_669] =
    {
        .baseHP = 44,
        .baseAttack = 38,
        .baseDefense = 39,
        .baseSpAttack = 61,
        .baseSpDefense = 79,
        .baseSpeed = 42,
        .type1 = TYPE_FAIRY,
        .type2 = TYPE_FAIRY,
        .catchRate = 225,
        .expYield = 82,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(100.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_FLOWER-VEIL,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SYMBIOSIS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-669LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_VINE_WHIP),
    LEVEL_UP_MOVE( 6, MOVE_FAIRY_WIND),
    LEVEL_UP_MOVE(10, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(15, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(20, MOVE_WISH),
    LEVEL_UP_MOVE(22, MOVE_MAGICAL_LEAF),
    LEVEL_UP_MOVE(24, MOVE_GRASSY_TERRAIN),
    LEVEL_UP_MOVE(28, MOVE_PETAL_BLIZZARD),
    LEVEL_UP_MOVE(33, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(37, MOVE_MISTY_TERRAIN),
    LEVEL_UP_MOVE(41, MOVE_MOONBLAST),
    LEVEL_UP_MOVE(45, MOVE_PETAL_DANCE),
    LEVEL_UP_MOVE(48, MOVE_SOLAR_BEAM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 303
// Types: TYPE_FAIRY / TYPE_FAIRY
// Abilities: ABILITY_FLOWER-VEIL, ABILITY_NONE, ABILITY_SYMBIOSIS
// Level Up Moves: 14
// Generation: 9

