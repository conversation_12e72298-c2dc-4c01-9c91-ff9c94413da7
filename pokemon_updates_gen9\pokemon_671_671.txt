// POKEMON_671 (#671) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_671] =
    {
        .baseHP = 78,
        .baseAttack = 65,
        .baseDefense = 68,
        .baseSpAttack = 112,
        .baseSpDefense = 154,
        .baseSpeed = 75,
        .type1 = TYPE_FAIRY,
        .type2 = TYPE_FAIRY,
        .catchRate = 45,
        .expYield = 143,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(100.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_FLOWER-VEIL,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SYMBIOSIS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-671LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DISARMING_VOICE),
    LEVEL_UP_MOVE( 1, MOVE_GRASS_KNOT),
    LEVEL_UP_MOVE( 1, MOVE_GRASSY_TERRAIN),
    LEVEL_UP_MOVE( 1, MOVE_MAGICAL_LEAF),
    LEVEL_UP_MOVE( 1, MOVE_MISTY_TERRAIN),
    LEVEL_UP_MOVE( 1, MOVE_PETAL_BLIZZARD),
    LEVEL_UP_MOVE( 1, MOVE_PETAL_DANCE),
    LEVEL_UP_MOVE( 1, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE( 1, MOVE_SOLAR_BEAM),
    LEVEL_UP_MOVE( 1, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE( 1, MOVE_WISH),
    LEVEL_UP_MOVE( 5, MOVE_MOONBLAST),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 552
// Types: TYPE_FAIRY / TYPE_FAIRY
// Abilities: ABILITY_FLOWER-VEIL, ABILITY_NONE, ABILITY_SYMBIOSIS
// Level Up Moves: 12
// Generation: 9

