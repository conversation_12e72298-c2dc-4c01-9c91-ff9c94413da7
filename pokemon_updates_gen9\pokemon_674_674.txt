// POKEMON_674 (#674) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_674] =
    {
        .baseHP = 67,
        .baseAttack = 82,
        .baseDefense = 62,
        .baseSpAttack = 46,
        .baseSpDefense = 48,
        .baseSpeed = 43,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 220,
        .expYield = 149,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_IRON-FIST,
        .ability2 = ABILITY_MOLD-BREAKER,
        .hiddenAbility = ABILITY_SCRAPPY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-674LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 4, MOVE_ARM_THRUST),
    LEVEL_UP_MOVE( 8, MOVE_TAUNT),
    LEVEL_UP_MOVE(12, MOVE_CIRCLE_THROW),
    LEVEL_UP_MOVE(16, MOVE_LOW_SWEEP),
    LEVEL_UP_MOVE(20, MOVE_WORK_UP),
    LEVEL_UP_MOVE(24, MOVE_SLASH),
    LEVEL_UP_MOVE(28, MOVE_VITAL_THROW),
    LEVEL_UP_MOVE(33, MOVE_CRUNCH),
    LEVEL_UP_MOVE(36, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(40, MOVE_PARTING_SHOT),
    LEVEL_UP_MOVE(44, MOVE_ENTRAINMENT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 348
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_IRON-FIST, ABILITY_MOLD-BREAKER, ABILITY_SCRAPPY
// Level Up Moves: 13
// Generation: 8

