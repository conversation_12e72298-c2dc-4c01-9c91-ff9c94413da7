// POKEMON_675 (#675) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_675] =
    {
        .baseHP = 95,
        .baseAttack = 124,
        .baseDefense = 78,
        .baseSpAttack = 69,
        .baseSpDefense = 71,
        .baseSpeed = 58,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_DARK,
        .catchRate = 65,
        .expYield = 219,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_IRON-FIST,
        .ability2 = ABILITY_MOLD-BREAKER,
        .hiddenAbility = ABILITY_SCRAPPY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-675LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE( 1, MOVE_ARM_THRUST),
    LEVEL_UP_MOVE( 1, MOVE_BULLET_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAUNT),
    LEVEL_UP_MOVE(12, MOVE_CIRCLE_THROW),
    LEVEL_UP_MOVE(16, MOVE_LOW_SWEEP),
    LEVEL_UP_MOVE(20, MOVE_WORK_UP),
    LEVEL_UP_MOVE(24, MOVE_SLASH),
    LEVEL_UP_MOVE(28, MOVE_VITAL_THROW),
    LEVEL_UP_MOVE(35, MOVE_CRUNCH),
    LEVEL_UP_MOVE(40, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(46, MOVE_PARTING_SHOT),
    LEVEL_UP_MOVE(52, MOVE_ENTRAINMENT),
    LEVEL_UP_MOVE(58, MOVE_HAMMER_ARM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 495
// Types: TYPE_FIGHTING / TYPE_DARK
// Abilities: ABILITY_IRON-FIST, ABILITY_MOLD-BREAKER, ABILITY_SCRAPPY
// Level Up Moves: 17
// Generation: 8

