// POKEMON_680 (#680) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_680] =
    {
        .baseHP = 59,
        .baseAttack = 110,
        .baseDefense = 150,
        .baseSpAttack = 45,
        .baseSpDefense = 49,
        .baseSpeed = 35,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_GHOST,
        .catchRate = 90,
        .expYield = 169,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_NO-GUARD,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-680LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_AUTOTOMIZE),
    LEVEL_UP_MOVE( 1, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 1, MOVE_SHADOW_SNEAK),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE(12, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(16, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(20, MOVE_SLASH),
    LEVEL_UP_MOVE(24, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(28, MOVE_RETALIATE),
    LEVEL_UP_MOVE(32, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(38, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(44, MOVE_POWER_TRICK),
    LEVEL_UP_MOVE(50, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(56, MOVE_SACRED_SWORD),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 448
// Types: TYPE_STEEL / TYPE_GHOST
// Abilities: ABILITY_NO-GUARD, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
// Generation: 8

