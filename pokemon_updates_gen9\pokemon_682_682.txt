// POKEMON_682 (#682) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_682] =
    {
        .baseHP = 78,
        .baseAttack = 52,
        .baseDefense = 60,
        .baseSpAttack = 63,
        .baseSpDefense = 65,
        .baseSpeed = 23,
        .type1 = TYPE_FAIRY,
        .type2 = TYPE_FAIRY,
        .catchRate = 200,
        .expYield = 130,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_HEALER,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_AROMA-VEIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-682LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_FAIRY_WIND),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE( 3, MOVE_SWEET_KISS),
    LEVEL_UP_MOVE( 6, MOVE_ECHOED_VOICE),
    LEVEL_UP_MOVE( 9, MOVE_DRAINING_KISS),
    LEVEL_UP_MOVE(12, MOVE_AROMATHERAPY),
    LEVEL_UP_MOVE(15, MOVE_DRAINING_KISS),
    LEVEL_UP_MOVE(18, MOVE_ATTRACT),
    LEVEL_UP_MOVE(21, MOVE_FLAIL),
    LEVEL_UP_MOVE(24, MOVE_MISTY_TERRAIN),
    LEVEL_UP_MOVE(27, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(30, MOVE_CHARM),
    LEVEL_UP_MOVE(33, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(36, MOVE_MOONBLAST),
    LEVEL_UP_MOVE(39, MOVE_SKILL_SWAP),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 341
// Types: TYPE_FAIRY / TYPE_FAIRY
// Abilities: ABILITY_HEALER, ABILITY_NONE, ABILITY_AROMA-VEIL
// Level Up Moves: 15
// Generation: 8

