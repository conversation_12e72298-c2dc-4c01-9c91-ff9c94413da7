// POKEMON_687 (#687) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_687] =
    {
        .baseHP = 86,
        .baseAttack = 92,
        .baseDefense = 88,
        .baseSpAttack = 68,
        .baseSpDefense = 75,
        .baseSpeed = 73,
        .type1 = TYPE_DARK,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 80,
        .expYield = 178,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_CONTRARY,
        .ability2 = ABILITY_SUCTION-CUPS,
        .hiddenAbility = ABILITY_INFILTRATOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-687LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_REVERSAL),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_WRAP),
    LEVEL_UP_MOVE( 9, MOVE_PAYBACK),
    LEVEL_UP_MOVE(12, MOVE_PLUCK),
    LEVEL_UP_MOVE(15, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(18, MOVE_SWAGGER),
    LEVEL_UP_MOVE(21, MOVE_SLASH),
    LEVEL_UP_MOVE(24, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(27, MOVE_PSYCHO_CUT),
    LEVEL_UP_MOVE(33, MOVE_SWITCHEROO),
    LEVEL_UP_MOVE(37, MOVE_FOUL_PLAY),
    LEVEL_UP_MOVE(42, MOVE_TOPSY_TURVY),
    LEVEL_UP_MOVE(47, MOVE_SUPERPOWER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 482
// Types: TYPE_DARK / TYPE_PSYCHIC
// Abilities: ABILITY_CONTRARY, ABILITY_SUCTION-CUPS, ABILITY_INFILTRATOR
// Level Up Moves: 16
// Generation: 9

