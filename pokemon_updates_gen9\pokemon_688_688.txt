// POKEMON_688 (#688) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_688] =
    {
        .baseHP = 42,
        .baseAttack = 52,
        .baseDefense = 67,
        .baseSpAttack = 39,
        .baseSpDefense = 56,
        .baseSpeed = 50,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_WATER,
        .catchRate = 120,
        .expYield = 94,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_TOUGH-CLAWS,
        .ability2 = ABILITY_SNIPER,
        .hiddenAbility = ABILITY_PICKPOCKET,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-688LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 4, MOVE_WITHDRAW),
    LEVEL_UP_MOVE( 8, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(12, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE(16, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(20, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(24, MOVE_ROCK_POLISH),
    LEVEL_UP_MOVE(28, MOVE_SLASH),
    LEVEL_UP_MOVE(32, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(36, MOVE_RAZOR_SHELL),
    LEVEL_UP_MOVE(40, MOVE_SHELL_SMASH),
    LEVEL_UP_MOVE(44, MOVE_CROSS_CHOP),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 306
// Types: TYPE_ROCK / TYPE_WATER
// Abilities: ABILITY_TOUGH-CLAWS, ABILITY_SNIPER, ABILITY_PICKPOCKET
// Level Up Moves: 13
// Generation: 8

