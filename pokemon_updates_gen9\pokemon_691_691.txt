// POKEMON_691 (#691) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_691] =
    {
        .baseHP = 65,
        .baseAttack = 75,
        .baseDefense = 90,
        .baseSpAttack = 97,
        .baseSpDefense = 123,
        .baseSpeed = 44,
        .type1 = TYPE_POISON,
        .type2 = TYPE_DRAGON,
        .catchRate = 55,
        .expYield = 140,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_POISON-POINT,
        .ability2 = ABILITY_POISON-TOUCH,
        .hiddenAbility = ABILITY_ADAPTABILITY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-691LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ACID),
    LEVEL_UP_MOVE( 1, MOVE_SMOKESCREEN),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(15, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE(20, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(25, MOVE_POISON_TAIL),
    LEVEL_UP_MOVE(30, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(35, MOVE_TOXIC),
    LEVEL_UP_MOVE(40, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(45, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(52, MOVE_SLUDGE_BOMB),
    LEVEL_UP_MOVE(59, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(66, MOVE_OUTRAGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 494
// Types: TYPE_POISON / TYPE_DRAGON
// Abilities: ABILITY_POISON-POINT, ABILITY_POISON-TOUCH, ABILITY_ADAPTABILITY
// Level Up Moves: 14
// Generation: 9

