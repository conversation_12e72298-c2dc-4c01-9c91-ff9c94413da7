// POKEMON_693 (#693) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_693] =
    {
        .baseHP = 71,
        .baseAttack = 73,
        .baseDefense = 88,
        .baseSpAttack = 120,
        .baseSpDefense = 89,
        .baseSpeed = 59,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 55,
        .expYield = 144,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_MEGA-LAUNCHER,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-693LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DARK_PULSE),
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE( 1, MOVE_FLAIL),
    LEVEL_UP_MOVE( 1, MOVE_HEAL_PULSE),
    LEVEL_UP_MOVE( 1, MOVE_SPLASH),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(15, MOVE_AQUA_JET),
    LEVEL_UP_MOVE(20, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE(25, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(30, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(35, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(42, MOVE_AURA_SPHERE),
    LEVEL_UP_MOVE(49, MOVE_BOUNCE),
    LEVEL_UP_MOVE(56, MOVE_MUDDY_WATER),
    LEVEL_UP_MOVE(63, MOVE_CRABHAMMER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_MEGA-LAUNCHER, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
// Generation: 9

