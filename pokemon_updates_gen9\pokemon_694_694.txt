// POKEMON_694 (#694) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_694] =
    {
        .baseHP = 44,
        .baseAttack = 38,
        .baseDefense = 33,
        .baseSpAttack = 61,
        .baseSpDefense = 43,
        .baseSpeed = 70,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_NORMAL,
        .catchRate = 190,
        .expYield = 82,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_DRY-SKIN,
        .ability2 = ABILITY_SAND-VEIL,
        .hiddenAbility = ABILITY_SOLAR-POWER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-694LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 4, MOVE_POUND),
    LEVEL_UP_MOVE( 8, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE(12, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(16, MOVE_CHARGE),
    LEVEL_UP_MOVE(20, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(24, MOVE_VOLT_SWITCH),
    LEVEL_UP_MOVE(28, MOVE_PARABOLIC_CHARGE),
    LEVEL_UP_MOVE(32, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE(36, MOVE_THUNDERBOLT),
    LEVEL_UP_MOVE(40, MOVE_ELECTRIFY),
    LEVEL_UP_MOVE(44, MOVE_THUNDER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 289
// Types: TYPE_ELECTRIC / TYPE_NORMAL
// Abilities: ABILITY_DRY-SKIN, ABILITY_SAND-VEIL, ABILITY_SOLAR-POWER
// Level Up Moves: 13
// Generation: 8

