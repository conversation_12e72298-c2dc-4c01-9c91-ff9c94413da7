// POKEMON_697 (#697) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_697] =
    {
        .baseHP = 82,
        .baseAttack = 121,
        .baseDefense = 119,
        .baseSpAttack = 69,
        .baseSpDefense = 59,
        .baseSpeed = 71,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 203,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STRONG-JAW,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_ROCK-HEAD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-697LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE( 1, MOVE_ROAR),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE(12, MOVE_CHARM),
    LEVEL_UP_MOVE(16, MOVE_BITE),
    LEVEL_UP_MOVE(20, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE(24, MOVE_STOMP),
    LEVEL_UP_MOVE(28, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(32, MOVE_CRUNCH),
    LEVEL_UP_MOVE(36, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(42, MOVE_THRASH),
    LEVEL_UP_MOVE(48, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(54, MOVE_HORN_DRILL),
    LEVEL_UP_MOVE(60, MOVE_GIGA_IMPACT),
    LEVEL_UP_MOVE(66, MOVE_HEAD_SMASH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 521
// Types: TYPE_ROCK / TYPE_DRAGON
// Abilities: ABILITY_STRONG-JAW, ABILITY_NONE, ABILITY_ROCK-HEAD
// Level Up Moves: 16
// Generation: 8

