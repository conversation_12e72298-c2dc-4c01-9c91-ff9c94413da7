// POKEMON_698 (#698) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_698] =
    {
        .baseHP = 77,
        .baseAttack = 59,
        .baseDefense = 50,
        .baseSpAttack = 67,
        .baseSpDefense = 63,
        .baseSpeed = 46,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_ICE,
        .catchRate = 45,
        .expYield = 136,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_REFRIGERATE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SNOW-WARNING,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-698LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_POWDER_SNOW),
    LEVEL_UP_MOVE( 4, MOVE_ENCORE),
    LEVEL_UP_MOVE( 8, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(12, MOVE_ICY_WIND),
    LEVEL_UP_MOVE(16, MOVE_ROUND),
    LEVEL_UP_MOVE(20, MOVE_MIST),
    LEVEL_UP_MOVE(24, MOVE_AURORA_BEAM),
    LEVEL_UP_MOVE(28, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE(32, MOVE_NATURE_POWER),
    LEVEL_UP_MOVE(36, MOVE_FREEZE_DRY),
    LEVEL_UP_MOVE(40, MOVE_ICE_BEAM),
    LEVEL_UP_MOVE(44, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(48, MOVE_HAIL),
    LEVEL_UP_MOVE(52, MOVE_BLIZZARD),
    LEVEL_UP_MOVE(56, MOVE_HYPER_BEAM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 362
// Types: TYPE_ROCK / TYPE_ICE
// Abilities: ABILITY_REFRIGERATE, ABILITY_NONE, ABILITY_SNOW-WARNING
// Level Up Moves: 16
// Generation: 8

