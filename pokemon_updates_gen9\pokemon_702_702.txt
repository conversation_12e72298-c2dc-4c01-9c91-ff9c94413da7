// POKEMON_702 (#702) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_702] =
    {
        .baseHP = 67,
        .baseAttack = 58,
        .baseDefense = 57,
        .baseSpAttack = 81,
        .baseSpDefense = 67,
        .baseSpeed = 101,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_FAIRY,
        .catchRate = 180,
        .expYield = 125,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_CHEEK-POUCH,
        .ability2 = ABILITY_PICKUP,
        .hiddenAbility = ABILITY_PLUS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-702LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_NUZZLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 5, MOVE_TACKLE),
    LEVEL_UP_MOVE(10, MOVE_CHARGE),
    LEVEL_UP_MOVE(15, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE(20, MOVE_CHARM),
    LEVEL_UP_MOVE(25, MOVE_PARABOLIC_CHARGE),
    LEVEL_UP_MOVE(30, MOVE_VOLT_SWITCH),
    LEVEL_UP_MOVE(35, MOVE_REST),
    LEVEL_UP_MOVE(35, MOVE_SNORE),
    LEVEL_UP_MOVE(40, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(45, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(50, MOVE_SUPER_FANG),
    LEVEL_UP_MOVE(55, MOVE_ENTRAINMENT),
    LEVEL_UP_MOVE(60, MOVE_THUNDER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 431
// Types: TYPE_ELECTRIC / TYPE_FAIRY
// Abilities: ABILITY_CHEEK-POUCH, ABILITY_PICKUP, ABILITY_PLUS
// Level Up Moves: 15
// Generation: 9

