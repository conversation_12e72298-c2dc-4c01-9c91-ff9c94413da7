// POKEMON_703 (#703) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_703] =
    {
        .baseHP = 50,
        .baseAttack = 50,
        .baseDefense = 150,
        .baseSpAttack = 50,
        .baseSpDefense = 150,
        .baseSpeed = 50,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_FAIRY,
        .catchRate = 60,
        .expYield = 100,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_CLEAR-BODY,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_STURDY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-703LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_GUARD_SPLIT),
    LEVEL_UP_MOVE(10, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE(15, MOVE_FLAIL),
    LEVEL_UP_MOVE(20, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(25, MOVE_ROCK_POLISH),
    LEVEL_UP_MOVE(30, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(35, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(40, MOVE_SKILL_SWAP),
    LEVEL_UP_MOVE(45, MOVE_POWER_GEM),
    LEVEL_UP_MOVE(50, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(55, MOVE_MOONBLAST),
    LEVEL_UP_MOVE(60, MOVE_STONE_EDGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_ROCK / TYPE_FAIRY
// Abilities: ABILITY_CLEAR-BODY, ABILITY_NONE, ABILITY_STURDY
// Level Up Moves: 14
// Generation: 9

