// POKEMON_705 (#705) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_705] =
    {
        .baseHP = 68,
        .baseAttack = 75,
        .baseDefense = 53,
        .baseSpAttack = 83,
        .baseSpDefense = 113,
        .baseSpeed = 60,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 143,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 40,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SAP-SIPPER,
        .ability2 = ABILITY_HYDRATION,
        .hiddenAbility = ABILITY_GOOEY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-705LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_ACID_SPRAY),
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_ACID_ARMOR),
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(15, MOVE_PROTECT),
    LEVEL_UP_MOVE(20, MOVE_FLAIL),
    LEVEL_UP_MOVE(25, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(30, MOVE_RAIN_DANCE),
    LEVEL_UP_MOVE(35, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(43, MOVE_CURSE),
    LEVEL_UP_MOVE(49, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(56, MOVE_MUDDY_WATER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 452
// Types: TYPE_DRAGON / TYPE_DRAGON
// Abilities: ABILITY_SAP-SIPPER, ABILITY_HYDRATION, ABILITY_GOOEY
// Level Up Moves: 14
// Generation: 9

