// POKEMON_707 (#707) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_707] =
    {
        .baseHP = 57,
        .baseAttack = 80,
        .baseDefense = 91,
        .baseSpAttack = 80,
        .baseSpDefense = 87,
        .baseSpeed = 75,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_FAIRY,
        .catchRate = 75,
        .expYield = 137,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PRANKSTER,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_MAGICIAN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-707LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 4, MOVE_TACKLE),
    LEVEL_UP_MOVE( 8, MOVE_FAIRY_WIND),
    LEVEL_UP_MOVE(12, MOVE_TORMENT),
    LEVEL_UP_MOVE(16, MOVE_FAIRY_LOCK),
    LEVEL_UP_MOVE(20, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(24, MOVE_DRAINING_KISS),
    LEVEL_UP_MOVE(28, MOVE_RECYCLE),
    LEVEL_UP_MOVE(32, MOVE_IMPRISON),
    LEVEL_UP_MOVE(36, MOVE_FLASH_CANNON),
    LEVEL_UP_MOVE(40, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(44, MOVE_MAGIC_ROOM),
    LEVEL_UP_MOVE(48, MOVE_FOUL_PLAY),
    LEVEL_UP_MOVE(52, MOVE_LAST_RESORT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 470
// Types: TYPE_STEEL / TYPE_FAIRY
// Abilities: ABILITY_PRANKSTER, ABILITY_NONE, ABILITY_MAGICIAN
// Level Up Moves: 14
// Generation: 9

