// POKEMON_708 (#708) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_708] =
    {
        .baseHP = 43,
        .baseAttack = 70,
        .baseDefense = 48,
        .baseSpAttack = 50,
        .baseSpDefense = 60,
        .baseSpeed = 38,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_GRASS,
        .catchRate = 120,
        .expYield = 113,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_NATURAL-CURE,
        .ability2 = ABILITY_FRISK,
        .hiddenAbility = ABILITY_HARVEST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-708LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 4, MOVE_BRANCH_POKE),
    LEVEL_UP_MOVE( 8, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(12, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(16, MOVE_WILL_O_WISP),
    LEVEL_UP_MOVE(20, MOVE_HEX),
    LEVEL_UP_MOVE(24, MOVE_GROWTH),
    LEVEL_UP_MOVE(28, MOVE_HORN_LEECH),
    LEVEL_UP_MOVE(32, MOVE_CURSE),
    LEVEL_UP_MOVE(36, MOVE_PHANTOM_FORCE),
    LEVEL_UP_MOVE(40, MOVE_INGRAIN),
    LEVEL_UP_MOVE(44, MOVE_WOOD_HAMMER),
    LEVEL_UP_MOVE(48, MOVE_DESTINY_BOND),
    LEVEL_UP_MOVE(52, MOVE_FORESTS_CURSE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 309
// Types: TYPE_GHOST / TYPE_GRASS
// Abilities: ABILITY_NATURAL-CURE, ABILITY_FRISK, ABILITY_HARVEST
// Level Up Moves: 15
// Generation: 9

