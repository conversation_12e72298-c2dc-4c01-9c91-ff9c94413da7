// POKEMON_712 (#712) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_712] =
    {
        .baseHP = 55,
        .baseAttack = 69,
        .baseDefense = 85,
        .baseSpAttack = 32,
        .baseSpDefense = 35,
        .baseSpeed = 28,
        .type1 = TYPE_ICE,
        .type2 = TYPE_ICE,
        .catchRate = 190,
        .expYield = 124,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_OWN-TEMPO,
        .ability2 = ABILITY_ICE-BODY,
        .hiddenAbility = ABILITY_STURDY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-712LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE( 3, MOVE_TACKLE),
    LEVEL_UP_MOVE( 6, MOVE_POWDER_SNOW),
    LEVEL_UP_MOVE( 9, MOVE_CURSE),
    LEVEL_UP_MOVE(12, MOVE_ICY_WIND),
    LEVEL_UP_MOVE(15, MOVE_PROTECT),
    LEVEL_UP_MOVE(18, MOVE_AVALANCHE),
    LEVEL_UP_MOVE(21, MOVE_BITE),
    LEVEL_UP_MOVE(24, MOVE_ICE_FANG),
    LEVEL_UP_MOVE(27, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(30, MOVE_RECOVER),
    LEVEL_UP_MOVE(33, MOVE_CRUNCH),
    LEVEL_UP_MOVE(36, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(39, MOVE_BLIZZARD),
    LEVEL_UP_MOVE(42, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 304
// Types: TYPE_ICE / TYPE_ICE
// Abilities: ABILITY_OWN-TEMPO, ABILITY_ICE-BODY, ABILITY_STURDY
// Level Up Moves: 16
// Generation: 9

