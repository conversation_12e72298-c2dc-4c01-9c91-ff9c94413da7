// POKEMON_715 (#715) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_715] =
    {
        .baseHP = 85,
        .baseAttack = 70,
        .baseDefense = 80,
        .baseSpAttack = 97,
        .baseSpDefense = 80,
        .baseSpeed = 123,
        .type1 = TYPE_FLYING,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 155,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_FRISK,
        .ability2 = ABILITY_INFILTRATOR,
        .hiddenAbility = ABILITY_TELEPATHY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-715LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 1, MOVE_MOONLIGHT),
    LEVEL_UP_MOVE( 1, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE(12, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(16, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(20, MOVE_BITE),
    LEVEL_UP_MOVE(24, MOVE_AIR_CUTTER),
    LEVEL_UP_MOVE(28, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(32, MOVE_SUPER_FANG),
    LEVEL_UP_MOVE(36, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(40, MOVE_SCREECH),
    LEVEL_UP_MOVE(44, MOVE_ROOST),
    LEVEL_UP_MOVE(51, MOVE_TAILWIND),
    LEVEL_UP_MOVE(56, MOVE_HURRICANE),
    LEVEL_UP_MOVE(62, MOVE_BOOMBURST),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 535
// Types: TYPE_FLYING / TYPE_DRAGON
// Abilities: ABILITY_FRISK, ABILITY_INFILTRATOR, ABILITY_TELEPATHY
// Level Up Moves: 18
// Generation: 9

