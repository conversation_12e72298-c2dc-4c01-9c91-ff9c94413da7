// POKEMON_718 (#718) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_718] =
    {
        .baseHP = 108,
        .baseAttack = 100,
        .baseDefense = 121,
        .baseSpAttack = 81,
        .baseSpDefense = 95,
        .baseSpeed = 95,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_GROUND,
        .catchRate = 3,
        .expYield = 208,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_AURA-BREAK,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-718LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BIND),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_BULLDOZE),
    LEVEL_UP_MOVE( 1, MOVE_CORE_ENFORCER),
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE( 1, MOVE_THOUSAND_ARROWS),
    LEVEL_UP_MOVE( 1, MOVE_THOUSAND_WAVES),
    LEVEL_UP_MOVE( 8, MOVE_HAZE),
    LEVEL_UP_MOVE(16, MOVE_DIG),
    LEVEL_UP_MOVE(24, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(32, MOVE_CRUNCH),
    LEVEL_UP_MOVE(40, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(48, MOVE_LANDS_WRATH),
    LEVEL_UP_MOVE(56, MOVE_GLARE),
    LEVEL_UP_MOVE(64, MOVE_SANDSTORM),
    LEVEL_UP_MOVE(72, MOVE_COIL),
    LEVEL_UP_MOVE(80, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(88, MOVE_OUTRAGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_DRAGON / TYPE_GROUND
// Abilities: ABILITY_AURA-BREAK, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 18
// Generation: 8

