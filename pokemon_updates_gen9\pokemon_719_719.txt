// POKEMON_719 (#719) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_719] =
    {
        .baseHP = 50,
        .baseAttack = 100,
        .baseDefense = 150,
        .baseSpAttack = 100,
        .baseSpDefense = 150,
        .baseSpeed = 50,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_FAIRY,
        .catchRate = 3,
        .expYield = 150,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_CLEAR-BODY,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-719LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DIAMOND_STORM),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 7, MOVE_GUARD_SPLIT),
    LEVEL_UP_MOVE(14, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE(21, MOVE_FLAIL),
    LEVEL_UP_MOVE(28, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(35, MOVE_ROCK_POLISH),
    LEVEL_UP_MOVE(42, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(49, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(56, MOVE_SKILL_SWAP),
    LEVEL_UP_MOVE(63, MOVE_POWER_GEM),
    LEVEL_UP_MOVE(70, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(77, MOVE_MOONBLAST),
    LEVEL_UP_MOVE(84, MOVE_STONE_EDGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_ROCK / TYPE_FAIRY
// Abilities: ABILITY_CLEAR-BODY, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
// Generation: 9

