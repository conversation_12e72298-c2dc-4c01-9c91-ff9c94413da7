// POKEMON_723 (#723) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_723] =
    {
        .baseHP = 78,
        .baseAttack = 75,
        .baseDefense = 75,
        .baseSpAttack = 70,
        .baseSpDefense = 70,
        .baseSpeed = 52,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 153,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_OVERGROW,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_LONG-REACH,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-723LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_LEAFAGE),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 9, MOVE_PECK),
    LEVEL_UP_MOVE(12, MOVE_SHADOW_SNEAK),
    LEVEL_UP_MOVE(15, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(20, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(25, MOVE_PLUCK),
    LEVEL_UP_MOVE(30, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(35, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(40, MOVE_LEAF_BLADE),
    LEVEL_UP_MOVE(45, MOVE_FEATHER_DANCE),
    LEVEL_UP_MOVE(50, MOVE_BRAVE_BIRD),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 420
// Types: TYPE_GRASS / TYPE_FLYING
// Abilities: ABILITY_OVERGROW, ABILITY_NONE, ABILITY_LONG-REACH
// Level Up Moves: 14
// Generation: 9

