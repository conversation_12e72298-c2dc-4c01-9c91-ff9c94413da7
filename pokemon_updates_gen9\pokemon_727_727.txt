// POKEMON_727 (#727) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_727] =
    {
        .baseHP = 95,
        .baseAttack = 115,
        .baseDefense = 90,
        .baseSpAttack = 80,
        .baseSpDefense = 90,
        .baseSpeed = 60,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_DARK,
        .catchRate = 45,
        .expYield = 210,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BLAZE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_INTIMIDATE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-727LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_DARKEST_LARIAT),
    LEVEL_UP_MOVE( 1, MOVE_BULK_UP),
    LEVEL_UP_MOVE( 1, MOVE_CROSS_CHOP),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_LICK),
    LEVEL_UP_MOVE( 9, MOVE_ROAR),
    LEVEL_UP_MOVE(12, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(15, MOVE_BITE),
    LEVEL_UP_MOVE(20, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(25, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE(30, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(32, MOVE_SWAGGER),
    LEVEL_UP_MOVE(44, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(51, MOVE_THRASH),
    LEVEL_UP_MOVE(58, MOVE_FLARE_BLITZ),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 530
// Types: TYPE_FIRE / TYPE_DARK
// Abilities: ABILITY_BLAZE, ABILITY_NONE, ABILITY_INTIMIDATE
// Level Up Moves: 15
// Generation: 9

