// POKEMON_730 (#730) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_730] =
    {
        .baseHP = 80,
        .baseAttack = 74,
        .baseDefense = 74,
        .baseSpAttack = 126,
        .baseSpDefense = 116,
        .baseSpeed = 60,
        .type1 = TYPE_WATER,
        .type2 = TYPE_FAIRY,
        .catchRate = 45,
        .expYield = 154,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_TORRENT,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_LIQUID-VOICE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-730LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_SPARKLING_ARIA),
    LEVEL_UP_MOVE( 1, MOVE_DISARMING_VOICE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 9, MOVE_AQUA_JET),
    LEVEL_UP_MOVE(12, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(15, MOVE_ICY_WIND),
    LEVEL_UP_MOVE(20, MOVE_SING),
    LEVEL_UP_MOVE(25, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(30, MOVE_ENCORE),
    LEVEL_UP_MOVE(37, MOVE_MISTY_TERRAIN),
    LEVEL_UP_MOVE(44, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(51, MOVE_MOONBLAST),
    LEVEL_UP_MOVE(58, MOVE_HYDRO_PUMP),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 530
// Types: TYPE_WATER / TYPE_FAIRY
// Abilities: ABILITY_TORRENT, ABILITY_NONE, ABILITY_LIQUID-VOICE
// Level Up Moves: 15
// Generation: 9

