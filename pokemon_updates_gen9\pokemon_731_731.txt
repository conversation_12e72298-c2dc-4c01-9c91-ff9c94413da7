// POKEMON_731 (#731) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_731] =
    {
        .baseHP = 35,
        .baseAttack = 75,
        .baseDefense = 30,
        .baseSpAttack = 30,
        .baseSpDefense = 30,
        .baseSpeed = 65,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 255,
        .expYield = 110,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_KEEN-EYE,
        .ability2 = ABILITY_SKILL-LINK,
        .hiddenAbility = ABILITY_PICKUP,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-731LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 3, MOVE_GROWL),
    LEVEL_UP_MOVE( 7, MOVE_ECHOED_VOICE),
    LEVEL_UP_MOVE( 9, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE(13, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(15, MOVE_PLUCK),
    LEVEL_UP_MOVE(19, MOVE_ROOST),
    LEVEL_UP_MOVE(21, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(25, MOVE_SCREECH),
    LEVEL_UP_MOVE(27, MOVE_DRILL_PECK),
    LEVEL_UP_MOVE(31, MOVE_BULLET_SEED),
    LEVEL_UP_MOVE(33, MOVE_FEATHER_DANCE),
    LEVEL_UP_MOVE(37, MOVE_HYPER_VOICE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 265
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_KEEN-EYE, ABILITY_SKILL-LINK, ABILITY_PICKUP
// Level Up Moves: 13
// Generation: 9

