// POKEMON_733 (#733) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_733] =
    {
        .baseHP = 80,
        .baseAttack = 120,
        .baseDefense = 75,
        .baseSpAttack = 75,
        .baseSpDefense = 75,
        .baseSpeed = 60,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 200,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_KEEN-EYE,
        .ability2 = ABILITY_SKILL-LINK,
        .hiddenAbility = ABILITY_SHEER-FORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-733LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_BEAK_BLAST),
    LEVEL_UP_MOVE( 1, MOVE_ECHOED_VOICE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_BLAST),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE(13, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE(16, MOVE_PLUCK),
    LEVEL_UP_MOVE(21, MOVE_ROOST),
    LEVEL_UP_MOVE(24, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(30, MOVE_SCREECH),
    LEVEL_UP_MOVE(34, MOVE_DRILL_PECK),
    LEVEL_UP_MOVE(40, MOVE_BULLET_SEED),
    LEVEL_UP_MOVE(44, MOVE_FEATHER_DANCE),
    LEVEL_UP_MOVE(50, MOVE_HYPER_VOICE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 485
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_KEEN-EYE, ABILITY_SKILL-LINK, ABILITY_SHEER-FORCE
// Level Up Moves: 15
// Generation: 9

