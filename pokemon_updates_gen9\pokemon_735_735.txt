// POKEMON_735 (#735) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_735] =
    {
        .baseHP = 88,
        .baseAttack = 110,
        .baseDefense = 60,
        .baseSpAttack = 55,
        .baseSpDefense = 60,
        .baseSpeed = 45,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 127,
        .expYield = 198,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STAKEOUT,
        .ability2 = ABILITY_STRONG-JAW,
        .hiddenAbility = ABILITY_ADAPTABILITY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-735LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_PAYBACK),
    LEVEL_UP_MOVE( 1, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE(13, MOVE_WORK_UP),
    LEVEL_UP_MOVE(19, MOVE_BITE),
    LEVEL_UP_MOVE(23, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE(27, MOVE_SUPER_FANG),
    LEVEL_UP_MOVE(31, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(35, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(39, MOVE_CRUNCH),
    LEVEL_UP_MOVE(43, MOVE_YAWN),
    LEVEL_UP_MOVE(47, MOVE_THRASH),
    LEVEL_UP_MOVE(52, MOVE_REST),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 418
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_STAKEOUT, ABILITY_STRONG-JAW, ABILITY_ADAPTABILITY
// Level Up Moves: 14
// Generation: 9

