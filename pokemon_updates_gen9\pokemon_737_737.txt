// POKEMON_737 (#737) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_737] =
    {
        .baseHP = 57,
        .baseAttack = 82,
        .baseDefense = 95,
        .baseSpAttack = 55,
        .baseSpDefense = 75,
        .baseSpeed = 36,
        .type1 = TYPE_BUG,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 120,
        .expYield = 139,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BATTERY,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-737LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_CHARGE),
    LEVEL_UP_MOVE( 1, MOVE_BUG_BITE),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_STRING_SHOT),
    LEVEL_UP_MOVE( 1, MOVE_VICE_GRIP),
    LEVEL_UP_MOVE(15, MOVE_BITE),
    LEVEL_UP_MOVE(23, MOVE_SPARK),
    LEVEL_UP_MOVE(29, MOVE_STICKY_WEB),
    LEVEL_UP_MOVE(36, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(43, MOVE_CRUNCH),
    LEVEL_UP_MOVE(50, MOVE_DIG),
    LEVEL_UP_MOVE(57, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(64, MOVE_DISCHARGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 400
// Types: TYPE_BUG / TYPE_ELECTRIC
// Abilities: ABILITY_BATTERY, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 13
// Generation: 9

