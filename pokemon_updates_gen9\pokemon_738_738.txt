// POKEMON_738 (#738) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_738] =
    {
        .baseHP = 77,
        .baseAttack = 70,
        .baseDefense = 90,
        .baseSpAttack = 145,
        .baseSpDefense = 75,
        .baseSpeed = 43,
        .type1 = TYPE_BUG,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 45,
        .expYield = 147,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_LEVITATE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-738LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_THUNDERBOLT),
    LEVEL_UP_MOVE( 1, MOVE_CHARGE),
    LEVEL_UP_MOVE( 1, MOVE_CRUNCH),
    LEVEL_UP_MOVE( 1, MOVE_DISCHARGE),
    LEVEL_UP_MOVE( 1, MOVE_STRING_SHOT),
    LEVEL_UP_MOVE(15, MOVE_BITE),
    LEVEL_UP_MOVE(23, MOVE_SPARK),
    LEVEL_UP_MOVE(29, MOVE_STICKY_WEB),
    LEVEL_UP_MOVE(36, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(43, MOVE_GUILLOTINE),
    LEVEL_UP_MOVE(50, MOVE_FLY),
    LEVEL_UP_MOVE(57, MOVE_AGILITY),
    LEVEL_UP_MOVE(64, MOVE_ZAP_CANNON),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_BUG / TYPE_ELECTRIC
// Abilities: ABILITY_LEVITATE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 13
// Generation: 9

