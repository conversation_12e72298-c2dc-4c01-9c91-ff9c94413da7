// POKEMON_739 (#739) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_739] =
    {
        .baseHP = 47,
        .baseAttack = 82,
        .baseDefense = 57,
        .baseSpAttack = 42,
        .baseSpDefense = 47,
        .baseSpeed = 63,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 225,
        .expYield = 129,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_HYPER-CUTTER,
        .ability2 = ABILITY_IRON-FIST,
        .hiddenAbility = ABILITY_ANGER-POINT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-739LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 5, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE( 9, MOVE_LEER),
    LEVEL_UP_MOVE(13, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(17, MOVE_PROTECT),
    LEVEL_UP_MOVE(22, MOVE_BRICK_BREAK),
    LEVEL_UP_MOVE(25, MOVE_SLAM),
    LEVEL_UP_MOVE(29, MOVE_PAYBACK),
    LEVEL_UP_MOVE(33, MOVE_REVERSAL),
    LEVEL_UP_MOVE(37, MOVE_CRABHAMMER),
    LEVEL_UP_MOVE(42, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(45, MOVE_DYNAMIC_PUNCH),
    LEVEL_UP_MOVE(49, MOVE_CLOSE_COMBAT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 338
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_HYPER-CUTTER, ABILITY_IRON-FIST, ABILITY_ANGER-POINT
// Level Up Moves: 12
// Generation: 9

