// POKEMON_741 (#741) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_741] =
    {
        .baseHP = 75,
        .baseAttack = 70,
        .baseDefense = 70,
        .baseSpAttack = 98,
        .baseSpDefense = 70,
        .baseSpeed = 93,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 145,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(75.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_DANCER,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-741LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 4, MOVE_GROWL),
    LEVEL_UP_MOVE( 6, MOVE_PECK),
    LEVEL_UP_MOVE(10, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(13, MOVE_AIR_CUTTER),
    LEVEL_UP_MOVE(16, MOVE_BATON_PASS),
    LEVEL_UP_MOVE(20, MOVE_FEATHER_DANCE),
    LEVEL_UP_MOVE(23, MOVE_ACROBATICS),
    LEVEL_UP_MOVE(26, MOVE_TEETER_DANCE),
    LEVEL_UP_MOVE(30, MOVE_ROOST),
    LEVEL_UP_MOVE(33, MOVE_FLATTER),
    LEVEL_UP_MOVE(36, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(40, MOVE_REVELATION_DANCE),
    LEVEL_UP_MOVE(43, MOVE_AGILITY),
    LEVEL_UP_MOVE(47, MOVE_HURRICANE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 476
// Types: TYPE_FIRE / TYPE_FLYING
// Abilities: ABILITY_DANCER, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
// Generation: 9

