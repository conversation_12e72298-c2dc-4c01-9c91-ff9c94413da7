// POKEMON_742 (#742) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_742] =
    {
        .baseHP = 40,
        .baseAttack = 45,
        .baseDefense = 40,
        .baseSpAttack = 55,
        .baseSpDefense = 40,
        .baseSpeed = 84,
        .type1 = TYPE_BUG,
        .type2 = TYPE_FAIRY,
        .catchRate = 190,
        .expYield = 85,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_HONEY-GATHER,
        .ability2 = ABILITY_SHIELD-DUST,
        .hiddenAbility = ABILITY_SWEET-VEIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-742LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_FAIRY_WIND),
    LEVEL_UP_MOVE( 6, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE(12, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(18, MOVE_DRAINING_KISS),
    LEVEL_UP_MOVE(24, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE(30, MOVE_COVET),
    LEVEL_UP_MOVE(36, MOVE_SWITCHEROO),
    LEVEL_UP_MOVE(42, MOVE_DAZZLING_GLEAM),
    LEVEL_UP_MOVE(48, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(54, MOVE_QUIVER_DANCE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 304
// Types: TYPE_BUG / TYPE_FAIRY
// Abilities: ABILITY_HONEY-GATHER, ABILITY_SHIELD-DUST, ABILITY_SWEET-VEIL
// Level Up Moves: 11
// Generation: 9

