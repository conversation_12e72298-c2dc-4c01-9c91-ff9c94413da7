// POKEMON_743 (#743) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_743] =
    {
        .baseHP = 60,
        .baseAttack = 55,
        .baseDefense = 60,
        .baseSpAttack = 95,
        .baseSpDefense = 70,
        .baseSpeed = 124,
        .type1 = TYPE_BUG,
        .type2 = TYPE_FAIRY,
        .catchRate = 75,
        .expYield = 115,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_HONEY-GATHER,
        .ability2 = ABILITY_SHIELD-DUST,
        .hiddenAbility = ABILITY_SWEET-VEIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-743LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_POLLEN_PUFF),
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_FAIRY_WIND),
    LEVEL_UP_MOVE( 1, MOVE_STUN_SPORE),
    LEVEL_UP_MOVE( 1, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(18, MOVE_DRAINING_KISS),
    LEVEL_UP_MOVE(24, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE(32, MOVE_COVET),
    LEVEL_UP_MOVE(40, MOVE_SWITCHEROO),
    LEVEL_UP_MOVE(48, MOVE_DAZZLING_GLEAM),
    LEVEL_UP_MOVE(56, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(64, MOVE_QUIVER_DANCE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 464
// Types: TYPE_BUG / TYPE_FAIRY
// Abilities: ABILITY_HONEY-GATHER, ABILITY_SHIELD-DUST, ABILITY_SWEET-VEIL
// Level Up Moves: 12
// Generation: 9

