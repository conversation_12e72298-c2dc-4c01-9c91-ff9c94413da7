// POKEMON_744 (#744) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_744] =
    {
        .baseHP = 45,
        .baseAttack = 65,
        .baseDefense = 40,
        .baseSpAttack = 30,
        .baseSpDefense = 40,
        .baseSpeed = 60,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_ROCK,
        .catchRate = 190,
        .expYield = 110,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_KEEN-EYE,
        .ability2 = ABILITY_VITAL-SPIRIT,
        .hiddenAbility = ABILITY_STEADFAST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-744LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 4, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE( 8, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(12, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE(16, MOVE_HOWL),
    LEVEL_UP_MOVE(20, MOVE_BITE),
    LEVEL_UP_MOVE(24, MOVE_ROCK_TOMB),
    LEVEL_UP_MOVE(28, MOVE_ROAR),
    LEVEL_UP_MOVE(32, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(36, MOVE_CRUNCH),
    LEVEL_UP_MOVE(40, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(44, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(48, MOVE_STONE_EDGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 280
// Types: TYPE_ROCK / TYPE_ROCK
// Abilities: ABILITY_KEEN-EYE, ABILITY_VITAL-SPIRIT, ABILITY_STEADFAST
// Level Up Moves: 14
// Generation: 9

