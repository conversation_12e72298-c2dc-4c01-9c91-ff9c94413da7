// POKEMON_746 (#746) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_746] =
    {
        .baseHP = 45,
        .baseAttack = 20,
        .baseDefense = 20,
        .baseSpAttack = 25,
        .baseSpDefense = 25,
        .baseSpeed = 40,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 60,
        .expYield = 65,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SCHOOLING,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-746LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 4, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 8, MOVE_BEAT_UP),
    LEVEL_UP_MOVE(12, MOVE_BRINE),
    LEVEL_UP_MOVE(16, MOVE_TEARFUL_LOOK),
    LEVEL_UP_MOVE(20, MOVE_DIVE),
    LEVEL_UP_MOVE(24, MOVE_SOAK),
    LEVEL_UP_MOVE(28, MOVE_UPROAR),
    LEVEL_UP_MOVE(32, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(36, MOVE_AQUA_RING),
    LEVEL_UP_MOVE(40, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(44, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(48, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 175
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_SCHOOLING, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
// Generation: 8

