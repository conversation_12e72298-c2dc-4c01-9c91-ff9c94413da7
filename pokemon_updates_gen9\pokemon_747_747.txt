// POKEMON_747 (#747) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_747] =
    {
        .baseHP = 50,
        .baseAttack = 53,
        .baseDefense = 62,
        .baseSpAttack = 43,
        .baseSpDefense = 52,
        .baseSpeed = 45,
        .type1 = TYPE_POISON,
        .type2 = TYPE_WATER,
        .catchRate = 190,
        .expYield = 103,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_MERCILESS,
        .ability2 = ABILITY_LIMBER,
        .hiddenAbility = ABILITY_REGENERATOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-747LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_POISON_STING),
    LEVEL_UP_MOVE( 5, MOVE_WIDE_GUARD),
    LEVEL_UP_MOVE(10, MOVE_BITE),
    LEVEL_UP_MOVE(15, MOVE_VENOSHOCK),
    LEVEL_UP_MOVE(20, MOVE_RECOVER),
    LEVEL_UP_MOVE(25, MOVE_PIN_MISSILE),
    LEVEL_UP_MOVE(30, MOVE_TOXIC_SPIKES),
    LEVEL_UP_MOVE(35, MOVE_LIQUIDATION),
    LEVEL_UP_MOVE(40, MOVE_ACID_SPRAY),
    LEVEL_UP_MOVE(45, MOVE_POISON_JAB),
    LEVEL_UP_MOVE(50, MOVE_TOXIC),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 305
// Types: TYPE_POISON / TYPE_WATER
// Abilities: ABILITY_MERCILESS, ABILITY_LIMBER, ABILITY_REGENERATOR
// Level Up Moves: 12
// Generation: 9

