// POKEMON_749 (#749) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_749] =
    {
        .baseHP = 70,
        .baseAttack = 100,
        .baseDefense = 70,
        .baseSpAttack = 45,
        .baseSpDefense = 55,
        .baseSpeed = 45,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_GROUND,
        .catchRate = 190,
        .expYield = 170,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_OWN-TEMPO,
        .ability2 = ABILITY_STAMINA,
        .hiddenAbility = ABILITY_INNER-FOCUS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-749LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE( 4, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE( 8, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(12, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(16, MOVE_STOMP),
    LEVEL_UP_MOVE(20, MOVE_STRENGTH),
    LEVEL_UP_MOVE(24, MOVE_COUNTER),
    LEVEL_UP_MOVE(28, MOVE_HIGH_HORSEPOWER),
    LEVEL_UP_MOVE(32, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(36, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(40, MOVE_MEGA_KICK),
    LEVEL_UP_MOVE(44, MOVE_SUPERPOWER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 385
// Types: TYPE_GROUND / TYPE_GROUND
// Abilities: ABILITY_OWN-TEMPO, ABILITY_STAMINA, ABILITY_INNER-FOCUS
// Level Up Moves: 13
// Generation: 9

