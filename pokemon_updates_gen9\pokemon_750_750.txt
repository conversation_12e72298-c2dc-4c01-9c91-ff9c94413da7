// POKEMON_750 (#750) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_750] =
    {
        .baseHP = 100,
        .baseAttack = 125,
        .baseDefense = 100,
        .baseSpAttack = 55,
        .baseSpDefense = 85,
        .baseSpeed = 35,
        .type1 = TYPE_GROUND,
        .type2 = TYPE_GROUND,
        .catchRate = 60,
        .expYield = 225,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_OWN-TEMPO,
        .ability2 = ABILITY_STAMINA,
        .hiddenAbility = ABILITY_INNER-FOCUS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-750LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE( 1, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE(12, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(16, MOVE_STOMP),
    LEVEL_UP_MOVE(20, MOVE_STRENGTH),
    LEVEL_UP_MOVE(24, MOVE_COUNTER),
    LEVEL_UP_MOVE(28, MOVE_HIGH_HORSEPOWER),
    LEVEL_UP_MOVE(34, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(40, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(46, MOVE_MEGA_KICK),
    LEVEL_UP_MOVE(52, MOVE_SUPERPOWER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_GROUND / TYPE_GROUND
// Abilities: ABILITY_OWN-TEMPO, ABILITY_STAMINA, ABILITY_INNER-FOCUS
// Level Up Moves: 13
// Generation: 9

