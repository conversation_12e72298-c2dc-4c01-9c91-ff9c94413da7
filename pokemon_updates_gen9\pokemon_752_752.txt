// POKEMON_752 (#752) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_752] =
    {
        .baseHP = 68,
        .baseAttack = 70,
        .baseDefense = 92,
        .baseSpAttack = 50,
        .baseSpDefense = 132,
        .baseSpeed = 42,
        .type1 = TYPE_WATER,
        .type2 = TYPE_BUG,
        .catchRate = 100,
        .expYield = 138,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_WATER-BUBBLE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_WATER-ABSORB,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-752LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_BUG_BITE),
    LEVEL_UP_MOVE( 1, MOVE_INFESTATION),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 1, MOVE_WIDE_GUARD),
    LEVEL_UP_MOVE(12, MOVE_BUBBLE_BEAM),
    LEVEL_UP_MOVE(16, MOVE_AQUA_RING),
    LEVEL_UP_MOVE(20, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(26, MOVE_CRUNCH),
    LEVEL_UP_MOVE(32, MOVE_SOAK),
    LEVEL_UP_MOVE(38, MOVE_ENTRAINMENT),
    LEVEL_UP_MOVE(44, MOVE_LUNGE),
    LEVEL_UP_MOVE(50, MOVE_LIQUIDATION),
    LEVEL_UP_MOVE(56, MOVE_LEECH_LIFE),
    LEVEL_UP_MOVE(62, MOVE_MIRROR_COAT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 454
// Types: TYPE_WATER / TYPE_BUG
// Abilities: ABILITY_WATER-BUBBLE, ABILITY_NONE, ABILITY_WATER-ABSORB
// Level Up Moves: 15
// Generation: 9

