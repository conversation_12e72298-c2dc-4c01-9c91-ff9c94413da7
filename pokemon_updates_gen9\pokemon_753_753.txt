// POKEMON_753 (#753) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_753] =
    {
        .baseHP = 40,
        .baseAttack = 55,
        .baseDefense = 35,
        .baseSpAttack = 50,
        .baseSpDefense = 35,
        .baseSpeed = 35,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 190,
        .expYield = 95,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_LEAF-GUARD,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_CONTRARY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-753LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 1, MOVE_LEAFAGE),
    LEVEL_UP_MOVE( 5, MOVE_GROWTH),
    LEVEL_UP_MOVE(10, MOVE_INGRAIN),
    LEVEL_UP_MOVE(15, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(20, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(25, MOVE_SLASH),
    LEVEL_UP_MOVE(30, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(35, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(40, MOVE_LEAF_BLADE),
    LEVEL_UP_MOVE(45, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE(50, MOVE_SOLAR_BEAM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 250
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_LEAF-GUARD, ABILITY_NONE, ABILITY_CONTRARY
// Level Up Moves: 12
// Generation: 9

