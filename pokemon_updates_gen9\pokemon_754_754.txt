// POKEMON_754 (#754) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_754] =
    {
        .baseHP = 70,
        .baseAttack = 105,
        .baseDefense = 90,
        .baseSpAttack = 80,
        .baseSpDefense = 90,
        .baseSpeed = 45,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 75,
        .expYield = 175,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_LEAF-GUARD,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_CONTRARY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-754LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_PETAL_BLIZZARD),
    LEVEL_UP_MOVE( 1, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 1, MOVE_INGRAIN),
    LEVEL_UP_MOVE( 1, MOVE_LEAFAGE),
    LEVEL_UP_MOVE( 1, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE( 1, MOVE_SOLAR_BEAM),
    LEVEL_UP_MOVE(15, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(20, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(25, MOVE_SLASH),
    LEVEL_UP_MOVE(30, MOVE_X_SCISSOR),
    LEVEL_UP_MOVE(37, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(44, MOVE_LEAF_BLADE),
    LEVEL_UP_MOVE(51, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE(63, MOVE_SOLAR_BLADE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 480
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_LEAF-GUARD, ABILITY_NONE, ABILITY_CONTRARY
// Level Up Moves: 15
// Generation: 9

