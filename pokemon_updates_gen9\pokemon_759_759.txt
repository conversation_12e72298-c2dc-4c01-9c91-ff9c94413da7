// POKEMON_759 (#759) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_759] =
    {
        .baseHP = 70,
        .baseAttack = 75,
        .baseDefense = 50,
        .baseSpAttack = 45,
        .baseSpDefense = 50,
        .baseSpeed = 50,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FIGHTING,
        .catchRate = 140,
        .expYield = 145,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_FLUFFY,
        .ability2 = ABILITY_KLUTZ,
        .hiddenAbility = ABILITY_CUTE-CHARM,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-759LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 4, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE( 8, MOVE_PAYBACK),
    LEVEL_UP_MOVE(12, MOVE_BRUTAL_SWING),
    LEVEL_UP_MOVE(16, MOVE_ENDURE),
    LEVEL_UP_MOVE(20, MOVE_STRENGTH),
    LEVEL_UP_MOVE(24, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(28, MOVE_FLAIL),
    LEVEL_UP_MOVE(32, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE(36, MOVE_THRASH),
    LEVEL_UP_MOVE(40, MOVE_PAIN_SPLIT),
    LEVEL_UP_MOVE(44, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(48, MOVE_SUPERPOWER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 340
// Types: TYPE_NORMAL / TYPE_FIGHTING
// Abilities: ABILITY_FLUFFY, ABILITY_KLUTZ, ABILITY_CUTE-CHARM
// Level Up Moves: 14
// Generation: 8

