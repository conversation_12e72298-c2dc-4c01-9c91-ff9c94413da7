// POKEMON_765 (#765) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_765] =
    {
        .baseHP = 90,
        .baseAttack = 60,
        .baseDefense = 80,
        .baseSpAttack = 90,
        .baseSpDefense = 110,
        .baseSpeed = 60,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 45,
        .expYield = 150,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_INNER-FOCUS,
        .ability2 = ABILITY_TELEPATHY,
        .hiddenAbility = ABILITY_SYMBIOSIS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-765LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_TAUNT),
    LEVEL_UP_MOVE( 5, MOVE_AFTER_YOU),
    LEVEL_UP_MOVE(10, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(15, MOVE_STORED_POWER),
    LEVEL_UP_MOVE(20, MOVE_PSYCH_UP),
    LEVEL_UP_MOVE(25, MOVE_QUASH),
    LEVEL_UP_MOVE(30, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(35, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(40, MOVE_TRICK_ROOM),
    LEVEL_UP_MOVE(45, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(50, MOVE_INSTRUCT),
    LEVEL_UP_MOVE(55, MOVE_FOUL_PLAY),
    LEVEL_UP_MOVE(60, MOVE_FUTURE_SIGHT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 490
// Types: TYPE_NORMAL / TYPE_PSYCHIC
// Abilities: ABILITY_INNER-FOCUS, ABILITY_TELEPATHY, ABILITY_SYMBIOSIS
// Level Up Moves: 14
// Generation: 9

