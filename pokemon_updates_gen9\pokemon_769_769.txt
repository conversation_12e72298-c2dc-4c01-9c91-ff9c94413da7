// POKEMON_769 (#769) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_769] =
    {
        .baseHP = 55,
        .baseAttack = 55,
        .baseDefense = 80,
        .baseSpAttack = 70,
        .baseSpDefense = 45,
        .baseSpeed = 15,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_GROUND,
        .catchRate = 140,
        .expYield = 110,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_WATER-COMPACTION,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SAND-VEIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-769LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 5, MOVE_ASTONISH),
    LEVEL_UP_MOVE(10, MOVE_SAND_TOMB),
    LEVEL_UP_MOVE(15, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(20, MOVE_SAND_ATTACK),
    LEVEL_UP_MOVE(25, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(30, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE(35, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(40, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(45, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(50, MOVE_EARTH_POWER),
    LEVEL_UP_MOVE(55, MOVE_SHORE_UP),
    LEVEL_UP_MOVE(60, MOVE_SANDSTORM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 320
// Types: TYPE_GHOST / TYPE_GROUND
// Abilities: ABILITY_WATER-COMPACTION, ABILITY_NONE, ABILITY_SAND-VEIL
// Level Up Moves: 14
// Generation: 9

