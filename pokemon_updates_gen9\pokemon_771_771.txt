// POKEMON_771 (#771) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_771] =
    {
        .baseHP = 55,
        .baseAttack = 60,
        .baseDefense = 130,
        .baseSpAttack = 30,
        .baseSpDefense = 130,
        .baseSpeed = 5,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 60,
        .expYield = 115,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_INNARDS-OUT,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_UNAWARE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-771LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BATON_PASS),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 5, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(10, MOVE_TAUNT),
    LEVEL_UP_MOVE(15, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(20, MOVE_COUNTER),
    LEVEL_UP_MOVE(25, MOVE_PURIFY),
    LEVEL_UP_MOVE(30, MOVE_CURSE),
    LEVEL_UP_MOVE(35, MOVE_GASTRO_ACID),
    LEVEL_UP_MOVE(40, MOVE_PAIN_SPLIT),
    LEVEL_UP_MOVE(45, MOVE_RECOVER),
    LEVEL_UP_MOVE(50, MOVE_SOAK),
    LEVEL_UP_MOVE(55, MOVE_TOXIC),
    LEVEL_UP_MOVE(60, MOVE_MEMENTO),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 410
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_INNARDS-OUT, ABILITY_NONE, ABILITY_UNAWARE
// Level Up Moves: 14
// Generation: 8

