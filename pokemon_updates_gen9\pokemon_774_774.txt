// POKEMON_774 (#774) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_774] =
    {
        .baseHP = 60,
        .baseAttack = 60,
        .baseDefense = 100,
        .baseSpAttack = 60,
        .baseSpDefense = 100,
        .baseSpeed = 60,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_FLYING,
        .catchRate = 30,
        .expYield = 120,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 25,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SHIELDS-DOWN,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-774LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 3, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 8, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(10, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(15, MOVE_SWIFT),
    LEVEL_UP_MOVE(17, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(22, MOVE_SELF_DESTRUCT),
    LEVEL_UP_MOVE(24, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(29, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(31, MOVE_ROCK_POLISH),
    LEVEL_UP_MOVE(36, MOVE_COSMIC_POWER),
    LEVEL_UP_MOVE(38, MOVE_POWER_GEM),
    LEVEL_UP_MOVE(43, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(45, MOVE_SHELL_SMASH),
    LEVEL_UP_MOVE(50, MOVE_EXPLOSION),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 440
// Types: TYPE_ROCK / TYPE_FLYING
// Abilities: ABILITY_SHIELDS-DOWN, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
// Generation: 9

