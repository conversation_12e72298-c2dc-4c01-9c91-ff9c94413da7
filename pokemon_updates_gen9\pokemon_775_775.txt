// POKEMON_775 (#775) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_775] =
    {
        .baseHP = 65,
        .baseAttack = 115,
        .baseDefense = 65,
        .baseSpAttack = 75,
        .baseSpDefense = 95,
        .baseSpeed = 65,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 45,
        .expYield = 180,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 70,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_COMATOSE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-775LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_ROLLOUT),
    LEVEL_UP_MOVE( 6, MOVE_SPIT_UP),
    LEVEL_UP_MOVE( 6, MOVE_STOCKPILE),
    LEVEL_UP_MOVE( 6, MOVE_SWALLOW),
    LEVEL_UP_MOVE(11, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE(16, MOVE_YAWN),
    LEVEL_UP_MOVE(21, MOVE_SLAM),
    LEVEL_UP_MOVE(26, MOVE_FLAIL),
    LEVEL_UP_MOVE(31, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(36, MOVE_PSYCH_UP),
    LEVEL_UP_MOVE(41, MOVE_WOOD_HAMMER),
    LEVEL_UP_MOVE(46, MOVE_THRASH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 480
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_COMATOSE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 13
// Generation: 9

