// POKEMON_776 (#776) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_776] =
    {
        .baseHP = 60,
        .baseAttack = 78,
        .baseDefense = 135,
        .baseSpAttack = 91,
        .baseSpDefense = 85,
        .baseSpeed = 36,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_DRAGON,
        .catchRate = 70,
        .expYield = 138,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SHELL-ARMOR,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-776LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SMOG),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 4, MOVE_EMBER),
    LEVEL_UP_MOVE( 8, MOVE_PROTECT),
    LEVEL_UP_MOVE(12, MOVE_ENDURE),
    LEVEL_UP_MOVE(16, MOVE_FLAIL),
    LEVEL_UP_MOVE(20, MOVE_INCINERATE),
    LEVEL_UP_MOVE(24, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(28, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(32, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(36, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(40, MOVE_SHELL_TRAP),
    LEVEL_UP_MOVE(44, MOVE_SHELL_SMASH),
    LEVEL_UP_MOVE(48, MOVE_OVERHEAT),
    LEVEL_UP_MOVE(52, MOVE_EXPLOSION),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 485
// Types: TYPE_FIRE / TYPE_DRAGON
// Abilities: ABILITY_SHELL-ARMOR, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
// Generation: 8

