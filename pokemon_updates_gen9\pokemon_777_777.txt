// POKEMON_777 (#777) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_777] =
    {
        .baseHP = 65,
        .baseAttack = 98,
        .baseDefense = 63,
        .baseSpAttack = 40,
        .baseSpDefense = 73,
        .baseSpeed = 96,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_STEEL,
        .catchRate = 180,
        .expYield = 163,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 10,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_IRON-BARBS,
        .ability2 = ABILITY_LIGHTNING-ROD,
        .hiddenAbility = ABILITY_STURDY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-777LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_NUZZLE),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE(10, MOVE_CHARGE),
    LEVEL_UP_MOVE(15, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE(20, MOVE_FELL_STINGER),
    LEVEL_UP_MOVE(25, MOVE_SPARK),
    LEVEL_UP_MOVE(30, MOVE_PIN_MISSILE),
    LEVEL_UP_MOVE(35, MOVE_MAGNET_RISE),
    LEVEL_UP_MOVE(40, MOVE_ZING_ZAP),
    LEVEL_UP_MOVE(45, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(50, MOVE_ELECTRIC_TERRAIN),
    LEVEL_UP_MOVE(55, MOVE_WILD_CHARGE),
    LEVEL_UP_MOVE(60, MOVE_SPIKY_SHIELD),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 435
// Types: TYPE_ELECTRIC / TYPE_STEEL
// Abilities: ABILITY_IRON-BARBS, ABILITY_LIGHTNING-ROD, ABILITY_STURDY
// Level Up Moves: 14
// Generation: 8

