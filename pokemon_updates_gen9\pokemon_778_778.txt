// POKEMON_778 (#778) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_778] =
    {
        .baseHP = 55,
        .baseAttack = 90,
        .baseDefense = 80,
        .baseSpAttack = 50,
        .baseSpDefense = 105,
        .baseSpeed = 96,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_FAIRY,
        .catchRate = 45,
        .expYield = 145,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_DISGUISE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-778LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_SPLASH),
    LEVEL_UP_MOVE( 1, MOVE_WOOD_HAMMER),
    LEVEL_UP_MOVE( 6, MOVE_SHADOW_SNEAK),
    LEVEL_UP_MOVE(12, MOVE_DOUBLE_TEAM),
    LEVEL_UP_MOVE(18, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(24, MOVE_MIMIC),
    LEVEL_UP_MOVE(30, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(36, MOVE_SLASH),
    LEVEL_UP_MOVE(42, MOVE_SHADOW_CLAW),
    LEVEL_UP_MOVE(48, MOVE_CHARM),
    LEVEL_UP_MOVE(54, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(60, MOVE_PAIN_SPLIT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 476
// Types: TYPE_GHOST / TYPE_FAIRY
// Abilities: ABILITY_DISGUISE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
// Generation: 9

