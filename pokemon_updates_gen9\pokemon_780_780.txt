// POKEMON_780 (#780) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_780] =
    {
        .baseHP = 78,
        .baseAttack = 60,
        .baseDefense = 85,
        .baseSpAttack = 135,
        .baseSpDefense = 91,
        .baseSpeed = 36,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_DRAGON,
        .catchRate = 70,
        .expYield = 138,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BERSERK,
        .ability2 = ABILITY_SAP-SIPPER,
        .hiddenAbility = ABILITY_CLOUD-NINE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-780LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ECHOED_VOICE),
    LEVEL_UP_MOVE( 1, MOVE_PLAY_NICE),
    LEVEL_UP_MOVE( 5, MOVE_TWISTER),
    LEVEL_UP_MOVE(10, MOVE_PROTECT),
    LEVEL_UP_MOVE(15, MOVE_GLARE),
    LEVEL_UP_MOVE(20, MOVE_SAFEGUARD),
    LEVEL_UP_MOVE(25, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(30, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE(35, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(40, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(45, MOVE_FLY),
    LEVEL_UP_MOVE(50, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(55, MOVE_OUTRAGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 485
// Types: TYPE_NORMAL / TYPE_DRAGON
// Abilities: ABILITY_BERSERK, ABILITY_SAP-SIPPER, ABILITY_CLOUD-NINE
// Level Up Moves: 13
// Generation: 8

