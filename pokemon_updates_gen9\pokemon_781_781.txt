// POKEMON_781 (#781) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_781] =
    {
        .baseHP = 70,
        .baseAttack = 131,
        .baseDefense = 100,
        .baseSpAttack = 86,
        .baseSpDefense = 90,
        .baseSpeed = 40,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_GRASS,
        .catchRate = 25,
        .expYield = 201,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STEELWORKER,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-781LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE( 4, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 8, MOVE_WRAP),
    LEVEL_UP_MOVE(12, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(16, MOVE_GROWTH),
    LEVEL_UP_MOVE(20, MOVE_GYRO_BALL),
    LEVEL_UP_MOVE(24, MOVE_SWITCHEROO),
    LEVEL_UP_MOVE(28, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(32, MOVE_WHIRLPOOL),
    LEVEL_UP_MOVE(36, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(40, MOVE_SLAM),
    LEVEL_UP_MOVE(44, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(48, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(52, MOVE_ANCHOR_SHOT),
    LEVEL_UP_MOVE(56, MOVE_ENERGY_BALL),
    LEVEL_UP_MOVE(60, MOVE_PHANTOM_FORCE),
    LEVEL_UP_MOVE(64, MOVE_POWER_WHIP),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 517
// Types: TYPE_GHOST / TYPE_GRASS
// Abilities: ABILITY_STEELWORKER, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 18
// Generation: 8

