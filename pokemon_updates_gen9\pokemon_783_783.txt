// POKEMON_783 (#783) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_783] =
    {
        .baseHP = 55,
        .baseAttack = 75,
        .baseDefense = 90,
        .baseSpAttack = 65,
        .baseSpDefense = 70,
        .baseSpeed = 65,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_FIGHTING,
        .catchRate = 45,
        .expYield = 130,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 40,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BULLETPROOF,
        .ability2 = ABILITY_SOUNDPROOF,
        .hiddenAbility = ABILITY_OVERCOAT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-783LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_PROTECT),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE(12, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(16, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(20, MOVE_WORK_UP),
    LEVEL_UP_MOVE(24, MOVE_SCREECH),
    LEVEL_UP_MOVE(28, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(32, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(38, MOVE_NOBLE_ROAR),
    LEVEL_UP_MOVE(44, MOVE_DRAGON_DANCE),
    LEVEL_UP_MOVE(50, MOVE_OUTRAGE),
    LEVEL_UP_MOVE(56, MOVE_CLOSE_COMBAT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 420
// Types: TYPE_DRAGON / TYPE_FIGHTING
// Abilities: ABILITY_BULLETPROOF, ABILITY_SOUNDPROOF, ABILITY_OVERCOAT
// Level Up Moves: 14
// Generation: 9

