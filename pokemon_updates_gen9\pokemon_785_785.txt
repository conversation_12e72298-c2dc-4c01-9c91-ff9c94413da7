// POKEMON_785 (#785) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_785] =
    {
        .baseHP = 70,
        .baseAttack = 115,
        .baseDefense = 85,
        .baseSpAttack = 95,
        .baseSpDefense = 75,
        .baseSpeed = 130,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_FAIRY,
        .catchRate = 3,
        .expYield = 185,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_ELECTRIC-SURGE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_TELEPATHY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-785LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 5, MOVE_WITHDRAW),
    LEVEL_UP_MOVE(10, MOVE_FAIRY_WIND),
    LEVEL_UP_MOVE(15, MOVE_FALSE_SWIPE),
    LEVEL_UP_MOVE(20, MOVE_SPARK),
    LEVEL_UP_MOVE(25, MOVE_SHOCK_WAVE),
    LEVEL_UP_MOVE(30, MOVE_CHARGE),
    LEVEL_UP_MOVE(35, MOVE_AGILITY),
    LEVEL_UP_MOVE(40, MOVE_SCREECH),
    LEVEL_UP_MOVE(45, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(50, MOVE_MEAN_LOOK),
    LEVEL_UP_MOVE(55, MOVE_NATURES_MADNESS),
    LEVEL_UP_MOVE(60, MOVE_WILD_CHARGE),
    LEVEL_UP_MOVE(65, MOVE_BRAVE_BIRD),
    LEVEL_UP_MOVE(70, MOVE_POWER_SWAP),
    LEVEL_UP_MOVE(75, MOVE_ELECTRIC_TERRAIN),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_ELECTRIC / TYPE_FAIRY
// Abilities: ABILITY_ELECTRIC-SURGE, ABILITY_NONE, ABILITY_TELEPATHY
// Level Up Moves: 17
// Generation: 8

