// POKEMON_786 (#786) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_786] =
    {
        .baseHP = 70,
        .baseAttack = 85,
        .baseDefense = 75,
        .baseSpAttack = 130,
        .baseSpDefense = 115,
        .baseSpeed = 95,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_FAIRY,
        .catchRate = 3,
        .expYield = 155,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PSYCHIC-SURGE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_TELEPATHY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-786LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 5, MOVE_WITHDRAW),
    LEVEL_UP_MOVE(10, MOVE_AROMATHERAPY),
    LEVEL_UP_MOVE(15, MOVE_DRAINING_KISS),
    LEVEL_UP_MOVE(20, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(25, MOVE_FLATTER),
    LEVEL_UP_MOVE(30, MOVE_AROMATIC_MIST),
    LEVEL_UP_MOVE(35, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(40, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE(45, MOVE_PSYSHOCK),
    LEVEL_UP_MOVE(50, MOVE_MEAN_LOOK),
    LEVEL_UP_MOVE(55, MOVE_NATURES_MADNESS),
    LEVEL_UP_MOVE(60, MOVE_MOONBLAST),
    LEVEL_UP_MOVE(65, MOVE_TICKLE),
    LEVEL_UP_MOVE(70, MOVE_SKILL_SWAP),
    LEVEL_UP_MOVE(75, MOVE_PSYCHIC_TERRAIN),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_PSYCHIC / TYPE_FAIRY
// Abilities: ABILITY_PSYCHIC-SURGE, ABILITY_NONE, ABILITY_TELEPATHY
// Level Up Moves: 17
// Generation: 8

