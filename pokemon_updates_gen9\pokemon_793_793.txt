// POKEMON_793 (#793) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_793] =
    {
        .baseHP = 109,
        .baseAttack = 53,
        .baseDefense = 47,
        .baseSpAttack = 127,
        .baseSpDefense = 131,
        .baseSpeed = 103,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_POISON,
        .catchRate = 45,
        .expYield = 162,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BEAST-BOOST,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-793LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_WRAP),
    LEVEL_UP_MOVE( 5, MOVE_ACID),
    LEVEL_UP_MOVE(10, MOVE_TICKLE),
    LEVEL_UP_MOVE(15, MOVE_ACID_SPRAY),
    LEVEL_UP_MOVE(20, MOVE_CLEAR_SMOG),
    LEVEL_UP_MOVE(25, MOVE_GUARD_SPLIT),
    LEVEL_UP_MOVE(25, MOVE_POWER_SPLIT),
    LEVEL_UP_MOVE(30, MOVE_VENOSHOCK),
    LEVEL_UP_MOVE(35, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(40, MOVE_TOXIC_SPIKES),
    LEVEL_UP_MOVE(45, MOVE_VENOM_DRENCH),
    LEVEL_UP_MOVE(50, MOVE_POWER_GEM),
    LEVEL_UP_MOVE(55, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(60, MOVE_MIRROR_COAT),
    LEVEL_UP_MOVE(65, MOVE_WONDER_ROOM),
    LEVEL_UP_MOVE(70, MOVE_HEAD_SMASH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_ROCK / TYPE_POISON
// Abilities: ABILITY_BEAST-BOOST, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 17
// Generation: 8

