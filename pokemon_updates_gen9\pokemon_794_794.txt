// POKEMON_794 (#794) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_794] =
    {
        .baseHP = 107,
        .baseAttack = 139,
        .baseDefense = 139,
        .baseSpAttack = 53,
        .baseSpDefense = 53,
        .baseSpeed = 79,
        .type1 = TYPE_BUG,
        .type2 = TYPE_FIGHTING,
        .catchRate = 45,
        .expYield = 246,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BEAST-BOOST,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-794LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_POWER_UP_PUNCH),
    LEVEL_UP_MOVE( 5, MOVE_TAUNT),
    LEVEL_UP_MOVE(10, MOVE_FELL_STINGER),
    LEVEL_UP_MOVE(15, MOVE_VITAL_THROW),
    LEVEL_UP_MOVE(20, MOVE_BULK_UP),
    LEVEL_UP_MOVE(25, MOVE_ENDURE),
    LEVEL_UP_MOVE(30, MOVE_REVERSAL),
    LEVEL_UP_MOVE(35, MOVE_MEGA_PUNCH),
    LEVEL_UP_MOVE(40, MOVE_LUNGE),
    LEVEL_UP_MOVE(45, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(50, MOVE_DYNAMIC_PUNCH),
    LEVEL_UP_MOVE(55, MOVE_COUNTER),
    LEVEL_UP_MOVE(60, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE(65, MOVE_SUPERPOWER),
    LEVEL_UP_MOVE(70, MOVE_FOCUS_PUNCH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_BUG / TYPE_FIGHTING
// Abilities: ABILITY_BEAST-BOOST, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
// Generation: 8

