// POKEMON_795 (#795) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_795] =
    {
        .baseHP = 71,
        .baseAttack = 137,
        .baseDefense = 37,
        .baseSpAttack = 137,
        .baseSpDefense = 37,
        .baseSpeed = 151,
        .type1 = TYPE_BUG,
        .type2 = TYPE_FIGHTING,
        .catchRate = 45,
        .expYield = 208,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BEAST-BOOST,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-795LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_FEINT),
    LEVEL_UP_MOVE( 1, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE( 5, MOVE_LEER),
    LEVEL_UP_MOVE(10, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(15, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(20, MOVE_LOW_KICK),
    LEVEL_UP_MOVE(25, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(30, MOVE_TRIPLE_KICK),
    LEVEL_UP_MOVE(35, MOVE_STOMP),
    LEVEL_UP_MOVE(40, MOVE_AGILITY),
    LEVEL_UP_MOVE(45, MOVE_LUNGE),
    LEVEL_UP_MOVE(50, MOVE_BOUNCE),
    LEVEL_UP_MOVE(55, MOVE_SPEED_SWAP),
    LEVEL_UP_MOVE(60, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(65, MOVE_QUIVER_DANCE),
    LEVEL_UP_MOVE(70, MOVE_HIGH_JUMP_KICK),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_BUG / TYPE_FIGHTING
// Abilities: ABILITY_BEAST-BOOST, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
// Generation: 8

