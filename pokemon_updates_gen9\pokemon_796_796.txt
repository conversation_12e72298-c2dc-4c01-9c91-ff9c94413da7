// POKEMON_796 (#796) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_796] =
    {
        .baseHP = 83,
        .baseAttack = 89,
        .baseDefense = 71,
        .baseSpAttack = 173,
        .baseSpDefense = 71,
        .baseSpeed = 83,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 45,
        .expYield = 172,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BEAST-BOOST,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-796LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 1, MOVE_WRAP),
    LEVEL_UP_MOVE( 5, MOVE_CHARGE),
    LEVEL_UP_MOVE(10, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE(15, MOVE_INGRAIN),
    LEVEL_UP_MOVE(20, MOVE_SPARK),
    LEVEL_UP_MOVE(25, MOVE_SHOCK_WAVE),
    LEVEL_UP_MOVE(30, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE(35, MOVE_EERIE_IMPULSE),
    LEVEL_UP_MOVE(40, MOVE_THUNDER_PUNCH),
    LEVEL_UP_MOVE(45, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(50, MOVE_MAGNET_RISE),
    LEVEL_UP_MOVE(55, MOVE_THUNDERBOLT),
    LEVEL_UP_MOVE(60, MOVE_ELECTRIC_TERRAIN),
    LEVEL_UP_MOVE(65, MOVE_POWER_WHIP),
    LEVEL_UP_MOVE(70, MOVE_ZAP_CANNON),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_BEAST-BOOST, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
// Generation: 8

