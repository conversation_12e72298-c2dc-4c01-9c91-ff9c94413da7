// POKEMON_797 (#797) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_797] =
    {
        .baseHP = 97,
        .baseAttack = 101,
        .baseDefense = 103,
        .baseSpAttack = 107,
        .baseSpDefense = 101,
        .baseSpeed = 61,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_FLYING,
        .catchRate = 45,
        .expYield = 198,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BEAST-BOOST,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-797LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ABSORB),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_HARDEN),
    LEVEL_UP_MOVE(10, MOVE_WIDE_GUARD),
    LEVEL_UP_MOVE(15, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(20, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE(25, MOVE_INGRAIN),
    LEVEL_UP_MOVE(30, MOVE_AUTOTOMIZE),
    LEVEL_UP_MOVE(35, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(40, MOVE_FLASH_CANNON),
    LEVEL_UP_MOVE(45, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(50, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(55, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(60, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(65, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(70, MOVE_SKULL_BASH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_STEEL / TYPE_FLYING
// Abilities: ABILITY_BEAST-BOOST, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
// Generation: 8

