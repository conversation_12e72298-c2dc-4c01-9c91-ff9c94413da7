// POKEMON_798 (#798) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_798] =
    {
        .baseHP = 59,
        .baseAttack = 181,
        .baseDefense = 131,
        .baseSpAttack = 59,
        .baseSpDefense = 31,
        .baseSpeed = 109,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_STEEL,
        .catchRate = 45,
        .expYield = 240,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BEAST-BOOST,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-798LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_FURY_CUTTER),
    LEVEL_UP_MOVE( 1, MOVE_VACUUM_WAVE),
    LEVEL_UP_MOVE( 5, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(10, MOVE_FALSE_SWIPE),
    LEVEL_UP_MOVE(15, MOVE_CUT),
    LEVEL_UP_MOVE(20, MOVE_AIR_CUTTER),
    LEVEL_UP_MOVE(25, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(30, MOVE_DETECT),
    LEVEL_UP_MOVE(35, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(40, MOVE_SYNTHESIS),
    LEVEL_UP_MOVE(45, MOVE_LASER_FOCUS),
    LEVEL_UP_MOVE(50, MOVE_DEFOG),
    LEVEL_UP_MOVE(55, MOVE_LEAF_BLADE),
    LEVEL_UP_MOVE(60, MOVE_SACRED_SWORD),
    LEVEL_UP_MOVE(65, MOVE_SWORDS_DANCE),
    LEVEL_UP_MOVE(70, MOVE_GUILLOTINE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_GRASS / TYPE_STEEL
// Abilities: ABILITY_BEAST-BOOST, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
// Generation: 8

