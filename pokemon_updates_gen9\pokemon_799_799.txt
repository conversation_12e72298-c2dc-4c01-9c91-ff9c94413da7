// POKEMON_799 (#799) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_799] =
    {
        .baseHP = 223,
        .baseAttack = 101,
        .baseDefense = 53,
        .baseSpAttack = 97,
        .baseSpDefense = 53,
        .baseSpeed = 43,
        .type1 = TYPE_DARK,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 255,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BEAST-BOOST,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-799LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE( 5, MOVE_STOCKPILE),
    LEVEL_UP_MOVE( 5, MOVE_SWALLOW),
    LEVEL_UP_MOVE(10, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(15, MOVE_STOMP),
    LEVEL_UP_MOVE(20, MOVE_STOMPING_TANTRUM),
    LEVEL_UP_MOVE(25, MOVE_WIDE_GUARD),
    LEVEL_UP_MOVE(30, MOVE_CRUNCH),
    LEVEL_UP_MOVE(35, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(40, MOVE_GASTRO_ACID),
    LEVEL_UP_MOVE(45, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE(50, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(55, MOVE_DRAGON_RUSH),
    LEVEL_UP_MOVE(60, MOVE_BELCH),
    LEVEL_UP_MOVE(65, MOVE_THRASH),
    LEVEL_UP_MOVE(70, MOVE_GIGA_IMPACT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_DARK / TYPE_DRAGON
// Abilities: ABILITY_BEAST-BOOST, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 17
// Generation: 8

