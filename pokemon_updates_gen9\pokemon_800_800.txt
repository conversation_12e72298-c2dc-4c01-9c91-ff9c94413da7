// POKEMON_800 (#800) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_800] =
    {
        .baseHP = 97,
        .baseAttack = 107,
        .baseDefense = 101,
        .baseSpAttack = 127,
        .baseSpDefense = 89,
        .baseSpeed = 79,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 255,
        .expYield = 204,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PRISM-ARMOR,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-800LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CHARGE_BEAM),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE( 1, MOVE_MOONLIGHT),
    LEVEL_UP_MOVE( 1, MOVE_MORNING_SUN),
    LEVEL_UP_MOVE( 8, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(16, MOVE_SLASH),
    LEVEL_UP_MOVE(24, MOVE_NIGHT_SLASH),
    LEVEL_UP_MOVE(32, MOVE_PSYCHO_CUT),
    LEVEL_UP_MOVE(40, MOVE_STORED_POWER),
    LEVEL_UP_MOVE(48, MOVE_ROCK_BLAST),
    LEVEL_UP_MOVE(56, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(64, MOVE_POWER_GEM),
    LEVEL_UP_MOVE(72, MOVE_PHOTON_GEYSER),
    LEVEL_UP_MOVE(80, MOVE_GRAVITY),
    LEVEL_UP_MOVE(88, MOVE_PRISMATIC_LASER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_PRISM-ARMOR, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
// Generation: 9

