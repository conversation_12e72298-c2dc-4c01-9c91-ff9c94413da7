// POKEMON_801 (#801) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_801] =
    {
        .baseHP = 80,
        .baseAttack = 95,
        .baseDefense = 115,
        .baseSpAttack = 130,
        .baseSpDefense = 115,
        .baseSpeed = 65,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_FAIRY,
        .catchRate = 3,
        .expYield = 175,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SOUL-HEART,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-801LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GYRO_BALL),
    LEVEL_UP_MOVE( 1, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 6, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE(12, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(18, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(24, MOVE_MAGNETIC_FLUX),
    LEVEL_UP_MOVE(30, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(36, MOVE_AURORA_BEAM),
    LEVEL_UP_MOVE(42, MOVE_LOCK_ON),
    LEVEL_UP_MOVE(48, MOVE_SHIFT_GEAR),
    LEVEL_UP_MOVE(54, MOVE_TRICK),
    LEVEL_UP_MOVE(60, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(66, MOVE_AURA_SPHERE),
    LEVEL_UP_MOVE(72, MOVE_FLASH_CANNON),
    LEVEL_UP_MOVE(78, MOVE_PAIN_SPLIT),
    LEVEL_UP_MOVE(84, MOVE_ZAP_CANNON),
    LEVEL_UP_MOVE(90, MOVE_FLEUR_CANNON),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_STEEL / TYPE_FAIRY
// Abilities: ABILITY_SOUL-HEART, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 17
// Generation: 9

