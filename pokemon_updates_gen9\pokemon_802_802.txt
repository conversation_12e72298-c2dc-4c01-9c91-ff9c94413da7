// POKEMON_802 (#802) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_802] =
    {
        .baseHP = 90,
        .baseAttack = 125,
        .baseDefense = 80,
        .baseSpAttack = 90,
        .baseSpDefense = 90,
        .baseSpeed = 125,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_GHOST,
        .catchRate = 3,
        .expYield = 215,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_TECHNICIAN,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-802LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_COPYCAT),
    LEVEL_UP_MOVE( 1, MOVE_COUNTER),
    LEVEL_UP_MOVE( 1, MOVE_FEINT),
    LEVEL_UP_MOVE( 1, MOVE_FIRE_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_ICE_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_SHADOW_SNEAK),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_PUNCH),
    LEVEL_UP_MOVE( 9, MOVE_ROLE_PLAY),
    LEVEL_UP_MOVE(18, MOVE_SHADOW_PUNCH),
    LEVEL_UP_MOVE(27, MOVE_FORCE_PALM),
    LEVEL_UP_MOVE(36, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(45, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(54, MOVE_DRAIN_PUNCH),
    LEVEL_UP_MOVE(63, MOVE_PSYCH_UP),
    LEVEL_UP_MOVE(72, MOVE_SPECTRAL_THIEF),
    LEVEL_UP_MOVE(81, MOVE_LASER_FOCUS),
    LEVEL_UP_MOVE(90, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(99, MOVE_CLOSE_COMBAT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_FIGHTING / TYPE_GHOST
// Abilities: ABILITY_TECHNICIAN, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 18
// Generation: 8

