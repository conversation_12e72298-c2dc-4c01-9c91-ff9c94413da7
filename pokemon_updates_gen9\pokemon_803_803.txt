// POKEMON_803 (#803) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_803] =
    {
        .baseHP = 67,
        .baseAttack = 73,
        .baseDefense = 67,
        .baseSpAttack = 73,
        .baseSpDefense = 67,
        .baseSpeed = 73,
        .type1 = TYPE_POISON,
        .type2 = TYPE_POISON,
        .catchRate = 45,
        .expYield = 140,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BEAST-BOOST,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-803LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ACID),
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 7, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(14, MOVE_FELL_STINGER),
    LEVEL_UP_MOVE(21, MOVE_CHARM),
    LEVEL_UP_MOVE(28, MOVE_VENOSHOCK),
    LEVEL_UP_MOVE(35, MOVE_VENOM_DRENCH),
    LEVEL_UP_MOVE(42, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(49, MOVE_POISON_JAB),
    LEVEL_UP_MOVE(56, MOVE_GASTRO_ACID),
    LEVEL_UP_MOVE(63, MOVE_TOXIC),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 420
// Types: TYPE_POISON / TYPE_POISON
// Abilities: ABILITY_BEAST-BOOST, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
// Generation: 8

