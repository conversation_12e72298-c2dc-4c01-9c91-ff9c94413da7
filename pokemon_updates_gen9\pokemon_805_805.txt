// POKEMON_805 (#805) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_805] =
    {
        .baseHP = 61,
        .baseAttack = 131,
        .baseDefense = 211,
        .baseSpAttack = 53,
        .baseSpDefense = 101,
        .baseSpeed = 13,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_STEEL,
        .catchRate = 30,
        .expYield = 192,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BEAST-BOOST,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-805LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE(10, MOVE_PROTECT),
    LEVEL_UP_MOVE(15, MOVE_STOMP),
    LEVEL_UP_MOVE(20, MOVE_BLOCK),
    LEVEL_UP_MOVE(25, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(30, MOVE_WIDE_GUARD),
    LEVEL_UP_MOVE(35, MOVE_AUTOTOMIZE),
    LEVEL_UP_MOVE(40, MOVE_ROCK_BLAST),
    LEVEL_UP_MOVE(45, MOVE_MAGNET_RISE),
    LEVEL_UP_MOVE(50, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(55, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(60, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(65, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(70, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_ROCK / TYPE_STEEL
// Abilities: ABILITY_BEAST-BOOST, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
// Generation: 8

