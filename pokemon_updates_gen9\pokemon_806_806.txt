// POKEMON_806 (#806) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_806] =
    {
        .baseHP = 53,
        .baseAttack = 127,
        .baseDefense = 53,
        .baseSpAttack = 151,
        .baseSpDefense = 79,
        .baseSpeed = 107,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_GHOST,
        .catchRate = 30,
        .expYield = 180,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BEAST-BOOST,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-806LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE( 5, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE(10, MOVE_EMBER),
    LEVEL_UP_MOVE(15, MOVE_NIGHT_SHADE),
    LEVEL_UP_MOVE(20, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(25, MOVE_MAGIC_COAT),
    LEVEL_UP_MOVE(30, MOVE_INCINERATE),
    LEVEL_UP_MOVE(35, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE(40, MOVE_MYSTICAL_FIRE),
    LEVEL_UP_MOVE(45, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(50, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(55, MOVE_WILL_O_WISP),
    LEVEL_UP_MOVE(60, MOVE_TRICK),
    LEVEL_UP_MOVE(65, MOVE_FIRE_BLAST),
    LEVEL_UP_MOVE(70, MOVE_MIND_BLOWN),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_FIRE / TYPE_GHOST
// Abilities: ABILITY_BEAST-BOOST, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
// Generation: 8

