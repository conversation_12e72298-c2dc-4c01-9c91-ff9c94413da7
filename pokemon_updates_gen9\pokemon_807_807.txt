// POKEMON_807 (#807) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_807] =
    {
        .baseHP = 88,
        .baseAttack = 112,
        .baseDefense = 75,
        .baseSpAttack = 102,
        .baseSpDefense = 80,
        .baseSpeed = 143,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 3,
        .expYield = 200,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_VOLT-ABSORB,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-807LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_FAKE_OUT),
    LEVEL_UP_MOVE( 1, MOVE_POWER_UP_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_SNARL),
    LEVEL_UP_MOVE( 1, MOVE_SPARK),
    LEVEL_UP_MOVE( 8, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(16, MOVE_QUICK_GUARD),
    LEVEL_UP_MOVE(24, MOVE_SLASH),
    LEVEL_UP_MOVE(32, MOVE_VOLT_SWITCH),
    LEVEL_UP_MOVE(40, MOVE_CHARGE),
    LEVEL_UP_MOVE(48, MOVE_THUNDER_PUNCH),
    LEVEL_UP_MOVE(56, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(64, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(72, MOVE_WILD_CHARGE),
    LEVEL_UP_MOVE(80, MOVE_AGILITY),
    LEVEL_UP_MOVE(88, MOVE_PLASMA_FISTS),
    LEVEL_UP_MOVE(96, MOVE_CLOSE_COMBAT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 600
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_VOLT-ABSORB, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 18
// Generation: 8

