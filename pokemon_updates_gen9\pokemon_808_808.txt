// POKEMON_808 (#808) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_808] =
    {
        .baseHP = 46,
        .baseAttack = 65,
        .baseDefense = 65,
        .baseSpAttack = 55,
        .baseSpDefense = 35,
        .baseSpeed = 34,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_STEEL,
        .catchRate = 3,
        .expYield = 111,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_MAGNET-PULL,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-808LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 8, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE(16, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(24, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE(32, MOVE_ACID_ARMOR),
    LEVEL_UP_MOVE(40, MOVE_FLASH_CANNON),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 300
// Types: TYPE_STEEL / TYPE_STEEL
// Abilities: ABILITY_MAGNET-PULL, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 7
// Generation: 8

