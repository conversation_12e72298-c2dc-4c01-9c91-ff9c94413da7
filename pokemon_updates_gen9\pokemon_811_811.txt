// POKEMON_811 (#811) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_811] =
    {
        .baseHP = 70,
        .baseAttack = 85,
        .baseDefense = 70,
        .baseSpAttack = 55,
        .baseSpDefense = 60,
        .baseSpeed = 80,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 45,
        .expYield = 155,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_OVERGROW,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_GRASSY-SURGE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-811LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE( 1, MOVE_BRANCH_POKE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 1, MOVE_TAUNT),
    LEVEL_UP_MOVE(12, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(19, MOVE_SCREECH),
    LEVEL_UP_MOVE(24, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(30, MOVE_SLAM),
    LEVEL_UP_MOVE(36, MOVE_UPROAR),
    LEVEL_UP_MOVE(42, MOVE_WOOD_HAMMER),
    LEVEL_UP_MOVE(48, MOVE_ENDEAVOR),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 420
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_OVERGROW, ABILITY_NONE, ABILITY_GRASSY-SURGE
// Level Up Moves: 12
// Generation: 9

