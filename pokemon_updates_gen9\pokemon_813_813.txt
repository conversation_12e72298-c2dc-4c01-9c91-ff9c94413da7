// POKEMON_813 (#813) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_813] =
    {
        .baseHP = 50,
        .baseAttack = 71,
        .baseDefense = 40,
        .baseSpAttack = 40,
        .baseSpDefense = 40,
        .baseSpeed = 69,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 45,
        .expYield = 121,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BLAZE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_LIBERO,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-813LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 6, MOVE_EMBER),
    LEVEL_UP_MOVE( 8, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(12, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(17, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE(20, MOVE_AGILITY),
    LEVEL_UP_MOVE(24, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(28, MOVE_COUNTER),
    LEVEL_UP_MOVE(32, MOVE_BOUNCE),
    LEVEL_UP_MOVE(36, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 310
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_BLAZE, ABILITY_NONE, ABILITY_LIBERO
// Level Up Moves: 11
// Generation: 9

