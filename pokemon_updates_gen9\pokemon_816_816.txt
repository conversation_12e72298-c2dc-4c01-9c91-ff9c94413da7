// POKEMON_816 (#816) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_816] =
    {
        .baseHP = 50,
        .baseAttack = 40,
        .baseDefense = 40,
        .baseSpAttack = 70,
        .baseSpDefense = 40,
        .baseSpeed = 70,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 45,
        .expYield = 90,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_TORRENT,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_SNIPER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-816LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 6, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 8, MOVE_BIND),
    LEVEL_UP_MOVE(12, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(17, MOVE_TEARFUL_LOOK),
    LEVEL_UP_MOVE(20, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(24, MOVE_U_TURN),
    LEVEL_UP_MOVE(28, MOVE_LIQUIDATION),
    LEVEL_UP_MOVE(32, MOVE_SOAK),
    LEVEL_UP_MOVE(36, MOVE_RAIN_DANCE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 310
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_TORRENT, ABILITY_NONE, ABILITY_SNIPER
// Level Up Moves: 11
// Generation: 9

