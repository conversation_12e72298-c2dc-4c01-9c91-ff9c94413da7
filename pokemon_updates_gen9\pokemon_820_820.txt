// POKEMON_820 (#820) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_820] =
    {
        .baseHP = 120,
        .baseAttack = 95,
        .baseDefense = 95,
        .baseSpAttack = 55,
        .baseSpDefense = 75,
        .baseSpeed = 20,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 90,
        .expYield = 215,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_CHEEK-POUCH,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_GLUTTONY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-820LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_COVET),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_STUFF_CHEEKS),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE(15, MOVE_SPIT_UP),
    LEVEL_UP_MOVE(15, MOVE_STOCKPILE),
    LEVEL_UP_MOVE(15, MOVE_SWALLOW),
    LEVEL_UP_MOVE(20, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(27, MOVE_REST),
    LEVEL_UP_MOVE(34, MOVE_COUNTER),
    LEVEL_UP_MOVE(41, MOVE_BULLET_SEED),
    LEVEL_UP_MOVE(48, MOVE_SUPER_FANG),
    LEVEL_UP_MOVE(55, MOVE_BELCH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 460
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_CHEEK-POUCH, ABILITY_NONE, ABILITY_GLUTTONY
// Level Up Moves: 14
// Generation: 9

