// POKEMON_821 (#821) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_821] =
    {
        .baseHP = 38,
        .baseAttack = 47,
        .baseDefense = 35,
        .baseSpAttack = 33,
        .baseSpDefense = 35,
        .baseSpeed = 57,
        .type1 = TYPE_FLYING,
        .type2 = TYPE_FLYING,
        .catchRate = 255,
        .expYield = 85,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_KEEN-EYE,
        .ability2 = ABILITY_UNNERVE,
        .hiddenAbility = ABILITY_BIG-PECKS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-821LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 4, MOVE_POWER_TRIP),
    LEVEL_UP_MOVE( 8, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE(12, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(16, MOVE_PLUCK),
    LEVEL_UP_MOVE(20, MOVE_TAUNT),
    LEVEL_UP_MOVE(24, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(28, MOVE_DRILL_PECK),
    LEVEL_UP_MOVE(32, MOVE_SWAGGER),
    LEVEL_UP_MOVE(36, MOVE_BRAVE_BIRD),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 245
// Types: TYPE_FLYING / TYPE_FLYING
// Abilities: ABILITY_KEEN-EYE, ABILITY_UNNERVE, ABILITY_BIG-PECKS
// Level Up Moves: 11
// Generation: 9

