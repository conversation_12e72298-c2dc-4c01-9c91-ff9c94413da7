// POKEMON_824 (#824) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_824] =
    {
        .baseHP = 25,
        .baseAttack = 20,
        .baseDefense = 20,
        .baseSpAttack = 25,
        .baseSpDefense = 45,
        .baseSpeed = 45,
        .type1 = TYPE_BUG,
        .type2 = TYPE_BUG,
        .catchRate = 255,
        .expYield = 45,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SWARM,
        .ability2 = ABILITY_COMPOUND-EYES,
        .hiddenAbility = ABILITY_TELEPATHY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-824LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_STRUGGLE_BUG),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 180
// Types: TYPE_BUG / TYPE_BUG
// Abilities: ABILITY_SWARM, ABILITY_COMPOUND-EYES, ABILITY_TELEPATHY
// Level Up Moves: 1
// Generation: 8

