// POKEMON_826 (#826) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_826] =
    {
        .baseHP = 60,
        .baseAttack = 45,
        .baseDefense = 110,
        .baseSpAttack = 80,
        .baseSpDefense = 120,
        .baseSpeed = 90,
        .type1 = TYPE_BUG,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 45,
        .expYield = 105,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SWARM,
        .ability2 = ABILITY_FRISK,
        .hiddenAbility = ABILITY_TELEPATHY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-826LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_LIGHT_SCREEN),
    LEVEL_UP_MOVE( 1, MOVE_REFLECT),
    LEVEL_UP_MOVE( 1, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE( 4, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE( 8, MOVE_MAGIC_COAT),
    LEVEL_UP_MOVE(12, MOVE_AGILITY),
    LEVEL_UP_MOVE(16, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(20, MOVE_HYPNOSIS),
    LEVEL_UP_MOVE(24, MOVE_ALLY_SWITCH),
    LEVEL_UP_MOVE(28, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(32, MOVE_MIRROR_COAT),
    LEVEL_UP_MOVE(36, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(40, MOVE_AFTER_YOU),
    LEVEL_UP_MOVE(44, MOVE_CALM_MIND),
    LEVEL_UP_MOVE(48, MOVE_PSYCHIC_TERRAIN),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 505
// Types: TYPE_BUG / TYPE_PSYCHIC
// Abilities: ABILITY_SWARM, ABILITY_FRISK, ABILITY_TELEPATHY
// Level Up Moves: 16
// Generation: 8

