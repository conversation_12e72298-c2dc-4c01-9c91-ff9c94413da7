// POKEMON_831 (#831) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_831] =
    {
        .baseHP = 42,
        .baseAttack = 40,
        .baseDefense = 55,
        .baseSpAttack = 40,
        .baseSpDefense = 45,
        .baseSpeed = 48,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 255,
        .expYield = 82,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_FLUFFY,
        .ability2 = ABILITY_RUN-AWAY,
        .hiddenAbility = ABILITY_BULLETPROOF,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-831LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 4, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 8, MOVE_COPYCAT),
    LEVEL_UP_MOVE(12, MOVE_GUARD_SPLIT),
    LEVEL_UP_MOVE(16, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(21, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(25, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(28, MOVE_GUARD_SWAP),
    LEVEL_UP_MOVE(32, MOVE_REVERSAL),
    LEVEL_UP_MOVE(36, MOVE_COTTON_GUARD),
    LEVEL_UP_MOVE(40, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 270
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_FLUFFY, ABILITY_RUN-AWAY, ABILITY_BULLETPROOF
// Level Up Moves: 12
// Generation: 8

