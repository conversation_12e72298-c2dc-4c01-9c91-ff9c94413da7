// POKEMON_833 (#833) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_833] =
    {
        .baseHP = 50,
        .baseAttack = 64,
        .baseDefense = 50,
        .baseSpAttack = 38,
        .baseSpDefense = 38,
        .baseSpeed = 44,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 255,
        .expYield = 114,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STRONG-JAW,
        .ability2 = ABILITY_SHELL-ARMOR,
        .hiddenAbility = ABILITY_SWIFT-SWIM,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-833LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 7, MOVE_BITE),
    LEVEL_UP_MOVE(14, MOVE_PROTECT),
    LEVEL_UP_MOVE(21, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(28, MOVE_COUNTER),
    LEVEL_UP_MOVE(35, MOVE_JAW_LOCK),
    LEVEL_UP_MOVE(42, MOVE_LIQUIDATION),
    LEVEL_UP_MOVE(49, MOVE_BODY_SLAM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 284
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_STRONG-JAW, ABILITY_SHELL-ARMOR, ABILITY_SWIFT-SWIM
// Level Up Moves: 9
// Generation: 9

