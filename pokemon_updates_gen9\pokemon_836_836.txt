// POKEMON_836 (#836) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_836] =
    {
        .baseHP = 69,
        .baseAttack = 90,
        .baseDefense = 60,
        .baseSpAttack = 90,
        .baseSpDefense = 60,
        .baseSpeed = 121,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 45,
        .expYield = 159,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STRONG-JAW,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_COMPETITIVE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-836LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_ELECTRIFY),
    LEVEL_UP_MOVE( 1, MOVE_NUZZLE),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE(15, MOVE_ROAR),
    LEVEL_UP_MOVE(20, MOVE_SPARK),
    LEVEL_UP_MOVE(28, MOVE_CHARM),
    LEVEL_UP_MOVE(34, MOVE_CRUNCH),
    LEVEL_UP_MOVE(41, MOVE_CHARGE),
    LEVEL_UP_MOVE(48, MOVE_WILD_CHARGE),
    LEVEL_UP_MOVE(55, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(62, MOVE_ELECTRIC_TERRAIN),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 490
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_STRONG-JAW, ABILITY_NONE, ABILITY_COMPETITIVE
// Level Up Moves: 13
// Generation: 8

