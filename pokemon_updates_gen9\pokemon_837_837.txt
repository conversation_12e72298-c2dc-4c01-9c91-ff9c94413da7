// POKEMON_837 (#837) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_837] =
    {
        .baseHP = 30,
        .baseAttack = 40,
        .baseDefense = 50,
        .baseSpAttack = 40,
        .baseSpDefense = 50,
        .baseSpeed = 30,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_ROCK,
        .catchRate = 255,
        .expYield = 70,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STEAM-ENGINE,
        .ability2 = ABILITY_HEATPROOF,
        .hiddenAbility = ABILITY_FLASH-FIRE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-837LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_SMOKESCREEN),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE(10, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE(15, MOVE_ROCK_POLISH),
    LEVEL_UP_MOVE(20, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(25, MOVE_INCINERATE),
    LEVEL_UP_MOVE(30, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(35, MOVE_HEAT_CRASH),
    LEVEL_UP_MOVE(40, MOVE_ROCK_BLAST),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 240
// Types: TYPE_ROCK / TYPE_ROCK
// Abilities: ABILITY_STEAM-ENGINE, ABILITY_HEATPROOF, ABILITY_FLASH-FIRE
// Level Up Moves: 10
// Generation: 9

