// POKEMON_839 (#839) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_839] =
    {
        .baseHP = 110,
        .baseAttack = 80,
        .baseDefense = 120,
        .baseSpAttack = 80,
        .baseSpDefense = 90,
        .baseSpeed = 30,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_FIRE,
        .catchRate = 45,
        .expYield = 190,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STEAM-ENGINE,
        .ability2 = ABILITY_FLAME-BODY,
        .hiddenAbility = ABILITY_FLASH-FIRE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-839LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_TAR_SHOT),
    LEVEL_UP_MOVE( 1, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE( 1, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE( 1, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE( 1, MOVE_SMOKESCREEN),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE(15, MOVE_ROCK_POLISH),
    LEVEL_UP_MOVE(20, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(27, MOVE_INCINERATE),
    LEVEL_UP_MOVE(37, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(45, MOVE_HEAT_CRASH),
    LEVEL_UP_MOVE(54, MOVE_ROCK_BLAST),
    LEVEL_UP_MOVE(63, MOVE_STONE_EDGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 510
// Types: TYPE_ROCK / TYPE_FIRE
// Abilities: ABILITY_STEAM-ENGINE, ABILITY_FLAME-BODY, ABILITY_FLASH-FIRE
// Level Up Moves: 13
// Generation: 9

