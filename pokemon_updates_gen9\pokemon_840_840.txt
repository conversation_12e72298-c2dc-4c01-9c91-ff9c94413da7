// POKEMON_840 (#840) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_840] =
    {
        .baseHP = 40,
        .baseAttack = 40,
        .baseDefense = 80,
        .baseSpAttack = 40,
        .baseSpDefense = 40,
        .baseSpeed = 20,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_DRAGON,
        .catchRate = 255,
        .expYield = 80,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_RIPEN,
        .ability2 = ABILITY_GLUTTONY,
        .hiddenAbility = ABILITY_BULLETPROOF,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-840LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_WITHDRAW),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 260
// Types: TYPE_GRASS / TYPE_DRAGON
// Abilities: ABILITY_RIPEN, ABILITY_GLUTTONY, ABILITY_BULLETPROOF
// Level Up Moves: 2
// Generation: 9

