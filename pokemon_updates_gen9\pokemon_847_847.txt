// POKEMON_847 (#847) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_847] =
    {
        .baseHP = 61,
        .baseAttack = 123,
        .baseDefense = 60,
        .baseSpAttack = 60,
        .baseSpDefense = 50,
        .baseSpeed = 136,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 60,
        .expYield = 184,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SWIFT-SWIM,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_PROPELLER-TAIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-847LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_AQUA_JET),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_THROAT_CHOP),
    LEVEL_UP_MOVE(18, MOVE_AGILITY),
    LEVEL_UP_MOVE(24, MOVE_DIVE),
    LEVEL_UP_MOVE(32, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(40, MOVE_CRUNCH),
    LEVEL_UP_MOVE(48, MOVE_LIQUIDATION),
    LEVEL_UP_MOVE(56, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 490
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_SWIFT-SWIM, ABILITY_NONE, ABILITY_PROPELLER-TAIL
// Level Up Moves: 11
// Generation: 9

