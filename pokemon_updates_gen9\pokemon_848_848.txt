// POKEMON_848 (#848) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_848] =
    {
        .baseHP = 40,
        .baseAttack = 38,
        .baseDefense = 35,
        .baseSpAttack = 54,
        .baseSpDefense = 35,
        .baseSpeed = 40,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_POISON,
        .catchRate = 75,
        .expYield = 78,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_RATTLED,
        .ability2 = ABILITY_STATIC,
        .hiddenAbility = ABILITY_KLUTZ,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-848LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ACID),
    LEVEL_UP_MOVE( 1, MOVE_BELCH),
    LEVEL_UP_MOVE( 1, MOVE_FLAIL),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_NUZZLE),
    LEVEL_UP_MOVE( 1, MOVE_TEARFUL_LOOK),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 242
// Types: TYPE_ELECTRIC / TYPE_POISON
// Abilities: ABILITY_RATTLED, ABILITY_STATIC, ABILITY_KLUTZ
// Level Up Moves: 6
// Generation: 9

