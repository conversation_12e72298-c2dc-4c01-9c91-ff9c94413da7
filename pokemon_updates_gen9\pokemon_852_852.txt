// POKEMON_852 (#852) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_852] =
    {
        .baseHP = 50,
        .baseAttack = 68,
        .baseDefense = 60,
        .baseSpAttack = 50,
        .baseSpDefense = 50,
        .baseSpeed = 32,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 180,
        .expYield = 118,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_LIMBER,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_TECHNICIAN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-852LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE( 5, MOVE_FEINT),
    LEVEL_UP_MOVE(10, MOVE_BIND),
    LEVEL_UP_MOVE(15, MOVE_DETECT),
    LEVEL_UP_MOVE(20, MOVE_BRICK_BREAK),
    LEVEL_UP_MOVE(25, MOVE_BULK_UP),
    LEVEL_UP_MOVE(30, MOVE_SUBMISSION),
    LEVEL_UP_MOVE(35, MOVE_TAUNT),
    LEVEL_UP_MOVE(40, MOVE_REVERSAL),
    LEVEL_UP_MOVE(45, MOVE_SUPERPOWER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 310
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_LIMBER, ABILITY_NONE, ABILITY_TECHNICIAN
// Level Up Moves: 11
// Generation: 8

