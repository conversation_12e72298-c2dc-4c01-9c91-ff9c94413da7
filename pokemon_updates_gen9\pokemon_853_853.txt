// POKEMON_853 (#853) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_853] =
    {
        .baseHP = 80,
        .baseAttack = 118,
        .baseDefense = 90,
        .baseSpAttack = 70,
        .baseSpDefense = 80,
        .baseSpeed = 42,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 45,
        .expYield = 198,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_LIMBER,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_TECHNICIAN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-853LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_OCTOLOCK),
    LEVEL_UP_MOVE( 1, MOVE_BIND),
    LEVEL_UP_MOVE( 1, MOVE_FEINT),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_OCTAZOOKA),
    LEVEL_UP_MOVE( 1, MOVE_OCTOLOCK),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE(15, MOVE_DETECT),
    LEVEL_UP_MOVE(20, MOVE_BRICK_BREAK),
    LEVEL_UP_MOVE(25, MOVE_BULK_UP),
    LEVEL_UP_MOVE(30, MOVE_SUBMISSION),
    LEVEL_UP_MOVE(35, MOVE_TAUNT),
    LEVEL_UP_MOVE(40, MOVE_REVERSAL),
    LEVEL_UP_MOVE(45, MOVE_SUPERPOWER),
    LEVEL_UP_MOVE(50, MOVE_TOPSY_TURVY),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 480
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_LIMBER, ABILITY_NONE, ABILITY_TECHNICIAN
// Level Up Moves: 15
// Generation: 8

