// POKEMON_855 (#855) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_855] =
    {
        .baseHP = 60,
        .baseAttack = 65,
        .baseDefense = 65,
        .baseSpAttack = 134,
        .baseSpDefense = 114,
        .baseSpeed = 70,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_GHOST,
        .catchRate = 60,
        .expYield = 125,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_WEAK-ARMOR,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_CURSED-BODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-855LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_TEATIME),
    LEVEL_UP_MOVE( 1, MOVE_AROMATIC_MIST),
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE( 1, MOVE_STRENGTH_SAP),
    LEVEL_UP_MOVE( 1, MOVE_WITHDRAW),
    LEVEL_UP_MOVE(18, MOVE_PROTECT),
    LEVEL_UP_MOVE(24, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(30, MOVE_SWEET_SCENT),
    LEVEL_UP_MOVE(36, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(42, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(48, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(54, MOVE_MEMENTO),
    LEVEL_UP_MOVE(60, MOVE_SHELL_SMASH),
    LEVEL_UP_MOVE(66, MOVE_CURSE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 508
// Types: TYPE_GHOST / TYPE_GHOST
// Abilities: ABILITY_WEAK-ARMOR, ABILITY_NONE, ABILITY_CURSED-BODY
// Level Up Moves: 15
// Generation: 9

