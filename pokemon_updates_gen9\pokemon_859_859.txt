// POKEMON_859 (#859) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_859] =
    {
        .baseHP = 45,
        .baseAttack = 45,
        .baseDefense = 30,
        .baseSpAttack = 55,
        .baseSpDefense = 40,
        .baseSpeed = 50,
        .type1 = TYPE_DARK,
        .type2 = TYPE_FAIRY,
        .catchRate = 255,
        .expYield = 90,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(0.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PRANKSTER,
        .ability2 = ABILITY_FRISK,
        .hiddenAbility = ABILITY_PICKPOCKET,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-859LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFIDE),
    LEVEL_UP_MOVE( 1, MOVE_FAKE_OUT),
    LEVEL_UP_MOVE( 4, MOVE_BITE),
    LEVEL_UP_MOVE( 8, MOVE_FLATTER),
    LEVEL_UP_MOVE(12, MOVE_FAKE_TEARS),
    LEVEL_UP_MOVE(16, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(20, MOVE_SWAGGER),
    LEVEL_UP_MOVE(24, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(28, MOVE_TORMENT),
    LEVEL_UP_MOVE(33, MOVE_DARK_PULSE),
    LEVEL_UP_MOVE(36, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(40, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(44, MOVE_FOUL_PLAY),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 265
// Types: TYPE_DARK / TYPE_FAIRY
// Abilities: ABILITY_PRANKSTER, ABILITY_FRISK, ABILITY_PICKPOCKET
// Level Up Moves: 13
// Generation: 9

