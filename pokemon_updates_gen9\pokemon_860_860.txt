// POKEMON_860 (#860) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_860] =
    {
        .baseHP = 65,
        .baseAttack = 60,
        .baseDefense = 45,
        .baseSpAttack = 75,
        .baseSpDefense = 55,
        .baseSpeed = 70,
        .type1 = TYPE_DARK,
        .type2 = TYPE_FAIRY,
        .catchRate = 120,
        .expYield = 125,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(0.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PRANKSTER,
        .ability2 = ABILITY_FRISK,
        .hiddenAbility = ABILITY_PICKPOCKET,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-860LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_FALSE_SURRENDER),
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_CONFIDE),
    LEVEL_UP_MOVE( 1, MOVE_FAKE_OUT),
    LEVEL_UP_MOVE( 1, MOVE_FLATTER),
    LEVEL_UP_MOVE(12, MOVE_FAKE_TEARS),
    LEVEL_UP_MOVE(16, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(20, MOVE_SWAGGER),
    LEVEL_UP_MOVE(24, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(28, MOVE_TORMENT),
    LEVEL_UP_MOVE(35, MOVE_DARK_PULSE),
    LEVEL_UP_MOVE(40, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(46, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(52, MOVE_FOUL_PLAY),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 370
// Types: TYPE_DARK / TYPE_FAIRY
// Abilities: ABILITY_PRANKSTER, ABILITY_FRISK, ABILITY_PICKPOCKET
// Level Up Moves: 14
// Generation: 9

