// POKEMON_877 (#877) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_877] =
    {
        .baseHP = 58,
        .baseAttack = 95,
        .baseDefense = 58,
        .baseSpAttack = 70,
        .baseSpDefense = 58,
        .baseSpeed = 97,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_DARK,
        .catchRate = 180,
        .expYield = 153,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 10,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_HUNGER-SWITCH,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-877LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 5, MOVE_LEER),
    LEVEL_UP_MOVE(10, MOVE_POWER_TRIP),
    LEVEL_UP_MOVE(15, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(20, MOVE_FLATTER),
    LEVEL_UP_MOVE(25, MOVE_BITE),
    LEVEL_UP_MOVE(30, MOVE_SPARK),
    LEVEL_UP_MOVE(35, MOVE_TORMENT),
    LEVEL_UP_MOVE(40, MOVE_AGILITY),
    LEVEL_UP_MOVE(45, MOVE_BULLET_SEED),
    LEVEL_UP_MOVE(50, MOVE_CRUNCH),
    LEVEL_UP_MOVE(55, MOVE_AURA_WHEEL),
    LEVEL_UP_MOVE(60, MOVE_THRASH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 436
// Types: TYPE_ELECTRIC / TYPE_DARK
// Abilities: ABILITY_HUNGER-SWITCH, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
// Generation: 9

