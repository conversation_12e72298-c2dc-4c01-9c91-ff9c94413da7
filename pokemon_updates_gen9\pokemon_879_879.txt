// POKEMON_879 (#879) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_879] =
    {
        .baseHP = 122,
        .baseAttack = 130,
        .baseDefense = 69,
        .baseSpAttack = 80,
        .baseSpDefense = 69,
        .baseSpeed = 30,
        .type1 = TYPE_STEEL,
        .type2 = TYPE_STEEL,
        .catchRate = 90,
        .expYield = 252,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SHEER-FORCE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_HEAVY-METAL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-879LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE( 1, MOVE_ROLLOUT),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE(15, MOVE_BULLDOZE),
    LEVEL_UP_MOVE(20, MOVE_STOMP),
    LEVEL_UP_MOVE(25, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(30, MOVE_DIG),
    LEVEL_UP_MOVE(37, MOVE_STRENGTH),
    LEVEL_UP_MOVE(44, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(51, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(58, MOVE_HIGH_HORSEPOWER),
    LEVEL_UP_MOVE(65, MOVE_SUPERPOWER),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_STEEL / TYPE_STEEL
// Abilities: ABILITY_SHEER-FORCE, ABILITY_NONE, ABILITY_HEAVY-METAL
// Level Up Moves: 14
// Generation: 9

