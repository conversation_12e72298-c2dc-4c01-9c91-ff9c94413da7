// POKEMON_882 (#882) - GENERATION 8 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_882] =
    {
        .baseHP = 90,
        .baseAttack = 90,
        .baseDefense = 100,
        .baseSpAttack = 70,
        .baseSpDefense = 80,
        .baseSpeed = 75,
        .type1 = TYPE_WATER,
        .type2 = TYPE_DRAGON,
        .catchRate = 45,
        .expYield = 180,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 35,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_WATER-ABSORB,
        .ability2 = ABILITY_STRONG-JAW,
        .hiddenAbility = ABILITY_SAND-RUSH,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-882LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 7, MOVE_PROTECT),
    LEVEL_UP_MOVE(14, MOVE_BRUTAL_SWING),
    LEVEL_UP_MOVE(21, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(28, MOVE_BITE),
    LEVEL_UP_MOVE(35, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(42, MOVE_STOMP),
    LEVEL_UP_MOVE(49, MOVE_SUPER_FANG),
    LEVEL_UP_MOVE(56, MOVE_CRUNCH),
    LEVEL_UP_MOVE(63, MOVE_FISHIOUS_REND),
    LEVEL_UP_MOVE(70, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(77, MOVE_DRAGON_RUSH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 505
// Types: TYPE_WATER / TYPE_DRAGON
// Abilities: ABILITY_WATER-ABSORB, ABILITY_STRONG-JAW, ABILITY_SAND-RUSH
// Level Up Moves: 13
// Generation: 8

