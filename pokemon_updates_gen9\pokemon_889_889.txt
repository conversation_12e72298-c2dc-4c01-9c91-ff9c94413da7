// POKEMON_889 (#889) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_889] =
    {
        .baseHP = 92,
        .baseAttack = 120,
        .baseDefense = 115,
        .baseSpAttack = 80,
        .baseSpDefense = 115,
        .baseSpeed = 138,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 10,
        .expYield = 212,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_DAUNTLESS-SHIELD,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-889LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BITE),
    LEVEL_UP_MOVE( 1, MOVE_HOWL),
    LEVEL_UP_MOVE( 1, MOVE_METAL_CLAW),
    LEVEL_UP_MOVE( 1, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 1, MOVE_WIDE_GUARD),
    LEVEL_UP_MOVE(11, MOVE_SLASH),
    LEVEL_UP_MOVE(22, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(33, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(44, MOVE_METAL_BURST),
    LEVEL_UP_MOVE(55, MOVE_CRUNCH),
    LEVEL_UP_MOVE(66, MOVE_MOONBLAST),
    LEVEL_UP_MOVE(77, MOVE_CLOSE_COMBAT),
    LEVEL_UP_MOVE(88, MOVE_GIGA_IMPACT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 660
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_DAUNTLESS-SHIELD, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 13
// Generation: 9

