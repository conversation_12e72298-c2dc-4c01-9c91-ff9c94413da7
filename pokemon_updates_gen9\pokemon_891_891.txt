// POKEMON_891 (#891) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_891] =
    {
        .baseHP = 60,
        .baseAttack = 90,
        .baseDefense = 60,
        .baseSpAttack = 53,
        .baseSpDefense = 50,
        .baseSpeed = 72,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_FIGHTING,
        .catchRate = 3,
        .expYield = 150,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 120,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_INNER-FOCUS,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-891LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE( 4, MOVE_ENDURE),
    LEVEL_UP_MOVE( 8, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(12, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(16, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(20, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(24, MOVE_BRICK_BREAK),
    LEVEL_UP_MOVE(28, MOVE_DETECT),
    LEVEL_UP_MOVE(32, MOVE_BULK_UP),
    LEVEL_UP_MOVE(36, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(40, MOVE_DYNAMIC_PUNCH),
    LEVEL_UP_MOVE(44, MOVE_COUNTER),
    LEVEL_UP_MOVE(48, MOVE_CLOSE_COMBAT),
    LEVEL_UP_MOVE(52, MOVE_FOCUS_PUNCH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 385
// Types: TYPE_FIGHTING / TYPE_FIGHTING
// Abilities: ABILITY_INNER-FOCUS, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
// Generation: 9

