// POKEMON_892 (#892) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_892] =
    {
        .baseHP = 100,
        .baseAttack = 130,
        .baseDefense = 100,
        .baseSpAttack = 63,
        .baseSpDefense = 60,
        .baseSpeed = 97,
        .type1 = TYPE_FIGHTING,
        .type2 = TYPE_DARK,
        .catchRate = 3,
        .expYield = 230,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 120,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_UNSEEN-FIST,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-892LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_WICKED_BLOW),
    LEVEL_UP_MOVE( 1, MOVE_ENDURE),
    LEVEL_UP_MOVE( 1, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_SMASH),
    LEVEL_UP_MOVE( 1, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(12, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(16, MOVE_SCARY_FACE),
    LEVEL_UP_MOVE(20, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(24, MOVE_BRICK_BREAK),
    LEVEL_UP_MOVE(28, MOVE_DETECT),
    LEVEL_UP_MOVE(32, MOVE_BULK_UP),
    LEVEL_UP_MOVE(36, MOVE_IRON_HEAD),
    LEVEL_UP_MOVE(40, MOVE_DYNAMIC_PUNCH),
    LEVEL_UP_MOVE(44, MOVE_COUNTER),
    LEVEL_UP_MOVE(48, MOVE_CLOSE_COMBAT),
    LEVEL_UP_MOVE(52, MOVE_FOCUS_PUNCH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 550
// Types: TYPE_FIGHTING / TYPE_DARK
// Abilities: ABILITY_UNSEEN-FIST, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 17
// Generation: 9

