// POKEMON_894 (#894) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_894] =
    {
        .baseHP = 80,
        .baseAttack = 100,
        .baseDefense = 50,
        .baseSpAttack = 100,
        .baseSpDefense = 50,
        .baseSpeed = 200,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 3,
        .expYield = 180,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_TRANSISTOR,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-894LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE( 1, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 6, MOVE_ELECTROWEB),
    LEVEL_UP_MOVE(12, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(18, MOVE_SHOCK_WAVE),
    LEVEL_UP_MOVE(24, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE(30, MOVE_EXTREME_SPEED),
    LEVEL_UP_MOVE(36, MOVE_THUNDER_CAGE),
    LEVEL_UP_MOVE(42, MOVE_THUNDERBOLT),
    LEVEL_UP_MOVE(48, MOVE_MAGNET_RISE),
    LEVEL_UP_MOVE(54, MOVE_THRASH),
    LEVEL_UP_MOVE(60, MOVE_LOCK_ON),
    LEVEL_UP_MOVE(66, MOVE_ZAP_CANNON),
    LEVEL_UP_MOVE(72, MOVE_HYPER_BEAM),
    LEVEL_UP_MOVE(78, MOVE_EXPLOSION),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 580
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_TRANSISTOR, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 15
// Generation: 9

