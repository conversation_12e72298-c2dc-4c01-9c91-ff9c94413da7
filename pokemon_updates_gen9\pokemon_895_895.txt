// POKEMON_895 (#895) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_895] =
    {
        .baseHP = 200,
        .baseAttack = 100,
        .baseDefense = 50,
        .baseSpAttack = 100,
        .baseSpDefense = 50,
        .baseSpeed = 80,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_DRAGON,
        .catchRate = 3,
        .expYield = 255,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_DRAGONS-MAW,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-895LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TWISTER),
    LEVEL_UP_MOVE( 6, MOVE_BITE),
    LEVEL_UP_MOVE(12, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(18, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(30, MOVE_CRUNCH),
    LEVEL_UP_MOVE(36, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(42, MOVE_HAMMER_ARM),
    LEVEL_UP_MOVE(48, MOVE_DRAGON_DANCE),
    LEVEL_UP_MOVE(54, MOVE_THRASH),
    LEVEL_UP_MOVE(60, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(66, MOVE_DRAGON_ENERGY),
    LEVEL_UP_MOVE(72, MOVE_HYPER_BEAM),
    LEVEL_UP_MOVE(78, MOVE_EXPLOSION),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 580
// Types: TYPE_DRAGON / TYPE_DRAGON
// Abilities: ABILITY_DRAGONS-MAW, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 13
// Generation: 9

