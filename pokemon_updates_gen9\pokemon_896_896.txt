// POKEMON_896 (#896) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_896] =
    {
        .baseHP = 100,
        .baseAttack = 145,
        .baseDefense = 130,
        .baseSpAttack = 65,
        .baseSpDefense = 110,
        .baseSpeed = 30,
        .type1 = TYPE_ICE,
        .type2 = TYPE_ICE,
        .catchRate = 3,
        .expYield = 245,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_CHILLING-NEIGH,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-896LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 6, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(12, MOVE_AVALANCHE),
    LEVEL_UP_MOVE(18, MOVE_STOMP),
    LEVEL_UP_MOVE(24, MOVE_TORMENT),
    LEVEL_UP_MOVE(30, MOVE_MIST),
    LEVEL_UP_MOVE(36, MOVE_ICICLE_CRASH),
    LEVEL_UP_MOVE(42, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(48, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(54, MOVE_THRASH),
    LEVEL_UP_MOVE(60, MOVE_TAUNT),
    LEVEL_UP_MOVE(66, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(72, MOVE_SWORDS_DANCE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 580
// Types: TYPE_ICE / TYPE_ICE
// Abilities: ABILITY_CHILLING-NEIGH, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
// Generation: 9

