// POKEMON_897 (#897) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_897] =
    {
        .baseHP = 100,
        .baseAttack = 65,
        .baseDefense = 60,
        .baseSpAttack = 145,
        .baseSpDefense = 80,
        .baseSpeed = 130,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_GHOST,
        .catchRate = 3,
        .expYield = 165,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 35,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_GRIM-NEIGH,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-897LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 1, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 6, MOVE_DOUBLE_KICK),
    LEVEL_UP_MOVE(12, MOVE_HEX),
    LEVEL_UP_MOVE(18, MOVE_STOMP),
    LEVEL_UP_MOVE(24, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE(30, MOVE_HAZE),
    LEVEL_UP_MOVE(36, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(42, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(48, MOVE_AGILITY),
    LEVEL_UP_MOVE(54, MOVE_THRASH),
    LEVEL_UP_MOVE(60, MOVE_DISABLE),
    LEVEL_UP_MOVE(66, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(72, MOVE_NASTY_PLOT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 580
// Types: TYPE_GHOST / TYPE_GHOST
// Abilities: ABILITY_GRIM-NEIGH, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
// Generation: 9

