// POKEMON_898 (#898) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_898] =
    {
        .baseHP = 100,
        .baseAttack = 80,
        .baseDefense = 80,
        .baseSpAttack = 80,
        .baseSpDefense = 80,
        .baseSpeed = 80,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_GRASS,
        .catchRate = 3,
        .expYield = 180,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 120,
        .friendship = 100,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_UNNERVE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-898LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 1, MOVE_GROWTH),
    LEVEL_UP_MOVE( 1, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 8, MOVE_LIFE_DEW),
    LEVEL_UP_MOVE(16, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(24, MOVE_PSYSHOCK),
    LEVEL_UP_MOVE(32, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(40, MOVE_GRASSY_TERRAIN),
    LEVEL_UP_MOVE(40, MOVE_PSYCHIC_TERRAIN),
    LEVEL_UP_MOVE(48, MOVE_ENERGY_BALL),
    LEVEL_UP_MOVE(56, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(64, MOVE_LEECH_SEED),
    LEVEL_UP_MOVE(72, MOVE_HEAL_PULSE),
    LEVEL_UP_MOVE(80, MOVE_SOLAR_BEAM),
    LEVEL_UP_MOVE(88, MOVE_FUTURE_SIGHT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 500
// Types: TYPE_PSYCHIC / TYPE_GRASS
// Abilities: ABILITY_UNNERVE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
// Generation: 9

