// POKEMON_904 (#904) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_904] =
    {
        .baseHP = 85,
        .baseAttack = 115,
        .baseDefense = 95,
        .baseSpAttack = 65,
        .baseSpDefense = 65,
        .baseSpeed = 85,
        .type1 = TYPE_DARK,
        .type2 = TYPE_POISON,
        .catchRate = 135,
        .expYield = 200,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_POISON-POINT,
        .ability2 = ABILITY_SWIFT-SWIM,
        .hiddenAbility = ABILITY_INTIMIDATE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-904LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POISON_STING),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 4, MOVE_HARDEN),
    LEVEL_UP_MOVE( 8, MOVE_BITE),
    LEVEL_UP_MOVE(12, MOVE_FELL_STINGER),
    LEVEL_UP_MOVE(16, MOVE_MINIMIZE),
    LEVEL_UP_MOVE(20, MOVE_SPIKES),
    LEVEL_UP_MOVE(24, MOVE_BRINE),
    LEVEL_UP_MOVE(28, MOVE_BARB_BARRAGE),
    LEVEL_UP_MOVE(32, MOVE_PIN_MISSILE),
    LEVEL_UP_MOVE(36, MOVE_TOXIC_SPIKES),
    LEVEL_UP_MOVE(40, MOVE_SPIT_UP),
    LEVEL_UP_MOVE(40, MOVE_STOCKPILE),
    LEVEL_UP_MOVE(44, MOVE_TOXIC),
    LEVEL_UP_MOVE(48, MOVE_CRUNCH),
    LEVEL_UP_MOVE(52, MOVE_ACUPRESSURE),
    LEVEL_UP_MOVE(56, MOVE_DESTINY_BOND),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 510
// Types: TYPE_DARK / TYPE_POISON
// Abilities: ABILITY_POISON-POINT, ABILITY_SWIFT-SWIM, ABILITY_INTIMIDATE
// Level Up Moves: 17
// Generation: 9

