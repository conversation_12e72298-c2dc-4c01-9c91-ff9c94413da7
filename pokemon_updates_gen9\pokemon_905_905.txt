// POKEMON_905 (#905) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_905] =
    {
        .baseHP = 74,
        .baseAttack = 115,
        .baseDefense = 70,
        .baseSpAttack = 135,
        .baseSpDefense = 80,
        .baseSpeed = 106,
        .type1 = TYPE_FAIRY,
        .type2 = TYPE_FLYING,
        .catchRate = 3,
        .expYield = 189,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(100.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_CUTE-CHARM,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_CONTRARY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-905LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_FAIRY_WIND),
    LEVEL_UP_MOVE( 5, MOVE_TORMENT),
    LEVEL_UP_MOVE(10, MOVE_FLATTER),
    LEVEL_UP_MOVE(15, MOVE_TWISTER),
    LEVEL_UP_MOVE(20, MOVE_DRAINING_KISS),
    LEVEL_UP_MOVE(25, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(30, MOVE_IMPRISON),
    LEVEL_UP_MOVE(35, MOVE_MYSTICAL_FIRE),
    LEVEL_UP_MOVE(40, MOVE_DAZZLING_GLEAM),
    LEVEL_UP_MOVE(45, MOVE_EXTRASENSORY),
    LEVEL_UP_MOVE(50, MOVE_UPROAR),
    LEVEL_UP_MOVE(55, MOVE_SUPERPOWER),
    LEVEL_UP_MOVE(60, MOVE_HEALING_WISH),
    LEVEL_UP_MOVE(65, MOVE_MOONBLAST),
    LEVEL_UP_MOVE(70, MOVE_OUTRAGE),
    LEVEL_UP_MOVE(75, MOVE_SPRINGTIDE_STORM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 580
// Types: TYPE_FAIRY / TYPE_FLYING
// Abilities: ABILITY_CUTE-CHARM, ABILITY_NONE, ABILITY_CONTRARY
// Level Up Moves: 17
// Generation: 9

