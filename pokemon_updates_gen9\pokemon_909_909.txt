// POKEMON_909 (#909) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_909] =
    {
        .baseHP = 67,
        .baseAttack = 45,
        .baseDefense = 59,
        .baseSpAttack = 63,
        .baseSpDefense = 40,
        .baseSpeed = 36,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 45,
        .expYield = 112,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BLAZE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_UNAWARE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-909LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 7, MOVE_ROUND),
    LEVEL_UP_MOVE(12, MOVE_BITE),
    LEVEL_UP_MOVE(15, MOVE_INCINERATE),
    LEVEL_UP_MOVE(17, MOVE_YAWN),
    LEVEL_UP_MOVE(21, MOVE_SNARL),
    LEVEL_UP_MOVE(25, MOVE_ROAR),
    LEVEL_UP_MOVE(28, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(32, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(36, MOVE_FIRE_BLAST),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 310
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_BLAZE, ABILITY_NONE, ABILITY_UNAWARE
// Level Up Moves: 12
// Generation: 9

