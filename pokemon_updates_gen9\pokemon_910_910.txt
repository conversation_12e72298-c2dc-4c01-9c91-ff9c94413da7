// POKEMON_910 (#910) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_910] =
    {
        .baseHP = 81,
        .baseAttack = 55,
        .baseDefense = 78,
        .baseSpAttack = 90,
        .baseSpDefense = 58,
        .baseSpeed = 49,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 45,
        .expYield = 136,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BLAZE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_UNAWARE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-910LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 7, MOVE_LICK),
    LEVEL_UP_MOVE(10, MOVE_ROUND),
    LEVEL_UP_MOVE(12, MOVE_BITE),
    LEVEL_UP_MOVE(15, MOVE_YAWN),
    LEVEL_UP_MOVE(17, MOVE_INCINERATE),
    LEVEL_UP_MOVE(24, MOVE_SNARL),
    LEVEL_UP_MOVE(28, MOVE_ROAR),
    LEVEL_UP_MOVE(32, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE(38, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(42, MOVE_WILL_O_WISP),
    LEVEL_UP_MOVE(47, MOVE_FIRE_BLAST),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 411
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_BLAZE, ABILITY_NONE, ABILITY_UNAWARE
// Level Up Moves: 14
// Generation: 9

