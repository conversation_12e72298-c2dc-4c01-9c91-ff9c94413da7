// POKEMON_913 (#913) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_913] =
    {
        .baseHP = 70,
        .baseAttack = 85,
        .baseDefense = 65,
        .baseSpAttack = 65,
        .baseSpDefense = 60,
        .baseSpeed = 65,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 45,
        .expYield = 155,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(12.5),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_TORRENT,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_MOXIE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-913LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 7, MOVE_WORK_UP),
    LEVEL_UP_MOVE(10, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE(13, MOVE_AQUA_JET),
    LEVEL_UP_MOVE(17, MOVE_WATER_PULSE),
    LEVEL_UP_MOVE(19, MOVE_LOW_SWEEP),
    LEVEL_UP_MOVE(23, MOVE_AQUA_CUTTER),
    LEVEL_UP_MOVE(27, MOVE_AIR_SLASH),
    LEVEL_UP_MOVE(32, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(38, MOVE_ACROBATICS),
    LEVEL_UP_MOVE(43, MOVE_LIQUIDATION),
    LEVEL_UP_MOVE(48, MOVE_FEATHER_DANCE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 410
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_TORRENT, ABILITY_NONE, ABILITY_MOXIE
// Level Up Moves: 15
// Generation: 9

