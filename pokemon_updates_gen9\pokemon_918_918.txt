// POKEMON_918 (#918) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_918] =
    {
        .baseHP = 60,
        .baseAttack = 79,
        .baseDefense = 92,
        .baseSpAttack = 52,
        .baseSpDefense = 86,
        .baseSpeed = 35,
        .type1 = TYPE_BUG,
        .type2 = TYPE_BUG,
        .catchRate = 120,
        .expYield = 139,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_INSOMNIA,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_STAKEOUT,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-918LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_SILK_TRAP),
    LEVEL_UP_MOVE( 1, MOVE_STRING_SHOT),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE( 8, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(11, MOVE_FEINT),
    LEVEL_UP_MOVE(14, MOVE_BUG_BITE),
    LEVEL_UP_MOVE(19, MOVE_BLOCK),
    LEVEL_UP_MOVE(24, MOVE_COUNTER),
    LEVEL_UP_MOVE(28, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(33, MOVE_STICKY_WEB),
    LEVEL_UP_MOVE(37, MOVE_GASTRO_ACID),
    LEVEL_UP_MOVE(41, MOVE_CIRCLE_THROW),
    LEVEL_UP_MOVE(45, MOVE_THROAT_CHOP),
    LEVEL_UP_MOVE(49, MOVE_SKITTER_SMACK),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 404
// Types: TYPE_BUG / TYPE_BUG
// Abilities: ABILITY_INSOMNIA, ABILITY_NONE, ABILITY_STAKEOUT
// Level Up Moves: 15
// Generation: 9

