// POKEMON_921 (#921) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_921] =
    {
        .baseHP = 45,
        .baseAttack = 50,
        .baseDefense = 20,
        .baseSpAttack = 40,
        .baseSpDefense = 25,
        .baseSpeed = 60,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 190,
        .expYield = 95,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_STATIC,
        .ability2 = ABILITY_NATURAL-CURE,
        .hiddenAbility = ABILITY_IRON-FIST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-921LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 3, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE( 6, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE( 8, MOVE_CHARGE),
    LEVEL_UP_MOVE(12, MOVE_NUZZLE),
    LEVEL_UP_MOVE(15, MOVE_DIG),
    LEVEL_UP_MOVE(19, MOVE_BITE),
    LEVEL_UP_MOVE(23, MOVE_SPARK),
    LEVEL_UP_MOVE(27, MOVE_THUNDER_WAVE),
    LEVEL_UP_MOVE(31, MOVE_ENTRAINMENT),
    LEVEL_UP_MOVE(35, MOVE_SLAM),
    LEVEL_UP_MOVE(38, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(40, MOVE_AGILITY),
    LEVEL_UP_MOVE(44, MOVE_WILD_CHARGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 240
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_STATIC, ABILITY_NATURAL-CURE, ABILITY_IRON-FIST
// Level Up Moves: 15
// Generation: 9

