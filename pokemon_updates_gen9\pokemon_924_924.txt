// POKEMON_924 (#924) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_924] =
    {
        .baseHP = 50,
        .baseAttack = 50,
        .baseDefense = 45,
        .baseSpAttack = 40,
        .baseSpDefense = 45,
        .baseSpeed = 75,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 150,
        .expYield = 100,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 10,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_RUN-AWAY,
        .ability2 = ABILITY_PICKUP,
        .hiddenAbility = ABILITY_OWN-TEMPO,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-924LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 5, MOVE_ECHOED_VOICE),
    LEVEL_UP_MOVE( 8, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(11, MOVE_SUPER_FANG),
    LEVEL_UP_MOVE(14, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE(18, MOVE_BULLET_SEED),
    LEVEL_UP_MOVE(22, MOVE_ENCORE),
    LEVEL_UP_MOVE(26, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(30, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(33, MOVE_CHARM),
    LEVEL_UP_MOVE(37, MOVE_BEAT_UP),
    LEVEL_UP_MOVE(41, MOVE_COPYCAT),
    LEVEL_UP_MOVE(46, MOVE_POPULATION_BOMB),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 305
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_RUN-AWAY, ABILITY_PICKUP, ABILITY_OWN-TEMPO
// Level Up Moves: 14
// Generation: 9

