// POKEMON_925 (#925) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_925] =
    {
        .baseHP = 74,
        .baseAttack = 75,
        .baseDefense = 70,
        .baseSpAttack = 65,
        .baseSpDefense = 75,
        .baseSpeed = 111,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 75,
        .expYield = 149,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 10,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_FRIEND-GUARD,
        .ability2 = ABILITY_CHEEK-POUCH,
        .hiddenAbility = ABILITY_TECHNICIAN,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-925LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE( 1, MOVE_FOLLOW_ME),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_TIDY_UP),
    LEVEL_UP_MOVE( 5, MOVE_ECHOED_VOICE),
    LEVEL_UP_MOVE( 8, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(11, MOVE_SUPER_FANG),
    LEVEL_UP_MOVE(14, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE(18, MOVE_BULLET_SEED),
    LEVEL_UP_MOVE(22, MOVE_ENCORE),
    LEVEL_UP_MOVE(29, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(33, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(37, MOVE_CHARM),
    LEVEL_UP_MOVE(41, MOVE_BEAT_UP),
    LEVEL_UP_MOVE(46, MOVE_COPYCAT),
    LEVEL_UP_MOVE(53, MOVE_POPULATION_BOMB),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 470
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_FRIEND-GUARD, ABILITY_CHEEK-POUCH, ABILITY_TECHNICIAN
// Level Up Moves: 16
// Generation: 9

