// POKEMON_927 (#927) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_927] =
    {
        .baseHP = 57,
        .baseAttack = 80,
        .baseDefense = 115,
        .baseSpAttack = 50,
        .baseSpDefense = 80,
        .baseSpeed = 95,
        .type1 = TYPE_FAIRY,
        .type2 = TYPE_FAIRY,
        .catchRate = 90,
        .expYield = 137,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_WELL-BAKED-BODY,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_AROMA-VEIL,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-927LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 3, MOVE_LICK),
    LEVEL_UP_MOVE( 6, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 8, MOVE_COVET),
    LEVEL_UP_MOVE(11, MOVE_BITE),
    LEVEL_UP_MOVE(15, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(18, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(22, MOVE_WORK_UP),
    LEVEL_UP_MOVE(29, MOVE_BATON_PASS),
    LEVEL_UP_MOVE(33, MOVE_ROAR),
    LEVEL_UP_MOVE(38, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(42, MOVE_CHARM),
    LEVEL_UP_MOVE(47, MOVE_CRUNCH),
    LEVEL_UP_MOVE(53, MOVE_LAST_RESORT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 477
// Types: TYPE_FAIRY / TYPE_FAIRY
// Abilities: ABILITY_WELL-BAKED-BODY, ABILITY_NONE, ABILITY_AROMA-VEIL
// Level Up Moves: 15
// Generation: 9

