// POKEMON_931 (#931) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_931] =
    {
        .baseHP = 82,
        .baseAttack = 96,
        .baseDefense = 51,
        .baseSpAttack = 45,
        .baseSpDefense = 51,
        .baseSpeed = 92,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_FLYING,
        .catchRate = 190,
        .expYield = 178,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 15,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_INTIMIDATE,
        .ability2 = ABILITY_HUSTLE,
        .hiddenAbility = ABILITY_GUTS,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-931LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_MIMIC),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 6, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(10, MOVE_TORMENT),
    LEVEL_UP_MOVE(13, MOVE_AERIAL_ACE),
    LEVEL_UP_MOVE(17, MOVE_FURY_ATTACK),
    LEVEL_UP_MOVE(20, MOVE_TAUNT),
    LEVEL_UP_MOVE(24, MOVE_UPROAR),
    LEVEL_UP_MOVE(27, MOVE_COPYCAT),
    LEVEL_UP_MOVE(30, MOVE_FLY),
    LEVEL_UP_MOVE(34, MOVE_FACADE),
    LEVEL_UP_MOVE(38, MOVE_SWAGGER),
    LEVEL_UP_MOVE(42, MOVE_BRAVE_BIRD),
    LEVEL_UP_MOVE(47, MOVE_ROOST),
    LEVEL_UP_MOVE(52, MOVE_REVERSAL),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 417
// Types: TYPE_NORMAL / TYPE_FLYING
// Abilities: ABILITY_INTIMIDATE, ABILITY_HUSTLE, ABILITY_GUTS
// Level Up Moves: 16
// Generation: 9

