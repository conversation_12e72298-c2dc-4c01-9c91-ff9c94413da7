// POKEMON_933 (#933) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_933] =
    {
        .baseHP = 60,
        .baseAttack = 60,
        .baseDefense = 100,
        .baseSpAttack = 35,
        .baseSpDefense = 65,
        .baseSpeed = 35,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_ROCK,
        .catchRate = 120,
        .expYield = 120,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PURIFYING-SALT,
        .ability2 = ABILITY_STURDY,
        .hiddenAbility = ABILITY_CLEAR-BODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-933LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_SALT_CURE),
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE( 7, MOVE_MUD_SHOT),
    LEVEL_UP_MOVE(10, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE(13, MOVE_ROCK_POLISH),
    LEVEL_UP_MOVE(16, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(20, MOVE_IRON_DEFENSE),
    LEVEL_UP_MOVE(30, MOVE_RECOVER),
    LEVEL_UP_MOVE(34, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(38, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(41, MOVE_HEAVY_SLAM),
    LEVEL_UP_MOVE(45, MOVE_EARTHQUAKE),
    LEVEL_UP_MOVE(51, MOVE_STONE_EDGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 355
// Types: TYPE_ROCK / TYPE_ROCK
// Abilities: ABILITY_PURIFYING-SALT, ABILITY_STURDY, ABILITY_CLEAR-BODY
// Level Up Moves: 15
// Generation: 9

