// POKEMON_935 (#935) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_935] =
    {
        .baseHP = 40,
        .baseAttack = 50,
        .baseDefense = 40,
        .baseSpAttack = 50,
        .baseSpDefense = 40,
        .baseSpeed = 35,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_FIRE,
        .catchRate = 90,
        .expYield = 90,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 35,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_FLASH-FIRE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_FLAME-BODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-935LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 8, MOVE_CLEAR_SMOG),
    LEVEL_UP_MOVE(12, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE(16, MOVE_WILL_O_WISP),
    LEVEL_UP_MOVE(20, MOVE_NIGHT_SHADE),
    LEVEL_UP_MOVE(24, MOVE_FLAME_CHARGE),
    LEVEL_UP_MOVE(28, MOVE_INCINERATE),
    LEVEL_UP_MOVE(32, MOVE_LAVA_PLUME),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 255
// Types: TYPE_FIRE / TYPE_FIRE
// Abilities: ABILITY_FLASH-FIRE, ABILITY_NONE, ABILITY_FLAME-BODY
// Level Up Moves: 10
// Generation: 9

