// POKEMON_938 (#938) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_938] =
    {
        .baseHP = 61,
        .baseAttack = 31,
        .baseDefense = 41,
        .baseSpAttack = 59,
        .baseSpDefense = 35,
        .baseSpeed = 45,
        .type1 = TYPE_ELECTRIC,
        .type2 = TYPE_ELECTRIC,
        .catchRate = 190,
        .expYield = 92,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_OWN-TEMPO,
        .ability2 = ABILITY_STATIC,
        .hiddenAbility = ABILITY_DAMP,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-938LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 7, MOVE_THUNDER_SHOCK),
    LEVEL_UP_MOVE(11, MOVE_WATER_GUN),
    LEVEL_UP_MOVE(17, MOVE_CHARGE),
    LEVEL_UP_MOVE(21, MOVE_SPARK),
    LEVEL_UP_MOVE(24, MOVE_MUD_SHOT),
    LEVEL_UP_MOVE(25, MOVE_FLAIL),
    LEVEL_UP_MOVE(32, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(36, MOVE_WEATHER_BALL),
    LEVEL_UP_MOVE(40, MOVE_ELECTRIC_TERRAIN),
    LEVEL_UP_MOVE(45, MOVE_SUCKER_PUNCH),
    LEVEL_UP_MOVE(50, MOVE_ZAP_CANNON),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 272
// Types: TYPE_ELECTRIC / TYPE_ELECTRIC
// Abilities: ABILITY_OWN-TEMPO, ABILITY_STATIC, ABILITY_DAMP
// Level Up Moves: 13
// Generation: 9

