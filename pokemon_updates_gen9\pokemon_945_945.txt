// POKEMON_945 (#945) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_945] =
    {
        .baseHP = 63,
        .baseAttack = 95,
        .baseDefense = 65,
        .baseSpAttack = 80,
        .baseSpDefense = 72,
        .baseSpeed = 110,
        .type1 = TYPE_POISON,
        .type2 = TYPE_NORMAL,
        .catchRate = 90,
        .expYield = 158,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_UNBURDEN,
        .ability2 = ABILITY_POISON-TOUCH,
        .hiddenAbility = ABILITY_PRANKSTER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-945LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_DOODLE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_SCRATCH),
    LEVEL_UP_MOVE( 5, MOVE_ACID_SPRAY),
    LEVEL_UP_MOVE( 8, MOVE_FURY_SWIPES),
    LEVEL_UP_MOVE(11, MOVE_SWITCHEROO),
    LEVEL_UP_MOVE(14, MOVE_POISON_FANG),
    LEVEL_UP_MOVE(18, MOVE_FLATTER),
    LEVEL_UP_MOVE(21, MOVE_SLASH),
    LEVEL_UP_MOVE(25, MOVE_U_TURN),
    LEVEL_UP_MOVE(33, MOVE_POISON_JAB),
    LEVEL_UP_MOVE(37, MOVE_TAUNT),
    LEVEL_UP_MOVE(40, MOVE_SUBSTITUTE),
    LEVEL_UP_MOVE(45, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(51, MOVE_GUNK_SHOT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 485
// Types: TYPE_POISON / TYPE_NORMAL
// Abilities: ABILITY_UNBURDEN, ABILITY_POISON-TOUCH, ABILITY_PRANKSTER
// Level Up Moves: 15
// Generation: 9

