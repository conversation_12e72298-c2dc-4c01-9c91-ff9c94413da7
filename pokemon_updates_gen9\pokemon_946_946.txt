// POKEMON_946 (#946) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_946] =
    {
        .baseHP = 40,
        .baseAttack = 65,
        .baseDefense = 30,
        .baseSpAttack = 45,
        .baseSpDefense = 35,
        .baseSpeed = 60,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GHOST,
        .catchRate = 190,
        .expYield = 105,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_WIND-RIDER,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_INFILTRATOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-946LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_ROLLOUT),
    LEVEL_UP_MOVE( 5, MOVE_ABSORB),
    LEVEL_UP_MOVE( 9, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE(13, MOVE_BULLET_SEED),
    LEVEL_UP_MOVE(17, MOVE_INFESTATION),
    LEVEL_UP_MOVE(21, MOVE_HEX),
    LEVEL_UP_MOVE(25, MOVE_MEGA_DRAIN),
    LEVEL_UP_MOVE(29, MOVE_DISABLE),
    LEVEL_UP_MOVE(35, MOVE_PHANTOM_FORCE),
    LEVEL_UP_MOVE(40, MOVE_GIGA_DRAIN),
    LEVEL_UP_MOVE(45, MOVE_CURSE),
    LEVEL_UP_MOVE(50, MOVE_PAIN_SPLIT),
    LEVEL_UP_MOVE(55, MOVE_POWER_WHIP),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 275
// Types: TYPE_GRASS / TYPE_GHOST
// Abilities: ABILITY_WIND-RIDER, ABILITY_NONE, ABILITY_INFILTRATOR
// Level Up Moves: 15
// Generation: 9

