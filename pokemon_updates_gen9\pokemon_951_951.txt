// POKEMON_951 (#951) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_951] =
    {
        .baseHP = 50,
        .baseAttack = 62,
        .baseDefense = 40,
        .baseSpAttack = 62,
        .baseSpDefense = 40,
        .baseSpeed = 50,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_GRASS,
        .catchRate = 190,
        .expYield = 112,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_CHLOROPHYLL,
        .ability2 = ABILITY_INSOMNIA,
        .hiddenAbility = ABILITY_KLUTZ,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-951LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEAFAGE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 4, MOVE_BITE),
    LEVEL_UP_MOVE(10, MOVE_GROWTH),
    LEVEL_UP_MOVE(13, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(17, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE(21, MOVE_BULLET_SEED),
    LEVEL_UP_MOVE(24, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(28, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(38, MOVE_CRUNCH),
    LEVEL_UP_MOVE(44, MOVE_SEED_BOMB),
    LEVEL_UP_MOVE(48, MOVE_SOLAR_BEAM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 304
// Types: TYPE_GRASS / TYPE_GRASS
// Abilities: ABILITY_CHLOROPHYLL, ABILITY_INSOMNIA, ABILITY_KLUTZ
// Level Up Moves: 12
// Generation: 9

