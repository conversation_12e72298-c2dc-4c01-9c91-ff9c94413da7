// POKEMON_952 (#952) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_952] =
    {
        .baseHP = 65,
        .baseAttack = 108,
        .baseDefense = 65,
        .baseSpAttack = 108,
        .baseSpDefense = 65,
        .baseSpeed = 75,
        .type1 = TYPE_GRASS,
        .type2 = TYPE_FIRE,
        .catchRate = 75,
        .expYield = 173,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_CHLOROPHYLL,
        .ability2 = ABILITY_INSOMNIA,
        .hiddenAbility = ABILITY_MOODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-952LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_FLAMETHROWER),
    LEVEL_UP_MOVE( 0, MOVE_SPICY_EXTRACT),
    LEVEL_UP_MOVE( 1, MOVE_FIRE_FANG),
    LEVEL_UP_MOVE( 1, MOVE_LEAFAGE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 4, MOVE_BITE),
    LEVEL_UP_MOVE(10, MOVE_GROWTH),
    LEVEL_UP_MOVE(13, MOVE_RAZOR_LEAF),
    LEVEL_UP_MOVE(17, MOVE_SUNNY_DAY),
    LEVEL_UP_MOVE(21, MOVE_BULLET_SEED),
    LEVEL_UP_MOVE(24, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(28, MOVE_ZEN_HEADBUTT),
    LEVEL_UP_MOVE(33, MOVE_WORRY_SEED),
    LEVEL_UP_MOVE(38, MOVE_CRUNCH),
    LEVEL_UP_MOVE(44, MOVE_SEED_BOMB),
    LEVEL_UP_MOVE(48, MOVE_OVERHEAT),
    LEVEL_UP_MOVE(48, MOVE_SOLAR_BEAM),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 486
// Types: TYPE_GRASS / TYPE_FIRE
// Abilities: ABILITY_CHLOROPHYLL, ABILITY_INSOMNIA, ABILITY_MOODY
// Level Up Moves: 17
// Generation: 9

