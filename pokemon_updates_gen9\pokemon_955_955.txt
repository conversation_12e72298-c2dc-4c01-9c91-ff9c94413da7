// POKEMON_955 (#955) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_955] =
    {
        .baseHP = 30,
        .baseAttack = 35,
        .baseDefense = 30,
        .baseSpAttack = 55,
        .baseSpDefense = 30,
        .baseSpeed = 75,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 120,
        .expYield = 65,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_ANTICIPATION,
        .ability2 = ABILITY_FRISK,
        .hiddenAbility = ABILITY_SPEED-BOOST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-955LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 5, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 8, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(11, MOVE_DISARMING_VOICE),
    LEVEL_UP_MOVE(15, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(19, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(24, MOVE_PLUCK),
    LEVEL_UP_MOVE(29, MOVE_AGILITY),
    LEVEL_UP_MOVE(34, MOVE_UPROAR),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 255
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_ANTICIPATION, ABILITY_FRISK, ABILITY_SPEED-BOOST
// Level Up Moves: 10
// Generation: 9

