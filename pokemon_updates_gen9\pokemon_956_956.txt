// POKEMON_956 (#956) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_956] =
    {
        .baseHP = 95,
        .baseAttack = 60,
        .baseDefense = 60,
        .baseSpAttack = 101,
        .baseSpDefense = 60,
        .baseSpeed = 105,
        .type1 = TYPE_PSYCHIC,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 60,
        .expYield = 155,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_OPPORTUNIST,
        .ability2 = ABILITY_FRISK,
        .hiddenAbility = ABILITY_SPEED-BOOST,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-956LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_LUMINA_CRASH),
    LEVEL_UP_MOVE( 1, MOVE_DRILL_PECK),
    LEVEL_UP_MOVE( 1, MOVE_FEATHER_DANCE),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 5, MOVE_CONFUSION),
    LEVEL_UP_MOVE( 8, MOVE_BABY_DOLL_EYES),
    LEVEL_UP_MOVE(11, MOVE_DISARMING_VOICE),
    LEVEL_UP_MOVE(15, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(19, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(24, MOVE_PLUCK),
    LEVEL_UP_MOVE(29, MOVE_AGILITY),
    LEVEL_UP_MOVE(34, MOVE_UPROAR),
    LEVEL_UP_MOVE(43, MOVE_DAZZLING_GLEAM),
    LEVEL_UP_MOVE(49, MOVE_PSYCHIC),
    LEVEL_UP_MOVE(54, MOVE_LAST_RESORT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 481
// Types: TYPE_PSYCHIC / TYPE_PSYCHIC
// Abilities: ABILITY_OPPORTUNIST, ABILITY_FRISK, ABILITY_SPEED-BOOST
// Level Up Moves: 16
// Generation: 9

