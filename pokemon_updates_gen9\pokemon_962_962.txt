// POKEMON_962 (#962) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_962] =
    {
        .baseHP = 70,
        .baseAttack = 103,
        .baseDefense = 85,
        .baseSpAttack = 60,
        .baseSpDefense = 85,
        .baseSpeed = 82,
        .type1 = TYPE_FLYING,
        .type2 = TYPE_DARK,
        .catchRate = 25,
        .expYield = 173,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 35,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_BIG-PECKS,
        .ability2 = ABILITY_KEEN-EYE,
        .hiddenAbility = ABILITY_ROCKY-PAYLOAD,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-962LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_HONE_CLAWS),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_MEMENTO),
    LEVEL_UP_MOVE( 1, MOVE_PECK),
    LEVEL_UP_MOVE( 1, MOVE_WING_ATTACK),
    LEVEL_UP_MOVE( 7, MOVE_THIEF),
    LEVEL_UP_MOVE(11, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE(16, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE(20, MOVE_PLUCK),
    LEVEL_UP_MOVE(24, MOVE_TORMENT),
    LEVEL_UP_MOVE(29, MOVE_ROCK_TOMB),
    LEVEL_UP_MOVE(36, MOVE_PAYBACK),
    LEVEL_UP_MOVE(42, MOVE_DUAL_WINGBEAT),
    LEVEL_UP_MOVE(47, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(53, MOVE_KNOCK_OFF),
    LEVEL_UP_MOVE(60, MOVE_PARTING_SHOT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 485
// Types: TYPE_FLYING / TYPE_DARK
// Abilities: ABILITY_BIG-PECKS, ABILITY_KEEN-EYE, ABILITY_ROCKY-PAYLOAD
// Level Up Moves: 16
// Generation: 9

