// POKEMON_964 (#964) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_964] =
    {
        .baseHP = 100,
        .baseAttack = 70,
        .baseDefense = 72,
        .baseSpAttack = 53,
        .baseSpDefense = 62,
        .baseSpeed = 100,
        .type1 = TYPE_WATER,
        .type2 = TYPE_WATER,
        .catchRate = 45,
        .expYield = 170,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 40,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_ZERO-TO-HERO,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_ZERO-TO-HERO,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-964LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_FLIP_TURN),
    LEVEL_UP_MOVE( 1, MOVE_JET_PUNCH),
    LEVEL_UP_MOVE( 1, MOVE_SUPERSONIC),
    LEVEL_UP_MOVE( 1, MOVE_WATER_GUN),
    LEVEL_UP_MOVE( 7, MOVE_ASTONISH),
    LEVEL_UP_MOVE(10, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(13, MOVE_AQUA_JET),
    LEVEL_UP_MOVE(17, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE(21, MOVE_DIVE),
    LEVEL_UP_MOVE(25, MOVE_CHARM),
    LEVEL_UP_MOVE(29, MOVE_ACROBATICS),
    LEVEL_UP_MOVE(34, MOVE_ENCORE),
    LEVEL_UP_MOVE(39, MOVE_AQUA_TAIL),
    LEVEL_UP_MOVE(44, MOVE_MIST),
    LEVEL_UP_MOVE(50, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(55, MOVE_FOCUS_PUNCH),
    LEVEL_UP_MOVE(61, MOVE_WAVE_CRASH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 457
// Types: TYPE_WATER / TYPE_WATER
// Abilities: ABILITY_ZERO-TO-HERO, ABILITY_NONE, ABILITY_ZERO-TO-HERO
// Level Up Moves: 17
// Generation: 9

