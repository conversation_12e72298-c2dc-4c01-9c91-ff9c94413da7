// POKEMON_967 (#967) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_967] =
    {
        .baseHP = 70,
        .baseAttack = 95,
        .baseDefense = 65,
        .baseSpAttack = 85,
        .baseSpDefense = 65,
        .baseSpeed = 121,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_NORMAL,
        .catchRate = 190,
        .expYield = 165,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SHED-SKIN,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_REGENERATOR,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-967LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 7, MOVE_RAPID_SPIN),
    LEVEL_UP_MOVE(11, MOVE_TAUNT),
    LEVEL_UP_MOVE(14, MOVE_BREAKING_SWIPE),
    LEVEL_UP_MOVE(18, MOVE_QUICK_ATTACK),
    LEVEL_UP_MOVE(23, MOVE_BITE),
    LEVEL_UP_MOVE(27, MOVE_U_TURN),
    LEVEL_UP_MOVE(31, MOVE_SHED_TAIL),
    LEVEL_UP_MOVE(36, MOVE_DRAGON_CLAW),
    LEVEL_UP_MOVE(40, MOVE_SHIFT_GEAR),
    LEVEL_UP_MOVE(45, MOVE_DRAGON_PULSE),
    LEVEL_UP_MOVE(51, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(57, MOVE_DRAGON_RUSH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 501
// Types: TYPE_DRAGON / TYPE_NORMAL
// Abilities: ABILITY_SHED-SKIN, ABILITY_NONE, ABILITY_REGENERATOR
// Level Up Moves: 14
// Generation: 9

