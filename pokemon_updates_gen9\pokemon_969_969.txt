// POKEMON_969 (#969) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_969] =
    {
        .baseHP = 48,
        .baseAttack = 35,
        .baseDefense = 42,
        .baseSpAttack = 105,
        .baseSpDefense = 60,
        .baseSpeed = 60,
        .type1 = TYPE_ROCK,
        .type2 = TYPE_POISON,
        .catchRate = 70,
        .expYield = 83,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 30,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_TOXIC-DEBRIS,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_CORROSION,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-969LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_HARDEN),
    LEVEL_UP_MOVE( 1, MOVE_ROCK_THROW),
    LEVEL_UP_MOVE( 1, MOVE_SMACK_DOWN),
    LEVEL_UP_MOVE( 7, MOVE_ACID_SPRAY),
    LEVEL_UP_MOVE(11, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(15, MOVE_ROCK_POLISH),
    LEVEL_UP_MOVE(18, MOVE_STEALTH_ROCK),
    LEVEL_UP_MOVE(22, MOVE_VENOSHOCK),
    LEVEL_UP_MOVE(26, MOVE_SANDSTORM),
    LEVEL_UP_MOVE(29, MOVE_SELF_DESTRUCT),
    LEVEL_UP_MOVE(33, MOVE_ROCK_SLIDE),
    LEVEL_UP_MOVE(37, MOVE_POWER_GEM),
    LEVEL_UP_MOVE(41, MOVE_ACID_ARMOR),
    LEVEL_UP_MOVE(46, MOVE_SLUDGE_WAVE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 350
// Types: TYPE_ROCK / TYPE_POISON
// Abilities: ABILITY_TOXIC-DEBRIS, ABILITY_NONE, ABILITY_CORROSION
// Level Up Moves: 14
// Generation: 9

