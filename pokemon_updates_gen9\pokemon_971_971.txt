// POKEMON_971 (#971) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_971] =
    {
        .baseHP = 50,
        .baseAttack = 61,
        .baseDefense = 60,
        .baseSpAttack = 30,
        .baseSpDefense = 55,
        .baseSpeed = 34,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_GHOST,
        .catchRate = 120,
        .expYield = 111,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PICKUP,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_FLUFFY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-971LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 3, MOVE_LICK),
    LEVEL_UP_MOVE( 6, MOVE_BITE),
    LEVEL_UP_MOVE( 6, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 9, MOVE_ROAR),
    LEVEL_UP_MOVE(12, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(16, MOVE_DIG),
    LEVEL_UP_MOVE(24, MOVE_REST),
    LEVEL_UP_MOVE(28, MOVE_CRUNCH),
    LEVEL_UP_MOVE(32, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(37, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(41, MOVE_PHANTOM_FORCE),
    LEVEL_UP_MOVE(46, MOVE_CHARM),
    LEVEL_UP_MOVE(52, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 290
// Types: TYPE_GHOST / TYPE_GHOST
// Abilities: ABILITY_PICKUP, ABILITY_NONE, ABILITY_FLUFFY
// Level Up Moves: 15
// Generation: 9

