// POKEMON_972 (#972) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_972] =
    {
        .baseHP = 72,
        .baseAttack = 101,
        .baseDefense = 100,
        .baseSpAttack = 50,
        .baseSpDefense = 97,
        .baseSpeed = 68,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_GHOST,
        .catchRate = 60,
        .expYield = 173,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SAND-RUSH,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_FLUFFY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-972LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 0, MOVE_LAST_RESPECTS),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 3, MOVE_LICK),
    LEVEL_UP_MOVE( 6, MOVE_BITE),
    LEVEL_UP_MOVE( 6, MOVE_TAIL_WHIP),
    LEVEL_UP_MOVE( 9, MOVE_ROAR),
    LEVEL_UP_MOVE(12, MOVE_HEADBUTT),
    LEVEL_UP_MOVE(16, MOVE_DIG),
    LEVEL_UP_MOVE(24, MOVE_REST),
    LEVEL_UP_MOVE(28, MOVE_CRUNCH),
    LEVEL_UP_MOVE(36, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(41, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(46, MOVE_PHANTOM_FORCE),
    LEVEL_UP_MOVE(51, MOVE_CHARM),
    LEVEL_UP_MOVE(58, MOVE_DOUBLE_EDGE),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 488
// Types: TYPE_GHOST / TYPE_GHOST
// Abilities: ABILITY_SAND-RUSH, ABILITY_NONE, ABILITY_FLUFFY
// Level Up Moves: 16
// Generation: 9

