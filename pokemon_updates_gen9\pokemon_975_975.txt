// POKEMON_975 (#975) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_975] =
    {
        .baseHP = 170,
        .baseAttack = 113,
        .baseDefense = 65,
        .baseSpAttack = 45,
        .baseSpDefense = 55,
        .baseSpeed = 73,
        .type1 = TYPE_ICE,
        .type2 = TYPE_ICE,
        .catchRate = 50,
        .expYield = 255,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 25,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_THICK-FAT,
        .ability2 = ABILITY_SLUSH-RUSH,
        .hiddenAbility = ABILITY_SHEER-FORCE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-975LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_POWDER_SNOW),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 6, MOVE_GROWL),
    LEVEL_UP_MOVE( 9, MOVE_ECHOED_VOICE),
    LEVEL_UP_MOVE(12, MOVE_ICE_SHARD),
    LEVEL_UP_MOVE(15, MOVE_REST),
    LEVEL_UP_MOVE(19, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(25, MOVE_FLAIL),
    LEVEL_UP_MOVE(27, MOVE_AVALANCHE),
    LEVEL_UP_MOVE(31, MOVE_BOUNCE),
    LEVEL_UP_MOVE(36, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(40, MOVE_AMNESIA),
    LEVEL_UP_MOVE(44, MOVE_ICE_SPINNER),
    LEVEL_UP_MOVE(49, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(53, MOVE_BLIZZARD),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 521
// Types: TYPE_ICE / TYPE_ICE
// Abilities: ABILITY_THICK-FAT, ABILITY_SLUSH-RUSH, ABILITY_SHEER-FORCE
// Level Up Moves: 15
// Generation: 9

