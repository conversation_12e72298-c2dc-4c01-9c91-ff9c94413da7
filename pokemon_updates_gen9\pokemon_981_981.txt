// POKEMON_981 (#981) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_981] =
    {
        .baseHP = 120,
        .baseAttack = 90,
        .baseDefense = 70,
        .baseSpAttack = 110,
        .baseSpDefense = 70,
        .baseSpeed = 60,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 45,
        .expYield = 210,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_CUD-CHEW,
        .ability2 = ABILITY_ARMOR-TAIL,
        .hiddenAbility = ABILITY_SAP-SIPPER,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-981LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_GROWL),
    LEVEL_UP_MOVE( 1, MOVE_GUARD_SWAP),
    LEVEL_UP_MOVE( 1, MOVE_POWER_SWAP),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 5, MOVE_CONFUSION),
    LEVEL_UP_MOVE(10, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(14, MOVE_STOMP),
    LEVEL_UP_MOVE(19, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(23, MOVE_AGILITY),
    LEVEL_UP_MOVE(28, MOVE_DOUBLE_HIT),
    LEVEL_UP_MOVE(32, MOVE_TWIN_BEAM),
    LEVEL_UP_MOVE(37, MOVE_CRUNCH),
    LEVEL_UP_MOVE(41, MOVE_BATON_PASS),
    LEVEL_UP_MOVE(46, MOVE_NASTY_PLOT),
    LEVEL_UP_MOVE(50, MOVE_PSYCHIC),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 520
// Types: TYPE_NORMAL / TYPE_PSYCHIC
// Abilities: ABILITY_CUD-CHEW, ABILITY_ARMOR-TAIL, ABILITY_SAP-SIPPER
// Level Up Moves: 16
// Generation: 9

