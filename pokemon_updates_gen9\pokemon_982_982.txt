// POKEMON_982 (#982) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_982] =
    {
        .baseHP = 125,
        .baseAttack = 100,
        .baseDefense = 80,
        .baseSpAttack = 85,
        .baseSpDefense = 75,
        .baseSpeed = 55,
        .type1 = TYPE_NORMAL,
        .type2 = TYPE_NORMAL,
        .catchRate = 45,
        .expYield = 225,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 20,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_SERENE-GRACE,
        .ability2 = ABILITY_RUN-AWAY,
        .hiddenAbility = ABILITY_RATTLED,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-982LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DEFENSE_CURL),
    LEVEL_UP_MOVE( 1, MOVE_FLAIL),
    LEVEL_UP_MOVE( 4, MOVE_MUD_SLAP),
    LEVEL_UP_MOVE( 8, MOVE_ROLLOUT),
    LEVEL_UP_MOVE(12, MOVE_GLARE),
    LEVEL_UP_MOVE(16, MOVE_SCREECH),
    LEVEL_UP_MOVE(20, MOVE_ANCIENT_POWER),
    LEVEL_UP_MOVE(24, MOVE_DRILL_RUN),
    LEVEL_UP_MOVE(28, MOVE_YAWN),
    LEVEL_UP_MOVE(32, MOVE_HYPER_DRILL),
    LEVEL_UP_MOVE(36, MOVE_ROOST),
    LEVEL_UP_MOVE(40, MOVE_DRAGON_RUSH),
    LEVEL_UP_MOVE(44, MOVE_COIL),
    LEVEL_UP_MOVE(48, MOVE_DOUBLE_EDGE),
    LEVEL_UP_MOVE(52, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(56, MOVE_HURRICANE),
    LEVEL_UP_MOVE(62, MOVE_BOOMBURST),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 520
// Types: TYPE_NORMAL / TYPE_NORMAL
// Abilities: ABILITY_SERENE-GRACE, ABILITY_RUN-AWAY, ABILITY_RATTLED
// Level Up Moves: 17
// Generation: 9

