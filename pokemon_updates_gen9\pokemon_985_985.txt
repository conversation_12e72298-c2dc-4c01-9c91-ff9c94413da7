// POKEMON_985 (#985) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_985] =
    {
        .baseHP = 115,
        .baseAttack = 65,
        .baseDefense = 99,
        .baseSpAttack = 65,
        .baseSpDefense = 115,
        .baseSpeed = 111,
        .type1 = TYPE_FAIRY,
        .type2 = TYPE_PSYCHIC,
        .catchRate = 50,
        .expYield = 180,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PROTOSYNTHESIS,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-985LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DISABLE),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_SING),
    LEVEL_UP_MOVE( 7, MOVE_HOWL),
    LEVEL_UP_MOVE(14, MOVE_NOBLE_ROAR),
    LEVEL_UP_MOVE(21, MOVE_BITE),
    LEVEL_UP_MOVE(28, MOVE_BODY_SLAM),
    LEVEL_UP_MOVE(35, MOVE_REST),
    LEVEL_UP_MOVE(42, MOVE_PLAY_ROUGH),
    LEVEL_UP_MOVE(49, MOVE_HYPER_VOICE),
    LEVEL_UP_MOVE(56, MOVE_PSYCHIC_FANGS),
    LEVEL_UP_MOVE(63, MOVE_CRUNCH),
    LEVEL_UP_MOVE(70, MOVE_WISH),
    LEVEL_UP_MOVE(77, MOVE_GYRO_BALL),
    LEVEL_UP_MOVE(84, MOVE_PERISH_SONG),
    LEVEL_UP_MOVE(91, MOVE_BOOMBURST),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_FAIRY / TYPE_PSYCHIC
// Abilities: ABILITY_PROTOSYNTHESIS, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
// Generation: 9

