// POKEMON_987 (#987) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_987] =
    {
        .baseHP = 55,
        .baseAttack = 55,
        .baseDefense = 55,
        .baseSpAttack = 135,
        .baseSpDefense = 135,
        .baseSpeed = 135,
        .type1 = TYPE_GHOST,
        .type2 = TYPE_FAIRY,
        .catchRate = 30,
        .expYield = 110,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_PROTOSYNTHESIS,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-987LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ASTONISH),
    LEVEL_UP_MOVE( 1, MOVE_CONFUSE_RAY),
    LEVEL_UP_MOVE( 1, MOVE_SPITE),
    LEVEL_UP_MOVE( 7, MOVE_PSYBEAM),
    LEVEL_UP_MOVE(14, MOVE_MEAN_LOOK),
    LEVEL_UP_MOVE(21, MOVE_MEMENTO),
    LEVEL_UP_MOVE(28, MOVE_WISH),
    LEVEL_UP_MOVE(35, MOVE_DAZZLING_GLEAM),
    LEVEL_UP_MOVE(42, MOVE_SHADOW_BALL),
    LEVEL_UP_MOVE(49, MOVE_MYSTICAL_FIRE),
    LEVEL_UP_MOVE(56, MOVE_POWER_GEM),
    LEVEL_UP_MOVE(63, MOVE_PSYSHOCK),
    LEVEL_UP_MOVE(70, MOVE_PHANTOM_FORCE),
    LEVEL_UP_MOVE(77, MOVE_PAIN_SPLIT),
    LEVEL_UP_MOVE(84, MOVE_MOONBLAST),
    LEVEL_UP_MOVE(91, MOVE_PERISH_SONG),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_GHOST / TYPE_FAIRY
// Abilities: ABILITY_PROTOSYNTHESIS, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 16
// Generation: 9

