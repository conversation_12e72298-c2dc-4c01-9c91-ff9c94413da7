// POKEMON_991 (#991) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_991] =
    {
        .baseHP = 56,
        .baseAttack = 80,
        .baseDefense = 114,
        .baseSpAttack = 124,
        .baseSpDefense = 60,
        .baseSpeed = 136,
        .type1 = TYPE_ICE,
        .type2 = TYPE_WATER,
        .catchRate = 50,
        .expYield = 136,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_QUARK-DRIVE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-991LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_PRESENT),
    LEVEL_UP_MOVE( 7, MOVE_POWDER_SNOW),
    LEVEL_UP_MOVE(14, MOVE_WHIRLPOOL),
    LEVEL_UP_MOVE(21, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(28, MOVE_DRILL_PECK),
    LEVEL_UP_MOVE(35, MOVE_HELPING_HAND),
    LEVEL_UP_MOVE(42, MOVE_FREEZE_DRY),
    LEVEL_UP_MOVE(49, MOVE_FLIP_TURN),
    LEVEL_UP_MOVE(56, MOVE_ICE_BEAM),
    LEVEL_UP_MOVE(63, MOVE_AGILITY),
    LEVEL_UP_MOVE(70, MOVE_SNOWSCAPE),
    LEVEL_UP_MOVE(77, MOVE_HYDRO_PUMP),
    LEVEL_UP_MOVE(84, MOVE_AURORA_VEIL),
    LEVEL_UP_MOVE(91, MOVE_BLIZZARD),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_ICE / TYPE_WATER
// Abilities: ABILITY_QUARK-DRIVE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 14
// Generation: 9

