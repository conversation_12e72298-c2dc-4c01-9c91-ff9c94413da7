// POKEMON_994 (#994) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_994] =
    {
        .baseHP = 80,
        .baseAttack = 70,
        .baseDefense = 60,
        .baseSpAttack = 140,
        .baseSpDefense = 110,
        .baseSpeed = 110,
        .type1 = TYPE_FIRE,
        .type2 = TYPE_POISON,
        .catchRate = 30,
        .expYield = 150,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(-12.5),
        .eggCycles = 50,
        .friendship = 0,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_QUARK-DRIVE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_NONE,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-994LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_ACID_SPRAY),
    LEVEL_UP_MOVE( 1, MOVE_EMBER),
    LEVEL_UP_MOVE( 1, MOVE_GUST),
    LEVEL_UP_MOVE( 1, MOVE_WHIRLWIND),
    LEVEL_UP_MOVE( 7, MOVE_STRUGGLE_BUG),
    LEVEL_UP_MOVE(14, MOVE_FIRE_SPIN),
    LEVEL_UP_MOVE(21, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(28, MOVE_LUNGE),
    LEVEL_UP_MOVE(35, MOVE_SCREECH),
    LEVEL_UP_MOVE(42, MOVE_DISCHARGE),
    LEVEL_UP_MOVE(49, MOVE_SLUDGE_WAVE),
    LEVEL_UP_MOVE(56, MOVE_FIERY_DANCE),
    LEVEL_UP_MOVE(63, MOVE_METAL_SOUND),
    LEVEL_UP_MOVE(70, MOVE_MORNING_SUN),
    LEVEL_UP_MOVE(77, MOVE_HURRICANE),
    LEVEL_UP_MOVE(84, MOVE_BUG_BUZZ),
    LEVEL_UP_MOVE(91, MOVE_OVERHEAT),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 570
// Types: TYPE_FIRE / TYPE_POISON
// Abilities: ABILITY_QUARK-DRIVE, ABILITY_NONE, ABILITY_NONE
// Level Up Moves: 17
// Generation: 9

