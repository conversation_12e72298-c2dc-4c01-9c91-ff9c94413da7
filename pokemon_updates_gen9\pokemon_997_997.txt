// POKEMON_997 (#997) - GENERATION 9 UPDATE

// BASE STATS ENTRY:
    [SPECIES_POKEMON_997] =
    {
        .baseHP = 90,
        .baseAttack = 95,
        .baseDefense = 66,
        .baseSpAttack = 45,
        .baseSpDefense = 65,
        .baseSpeed = 62,
        .type1 = TYPE_DRAGON,
        .type2 = TYPE_ICE,
        .catchRate = 25,
        .expYield = 185,
        .evYield_HP = 0,
        .evYield_Attack = 0,
        .evYield_Defense = 0,
        .evYield_SpAttack = 0,
        .evYield_SpDefense = 0,
        .evYield_Speed = 0,
        .item1 = ITEM_NONE,
        .item2 = ITEM_NONE,
        .genderRatio = PERCENT_FEMALE(50.0),
        .eggCycles = 40,
        .friendship = 50,
        .growthRate = GROWTH_MEDIUM_SLOW,
        .eggGroup1 = EGG_GROUP_FIELD,
        .eggGroup2 = EGG_GROUP_FIELD,
        .ability1 = ABILITY_THERMAL-EXCHANGE,
        .ability2 = ABILITY_NONE,
        .hiddenAbility = ABILITY_ICE-BODY,
        .safariZoneFleeRate = 0,
        .bodyColor = BODY_COLOR_GREEN, // Placeholder - needs manual adjustment
        .noFlip = FALSE,
    },

// LEVEL UP MOVES:
static const struct LevelUpMove sPokemon-997LevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_DRAGON_TAIL),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_TACKLE),
    LEVEL_UP_MOVE( 6, MOVE_ICY_WIND),
    LEVEL_UP_MOVE(12, MOVE_DRAGON_BREATH),
    LEVEL_UP_MOVE(18, MOVE_FOCUS_ENERGY),
    LEVEL_UP_MOVE(24, MOVE_BITE),
    LEVEL_UP_MOVE(29, MOVE_ICE_FANG),
    LEVEL_UP_MOVE(40, MOVE_TAKE_DOWN),
    LEVEL_UP_MOVE(45, MOVE_ICE_BEAM),
    LEVEL_UP_MOVE(50, MOVE_CRUNCH),
    LEVEL_UP_MOVE(55, MOVE_ICICLE_CRASH),
    LEVEL_UP_END
};

// SUMMARY:
// Stats Total: 423
// Types: TYPE_DRAGON / TYPE_ICE
// Abilities: ABILITY_THERMAL-EXCHANGE, ABILITY_NONE, ABILITY_ICE-BODY
// Level Up Moves: 12
// Generation: 9

