#!/usr/bin/env python3
"""
Script para padronizar todas as hidden abilities para o formato original do projeto
"""

import re
import os

def standardize_hidden_ability():
    """Converte todas as .hiddenAbility para .abilityHidden"""
    
    print("🔧 PADRONIZAÇÃO PARA FORMATO ORIGINAL")
    print("=" * 45)
    
    # Lê o arquivo atual
    with open("src/Base_Stats.c", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Conta ocorrências antes
    hidden_ability_count = content.count('.hiddenAbility')
    ability_hidden_count = content.count('.abilityHidden')
    
    print(f"📊 ANTES DA CONVERSÃO:")
    print(f"   .hiddenAbility: {hidden_ability_count} ocorrências")
    print(f"   .abilityHidden: {ability_hidden_count} ocorrências")
    
    # Converte todas as .hiddenAbility para .abilityHidden
    content = content.replace('.hiddenAbility', '.abilityHidden')
    
    # Conta ocorrências depois
    hidden_ability_count_after = content.count('.hiddenAbility')
    ability_hidden_count_after = content.count('.abilityHidden')
    
    print(f"\n📊 DEPOIS DA CONVERSÃO:")
    print(f"   .hiddenAbility: {hidden_ability_count_after} ocorrências")
    print(f"   .abilityHidden: {ability_hidden_count_after} ocorrências")
    
    # Salva o arquivo padronizado
    with open("src/Base_Stats.c", "w", encoding="utf-8") as f:
        f.write(content)
    
    print(f"\n✅ PADRONIZAÇÃO COMPLETA!")
    print(f"   Convertidas {hidden_ability_count} ocorrências")
    print(f"   Formato consistente: .abilityHidden")
    
    return True

def verify_standardization():
    """Verifica se a padronização foi bem-sucedida"""
    
    print("\n🔍 VERIFICANDO PADRONIZAÇÃO...")
    print("=" * 35)
    
    with open("src/Base_Stats.c", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Verifica se ainda há .hiddenAbility
    hidden_ability_count = content.count('.hiddenAbility')
    ability_hidden_count = content.count('.abilityHidden')
    
    if hidden_ability_count == 0:
        print("✅ PADRONIZAÇÃO PERFEITA!")
        print(f"   .abilityHidden: {ability_hidden_count} ocorrências")
        print("   .hiddenAbility: 0 ocorrências")
        return True
    else:
        print(f"❌ AINDA HÁ {hidden_ability_count} ocorrências de .hiddenAbility")
        return False

def main():
    """Função principal"""
    
    if not os.path.exists("src/Base_Stats.c"):
        print("❌ Arquivo src/Base_Stats.c não encontrado!")
        return False
    
    # Padroniza para .abilityHidden
    success = standardize_hidden_ability()
    
    if success:
        # Verifica padronização
        verify_standardization()
        
        print("\n🎯 PRÓXIMO PASSO:")
        print("Execute: python scripts/make.py")
        print("Para testar a compilação com formato padronizado")
        
        return True
    
    return False

if __name__ == "__main__":
    main()
