#!/usr/bin/env python3
"""
Teste do Sistema de Mapeamento de Habilidades
Verifica se as habilidades da PokeAPI são mapeadas corretamente para as constantes do projeto
"""

from pokemon_updater import PokemonUpdater

def test_ability_mapping():
    """Testa o mapeamento de habilidades específicas"""
    print("🧪 TESTE DO MAPEAMENTO DE HABILIDADES")
    print("=" * 60)
    
    updater = PokemonUpdater()
    
    # Casos de teste específicos que podem causar problemas
    test_cases = [
        # Habilidades com hífens que devem ser removidos
        ("serene-grace", "ABILITY_SERENEGRACE"),
        ("compound-eyes", "ABILITY_COMPOUNDEYES"),
        ("lightning-rod", "ABILITY_LIGHTNINGROD"),
        ("swift-swim", "ABILITY_SWIFTSWIM"),
        ("huge-power", "ABILITY_HUGEPOWER"),
        ("clear-body", "ABILITY_CLEARBODY"),
        ("natural-cure", "ABILITY_NATURALCURE"),
        
        # Habilidades simples (sem hífens)
        ("overgrow", "ABILITY_OVERGROW"),
        ("blaze", "ABILITY_BLAZE"),
        ("torrent", "ABILITY_TORRENT"),
        ("static", "ABILITY_STATIC"),
        ("pressure", "ABILITY_PRESSURE"),
        
        # Habilidades com aliases
        ("air-lock", "ABILITY_CLOUDNINE"),
        ("vital-spirit", "ABILITY_INSOMNIA"),
        ("iron-barbs", "ABILITY_ROUGHSKIN"),
        ("white-smoke", "ABILITY_CLEARBODY"),
        ("pure-power", "ABILITY_HUGEPOWER"),
        
        # Habilidades da Generation IX
        ("protosynthesis", "ABILITY_PROTOSYNTHESIS"),
        ("quark-drive", "ABILITY_QUARKDRIVE"),
        ("good-as-gold", "ABILITY_GOODASGOLD"),
        ("purifying-salt", "ABILITY_PURIFYINGSALT"),
    ]
    
    print("📋 Testando mapeamentos específicos:")
    
    passed = 0
    failed = 0
    
    for api_name, expected_constant in test_cases:
        result = updater.get_ability_constant(api_name)
        
        if result == expected_constant:
            print(f"   ✅ {api_name} → {result}")
            passed += 1
        else:
            print(f"   ❌ {api_name} → {result} (esperado: {expected_constant})")
            failed += 1
    
    print(f"\n📊 Resultado: {passed}/{len(test_cases)} testes passaram")
    
    return failed == 0

def test_pokemon_abilities():
    """Testa habilidades de Pokémon específicos"""
    print(f"\n🔍 TESTE DE HABILIDADES DE POKÉMON ESPECÍFICOS")
    print("=" * 60)
    
    updater = PokemonUpdater()
    
    # Pokémon com habilidades que podem causar problemas
    test_pokemon = [
        (253, "grovyle", ["overgrow", "unburden"]),  # Hidden ability
        (25, "pikachu", ["static", "lightning-rod"]),  # Lightning Rod
        (144, "articuno", ["pressure", "snow-cloak"]),  # Snow Cloak
        (6, "charizard", ["blaze", "solar-power"]),  # Solar Power
    ]
    
    for pokemon_id, pokemon_name, expected_abilities in test_pokemon:
        print(f"\n📊 {pokemon_name.upper()} (#{pokemon_id}):")
        
        pokemon_data = updater.get_pokemon_data(pokemon_id)
        if pokemon_data:
            latest_data = updater.get_latest_generation_data(pokemon_data)
            abilities = latest_data['abilities']
            
            print(f"   Ability 1: {abilities['ability1']}")
            print(f"   Ability 2: {abilities['ability2']}")
            print(f"   Hidden: {abilities['hiddenAbility']}")
            
            # Verifica se as habilidades estão no formato correto
            for ability_key, ability_value in abilities.items():
                if ability_value != 'ABILITY_NONE':
                    if ability_value.startswith('ABILITY_') and '_' not in ability_value[8:]:
                        print(f"   ✅ {ability_key}: {ability_value} (formato correto)")
                    elif ability_value.startswith('ABILITY_') and '_' in ability_value[8:]:
                        print(f"   ⚠️  {ability_key}: {ability_value} (pode ter underscore desnecessário)")
                    else:
                        print(f"   ❌ {ability_key}: {ability_value} (formato incorreto)")

def test_problematic_abilities():
    """Testa habilidades que historicamente causam problemas"""
    print(f"\n⚠️  TESTE DE HABILIDADES PROBLEMÁTICAS")
    print("=" * 60)
    
    updater = PokemonUpdater()
    
    # Habilidades que podem vir com formatação diferente
    problematic_cases = [
        # Casos que podem gerar ABILITY_SERENE_GRACE em vez de ABILITY_SERENEGRACE
        "serene-grace",
        "compound-eyes", 
        "lightning-rod",
        "swift-swim",
        "huge-power",
        "clear-body",
        "natural-cure",
        "poison-point",
        "inner-focus",
        "magma-armor",
        "water-veil",
        "magnet-pull",
        "rain-dish",
        "sand-stream",
        "thick-fat",
        "early-bird",
        "flame-body",
        "keen-eye",
        "hyper-cutter",
        "cute-charm",
        "sticky-hold",
        "shed-skin",
        "marvel-scale",
        "liquid-ooze",
        "rock-head",
        "arena-trap",
        "shell-armor",
    ]
    
    print("📋 Verificando habilidades problemáticas:")
    
    issues_found = 0
    
    for ability_name in problematic_cases:
        result = updater.get_ability_constant(ability_name)
        
        # Verifica se tem underscore desnecessário após ABILITY_
        ability_part = result[8:]  # Remove "ABILITY_"
        if '_' in ability_part:
            print(f"   ❌ {ability_name} → {result} (contém underscore desnecessário)")
            issues_found += 1
        else:
            print(f"   ✅ {ability_name} → {result}")
    
    if issues_found == 0:
        print(f"\n✅ Nenhum problema encontrado!")
    else:
        print(f"\n⚠️  {issues_found} problemas encontrados")
    
    return issues_found == 0

def test_generation_ix_abilities():
    """Testa habilidades específicas da Generation IX"""
    print(f"\n🆕 TESTE DE HABILIDADES DA GENERATION IX")
    print("=" * 60)
    
    updater = PokemonUpdater()
    
    # Habilidades novas da Gen IX
    gen9_abilities = [
        "protosynthesis",
        "quark-drive", 
        "good-as-gold",
        "purifying-salt",
        "well-baked-body",
        "wind-power",
        "guard-dog",
        "rocky-payload",
        "wind-rider",
        "mycelium-might",
        "minds-eye",
        "supersweet-syrup",
        "hadron-engine",
        "orichalcum-pulse",
        "toxic-chain",
        "toxic-debris",
        "earth-eater",
        "seed-sower",
    ]
    
    print("📋 Verificando habilidades da Generation IX:")
    
    for ability in gen9_abilities:
        result = updater.get_ability_constant(ability)
        print(f"   {ability} → {result}")

def main():
    """Função principal de teste"""
    print("🧪 TESTE COMPLETO DO SISTEMA DE MAPEAMENTO DE HABILIDADES")
    print("=" * 70)
    
    # Executa todos os testes
    tests = [
        ("Mapeamento Específico", test_ability_mapping),
        ("Habilidades de Pokémon", test_pokemon_abilities),
        ("Habilidades Problemáticas", test_problematic_abilities),
        ("Habilidades Generation IX", test_generation_ix_abilities),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            if test_name in ["Habilidades de Pokémon", "Habilidades Generation IX"]:
                # Estes testes são informativos, não retornam boolean
                test_func()
                results.append((test_name, True))
            else:
                result = test_func()
                results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erro no teste {test_name}: {e}")
            results.append((test_name, False))
    
    # Resumo final
    print("\n" + "=" * 70)
    print("📊 RESUMO DOS TESTES DE HABILIDADES")
    print("=" * 70)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSOU" if result else "❌ FALHOU"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 Resultado: {passed}/{len(results)} testes passaram")
    
    if passed == len(results):
        print("\n🎉 TODOS OS TESTES DE HABILIDADES PASSARAM!")
        print("✅ Sistema de mapeamento funcionando corretamente")
        print("✅ Nenhum conflito de nomenclatura detectado")
        print("✅ Habilidades serão mapeadas corretamente no projeto")
    else:
        print("\n⚠️  ALGUNS TESTES FALHARAM!")
        print("❌ Verifique os problemas reportados acima")
    
    print(f"\n🎯 PRÓXIMO PASSO:")
    print("   Executar atualização completa com mapeamento de habilidades corrigido")

if __name__ == "__main__":
    main()
