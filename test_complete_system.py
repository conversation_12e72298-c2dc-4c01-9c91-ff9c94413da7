#!/usr/bin/env python3
"""
Teste do Sistema Completo de Atualização
Verifica se o sistema está pronto para atualizar todos os 1439 Pokémon
"""

from complete_pokemon_update import get_all_pokemon_list
from pokemon_updater import PokemonUpdater

def test_pokemon_list_generation():
    """Testa a geração da lista completa de Pokémon"""
    print("🧪 TESTE DA LISTA COMPLETA DE POKÉMON")
    print("=" * 60)
    
    all_pokemon = get_all_pokemon_list()
    
    print(f"📊 Total de Pokémon gerados: {len(all_pokemon)}")
    print(f"📋 Esperado: 1439 Pokémon")
    
    if len(all_pokemon) == 1439:
        print("✅ Quantidade correta de Pokémon")
    else:
        print(f"❌ Quantidade incorreta! Esperado: 1439, Obtido: {len(all_pokemon)}")
    
    # Verifica alguns Pokémon específicos
    test_cases = [
        (1, "bulbasaur"),
        (25, "pikachu"),
        (151, "mew"),
        (152, "chikorita"),
        (251, "celebi"),
        (252, "treecko"),
        (386, "deoxys"),
        (387, "pokemon_387"),  # Gen IV sem nome conhecido
        (493, "pokemon_493"),  # Arceus
        (649, "pokemon_649"),  # Gen V
        (721, "pokemon_721"),  # Gen VI
        (809, "pokemon_809"),  # Gen VII
        (905, "pokemon_905"),  # Gen VIII
        (1025, "pokemon_1025"), # Gen IX
        (1439, "pokemon_1439")  # Último Pokémon
    ]
    
    print(f"\n📋 Verificando Pokémon específicos:")
    for expected_id, expected_name in test_cases:
        found = False
        for pokemon_id, pokemon_name in all_pokemon:
            if pokemon_id == expected_id:
                if pokemon_name == expected_name:
                    print(f"   ✅ #{expected_id:4d}: {pokemon_name}")
                else:
                    print(f"   ⚠️  #{expected_id:4d}: {pokemon_name} (esperado: {expected_name})")
                found = True
                break
        
        if not found:
            print(f"   ❌ #{expected_id:4d}: NÃO ENCONTRADO")
    
    # Verifica distribuição por geração
    print(f"\n📊 Distribuição por geração:")
    generation_ranges = [
        (1, 151, "Generation I"),
        (152, 251, "Generation II"), 
        (252, 386, "Generation III"),
        (387, 493, "Generation IV"),
        (494, 649, "Generation V"),
        (650, 721, "Generation VI"),
        (722, 809, "Generation VII"),
        (810, 905, "Generation VIII"),
        (906, 1025, "Generation IX"),
        (1026, 1439, "Formas/Extras")
    ]
    
    for start, end, gen_name in generation_ranges:
        gen_pokemon = [p for p in all_pokemon if start <= p[0] <= end]
        expected_count = end - start + 1
        actual_count = len(gen_pokemon)
        
        if actual_count == expected_count:
            print(f"   ✅ {gen_name}: {actual_count} Pokémon (#{start}-{end})")
        else:
            print(f"   ❌ {gen_name}: {actual_count} Pokémon (esperado: {expected_count})")
    
    return len(all_pokemon) == 1439

def test_system_functionality():
    """Testa a funcionalidade básica do sistema"""
    print(f"\n🔧 TESTE DE FUNCIONALIDADE DO SISTEMA")
    print("=" * 60)
    
    updater = PokemonUpdater()
    
    # Testa com alguns Pokémon de diferentes gerações
    test_pokemon = [
        (1, "bulbasaur", "Gen I"),
        (152, "chikorita", "Gen II"),
        (252, "treecko", "Gen III"),
        (387, "pokemon_387", "Gen IV"),
        (494, "pokemon_494", "Gen V"),
        (650, "pokemon_650", "Gen VI"),
        (722, "pokemon_722", "Gen VII"),
        (810, "pokemon_810", "Gen VIII"),
        (906, "pokemon_906", "Gen IX")
    ]
    
    successful_tests = 0
    
    for pokemon_id, pokemon_name, generation in test_pokemon:
        print(f"\n🔍 Testando {pokemon_name.upper()} (#{pokemon_id}) - {generation}:")
        
        try:
            # Testa obtenção de dados
            pokemon_data = updater.get_pokemon_data(pokemon_id)
            if pokemon_data:
                print(f"   ✅ Dados obtidos da PokeAPI")
                
                # Testa processamento
                latest_data = updater.get_latest_generation_data(pokemon_data)
                generation_used = latest_data['moves'].get('generation_used', 'unknown')
                move_count = len(latest_data['moves']['level_up'])
                
                print(f"   ✅ Processamento: {generation_used} ({move_count} moves)")
                
                # Testa geração de código
                base_stats = updater.generate_base_stats_entry(pokemon_id, pokemon_name, latest_data)
                moveset = updater.generate_level_up_moves(pokemon_name, latest_data['moves']['level_up'])
                
                if base_stats and moveset:
                    print(f"   ✅ Código gerado com sucesso")
                    successful_tests += 1
                else:
                    print(f"   ❌ Erro na geração de código")
            else:
                print(f"   ❌ Erro ao obter dados da PokeAPI")
                
        except Exception as e:
            print(f"   ❌ Erro crítico: {str(e)[:50]}")
    
    success_rate = (successful_tests / len(test_pokemon)) * 100
    print(f"\n📊 Taxa de sucesso: {successful_tests}/{len(test_pokemon)} ({success_rate:.1f}%)")
    
    return success_rate >= 80

def test_generation_priority():
    """Testa o sistema de priorização de gerações"""
    print(f"\n🎯 TESTE DE PRIORIZAÇÃO DE GERAÇÕES")
    print("=" * 60)
    
    updater = PokemonUpdater()
    
    # Testa com Pokémon que sabemos que têm dados em múltiplas gerações
    priority_tests = [
        (25, "pikachu", "Deve priorizar Gen IX"),
        (1, "bulbasaur", "Deve priorizar Gen IX"),
        (252, "treecko", "Deve priorizar Gen IX"),
        (253, "grovyle", "Deve priorizar Gen IX")
    ]
    
    correct_priorities = 0
    
    for pokemon_id, pokemon_name, expectation in priority_tests:
        print(f"\n🔍 {pokemon_name.upper()}: {expectation}")
        
        try:
            pokemon_data = updater.get_pokemon_data(pokemon_id)
            if pokemon_data:
                latest_data = updater.get_latest_generation_data(pokemon_data)
                generation_used = latest_data['moves'].get('generation_used', 'unknown')
                
                if generation_used == 'scarlet-violet':
                    print(f"   ✅ Priorização correta: {generation_used}")
                    correct_priorities += 1
                elif generation_used in ['sword-shield', 'ultra-sun-ultra-moon']:
                    print(f"   ⚠️  Fallback usado: {generation_used}")
                    correct_priorities += 0.5  # Meio ponto para fallback válido
                else:
                    print(f"   ❌ Priorização incorreta: {generation_used}")
            else:
                print(f"   ❌ Erro ao obter dados")
                
        except Exception as e:
            print(f"   ❌ Erro: {str(e)[:50]}")
    
    priority_score = (correct_priorities / len(priority_tests)) * 100
    print(f"\n📊 Score de priorização: {correct_priorities}/{len(priority_tests)} ({priority_score:.1f}%)")
    
    return priority_score >= 75

def test_ability_mapping():
    """Testa o sistema de mapeamento de habilidades"""
    print(f"\n🧬 TESTE DE MAPEAMENTO DE HABILIDADES")
    print("=" * 60)
    
    updater = PokemonUpdater()
    
    # Testa casos problemáticos conhecidos
    ability_tests = [
        ("serene-grace", "ABILITY_SERENEGRACE"),
        ("compound-eyes", "ABILITY_COMPOUNDEYES"),
        ("lightning-rod", "ABILITY_LIGHTNINGROD"),
        ("overgrow", "ABILITY_OVERGROW"),
        ("protosynthesis", "ABILITY_PROTOSYNTHESIS")
    ]
    
    correct_mappings = 0
    
    for api_name, expected_constant in ability_tests:
        result = updater.get_ability_constant(api_name)
        
        if result == expected_constant:
            print(f"   ✅ {api_name} → {result}")
            correct_mappings += 1
        else:
            print(f"   ❌ {api_name} → {result} (esperado: {expected_constant})")
    
    mapping_score = (correct_mappings / len(ability_tests)) * 100
    print(f"\n📊 Score de mapeamento: {correct_mappings}/{len(ability_tests)} ({mapping_score:.1f}%)")
    
    return mapping_score == 100

def main():
    """Função principal de teste"""
    print("🧪 TESTE COMPLETO DO SISTEMA DE ATUALIZAÇÃO MASSIVA")
    print("=" * 70)
    print("Verificando se o sistema está pronto para atualizar 1439 Pokémon")
    print("=" * 70)
    
    # Executa todos os testes
    tests = [
        ("Lista de Pokémon", test_pokemon_list_generation),
        ("Funcionalidade do Sistema", test_system_functionality),
        ("Priorização de Gerações", test_generation_priority),
        ("Mapeamento de Habilidades", test_ability_mapping)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erro no teste {test_name}: {e}")
            results.append((test_name, False))
    
    # Resumo final
    print("\n" + "=" * 70)
    print("📊 RESUMO DOS TESTES DO SISTEMA COMPLETO")
    print("=" * 70)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSOU" if result else "❌ FALHOU"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    overall_score = (passed / len(results)) * 100
    print(f"\n📈 Score geral: {passed}/{len(results)} ({overall_score:.1f}%)")
    
    if overall_score >= 75:
        print("\n🎉 SISTEMA PRONTO PARA ATUALIZAÇÃO MASSIVA!")
        print("✅ Todos os componentes funcionando corretamente")
        print("✅ Lista de 1439 Pokémon gerada")
        print("✅ Sistema de priorização ativo")
        print("✅ Mapeamento de habilidades correto")
        print("✅ Funcionalidade básica verificada")
        
        print(f"\n🚀 PRÓXIMO PASSO:")
        print("   Executar: python complete_pokemon_update.py")
        print("   Para atualizar todos os 1439 Pokémon")
        
    else:
        print("\n⚠️  SISTEMA PRECISA DE AJUSTES!")
        print("❌ Alguns testes falharam")
        print("🔧 Verificar problemas reportados acima")
        print("🔧 Corrigir antes de executar atualização massiva")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
