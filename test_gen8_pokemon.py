#!/usr/bin/env python3
"""
Teste específico para Pokémon da Generation VIII (Sizzlipede e Centiskorch)
"""

from pokemon_updater import PokemonUpdater
import time

def test_gen8_pokemon():
    print('🔥 TESTE: SIZZLIPEDE E CENTISKORCH (Generation VIII)')
    print('=' * 60)

    updater = PokemonUpdater()

    # Lista de Pokémon para testar
    pokemon_tests = [
        (850, 'sizzlipede'),
        (851, 'centiskorch')
    ]

    for pokemon_id, pokemon_name in pokemon_tests:
        print(f'\n🔍 TESTANDO: {pokemon_name.upper()} (#{pokemon_id})')
        print('-' * 40)

        try:
            # Obtém dados do Pokémon
            print(f'📡 Obtendo dados da PokeAPI...')
            pokemon_data = updater.get_pokemon_data(pokemon_id)

            if pokemon_data:
                print(f'✅ Dados obtidos com sucesso')

                # Processa dados da geração mais recente
                print(f'🔄 Processando dados da geração mais recente...')
                latest_data = updater.get_latest_generation_data(pokemon_data)

                # Mostra informações da geração
                generation_used = latest_data['moves'].get('generation_used', 'unknown')
                moves = latest_data['moves']['level_up']

                print(f'✅ Geração usada: {generation_used}')
                print(f'✅ Número de moves: {len(moves)}')

                # Mostra alguns moves para verificação
                print(f'📋 Primeiros 8 moves:')
                for i, move in enumerate(moves[:8]):
                    if isinstance(move, dict):
                        level = move.get('level', 0)
                        move_name = move.get('move', {}).get('name', 'unknown')
                    else:
                        level = move[0] if len(move) > 0 else 0
                        move_name = move[1] if len(move) > 1 else 'unknown'
                    print(f'   Level {level:2d}: {move_name}')

                # Gera base stats
                print(f'\n🔧 Gerando base stats...')
                base_stats = updater.generate_base_stats_entry(pokemon_id, pokemon_name, latest_data)

                # Gera moveset
                print(f'🔧 Gerando moveset...')
                moveset_code = updater.generate_level_up_moves(pokemon_name, moves)

                print(f'\n📝 MOVESET GERADO:')
                print(moveset_code)

                # Verifica se existe no arquivo atual
                print(f'\n🔍 Verificando arquivo atual...')
                try:
                    with open('src/Learnsets.c', 'r', encoding='utf-8') as f:
                        content = f.read()
                        pattern = f's{pokemon_name.capitalize()}LevelUpLearnset'
                        if pattern in content:
                            print(f'✅ Moveset já existe no arquivo')

                            # Extrai moveset atual
                            import re
                            current_pattern = rf'static const struct LevelUpMove s{pokemon_name.capitalize()}LevelUpLearnset\[\] = \{{(.*?)\}};'
                            match = re.search(current_pattern, content, re.DOTALL)
                            if match:
                                current_moveset = match.group(1).strip()
                                print(f'\n📋 MOVESET ATUAL:')
                                print(f'static const struct LevelUpMove s{pokemon_name.capitalize()}LevelUpLearnset[] = {{')
                                print(current_moveset)
                                print('};')
                        else:
                            print(f'⚠️  Moveset não encontrado no arquivo')
                except Exception as e:
                    print(f'❌ Erro ao verificar arquivo: {e}')

            else:
                print(f'❌ Erro ao obter dados do {pokemon_name}')

        except Exception as e:
            print(f'❌ Erro durante o teste: {e}')

        print(f'\n' + '='*60)
        time.sleep(1)  # Pausa entre requests

if __name__ == '__main__':
    test_gen8_pokemon()
