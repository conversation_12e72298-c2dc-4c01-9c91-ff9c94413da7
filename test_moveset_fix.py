#!/usr/bin/env python3
"""
Teste específico para corrigir o problema dos movesets
"""

from pokemon_updater import PokemonUpdater

def test_moveset_update():
    """Testa a atualização de moveset especificamente"""
    print("🔧 TESTE DE CORREÇÃO DE MOVESETS")
    print("=" * 50)
    
    updater = PokemonUpdater()
    
    # Testa com Grovyle especificamente
    pokemon_id = 253
    pokemon_name = "grovyle"
    
    print(f"🔍 Testando {pokemon_name.upper()} (#{pokemon_id})...")
    
    # Obtém dados
    pokemon_data = updater.get_pokemon_data(pokemon_id)
    if not pokemon_data:
        print("❌ Erro ao obter dados")
        return False
    
    # Extrai dados com sistema corrigido
    latest_data = updater.get_latest_generation_data(pokemon_data)
    generation_used = latest_data['moves'].get('generation_used', 'unknown')
    moves = latest_data['moves']['level_up']
    
    print(f"✅ Geração usada: {generation_used}")
    print(f"✅ Total de moves: {len(moves)}")
    
    # Mostra moveset Generation IX
    print(f"\n📋 Moveset completo da {generation_used}:")
    for move in moves:
        level = move['level']
        move_name = move['move']
        print(f"   Level {level:2d}: {move_name}")
    
    # Gera código do moveset
    moveset_code = updater.generate_level_up_moves(pokemon_name, moves)
    
    print(f"\n📝 Código gerado:")
    lines = moveset_code.split('\n')
    for line in lines:
        print(f"   {line}")
    
    # Prepara dados para aplicação
    base_stats_entry = updater.generate_base_stats_entry(pokemon_id, pokemon_name, latest_data)
    
    pokemon_updates = [{
        'pokemon_id': pokemon_id,
        'pokemon_name': pokemon_name,
        'base_stats_entry': base_stats_entry,
        'level_up_moves': moveset_code,
        'latest_data': latest_data
    }]
    
    # Aplica atualização
    print(f"\n🔧 Aplicando atualização...")
    if updater.apply_updates_to_files(pokemon_updates):
        print("✅ Atualização aplicada com sucesso!")
        return True
    else:
        print("❌ Erro ao aplicar atualização")
        return False

def verify_moveset_update():
    """Verifica se o moveset foi atualizado corretamente"""
    print(f"\n🔍 VERIFICANDO ATUALIZAÇÃO DO MOVESET")
    print("=" * 50)
    
    try:
        # Lê o arquivo atualizado
        with open("src/Learnsets.c", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Procura pelo moveset do Grovyle
        import re
        pattern = r'static const struct LevelUpMove sGrovyleLevelUpLearnset\[\][^;]+;'
        match = re.search(pattern, content, re.DOTALL)
        
        if match:
            moveset = match.group(0)
            print("✅ Moveset do Grovyle encontrado:")
            
            # Mostra o moveset atualizado
            lines = moveset.split('\n')
            for line in lines:
                print(f"   {line}")
            
            # Verifica se é da Generation IX (deve ter moves específicos)
            if "MOVE_LEAFAGE" in moveset:
                print("\n✅ Moveset contém LEAFAGE (Generation IX confirmado)")
            else:
                print("\n⚠️  Moveset pode não ser da Generation IX")
            
            # Conta moves
            move_count = moveset.count("LEVEL_UP_MOVE")
            print(f"\n📊 Total de moves: {move_count}")
            
            return True
        else:
            print("❌ Moveset do Grovyle não encontrado")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao verificar: {e}")
        return False

def test_compilation():
    """Testa se o projeto ainda compila após a correção"""
    print(f"\n🔨 TESTANDO COMPILAÇÃO")
    print("=" * 50)
    
    import subprocess
    
    try:
        print("🔧 Executando compilação...")
        result = subprocess.run(["python", "scripts/make.py"], 
                              capture_output=True, 
                              text=True, 
                              timeout=60)
        
        if result.returncode == 0:
            print("✅ COMPILAÇÃO BEM-SUCEDIDA!")
            print("🎉 Moveset corrigido e projeto funcionando")
            return True
        else:
            print("❌ ERRO DE COMPILAÇÃO!")
            print("Erros:")
            print(result.stderr[:300])
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Timeout na compilação")
        return False
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def main():
    """Função principal"""
    print("🧪 TESTE COMPLETO DE CORREÇÃO DE MOVESETS")
    print("=" * 60)
    
    # Passo 1: Atualiza moveset
    update_success = test_moveset_update()
    
    if update_success:
        # Passo 2: Verifica atualização
        verify_success = verify_moveset_update()
        
        if verify_success:
            # Passo 3: Testa compilação
            compile_success = test_compilation()
            
            # Resultado final
            print(f"\n" + "=" * 60)
            print("🎯 RESULTADO FINAL")
            print("=" * 60)
            
            if compile_success:
                print("🎉 CORREÇÃO DE MOVESETS COMPLETAMENTE BEM-SUCEDIDA!")
                print("✅ Moveset atualizado para Generation IX")
                print("✅ Sistema de geração única funcionando")
                print("✅ Projeto compila sem erros")
                print("✅ Pronto para atualização completa")
            else:
                print("⚠️  CORREÇÃO PARCIALMENTE BEM-SUCEDIDA")
                print("✅ Moveset atualizado")
                print("❌ Problemas na compilação")
        else:
            print("❌ FALHA NA VERIFICAÇÃO")
    else:
        print("❌ FALHA NA ATUALIZAÇÃO")
    
    print(f"\n🎯 PRÓXIMO PASSO:")
    print("Executar atualização completa com sistema corrigido")

if __name__ == "__main__":
    main()
