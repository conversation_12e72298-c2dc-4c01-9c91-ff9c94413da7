#!/usr/bin/env python3
"""
Teste Real de Atualização com 10 Pokémon
Executa uma atualização real e testa a compilação
"""

from pokemon_updater import PokemonUpdater
import time

def test_real_update():
    """Executa atualização real com 10 Pokémon específicos"""
    print("🔄 TESTE REAL DE ATUALIZAÇÃO - 10 POKÉMON")
    print("=" * 60)
    
    updater = PokemonUpdater()
    
    # 10 Pokémon específicos para teste (incluindo alguns que sabemos que foram modificados)
    test_pokemon = [
        (1, "bulbasaur"),     # Starter Gen I
        (4, "charmander"),    # Starter Gen <PERSON>  
        (7, "squirtle"),      # Starter Gen I
        (25, "pikachu"),      # Pokémon icônico
        (22, "fearow"),       # Sabemos que foi modificado
        (24, "arbok"),        # Sabemos que foi modificado
        (252, "treecko"),     # Starter Gen III
        (253, "grovyle"),     # Evolução que testamos
        (328, "trapinch"),    # Pokémon que testamos
        (329, "vibrava"),     # Pokémon que sabemos que foi drasticamente modificado
    ]
    
    print(f"📋 Pokémon selecionados para teste:")
    for pokemon_id, pokemon_name in test_pokemon:
        print(f"   #{pokemon_id:03d}: {pokemon_name.title()}")
    
    print(f"\n🔧 Iniciando atualização...")
    
    # Processa cada Pokémon
    successful_updates = []
    failed_updates = []
    pokemon_updates = []
    
    for i, (pokemon_id, pokemon_name) in enumerate(test_pokemon, 1):
        print(f"\n[{i:2d}/10] Processando {pokemon_name.upper()}...")
        
        try:
            # Obtém dados da PokeAPI
            pokemon_data = updater.get_pokemon_data(pokemon_id)
            if pokemon_data:
                latest_data = updater.get_latest_generation_data(pokemon_data)
                
                # Mostra informações básicas
                stats = latest_data['stats']
                generation_used = latest_data['moves'].get('generation_used', 'unknown')
                
                print(f"   ✅ Stats: {stats['baseHP']}/{stats['baseAttack']}/{stats['baseDefense']}/{stats['baseSpAttack']}/{stats['baseSpDefense']}/{stats['baseSpeed']}")
                print(f"   ✅ Tipos: {latest_data['types'][0]} / {latest_data['types'][1]}")
                print(f"   ✅ Habilidades: {latest_data['abilities']['ability1']}, {latest_data['abilities']['hiddenAbility']}")
                print(f"   ✅ Moveset: {len(latest_data['moves']['level_up'])} moves da {generation_used}")
                
                # Prepara dados para aplicação
                pokemon_updates.append({
                    'pokemon_id': pokemon_id,
                    'pokemon_name': pokemon_name,
                    'base_stats_entry': updater.generate_base_stats_entry(pokemon_id, pokemon_name, latest_data),
                    'level_up_moves': updater.generate_level_up_moves(pokemon_name, latest_data['moves']['level_up']),
                    'latest_data': latest_data
                })
                
                successful_updates.append((pokemon_id, pokemon_name))
                
            else:
                print(f"   ❌ Erro ao obter dados")
                failed_updates.append((pokemon_id, pokemon_name))
                
        except Exception as e:
            print(f"   ❌ Erro crítico: {e}")
            failed_updates.append((pokemon_id, pokemon_name))
        
        # Pequena pausa para evitar rate limiting
        time.sleep(0.5)
    
    # Resumo do processamento
    print(f"\n" + "=" * 60)
    print("📊 RESUMO DO PROCESSAMENTO:")
    print(f"✅ Sucessos: {len(successful_updates)}")
    print(f"❌ Falhas: {len(failed_updates)}")
    
    if failed_updates:
        print(f"\n❌ Pokémon que falharam:")
        for pokemon_id, pokemon_name in failed_updates:
            print(f"   - {pokemon_name} (#{pokemon_id})")
    
    # Aplica atualizações se tudo deu certo
    if len(successful_updates) >= 8:  # Pelo menos 8 de 10 devem ter sucesso
        print(f"\n🔧 APLICANDO ATUALIZAÇÕES AOS ARQUIVOS...")
        
        if updater.apply_updates_to_files(pokemon_updates):
            print("✅ Atualizações aplicadas com sucesso!")
            return True
        else:
            print("❌ Erro ao aplicar atualizações")
            return False
    else:
        print(f"\n⚠️  Muitas falhas ({len(failed_updates)}/10), não aplicando atualizações")
        return False

def test_compilation():
    """Testa se o projeto compila após as atualizações"""
    print(f"\n🔨 TESTANDO COMPILAÇÃO DO PROJETO")
    print("=" * 60)
    
    import subprocess
    import os
    
    # Verifica se existe Makefile
    if not os.path.exists("Makefile"):
        print("❌ Makefile não encontrado")
        print("⚠️  Teste de compilação manual necessário")
        return False
    
    try:
        print("🔧 Executando 'make clean'...")
        result_clean = subprocess.run(["make", "clean"], 
                                    capture_output=True, 
                                    text=True, 
                                    timeout=30)
        
        if result_clean.returncode == 0:
            print("✅ Clean executado com sucesso")
        else:
            print(f"⚠️  Clean com warnings: {result_clean.stderr[:200]}")
        
        print("🔧 Executando 'make'...")
        result_make = subprocess.run(["make"], 
                                   capture_output=True, 
                                   text=True, 
                                   timeout=120)
        
        if result_make.returncode == 0:
            print("✅ COMPILAÇÃO BEM-SUCEDIDA!")
            print("🎉 Projeto compila corretamente com as atualizações")
            return True
        else:
            print("❌ ERRO DE COMPILAÇÃO!")
            print("Erros encontrados:")
            print(result_make.stderr[:500])
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Timeout na compilação")
        return False
    except FileNotFoundError:
        print("❌ Comando 'make' não encontrado")
        print("⚠️  Teste de compilação manual necessário")
        return False
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        return False

def verify_changes():
    """Verifica se as mudanças foram aplicadas corretamente"""
    print(f"\n🔍 VERIFICANDO MUDANÇAS APLICADAS")
    print("=" * 60)
    
    try:
        # Verifica se Vibrava foi corrigido
        with open("src/Base_Stats.c", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Procura pela entrada do Vibrava
        import re
        vibrava_pattern = r'\[SPECIES_VIBRAVA\]\s*=\s*{([^}]+)}'
        match = re.search(vibrava_pattern, content, re.DOTALL)
        
        if match:
            entry = match.group(1)
            
            # Verifica se os tipos estão corretos (Ground/Dragon)
            if "TYPE_GROUND" in entry and "TYPE_DRAGON" in entry:
                print("✅ Vibrava: Tipos corrigidos para Ground/Dragon")
            else:
                print("❌ Vibrava: Tipos ainda incorretos")
            
            # Verifica stats (devem ser menores que antes)
            hp_match = re.search(r'\.baseHP\s*=\s*(\d+)', entry)
            attack_match = re.search(r'\.baseAttack\s*=\s*(\d+)', entry)
            
            if hp_match and attack_match:
                hp = int(hp_match.group(1))
                attack = int(attack_match.group(1))
                
                if hp == 50 and attack == 70:
                    print("✅ Vibrava: Stats corrigidos (50/70/50/50/50/70)")
                else:
                    print(f"⚠️  Vibrava: Stats diferentes do esperado (HP:{hp}, ATK:{attack})")
            
        else:
            print("❌ Vibrava: Entrada não encontrada")
        
        # Verifica arquivo de backup
        if os.path.exists("src/Base_Stats.c.backup"):
            print("✅ Backup criado: src/Base_Stats.c.backup")
        else:
            print("⚠️  Backup não encontrado")
        
        if os.path.exists("src/Learnsets.c.backup"):
            print("✅ Backup criado: src/Learnsets.c.backup")
        else:
            print("⚠️  Backup não encontrado")
            
    except Exception as e:
        print(f"❌ Erro ao verificar mudanças: {e}")

def main():
    """Função principal do teste real"""
    print("🧪 TESTE REAL COMPLETO - ATUALIZAÇÃO E COMPILAÇÃO")
    print("=" * 70)
    
    # Passo 1: Atualização
    update_success = test_real_update()
    
    if update_success:
        # Passo 2: Verificação
        verify_changes()
        
        # Passo 3: Compilação
        compile_success = test_compilation()
        
        # Resultado final
        print(f"\n" + "=" * 70)
        print("🎯 RESULTADO FINAL DO TESTE REAL")
        print("=" * 70)
        
        if compile_success:
            print("🎉 TESTE REAL COMPLETAMENTE BEM-SUCEDIDO!")
            print("✅ Atualização aplicada corretamente")
            print("✅ Projeto compila sem erros")
            print("✅ Sistema pronto para atualização completa")
            
            print(f"\n🚀 PRÓXIMOS PASSOS:")
            print("1. Executar atualização completa: python complete_pokemon_update.py")
            print("2. Testar alguns Pokémon no jogo")
            print("3. Fazer commit das mudanças")
            
        else:
            print("⚠️  TESTE PARCIALMENTE BEM-SUCEDIDO")
            print("✅ Atualização aplicada corretamente")
            print("❌ Problemas na compilação detectados")
            print("🔧 Verificar erros de compilação antes de prosseguir")
    else:
        print(f"\n❌ TESTE FALHOU NA ATUALIZAÇÃO")
        print("🔧 Verificar problemas na obtenção/aplicação de dados")

if __name__ == "__main__":
    import os
    main()
