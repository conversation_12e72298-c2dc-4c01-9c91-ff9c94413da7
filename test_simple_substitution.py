#!/usr/bin/env python3
"""
Teste simples para verificar se a substituição está funcionando
"""

import re

def test_simple_substitution():
    """Testa substituição simples no Grovyle"""
    
    print("🧪 TESTE SIMPLES DE SUBSTITUIÇÃO")
    print("=" * 40)
    
    # Carrega arquivo atual
    with open("src/Learnsets.c", 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"📂 Arquivo carregado: {len(content)} chars")
    
    # Procura pelo Grovyle
    pattern = r'static const struct LevelUpMove sGrovyleLevelUpLearnset\[\] = \{.*?\};'
    match = re.search(pattern, content, re.DOTALL)
    
    if match:
        old_learnset = match.group(0)
        print(f"✅ Grovyle encontrado: {len(old_learnset)} chars")
        
        # Cria um novo learnset simples para teste
        new_learnset = """static const struct LevelUpMove sGrovyleLevelUpLearnset[] = {
    LEVEL_UP_MOVE( 1, MOVE_LEAFAGE),
    LEVEL_UP_MOVE( 1, MOVE_LEER),
    LEVEL_UP_MOVE( 1, MOVE_POUND),
    LEVEL_UP_MOVE( 1, MOVE_QUICKATTACK),
    LEVEL_UP_MOVE( 9, MOVE_MEGADRAIN),
    LEVEL_UP_MOVE(12, MOVE_DETECT),
    LEVEL_UP_MOVE(15, MOVE_QUICKGUARD),
    LEVEL_UP_MOVE(20, MOVE_ASSURANCE),
    LEVEL_UP_MOVE(25, MOVE_GIGADRAIN),
    LEVEL_UP_MOVE(30, MOVE_SLAM),
    LEVEL_UP_MOVE(35, MOVE_DOUBLETEAM),
    LEVEL_UP_MOVE(40, MOVE_LEAFBLADE),
    LEVEL_UP_MOVE(45, MOVE_SCREECH),
    LEVEL_UP_MOVE(50, MOVE_ENDEAVOR),
    LEVEL_UP_MOVE(55, MOVE_LEAFSTORM),
    LEVEL_UP_END
};"""
        
        print(f"🔧 Novo learnset criado: {len(new_learnset)} chars")
        
        # Faz a substituição
        updated_content = content.replace(old_learnset, new_learnset)
        
        if updated_content != content:
            print("✅ SUBSTITUIÇÃO FUNCIONOU!")
            
            # Salva o arquivo atualizado
            with open("src/Learnsets.c", 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            print("💾 Arquivo salvo com sucesso!")
            
            # Verifica se a mudança foi aplicada
            with open("src/Learnsets.c", 'r', encoding='utf-8') as f:
                verify_content = f.read()
            
            if "MOVE_LEAFAGE" in verify_content and "MOVE_FURYCUTTER" not in verify_content:
                print("🎉 VERIFICAÇÃO: Mudança aplicada com sucesso!")
            else:
                print("❌ VERIFICAÇÃO: Mudança não foi aplicada!")
        else:
            print("❌ SUBSTITUIÇÃO NÃO FUNCIONOU!")
            print("🔍 Comparando primeiras 100 chars:")
            print(f"Old: {old_learnset[:100]}...")
            print(f"New: {new_learnset[:100]}...")
    else:
        print("❌ Grovyle não encontrado!")

if __name__ == "__main__":
    test_simple_substitution()
