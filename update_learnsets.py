#!/usr/bin/env python3
"""
Script para atualizar os learnsets com dados da Generation IX
"""

import os
import re
import glob

def extract_learnset_from_file(file_path):
    """Extrai o learnset de um arquivo de atualização"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Procura pelo learnset
    learnset_match = re.search(r'static const struct LevelUpMove s(\w+)LevelUpLearnset\[\] = \{([^}]+)\}', content, re.DOTALL)
    
    if learnset_match:
        pokemon_name = learnset_match.group(1)
        moves_content = learnset_match.group(2)
        
        # Limpa e formata os moves
        moves_lines = []
        for line in moves_content.split('\n'):
            line = line.strip()
            if line and 'LEVEL_UP_MOVE' in line:
                moves_lines.append(f"\t{line}")
            elif line and 'LEVEL_UP_END' in line:
                moves_lines.append(f"\t{line}")
        
        return pokemon_name, moves_lines
    
    return None, None

def update_learnsets():
    """Atualiza os learnsets no arquivo Learnsets.c"""
    
    print("🔧 ATUALIZANDO LEARNSETS PARA GENERATION IX")
    print("=" * 50)
    
    # Lê o arquivo atual de learnsets
    with open("src/Learnsets.c", "r", encoding="utf-8") as f:
        learnsets_content = f.read()
    
    # Backup do arquivo original
    with open("src/Learnsets.c.backup", "w", encoding="utf-8") as f:
        f.write(learnsets_content)
    
    print("📋 Backup criado: src/Learnsets.c.backup")
    
    # Processa arquivos de atualização
    update_files = glob.glob("pokemon_updates/pokemon_*.txt")
    updated_count = 0
    
    print(f"📁 Encontrados {len(update_files)} arquivos de atualização")
    
    for file_path in update_files:
        pokemon_name, moves_lines = extract_learnset_from_file(file_path)
        
        if pokemon_name and moves_lines:
            # Mapeia nomes especiais
            name_mapping = {
                'pokemon_253': 'Grovyle',
                'pokemon_254': 'Sceptile',
                'pokemon_252': 'Treecko',
                # Adicione mais mapeamentos conforme necessário
            }
            
            # Extrai número do Pokémon do nome do arquivo
            file_match = re.search(r'pokemon_(\d+)_\d+\.txt', file_path)
            if file_match:
                pokemon_id = int(file_match.group(1))
                
                # Procura pelo learnset existente no arquivo
                pattern = rf'static const struct LevelUpMove s\w*LevelUpLearnset\[\] = \{{[^}}]+\}};'
                
                # Encontra todos os learnsets
                learnset_matches = list(re.finditer(pattern, learnsets_content, re.DOTALL))
                
                # Atualiza o learnset correspondente (baseado na posição)
                if pokemon_id <= len(learnset_matches):
                    old_learnset = learnset_matches[pokemon_id - 1].group(0)
                    
                    # Extrai o nome do learnset existente
                    name_match = re.search(r's(\w+)LevelUpLearnset', old_learnset)
                    if name_match:
                        existing_name = name_match.group(1)
                        
                        # Cria o novo learnset
                        new_learnset = f"static const struct LevelUpMove s{existing_name}LevelUpLearnset[] = {{\n"
                        new_learnset += "\n".join(moves_lines)
                        new_learnset += "\n};"
                        
                        # Substitui no conteúdo
                        learnsets_content = learnsets_content.replace(old_learnset, new_learnset)
                        updated_count += 1
                        
                        if updated_count <= 10:  # Mostra apenas os primeiros 10
                            print(f"✅ Atualizado: {existing_name} (#{pokemon_id})")
    
    print(f"\n📊 RESULTADO:")
    print(f"   ✅ {updated_count} learnsets atualizados")
    
    # Salva o arquivo atualizado
    with open("src/Learnsets.c", "w", encoding="utf-8") as f:
        f.write(learnsets_content)
    
    print("💾 Arquivo Learnsets.c atualizado!")
    
    return updated_count > 0

def verify_grovyle_update():
    """Verifica se o Grovyle foi atualizado corretamente"""
    
    print("\n🔍 VERIFICANDO ATUALIZAÇÃO DO GROVYLE...")
    print("=" * 40)
    
    with open("src/Learnsets.c", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Procura pelo learnset do Grovyle
    grovyle_match = re.search(r'static const struct LevelUpMove sGrovyleLevelUpLearnset\[\] = \{([^}]+)\}', content, re.DOTALL)
    
    if grovyle_match:
        moves_content = grovyle_match.group(1)
        
        # Verifica se tem os moves corretos da Generation IX
        gen9_moves = ['MOVE_LEAFAGE', 'MOVE_ASSURANCE', 'MOVE_QUICK_GUARD']
        found_gen9_moves = []
        
        for move in gen9_moves:
            if move in moves_content:
                found_gen9_moves.append(move)
        
        if found_gen9_moves:
            print(f"✅ Grovyle atualizado com moves da Gen IX:")
            for move in found_gen9_moves:
                print(f"   - {move}")
            return True
        else:
            print("❌ Grovyle ainda não tem moves da Generation IX")
            return False
    else:
        print("❌ Learnset do Grovyle não encontrado")
        return False

def main():
    """Função principal"""
    
    if not os.path.exists("src/Learnsets.c"):
        print("❌ Arquivo src/Learnsets.c não encontrado!")
        return False
    
    if not os.path.exists("pokemon_updates"):
        print("❌ Pasta pokemon_updates não encontrada!")
        return False
    
    # Atualiza learnsets
    success = update_learnsets()
    
    if success:
        # Verifica atualização do Grovyle
        verify_grovyle_update()
        
        print("\n🎯 PRÓXIMO PASSO:")
        print("Execute: python scripts/make.py")
        print("Para testar a compilação com learnsets atualizados")
        
        return True
    
    return False

if __name__ == "__main__":
    main()
