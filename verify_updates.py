#!/usr/bin/env python3
"""
Verificação de Atualizações Pokémon
Script para verificar se as atualizações foram aplicadas corretamente
"""

import re
import os
from pokemon_updater import PokemonUpdater

def verify_base_stats_updates():
    """Verifica se as atualizações no Base_Stats.c foram aplicadas"""
    print("🔍 VERIFICANDO ATUALIZAÇÕES EM BASE_STATS.C")
    print("=" * 50)
    
    try:
        with open("src/Base_Stats.c", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Pokémon que sabemos que foram atualizados
        test_pokemon = [
            ("VIBRAVA", "TYPE_GROUND", "TYPE_DRAGON", 50, 70),  # HP, Attack
            ("TREECKO", "TYPE_GRASS", "TYPE_GRASS", 40, 45),
            ("BLAZIKEN", "TYPE_FIRE", "TYPE_FIGHTING", 80, 120),
            ("BULBASAUR", "TYPE_GRASS", "TYPE_POISON", 45, 49)
        ]
        
        verified = 0
        total = len(test_pokemon)
        
        for pokemon_name, type1, type2, hp, attack in test_pokemon:
            # Procura pela entrada do Pokémon
            pattern = rf'\[SPECIES_{pokemon_name}\]\s*=\s*{{([^}}]+)}}'
            match = re.search(pattern, content, re.DOTALL)
            
            if match:
                entry = match.group(1)
                
                # Verifica stats e tipos
                hp_match = re.search(r'\.baseHP\s*=\s*(\d+)', entry)
                attack_match = re.search(r'\.baseAttack\s*=\s*(\d+)', entry)
                type1_match = re.search(r'\.type1\s*=\s*(\w+)', entry)
                type2_match = re.search(r'\.type2\s*=\s*(\w+)', entry)
                
                if (hp_match and int(hp_match.group(1)) == hp and
                    attack_match and int(attack_match.group(1)) == attack and
                    type1_match and type1_match.group(1) == type1 and
                    type2_match and type2_match.group(1) == type2):
                    
                    print(f"✅ {pokemon_name}: Stats e tipos corretos")
                    verified += 1
                else:
                    print(f"❌ {pokemon_name}: Dados incorretos ou não atualizados")
                    if hp_match:
                        print(f"   HP: esperado {hp}, encontrado {hp_match.group(1)}")
                    if attack_match:
                        print(f"   Attack: esperado {attack}, encontrado {attack_match.group(1)}")
                    if type1_match:
                        print(f"   Type1: esperado {type1}, encontrado {type1_match.group(1)}")
                    if type2_match:
                        print(f"   Type2: esperado {type2}, encontrado {type2_match.group(1)}")
            else:
                print(f"❌ {pokemon_name}: Entrada não encontrada")
        
        print(f"\n📊 Verificação Base Stats: {verified}/{total} Pokémon corretos")
        return verified == total
        
    except FileNotFoundError:
        print("❌ Arquivo src/Base_Stats.c não encontrado")
        return False

def verify_learnsets_updates():
    """Verifica se as atualizações no Learnsets.c foram aplicadas"""
    print("\n🔍 VERIFICANDO ATUALIZAÇÕES EM LEARNSETS.C")
    print("=" * 50)
    
    try:
        with open("src/Learnsets.c", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Pokémon para verificar movesets
        test_pokemon = ["Vibrava", "Treecko", "Blaziken", "Bulbasaur"]
        
        verified = 0
        total = len(test_pokemon)
        
        for pokemon_name in test_pokemon:
            # Procura pela definição do moveset
            pattern = rf'static const struct LevelUpMove s{pokemon_name}LevelUpLearnset\[\]\s*=\s*{{([^}}]+LEVEL_UP_END[^}}]*)}}'
            match = re.search(pattern, content, re.DOTALL)
            
            if match:
                moveset = match.group(1)
                
                # Conta quantos moves tem
                move_count = len(re.findall(r'LEVEL_UP_MOVE\(', moveset))
                
                if move_count > 0:
                    print(f"✅ {pokemon_name}: Moveset atualizado ({move_count} moves)")
                    verified += 1
                else:
                    print(f"❌ {pokemon_name}: Moveset vazio")
            else:
                print(f"❌ {pokemon_name}: Moveset não encontrado")
        
        print(f"\n📊 Verificação Learnsets: {verified}/{total} Pokémon corretos")
        return verified == total
        
    except FileNotFoundError:
        print("❌ Arquivo src/Learnsets.c não encontrado")
        return False

def verify_backup_files():
    """Verifica se os arquivos de backup foram criados"""
    print("\n🔍 VERIFICANDO ARQUIVOS DE BACKUP")
    print("=" * 50)
    
    backup_files = [
        "src/Base_Stats.c.backup",
        "src/Learnsets.c.backup"
    ]
    
    verified = 0
    total = len(backup_files)
    
    for backup_file in backup_files:
        if os.path.exists(backup_file):
            size = os.path.getsize(backup_file)
            print(f"✅ {backup_file}: Existe ({size:,} bytes)")
            verified += 1
        else:
            print(f"❌ {backup_file}: Não encontrado")
    
    print(f"\n📊 Verificação Backups: {verified}/{total} arquivos encontrados")
    return verified == total

def verify_output_files():
    """Verifica se os arquivos de saída foram criados"""
    print("\n🔍 VERIFICANDO ARQUIVOS DE SAÍDA")
    print("=" * 50)
    
    # Verifica diretório pokemon_updates
    if os.path.exists("pokemon_updates"):
        files = [f for f in os.listdir("pokemon_updates") if f.endswith('.txt')]
        print(f"✅ Diretório pokemon_updates: {len(files)} arquivos")
        
        # Mostra alguns exemplos
        for i, file in enumerate(files[:5]):
            print(f"   📄 {file}")
        if len(files) > 5:
            print(f"   ... e mais {len(files) - 5} arquivos")
    else:
        print("❌ Diretório pokemon_updates não encontrado")
        return False
    
    # Verifica relatórios
    reports = [
        "pokemon_update_report.md",
        "complete_pokemon_update_report.md"
    ]
    
    found_reports = 0
    for report in reports:
        if os.path.exists(report):
            size = os.path.getsize(report)
            print(f"✅ {report}: Existe ({size:,} bytes)")
            found_reports += 1
        else:
            print(f"⚠️  {report}: Não encontrado (pode não ter sido gerado ainda)")
    
    return len(files) > 0

def check_compilation():
    """Verifica se o projeto pode ser compilado"""
    print("\n🔍 VERIFICANDO COMPILAÇÃO DO PROJETO")
    print("=" * 50)
    
    # Verifica se existe Makefile
    if os.path.exists("Makefile"):
        print("✅ Makefile encontrado")
        
        # Tenta compilar (dry run)
        print("⚠️  Para verificar compilação, execute manualmente:")
        print("   make clean && make")
        print("   Ou use seu ambiente de desenvolvimento preferido")
        
        return True
    else:
        print("❌ Makefile não encontrado")
        print("⚠️  Verifique manualmente se o projeto compila")
        return False

def generate_verification_report():
    """Gera relatório de verificação"""
    print("\n📄 GERANDO RELATÓRIO DE VERIFICAÇÃO")
    print("=" * 50)
    
    report = """# RELATÓRIO DE VERIFICAÇÃO - ATUALIZAÇÃO POKÉMON

## Resumo da Verificação

### ✅ Verificações Realizadas:
1. **Base Stats**: Verificação de stats e tipos atualizados
2. **Learnsets**: Verificação de movesets atualizados  
3. **Backups**: Verificação de arquivos de backup
4. **Arquivos de Saída**: Verificação de relatórios e dados gerados

### 🎯 Próximos Passos:
1. **Compilar o projeto** para verificar se não há erros
2. **Testar no jogo** alguns Pokémon atualizados
3. **Ajustar manualmente** dados que precisam de refinamento:
   - EV Yields (valores de EV dados quando derrotado)
   - Exp Yield (experiência dada quando derrotado)
   - Body Color (cor do Pokémon)
   - Held Items (itens carregados na natureza)

### 📊 Exemplos de Pokémon Atualizados:

#### Vibrava (Corrigido)
- **Antes**: Bug/Dragon, Stats inflados
- **Depois**: Ground/Dragon, Stats oficiais (340 BST)

#### Treecko (Mantido fiel)
- **Stats**: 40/45/35/65/55/70 (310 BST)
- **Tipos**: Grass/Grass
- **Habilidades**: Overgrow + Unburden (Hidden)

### ⚠️ Dados que Precisam de Ajuste Manual:

Alguns dados não estão disponíveis na PokeAPI e foram marcados como placeholders:
- `expYield = 64` - Precisa ser calculado baseado no BST
- `evYield_* = 0` - Precisa ser definido baseado no papel do Pokémon
- `bodyColor = BODY_COLOR_GREEN` - Precisa ser definido corretamente

### 🔧 Como Ajustar Dados Manualmente:

1. **EV Yields**: Baseie-se no stat mais alto do Pokémon
2. **Exp Yield**: Use fórmulas baseadas no BST e raridade
3. **Body Color**: Consulte sprites oficiais
4. **Held Items**: Consulte dados oficiais dos jogos

---

**Data da Verificação**: """ + time.strftime('%Y-%m-%d %H:%M:%S') + """
**Sistema**: Pokemon Data Updater - Generation IX
"""
    
    with open("verification_report.md", "w", encoding="utf-8") as f:
        f.write(report)
    
    print("✅ Relatório salvo em: verification_report.md")

def main():
    """Função principal de verificação"""
    print("🔍 VERIFICAÇÃO DE ATUALIZAÇÕES POKÉMON")
    print("=" * 60)
    
    # Executa todas as verificações
    checks = [
        ("Base Stats", verify_base_stats_updates),
        ("Learnsets", verify_learnsets_updates),
        ("Backups", verify_backup_files),
        ("Arquivos de Saída", verify_output_files),
        ("Compilação", check_compilation)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ Erro na verificação {check_name}: {e}")
            results.append((check_name, False))
    
    # Resumo final
    print("\n" + "=" * 60)
    print("📊 RESUMO DA VERIFICAÇÃO")
    print("=" * 60)
    
    passed = 0
    for check_name, result in results:
        status = "✅ PASSOU" if result else "❌ FALHOU"
        print(f"{status}: {check_name}")
        if result:
            passed += 1
    
    print(f"\n📈 Resultado: {passed}/{len(results)} verificações passaram")
    
    if passed == len(results):
        print("\n🎉 TODAS AS VERIFICAÇÕES PASSARAM!")
        print("✅ Atualizações aplicadas com sucesso")
    else:
        print("\n⚠️  ALGUMAS VERIFICAÇÕES FALHARAM!")
        print("🔧 Verifique os problemas reportados acima")
    
    # Gera relatório
    generate_verification_report()
    
    print(f"\n📄 Relatório detalhado salvo em: verification_report.md")

if __name__ == "__main__":
    import time
    main()
